/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
import{_ as a,h as s,o as e,d as t,f as o,j as r,e as i,g as l}from"./index.a5cb1178.js";import n from"./header.c1c70765.js";import c from"./menu.29eff15f.js";import"./ASD.492c8837.js";const f={class:"layout-page"},u={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},d=a(Object.assign({name:"Client"},{setup:a=>(a,d)=>{const p=s("router-view");return e(),t("div",f,[o("公共顶部菜单-"),r(n),i("div",u,[o("公共侧边栏菜单"),r(c),i("div",m,[o("主流程路由渲染点"),(e(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{d as default};
