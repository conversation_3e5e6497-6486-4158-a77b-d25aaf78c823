/*! 
 Build based on gin-vue-admin 
 Time : 1749637440000 */
System.register(["./header-legacy.9b8775c2.js","./menu-legacy.a5510bd9.js","./index-legacy.453b6b0c.js","./ASD-legacy.b6ffb1bc.js"],(function(t,e){"use strict";var a,n,i,u,o,l,c,r,d=document.createElement("style");return d.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(d),{setters:[function(t){a=t.default},function(t){n=t.default},function(t){i=t.h,u=t.o,o=t.d,l=t.j,c=t.e,r=t.f},function(){}],execute:function(){var e={class:"layout-page"},d={class:"layout-wrap"},f={id:"layoutMain",class:"layout-main"};t("default",Object.assign({name:"Client"},{setup:function(t){return function(t,s){var h=i("router-view");return u(),o("div",e,[l(a),c("div",d,[l(n),c("div",f,[(u(),r(h,{key:t.$route.fullPath}))])])])}}}))}}}));
