{"version": 3, "file": "##.min.js", "names": ["MutationObserver", "mutations", "i", "length", "mutation", "type", "j", "addedNodes", "addedNode", "tagName", "sp", "System", "prepareImport", "observe", "document", "childList", "subtree"], "sources": ["dynamic-import-maps.js"], "sourcesContent": ["(function () {\n\n  /*\r\n   * Support for live DOM updating import maps\r\n   */\r\n  new MutationObserver(function (mutations) {\r\n    for (var i = 0; i < mutations.length; i++) {\r\n      var mutation = mutations[i];\r\n      if (mutation.type === 'childList')\r\n      for (var j = 0; j < mutation.addedNodes.length; j++) {\r\n        var addedNode = mutation.addedNodes[j];\r\n        if (addedNode.tagName === 'SCRIPT' && addedNode.type === 'systemjs-importmap' && !addedNode.sp) {\r\n          System.prepareImport(true);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  }).observe(document, { childList: true, subtree: true });\n\n})();\n"], "mappings": "AAKE,IAAIA,kBAAiB,SAAUC,GAC7B,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAUE,OAAQD,IAAK,CACzC,IAAIE,EAAWH,EAAUC,GACzB,GAAsB,cAAlBE,EAASC,KACb,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASG,WAAWJ,OAAQG,IAAK,CACnD,IAAIE,EAAYJ,EAASG,WAAWD,GACpC,GAA0B,WAAtBE,EAAUC,SAA2C,uBAAnBD,EAAUH,OAAkCG,EAAUE,GAAI,CAC9FC,OAAOC,eAAc,GACrB,KACF,CACF,CACF,CACF,IAAGC,QAAQC,SAAU,CAAEC,WAAW,EAAMC,SAAS"}