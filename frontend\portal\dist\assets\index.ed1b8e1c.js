/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{_ as e,b as a,a as o,u as n,U as t,r as s,N as i,P as l,K as c,c as r,V as d,p as u,h as v,o as m,g as p,w as f,e as g,I as h,j as w,B as y,f as b,d as x,T as k,F as I,i as C,t as _,m as j,R as S,W as N,A as U}from"./index.f6c71253.js";import{_ as A}from"./ASD.492c8837.js";import M from"./index.5dae6e86.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.453bb9e8.js";import"./menuItem.cfb7f063.js";import"./asyncSubmenu.9a954938.js";
/*! js-cookie v3.0.5 | MIT */function O(e){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var n in o)e[n]=o[n]}return e}var R=function e(a,o){function n(e,n,t){if("undefined"!=typeof document){"number"==typeof(t=O({},o,t)).expires&&(t.expires=new Date(Date.now()+864e5*t.expires)),t.expires&&(t.expires=t.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var s="";for(var i in t)t[i]&&(s+="; "+i,!0!==t[i]&&(s+="="+t[i].split(";")[0]));return document.cookie=e+"="+a.write(n,e)+s}}return Object.create({set:n,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var o=document.cookie?document.cookie.split("; "):[],n={},t=0;t<o.length;t++){var s=o[t].split("="),i=s.slice(1).join("=");try{var l=decodeURIComponent(s[0]);if(n[l]=a.read(i,l),e===l)break}catch(c){}}return e?n[e]:n}},remove:function(e,a){n(e,"",O({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,O({},this.attributes,a))},withConverter:function(a){return e(O({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const z={key:0,class:"icon menu-footer-icon","aria-hidden":"true"},D={key:1,class:"icon menu-footer-icon","aria-hidden":"true"},B={class:"header-row"},L={class:"header-col"},E={class:"header-cont"},T={class:"header-content pd-0"},$={class:"breadcrumb-col"},F={class:"breadcrumb"},q={class:"user-col"},J={class:"right-box"},V={class:"dp-flex justify-content-center align-items height-full width-full"},W={class:"header-avatar",style:{cursor:"pointer"}},G={style:{"margin-right":"9px",color:"#252631"}},H={key:0,class:"dropdown-menu"},P={key:0,class:"loading-overlay"},K=e(Object.assign({name:"Layout"},{setup(e){const O=a(),K=o(),Q=n(),X=t(),Y=s(!0),Z=s(!1),ee=s(!1),ae=s("7"),oe=()=>{document.body.clientWidth;ee.value=!1,Z.value=!1,Y.value=!0};oe();const ne=s(!1);i((()=>{l.emit("collapse",Y.value),l.emit("mobile",ee.value),l.on("reload",re),l.on("showLoading",(()=>{ne.value=!0})),l.on("closeLoading",(()=>{ne.value=!1})),window.onresize=()=>(oe(),l.emit("collapse",Y.value),void l.emit("mobile",ee.value)),O.loadingInstance&&O.loadingInstance.close(),document.addEventListener("click",te)})),c((()=>{document.removeEventListener("click",te)}));const te=e=>{if(!Y.value&&!ee.value){const a=document.querySelector(".gva-aside"),o=document.querySelector(".menu-total");a&&!a.contains(e.target)&&o&&!o.contains(e.target)&&(Y.value=!0,Z.value=!1,de.value=!1,l.emit("collapse",Y.value))}if(ue.value){const a=document.querySelector(".dropdown");a&&!a.contains(e.target)&&(ue.value=!1)}};r((()=>"dark"===O.sideMode?"#fff":"light"===O.sideMode?"#273444":O.baseColor));const se=r((()=>"dark"===O.sideMode?"#273444":"light"===O.sideMode?"#fff":O.sideMode)),ie=r((()=>Q.meta.matched)),le=s(!0);let ce=null;const re=async()=>{ce&&window.clearTimeout(ce),ce=window.setTimeout((async()=>{if(Q.meta.keepAlive)le.value=!1,await d(),le.value=!0;else{const e=Q.meta.title;K.push({name:"Reload",params:{title:e}})}}),400)},de=s(!1),ue=s(!1),ve=()=>{Y.value=!Y.value,Z.value=!Y.value,de.value=!Y.value,l.emit("collapse",Y.value)},me=()=>{ue.value=!ue.value},pe=()=>{K.push({name:"person"})};return u("day",ae),(e,a)=>{const o=v("base-aside"),n=v("base-icon"),t=v("router-view"),i=v("base-main"),l=v("base-container");return m(),p(l,{class:"layout-cont"},{default:f((()=>[g("div",{class:h([[Z.value?"openside":"hideside",ee.value?"mobile":""],"layout-wrapper"])},[g("div",{class:h([[de.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(de.value=!de.value,Z.value=!!Y.value,void ve()))},null,2),w(o,{class:"main-cont main-left gva-aside",collapsed:Y.value},{default:f((()=>[g("div",{class:h(["tilte",[Z.value?"openlogoimg":"hidelogoimg"]]),style:y({background:se.value})},[a[2]||(a[2]=g("img",{alt:"",class:"logoimg",src:A},null,-1)),b("          <div>"),b('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),b('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),b("          </div>")],6),w(M,{class:"aside"}),g("div",{class:"footer",style:y({background:se.value})},[g("div",{class:"menu-total",onClick:ve},[Y.value?(m(),x("svg",z,a[3]||(a[3]=[g("use",{"xlink:href":"#icon-expand"},null,-1)]))):(m(),x("svg",D,a[4]||(a[4]=[g("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),b(" 分块滑动功能 "),w(i,{class:"main-cont main-right"},{default:f((()=>[w(k,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[g("div",{style:y({width:`calc(100% - ${ee.value?"0px":Y.value?"54px":"220px"})`}),class:"topfix"},[g("div",B,[g("div",L,[g("header",E,[g("div",T,[a[7]||(a[7]=g("div",{class:"header-menu-col",style:{"z-index":"100"}},[b('                      <div class="menu-total" @click="totalCollapse">'),b('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),b('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),b("                      </div>")],-1)),g("div",$,[g("nav",F,[(m(!0),x(I,null,C(ie.value.slice(1,ie.value.length),(e=>(m(),x("div",{key:e.path,class:"breadcrumb-item"},_(j(S)(e.meta.topTitle||"",j(Q))),1)))),128))])]),g("div",q,[g("div",J,[b("                        <Search />"),g("div",{class:"dropdown",onClick:me},[g("div",V,[g("span",W,[b(" 展示当前登录用户名 "),g("span",G,_(j(O).userInfo.displayName?j(O).userInfo.displayName:j(O).userInfo.name),1),w(n,{name:"zhankai",size:"10px"})])]),ue.value?(m(),x("div",H,[b(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),g("div",{class:"dropdown-item",onClick:pe},a[5]||(a[5]=[g("svg",{class:"icon dropdown-item-icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-person"})],-1),g("span",null,"个人信息",-1)])),g("div",{class:"dropdown-item",onClick:a[1]||(a[1]=e=>(async()=>{document.location.protocol,document.location.host;const e={action:1,msg:"",platform:document.location.hostname},a=s({}),o=s("ws://127.0.0.1:50001"),n=navigator.platform;0!==n.indexOf("Mac")&&"MacIntel"!==n||(o.value="wss://127.0.0.1:50001");const t=async e=>{console.log(e,"0"),await a.value.send(e)},i=async()=>{console.log("socket断开链接"),await a.value.close()};console.log(`asecagent://?web=${JSON.stringify(e)}`),await O.LoginOut(),a.value=new WebSocket(o.value),a.value.onopen=async()=>{console.log("socket连接成功"),await t(JSON.stringify(e))},a.value.onmessage=async e=>{console.log(e),await i()},a.value.onerror=()=>{console.log("socket连接错误")},R.remove("asce_sms")})())},a[6]||(a[6]=[g("svg",{class:"icon dropdown-item-icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-logout"})],-1),g("span",null,"注销登录",-1)]))])):b("v-if",!0)]),b('                        <base-button type="text"'),b('                                   class="iconfont icon-rizhi1"'),b('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),b('                                   @click="toLog"'),b("                        >日志中心"),b("                        </base-button>")])])])])])]),b(" 当前面包屑用路由自动生成可根据需求修改 "),b('\r\n            :to="{ path: item.path }" 暂时注释不用'),b('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)])),_:1}),g("div",{class:h(["router-view-container",{loading:ne.value}])},[ne.value?(m(),x("div",P,a[8]||(a[8]=[g("div",{class:"loading-spinner"},[g("div",{class:"spinner"}),g("div",{class:"loading-text"},"正在加载中")],-1)]))):b("v-if",!0),le.value?(m(),p(t,{key:1,class:"admin-box"},{default:f((({Component:e})=>[g("div",null,[w(k,{mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[(m(),p(N,{include:j(X).keepAliveRouters},[(m(),p(U(e)))],1032,["include"]))])),_:2},1024)])])),_:1})):b("v-if",!0)],2),b("        <BottomInfo />"),b("        <setting />")])),_:1})],2)])),_:1})}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]);export{K as default};
