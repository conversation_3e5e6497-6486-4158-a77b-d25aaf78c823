/*! 
 Build based on gin-vue-admin 
 Time : 1749610601000 */
import s from"./index.aa6174f6.js";import{_ as a,h as c,o as t,d as e,e as p,j as n,w as o,k as l}from"./index.d0594432.js";const r={class:"access-main"},u={class:"content-wrapper"},m={class:"access-proxy-status"},d={class:"access-proxy-status-span"},i={class:"access-app"};const x=a({name:"Access",components:{AppPage:s}},[["render",function(s,a,x,f,y,_){const b=c("base-button"),g=c("AppPage");return t(),e("div",r,[p("ul",u,[p("li",m,[a[2]||(a[2]=p("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),p("span",d,[a[1]||(a[1]=p("span",{class:"access-proxy-status-tips"}," 点击连接，即可安全便捷地访问应用 ",-1)),n(b,{class:"access-proxy-status-btn",color:"#626aef",type:"primary"},{default:o((()=>a[0]||(a[0]=[l(" 一键连接 ")]))),_:1,__:[0]})])]),a[3]||(a[3]=p("li",{class:"access-common-status"},[p("span",{class:"access-common-status-span"},[p("span",null,"准入状态（企业网络下使用）："),p("span",{style:{color:"red"}},"未入网"),p("span",null,"（请重新建立连接）")]),p("span",{class:"access-common-status-detail"},[p("span",null,"查看详情")])],-1)),p("li",i,[n(g,{class:"access-app-page"})])])])}],["__scopeId","data-v-79882dcf"]]);export{x as default};
