/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import n from"./index.aa6174f6.js";import{_ as p,h as t,o as l,d as r,e as s,j as e,w as _,k as d}from"./index.d0594432.js";const i={name:"Access",components:{AppPage:n}},m={class:"access-main"},u={class:"content-wrapper"},x={class:"access-proxy-status"},f={class:"access-proxy-status-span"},y={class:"access-app"};function b(g,a,v,A,$,k){const o=t("base-button"),c=t("AppPage");return l(),r("div",m,[s("ul",u,[s("li",x,[a[2]||(a[2]=s("span",{class:"access-proxy-status-text"}," \u8FDE\u63A5\u72B6\u6001 ",-1)),s("span",f,[a[1]||(a[1]=s("span",{class:"access-proxy-status-tips"}," \u70B9\u51FB\u8FDE\u63A5\uFF0C\u5373\u53EF\u5B89\u5168\u4FBF\u6377\u5730\u8BBF\u95EE\u5E94\u7528 ",-1)),e(o,{class:"access-proxy-status-btn",color:"#626aef",type:"primary"},{default:_(()=>a[0]||(a[0]=[d(" \u4E00\u952E\u8FDE\u63A5 ")])),_:1,__:[0]})])]),a[3]||(a[3]=s("li",{class:"access-common-status"},[s("span",{class:"access-common-status-span"},[s("span",null,"\u51C6\u5165\u72B6\u6001\uFF08\u4F01\u4E1A\u7F51\u7EDC\u4E0B\u4F7F\u7528\uFF09\uFF1A"),s("span",{style:{color:"red"}},"\u672A\u5165\u7F51"),s("span",null,"\uFF08\u8BF7\u91CD\u65B0\u5EFA\u7ACB\u8FDE\u63A5\uFF09")]),s("span",{class:"access-common-status-detail"},[s("span",null,"\u67E5\u770B\u8BE6\u60C5")])],-1)),s("li",y,[e(c,{class:"access-app-page"})])])])}const N=p(i,[["render",b],["__scopeId","data-v-79882dcf"]]);export{N as default};
