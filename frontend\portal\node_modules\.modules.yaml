hoistPattern:
  - '*'
hoistedLocations:
  '@achrinza/node-ipc@9.2.2':
    - node_modules\@achrinza\node-ipc
  '@ampproject/remapping@2.3.0':
    - node_modules\@ampproject\remapping
  '@babel/code-frame@7.27.1':
    - node_modules\@babel\code-frame
  '@babel/compat-data@7.27.5':
    - node_modules\@babel\compat-data
  '@babel/core@7.27.4':
    - node_modules\@babel\core
  '@babel/generator@7.27.5':
    - node_modules\@babel\generator
  '@babel/helper-annotate-as-pure@7.27.3':
    - node_modules\@babel\helper-annotate-as-pure
  '@babel/helper-compilation-targets@7.27.2':
    - node_modules\@babel\helper-compilation-targets
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\helper-create-class-features-plugin
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\helper-create-regexp-features-plugin
  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.27.4)':
    - node_modules\@babel\helper-define-polyfill-provider
  '@babel/helper-member-expression-to-functions@7.27.1':
    - node_modules\@babel\helper-member-expression-to-functions
  '@babel/helper-module-imports@7.27.1':
    - node_modules\@babel\helper-module-imports
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    - node_modules\@babel\helper-module-transforms
  '@babel/helper-optimise-call-expression@7.27.1':
    - node_modules\@babel\helper-optimise-call-expression
  '@babel/helper-plugin-utils@7.27.1':
    - node_modules\@babel\helper-plugin-utils
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\helper-remap-async-to-generator
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\helper-replace-supers
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    - node_modules\@babel\helper-skip-transparent-expression-wrappers
  '@babel/helper-string-parser@7.27.1':
    - node_modules\@babel\helper-string-parser
  '@babel/helper-validator-identifier@7.27.1':
    - node_modules\@babel\helper-validator-identifier
  '@babel/helper-validator-option@7.27.1':
    - node_modules\@babel\helper-validator-option
  '@babel/helper-wrap-function@7.27.1':
    - node_modules\@babel\helper-wrap-function
  '@babel/helpers@7.27.6':
    - node_modules\@babel\helpers
  '@babel/parser@7.27.5':
    - node_modules\@babel\parser
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-bugfix-firefox-class-in-computed-class-key
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-bugfix-safari-class-field-initializer-scope
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-bugfix-safari-id-destructuring-collision-in-function-expression
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-bugfix-v8-spread-parameters-in-optional-chaining
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-bugfix-v8-static-class-fields-redefine-readonly
  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-proposal-class-properties
  '@babel/plugin-proposal-decorators@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-proposal-decorators
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-proposal-private-property-in-object
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-decorators
  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-dynamic-import
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-import-assertions
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-import-attributes
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-import-meta
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-jsx
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-syntax-unicode-sets-regex
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-arrow-functions
  '@babel/plugin-transform-async-generator-functions@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-async-generator-functions
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-async-to-generator
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-block-scoped-functions
  '@babel/plugin-transform-block-scoping@7.27.5(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-block-scoping
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-class-properties
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-class-static-block
  '@babel/plugin-transform-classes@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-classes
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-computed-properties
  '@babel/plugin-transform-destructuring@7.27.3(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-destructuring
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-dotall-regex
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-duplicate-keys
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-duplicate-named-capturing-groups-regex
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-dynamic-import
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-exponentiation-operator
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-export-namespace-from
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-for-of
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-function-name
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-json-strings
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-literals
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-logical-assignment-operators
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-member-expression-literals
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-modules-amd
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-modules-commonjs
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-modules-systemjs
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-modules-umd
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-named-capturing-groups-regex
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-new-target
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-nullish-coalescing-operator
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-numeric-separator
  '@babel/plugin-transform-object-rest-spread@7.27.3(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-object-rest-spread
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-object-super
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-optional-catch-binding
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-optional-chaining
  '@babel/plugin-transform-parameters@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-parameters
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-private-methods
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-private-property-in-object
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-property-literals
  '@babel/plugin-transform-regenerator@7.27.5(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-regenerator
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-regexp-modifiers
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-reserved-words
  '@babel/plugin-transform-runtime@7.27.4(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-runtime
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-shorthand-properties
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-spread
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-sticky-regex
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-template-literals
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-typeof-symbol
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-unicode-escapes
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-unicode-property-regex
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-unicode-regex
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.4)':
    - node_modules\@babel\plugin-transform-unicode-sets-regex
  '@babel/preset-env@7.27.2(@babel/core@7.27.4)':
    - node_modules\@babel\preset-env
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.4)':
    - node_modules\@babel\preset-modules
  '@babel/runtime@7.27.6':
    - node_modules\@babel\runtime
  '@babel/standalone@7.27.6':
    - node_modules\@babel\standalone
  '@babel/template@7.27.2':
    - node_modules\@babel\template
  '@babel/traverse@7.27.4':
    - node_modules\@babel\traverse
  '@babel/types@7.27.6':
    - node_modules\@babel\types
  '@hapi/address@2.1.4':
    - node_modules\@hapi\address
  '@hapi/bourne@1.3.2':
    - node_modules\@hapi\bourne
  '@hapi/hoek@8.5.1':
    - node_modules\@hapi\hoek
  '@hapi/joi@15.1.1':
    - node_modules\@hapi\joi
  '@hapi/topo@3.1.6':
    - node_modules\@hapi\topo
  '@intervolga/optimize-cssnano-plugin@1.0.6(webpack@4.47.0)':
    - node_modules\@intervolga\optimize-cssnano-plugin
  '@isaacs/cliui@8.0.2':
    - node_modules\@isaacs\cliui
  '@jridgewell/gen-mapping@0.3.8':
    - node_modules\@jridgewell\gen-mapping
  '@jridgewell/resolve-uri@3.1.2':
    - node_modules\@jridgewell\resolve-uri
  '@jridgewell/set-array@1.2.1':
    - node_modules\@jridgewell\set-array
  '@jridgewell/source-map@0.3.6':
    - node_modules\@jridgewell\source-map
  '@jridgewell/sourcemap-codec@1.5.0':
    - node_modules\@jridgewell\sourcemap-codec
  '@jridgewell/trace-mapping@0.3.25':
    - node_modules\@jridgewell\trace-mapping
  '@mrmlnc/readdir-enhanced@2.2.1':
    - node_modules\@mrmlnc\readdir-enhanced
  '@node-ipc/js-queue@2.0.3':
    - node_modules\@node-ipc\js-queue
  '@nodelib/fs.scandir@2.1.5':
    - node_modules\@nodelib\fs.scandir
  '@nodelib/fs.stat@1.1.3':
    - node_modules\fast-glob\node_modules\@nodelib\fs.stat
  '@nodelib/fs.stat@2.0.5':
    - node_modules\@nodelib\fs.stat
  '@nodelib/fs.walk@1.2.8':
    - node_modules\@nodelib\fs.walk
  '@parcel/watcher-win32-x64@2.5.1':
    - node_modules\@parcel\watcher-win32-x64
  '@parcel/watcher@2.5.1':
    - node_modules\@parcel\watcher
  '@pkgjs/parseargs@0.11.0':
    - node_modules\@pkgjs\parseargs
  '@remax/mini-types@0.1.0':
    - node_modules\@remax\mini-types
  '@rollup/pluginutils@4.2.1':
    - node_modules\@rollup\pluginutils
  '@soda/friendly-errors-webpack-plugin@1.8.1(webpack@4.47.0)':
    - node_modules\@soda\friendly-errors-webpack-plugin
  '@soda/get-current-script@1.0.2':
    - node_modules\@soda\get-current-script
  '@types/body-parser@1.19.6':
    - node_modules\@types\body-parser
  '@types/connect-history-api-fallback@1.5.4':
    - node_modules\@types\connect-history-api-fallback
  '@types/connect@3.4.38':
    - node_modules\@types\connect
  '@types/express-serve-static-core@5.0.6':
    - node_modules\@types\express-serve-static-core
  '@types/express@5.0.3':
    - node_modules\@types\express
  '@types/glob@7.2.0':
    - node_modules\@types\glob
  '@types/http-errors@2.0.5':
    - node_modules\@types\http-errors
  '@types/http-proxy@1.17.16':
    - node_modules\@types\http-proxy
  '@types/json-schema@7.0.15':
    - node_modules\@types\json-schema
  '@types/mime@1.3.5':
    - node_modules\@types\mime
  '@types/minimatch@5.1.2':
    - node_modules\@types\minimatch
  '@types/minimist@1.2.5':
    - node_modules\@types\minimist
  '@types/node@24.0.0':
    - node_modules\@types\node
  '@types/normalize-package-data@2.4.4':
    - node_modules\@types\normalize-package-data
  '@types/q@1.5.8':
    - node_modules\@types\q
  '@types/qs@6.14.0':
    - node_modules\@types\qs
  '@types/range-parser@1.2.7':
    - node_modules\@types\range-parser
  '@types/send@0.17.5':
    - node_modules\@types\send
  '@types/serve-static@1.15.8':
    - node_modules\@types\serve-static
  '@types/source-list-map@0.1.6':
    - node_modules\@types\source-list-map
  '@types/tapable@1.0.12':
    - node_modules\@types\tapable
  '@types/uglify-js@3.17.5':
    - node_modules\@types\uglify-js
  '@types/webpack-dev-server@3.11.6(debug@4.4.1)':
    - node_modules\@types\webpack-dev-server
  '@types/webpack-sources@3.2.3':
    - node_modules\@types\webpack-sources
  '@types/webpack@4.41.40':
    - node_modules\@types\webpack
  '@vitejs/plugin-legacy@2.3.1(terser@5.42.0)(vite@3.2.11(@types/node@24.0.0)(sass@1.89.2)(terser@5.42.0))':
    - node_modules\@vitejs\plugin-legacy
  '@vitejs/plugin-vue@3.2.0(vite@3.2.11(@types/node@24.0.0)(sass@1.89.2)(terser@5.42.0))(vue@3.5.16)':
    - node_modules\@vitejs\plugin-vue
  '@vue/babel-helper-vue-jsx-merge-props@1.4.0':
    - node_modules\@vue\babel-helper-vue-jsx-merge-props
  '@vue/babel-helper-vue-transform-on@1.4.0':
    - node_modules\@vue\babel-helper-vue-transform-on
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-plugin-jsx
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-plugin-resolve-type
  '@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-plugin-transform-vue-jsx
  '@vue/babel-preset-app@4.5.19(@babel/core@7.27.4)(core-js@3.43.0)(vue@3.5.16)':
    - node_modules\@vue\babel-preset-app
  '@vue/babel-preset-jsx@1.4.0(@babel/core@7.27.4)(vue@3.5.16)':
    - node_modules\@vue\babel-preset-jsx
  '@vue/babel-sugar-composition-api-inject-h@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-sugar-composition-api-inject-h
  '@vue/babel-sugar-composition-api-render-instance@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-sugar-composition-api-render-instance
  '@vue/babel-sugar-functional-vue@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-sugar-functional-vue
  '@vue/babel-sugar-inject-h@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-sugar-inject-h
  '@vue/babel-sugar-v-model@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-sugar-v-model
  '@vue/babel-sugar-v-on@1.4.0(@babel/core@7.27.4)':
    - node_modules\@vue\babel-sugar-v-on
  '@vue/cli-overlay@4.5.19':
    - node_modules\@vue\cli-overlay
  '@vue/cli-plugin-babel@4.5.19(@vue/cli-service@4.5.19(@vue/compiler-sfc@3.5.16)(ejs@3.1.10)(lodash@4.17.21)(vue@3.5.16))(core-js@3.43.0)(vue@3.5.16)':
    - node_modules\@vue\cli-plugin-babel
  '@vue/cli-plugin-eslint@4.5.19(@vue/cli-service@4.5.19(@vue/compiler-sfc@3.5.16)(ejs@3.1.10)(lodash@4.17.21)(vue@3.5.16))(eslint@6.8.0)':
    - node_modules\@vue\cli-plugin-eslint
  '@vue/cli-plugin-router@4.5.19(@vue/cli-service@4.5.19(@vue/compiler-sfc@3.5.16)(ejs@3.1.10)(lodash@4.17.21)(vue@3.5.16))':
    - node_modules\@vue\cli-plugin-router
  '@vue/cli-plugin-vuex@4.5.19(@vue/cli-service@4.5.19(@vue/compiler-sfc@3.5.16)(ejs@3.1.10)(lodash@4.17.21)(vue@3.5.16))':
    - node_modules\@vue\cli-plugin-vuex
  '@vue/cli-service@4.5.19(@vue/compiler-sfc@3.5.16)(ejs@3.1.10)(lodash@4.17.21)(vue@3.5.16)':
    - node_modules\@vue\cli-service
  '@vue/cli-shared-utils@4.5.19':
    - node_modules\@vue\cli-shared-utils
  '@vue/compiler-core@3.5.16':
    - node_modules\@vue\compiler-core
  '@vue/compiler-dom@3.5.16':
    - node_modules\@vue\compiler-dom
  '@vue/compiler-sfc@3.5.16':
    - node_modules\@vue\compiler-sfc
  '@vue/compiler-ssr@3.5.16':
    - node_modules\@vue\compiler-ssr
  '@vue/component-compiler-utils@3.3.0(ejs@3.1.10)(lodash@4.17.21)':
    - node_modules\@vue\component-compiler-utils
  '@vue/devtools-api@6.6.4':
    - node_modules\@vue\devtools-api
  '@vue/preload-webpack-plugin@1.1.2(html-webpack-plugin@3.2.0(webpack@4.47.0))(webpack@4.47.0)':
    - node_modules\@vue\preload-webpack-plugin
  '@vue/reactivity@3.5.16':
    - node_modules\@vue\reactivity
  '@vue/runtime-core@3.5.16':
    - node_modules\@vue\runtime-core
  '@vue/runtime-dom@3.5.16':
    - node_modules\@vue\runtime-dom
  '@vue/server-renderer@3.5.16(vue@3.5.16)':
    - node_modules\@vue\server-renderer
  '@vue/shared@3.5.16':
    - node_modules\@vue\shared
  '@vue/web-component-wrapper@1.3.0':
    - node_modules\@vue\web-component-wrapper
  '@webassemblyjs/ast@1.9.0':
    - node_modules\@webassemblyjs\ast
  '@webassemblyjs/floating-point-hex-parser@1.9.0':
    - node_modules\@webassemblyjs\floating-point-hex-parser
  '@webassemblyjs/helper-api-error@1.9.0':
    - node_modules\@webassemblyjs\helper-api-error
  '@webassemblyjs/helper-buffer@1.9.0':
    - node_modules\@webassemblyjs\helper-buffer
  '@webassemblyjs/helper-code-frame@1.9.0':
    - node_modules\@webassemblyjs\helper-code-frame
  '@webassemblyjs/helper-fsm@1.9.0':
    - node_modules\@webassemblyjs\helper-fsm
  '@webassemblyjs/helper-module-context@1.9.0':
    - node_modules\@webassemblyjs\helper-module-context
  '@webassemblyjs/helper-wasm-bytecode@1.9.0':
    - node_modules\@webassemblyjs\helper-wasm-bytecode
  '@webassemblyjs/helper-wasm-section@1.9.0':
    - node_modules\@webassemblyjs\helper-wasm-section
  '@webassemblyjs/ieee754@1.9.0':
    - node_modules\@webassemblyjs\ieee754
  '@webassemblyjs/leb128@1.9.0':
    - node_modules\@webassemblyjs\leb128
  '@webassemblyjs/utf8@1.9.0':
    - node_modules\@webassemblyjs\utf8
  '@webassemblyjs/wasm-edit@1.9.0':
    - node_modules\@webassemblyjs\wasm-edit
  '@webassemblyjs/wasm-gen@1.9.0':
    - node_modules\@webassemblyjs\wasm-gen
  '@webassemblyjs/wasm-opt@1.9.0':
    - node_modules\@webassemblyjs\wasm-opt
  '@webassemblyjs/wasm-parser@1.9.0':
    - node_modules\@webassemblyjs\wasm-parser
  '@webassemblyjs/wast-parser@1.9.0':
    - node_modules\@webassemblyjs\wast-parser
  '@webassemblyjs/wast-printer@1.9.0':
    - node_modules\@webassemblyjs\wast-printer
  '@xtuc/ieee754@1.2.0':
    - node_modules\@xtuc\ieee754
  '@xtuc/long@4.2.2':
    - node_modules\@xtuc\long
  accepts@1.3.8:
    - node_modules\accepts
  acorn-jsx@5.3.2(acorn@7.4.1):
    - node_modules\acorn-jsx
  acorn-walk@7.2.0:
    - node_modules\acorn-walk
  acorn@6.4.2:
    - node_modules\webpack\node_modules\acorn
  acorn@7.4.1:
    - node_modules\acorn
  acorn@8.15.0:
    - node_modules\terser\node_modules\acorn
    - node_modules\terser-webpack-plugin\node_modules\acorn
  address@1.2.2:
    - node_modules\address
  ajv-errors@1.0.1(ajv@6.12.6):
    - node_modules\ajv-errors
  ajv-keywords@3.5.2(ajv@6.12.6):
    - node_modules\ajv-keywords
  ajv@6.12.6:
    - node_modules\ajv
  alphanum-sort@1.0.2:
    - node_modules\alphanum-sort
  ansi-colors@3.2.4:
    - node_modules\ansi-colors
  ansi-escapes@4.3.2:
    - node_modules\ansi-escapes
  ansi-html-community@0.0.8:
    - node_modules\ansi-html-community
  ansi-regex@2.1.1:
    - node_modules\webpack-dev-server\node_modules\ansi-regex
    - node_modules\renderkid\node_modules\ansi-regex
  ansi-regex@4.1.1:
    - node_modules\eslint\node_modules\ansi-regex
    - node_modules\table\node_modules\ansi-regex
    - node_modules\ora\node_modules\ansi-regex
    - node_modules\webpack-dev-server\node_modules\cliui\node_modules\ansi-regex
    - node_modules\webpack-dev-server\node_modules\string-width\node_modules\ansi-regex
    - node_modules\webpack-dev-server\node_modules\wrap-ansi\node_modules\ansi-regex
  ansi-regex@5.0.1:
    - node_modules\ansi-regex
  ansi-regex@6.1.0:
    - node_modules\@isaacs\cliui\node_modules\ansi-regex
  ansi-styles@3.2.1:
    - node_modules\eslint\node_modules\ansi-styles
    - node_modules\@vue\cli-shared-utils\node_modules\ansi-styles
    - node_modules\webpack-dev-server\node_modules\ansi-styles
    - node_modules\webpack-bundle-analyzer\node_modules\ansi-styles
    - node_modules\ora\node_modules\ansi-styles
    - node_modules\slice-ansi\node_modules\ansi-styles
    - node_modules\log-symbols\node_modules\ansi-styles
    - node_modules\svgo\node_modules\ansi-styles
    - node_modules\coa\node_modules\ansi-styles
  ansi-styles@4.3.0:
    - node_modules\ansi-styles
  ansi-styles@6.2.1:
    - node_modules\@isaacs\cliui\node_modules\ansi-styles
  any-promise@1.3.0:
    - node_modules\any-promise
  anymatch@2.0.0(supports-color@6.1.0):
    - node_modules\chokidar\node_modules\anymatch
  anymatch@3.1.3:
    - node_modules\anymatch
  aproba@1.2.0:
    - node_modules\aproba
  arch@2.2.0:
    - node_modules\arch
  argparse@1.0.10:
    - node_modules\argparse
  arr-diff@4.0.0:
    - node_modules\arr-diff
  arr-flatten@1.1.0:
    - node_modules\arr-flatten
  arr-union@3.1.0:
    - node_modules\arr-union
  array-buffer-byte-length@1.0.2:
    - node_modules\array-buffer-byte-length
  array-flatten@1.1.1:
    - node_modules\array-flatten
  array-flatten@2.1.2:
    - node_modules\bonjour\node_modules\array-flatten
  array-union@1.0.2:
    - node_modules\array-union
  array-uniq@1.0.3:
    - node_modules\array-uniq
  array-unique@0.3.2:
    - node_modules\array-unique
  array.prototype.reduce@1.0.8:
    - node_modules\array.prototype.reduce
  arraybuffer.prototype.slice@1.0.4:
    - node_modules\arraybuffer.prototype.slice
  asn1.js@4.10.1:
    - node_modules\asn1.js
  asn1@0.2.6:
    - node_modules\asn1
  assert-plus@1.0.0:
    - node_modules\assert-plus
  assert@1.5.1:
    - node_modules\assert
  assign-symbols@1.0.0:
    - node_modules\assign-symbols
  astral-regex@1.0.0:
    - node_modules\astral-regex
  async-each@1.0.6:
    - node_modules\async-each
  async-function@1.0.0:
    - node_modules\async-function
  async-limiter@1.0.1:
    - node_modules\async-limiter
  async@3.2.6:
    - node_modules\async
  asynckit@0.4.0:
    - node_modules\asynckit
  atob@2.1.2:
    - node_modules\atob
  autoprefixer@9.8.8:
    - node_modules\autoprefixer
  available-typed-arrays@1.0.7:
    - node_modules\available-typed-arrays
  aws-sign2@0.7.0:
    - node_modules\aws-sign2
  aws4@1.13.2:
    - node_modules\aws4
  axios@0.19.2:
    - node_modules\axios
  babel-eslint@10.1.0(eslint@6.8.0):
    - node_modules\babel-eslint
  babel-loader@8.4.1(@babel/core@7.27.4)(webpack@4.47.0):
    - node_modules\babel-loader
  babel-plugin-dynamic-import-node@2.3.3:
    - node_modules\babel-plugin-dynamic-import-node
  babel-plugin-import@1.13.8:
    - node_modules\babel-plugin-import
  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.27.4):
    - node_modules\babel-plugin-polyfill-corejs2
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.27.4):
    - node_modules\babel-plugin-polyfill-corejs3
  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.27.4):
    - node_modules\babel-plugin-polyfill-regenerator
  balanced-match@1.0.2:
    - node_modules\balanced-match
  base64-js@1.5.1:
    - node_modules\base64-js
  base@0.11.2:
    - node_modules\base
  batch@0.6.1:
    - node_modules\batch
  bcrypt-pbkdf@1.0.2:
    - node_modules\bcrypt-pbkdf
  bfj@6.1.2:
    - node_modules\bfj
  big.js@3.2.0:
    - node_modules\html-webpack-plugin\node_modules\big.js
  big.js@5.2.2:
    - node_modules\big.js
  binary-extensions@1.13.1:
    - node_modules\chokidar\node_modules\binary-extensions
  binary-extensions@2.3.0:
    - node_modules\binary-extensions
  bluebird@3.7.2:
    - node_modules\bluebird
  bn.js@4.12.2:
    - node_modules\bn.js
  bn.js@5.2.2:
    - node_modules\browserify-sign\node_modules\bn.js
    - node_modules\browserify-rsa\node_modules\bn.js
  body-parser@1.20.3(supports-color@6.1.0):
    - node_modules\body-parser
  bonjour@3.5.0:
    - node_modules\bonjour
  boolbase@1.0.0:
    - node_modules\boolbase
  brace-expansion@1.1.11:
    - node_modules\minimatch\node_modules\brace-expansion
  brace-expansion@2.0.1:
    - node_modules\brace-expansion
  braces@2.3.2(supports-color@6.1.0):
    - node_modules\chokidar\node_modules\braces
    - node_modules\micromatch\node_modules\braces
  braces@3.0.3:
    - node_modules\braces
  brorand@1.1.0:
    - node_modules\brorand
  browserify-aes@1.2.0:
    - node_modules\browserify-aes
  browserify-cipher@1.0.1:
    - node_modules\browserify-cipher
  browserify-des@1.0.2:
    - node_modules\browserify-des
  browserify-rsa@4.1.1:
    - node_modules\browserify-rsa
  browserify-sign@4.2.3:
    - node_modules\browserify-sign
  browserify-zlib@0.2.0:
    - node_modules\browserify-zlib
  browserslist@4.25.0:
    - node_modules\browserslist
  buffer-from@1.1.2:
    - node_modules\buffer-from
  buffer-indexof@1.1.1:
    - node_modules\buffer-indexof
  buffer-json@2.0.0:
    - node_modules\buffer-json
  buffer-xor@1.0.3:
    - node_modules\buffer-xor
  buffer@4.9.2:
    - node_modules\buffer
  builtin-status-codes@3.0.0:
    - node_modules\builtin-status-codes
  bytes@3.1.2:
    - node_modules\bytes
  cacache@12.0.4:
    - node_modules\cacache
  cache-base@1.0.1:
    - node_modules\cache-base
  cache-loader@4.1.0(webpack@4.47.0):
    - node_modules\cache-loader
  call-bind-apply-helpers@1.0.2:
    - node_modules\call-bind-apply-helpers
  call-bind@1.0.8:
    - node_modules\call-bind
  call-bound@1.0.4:
    - node_modules\call-bound
  call-me-maybe@1.0.2:
    - node_modules\call-me-maybe
  caller-callsite@2.0.0:
    - node_modules\caller-callsite
  caller-path@2.0.0:
    - node_modules\caller-path
  callsites@2.0.0:
    - node_modules\callsites
  callsites@3.1.0:
    - node_modules\parent-module\node_modules\callsites
  camel-case@3.0.0:
    - node_modules\camel-case
  camel-case@4.1.2:
    - node_modules\html-minifier-terser\node_modules\camel-case
  camelcase@5.3.1:
    - node_modules\camelcase
  caniuse-api@3.0.0:
    - node_modules\caniuse-api
  caniuse-lite@1.0.30001721:
    - node_modules\caniuse-lite
  case-sensitive-paths-webpack-plugin@2.4.0:
    - node_modules\case-sensitive-paths-webpack-plugin
  caseless@0.12.0:
    - node_modules\caseless
  chalk@2.4.2:
    - node_modules\eslint\node_modules\chalk
    - node_modules\@vue\cli-shared-utils\node_modules\chalk
    - node_modules\webpack-bundle-analyzer\node_modules\chalk
    - node_modules\ora\node_modules\chalk
    - node_modules\log-symbols\node_modules\chalk
    - node_modules\svgo\node_modules\chalk
    - node_modules\coa\node_modules\chalk
  chalk@3.0.0:
    - node_modules\@soda\friendly-errors-webpack-plugin\node_modules\chalk
  chalk@4.1.2:
    - node_modules\chalk
  chardet@0.7.0:
    - node_modules\chardet
  check-types@8.0.3:
    - node_modules\check-types
  chokidar@2.1.8(supports-color@6.1.0):
    - node_modules\chokidar
  chokidar@3.6.0:
    - node_modules\watchpack\node_modules\chokidar
  chokidar@4.0.3:
    - node_modules\sass\node_modules\chokidar
  chownr@1.1.4:
    - node_modules\chownr
  chrome-trace-event@1.0.4:
    - node_modules\chrome-trace-event
  ci-info@1.6.0:
    - node_modules\ci-info
  cipher-base@1.0.6:
    - node_modules\cipher-base
  class-utils@0.3.6:
    - node_modules\class-utils
  clean-css@4.2.4:
    - node_modules\clean-css
  clean-css@5.3.3:
    - node_modules\html-minifier-terser\node_modules\clean-css
  cli-cursor@2.1.0:
    - node_modules\cli-cursor
  cli-cursor@3.1.0:
    - node_modules\inquirer\node_modules\cli-cursor
  cli-highlight@2.1.11:
    - node_modules\cli-highlight
  cli-spinners@2.9.2:
    - node_modules\cli-spinners
  cli-width@3.0.0:
    - node_modules\cli-width
  clipboard@2.0.11:
    - node_modules\clipboard
  clipboardy@2.3.0:
    - node_modules\clipboardy
  cliui@5.0.0:
    - node_modules\webpack-dev-server\node_modules\cliui
  cliui@6.0.0:
    - node_modules\cliui
  cliui@7.0.4:
    - node_modules\cli-highlight\node_modules\cliui
  clone@1.0.4:
    - node_modules\clone
  coa@2.0.2:
    - node_modules\coa
  collection-visit@1.0.0:
    - node_modules\collection-visit
  color-convert@1.9.3:
    - node_modules\color-convert
  color-convert@2.0.1:
    - node_modules\ansi-styles\node_modules\color-convert
  color-name@1.1.3:
    - node_modules\color-convert\node_modules\color-name
  color-name@1.1.4:
    - node_modules\color-name
  color-string@1.9.1:
    - node_modules\color-string
  color@3.2.1:
    - node_modules\color
  colorette@2.0.20:
    - node_modules\colorette
  combined-stream@1.0.8:
    - node_modules\combined-stream
  commander@2.17.1:
    - node_modules\html-minifier\node_modules\commander
  commander@2.19.0:
    - node_modules\uglify-js\node_modules\commander
  commander@2.20.3:
    - node_modules\commander
  commander@8.3.0:
    - node_modules\html-minifier-terser\node_modules\commander
  commondir@1.0.1:
    - node_modules\commondir
  component-emitter@1.3.1:
    - node_modules\component-emitter
  compressible@2.0.18:
    - node_modules\compressible
  compression@1.8.0(supports-color@6.1.0):
    - node_modules\compression
  concat-map@0.0.1:
    - node_modules\concat-map
  concat-stream@1.6.2:
    - node_modules\concat-stream
  connect-history-api-fallback@1.6.0:
    - node_modules\connect-history-api-fallback
  consola@2.15.3:
    - node_modules\consola
  console-browserify@1.2.0:
    - node_modules\console-browserify
  consolidate@0.15.1(ejs@3.1.10)(lodash@4.17.21):
    - node_modules\consolidate
  constants-browserify@1.0.0:
    - node_modules\constants-browserify
  content-disposition@0.5.4:
    - node_modules\content-disposition
  content-type@1.0.5:
    - node_modules\content-type
  convert-source-map@2.0.0:
    - node_modules\convert-source-map
  cookie-signature@1.0.6:
    - node_modules\cookie-signature
  cookie@0.7.1:
    - node_modules\cookie
  copy-concurrently@1.0.5:
    - node_modules\copy-concurrently
  copy-descriptor@0.1.1:
    - node_modules\copy-descriptor
  copy-webpack-plugin@5.1.2(webpack@4.47.0):
    - node_modules\copy-webpack-plugin
  core-js-compat@3.43.0:
    - node_modules\core-js-compat
  core-js@3.43.0:
    - node_modules\core-js
  core-util-is@1.0.2:
    - node_modules\verror\node_modules\core-util-is
  core-util-is@1.0.3:
    - node_modules\core-util-is
  cosmiconfig@5.2.1:
    - node_modules\cosmiconfig
  create-ecdh@4.0.4:
    - node_modules\create-ecdh
  create-hash@1.2.0:
    - node_modules\create-hash
  create-hmac@1.1.7:
    - node_modules\create-hmac
  cross-spawn@5.1.0:
    - node_modules\yorkie\node_modules\cross-spawn
  cross-spawn@6.0.6:
    - node_modules\cross-spawn
  cross-spawn@7.0.6:
    - node_modules\default-gateway\node_modules\cross-spawn
    - node_modules\foreground-child\node_modules\cross-spawn
  crypto-browserify@3.12.1:
    - node_modules\crypto-browserify
  css-color-names@0.0.4:
    - node_modules\css-color-names
  css-declaration-sorter@4.0.1:
    - node_modules\css-declaration-sorter
  css-loader@3.6.0(webpack@4.47.0):
    - node_modules\css-loader
  css-select-base-adapter@0.1.1:
    - node_modules\css-select-base-adapter
  css-select@2.1.0:
    - node_modules\svgo\node_modules\css-select
  css-select@4.3.0:
    - node_modules\css-select
  css-tree@1.0.0-alpha.37:
    - node_modules\css-tree
  css-tree@1.1.3:
    - node_modules\csso\node_modules\css-tree
  css-what@3.4.2:
    - node_modules\css-what
  css-what@6.1.0:
    - node_modules\css-select\node_modules\css-what
  cssesc@3.0.0:
    - node_modules\cssesc
  cssnano-preset-default@4.0.8:
    - node_modules\cssnano-preset-default
  cssnano-util-get-arguments@4.0.0:
    - node_modules\cssnano-util-get-arguments
  cssnano-util-get-match@4.0.0:
    - node_modules\cssnano-util-get-match
  cssnano-util-raw-cache@4.0.1:
    - node_modules\cssnano-util-raw-cache
  cssnano-util-same-parent@4.0.1:
    - node_modules\cssnano-util-same-parent
  cssnano@4.1.11:
    - node_modules\cssnano
  csso@4.2.0:
    - node_modules\csso
  csstype@3.1.3:
    - node_modules\csstype
  cyclist@1.0.2:
    - node_modules\cyclist
  dashdash@1.14.1:
    - node_modules\dashdash
  data-view-buffer@1.0.2:
    - node_modules\data-view-buffer
  data-view-byte-length@1.0.2:
    - node_modules\data-view-byte-length
  data-view-byte-offset@1.0.1:
    - node_modules\data-view-byte-offset
  debug@2.6.9(supports-color@6.1.0):
    - node_modules\express\node_modules\debug
    - node_modules\compression\node_modules\debug
    - node_modules\serve-index\node_modules\debug
    - node_modules\snapdragon\node_modules\debug
    - node_modules\body-parser\node_modules\debug
    - node_modules\finalhandler\node_modules\debug
    - node_modules\expand-brackets\node_modules\debug
    - node_modules\send\node_modules\debug
  debug@3.1.0:
    - node_modules\follow-redirects\node_modules\debug
  debug@3.2.7(supports-color@6.1.0):
    - node_modules\sockjs-client\node_modules\debug
  debug@4.4.1(supports-color@6.1.0):
    - node_modules\debug
  decamelize@1.2.0:
    - node_modules\decamelize
  decode-uri-component@0.2.2:
    - node_modules\decode-uri-component
  deep-equal@1.1.2:
    - node_modules\deep-equal
  deep-is@0.1.4:
    - node_modules\deep-is
  deepmerge@1.5.2:
    - node_modules\deepmerge
  default-gateway@4.2.0:
    - node_modules\internal-ip\node_modules\default-gateway
  default-gateway@5.0.5:
    - node_modules\default-gateway
  defaults@1.0.4:
    - node_modules\defaults
  define-data-property@1.1.4:
    - node_modules\define-data-property
  define-properties@1.2.1:
    - node_modules\define-properties
  define-property@0.2.5:
    - node_modules\define-property
  define-property@1.0.0:
    - node_modules\extglob\node_modules\define-property
    - node_modules\snapdragon-node\node_modules\define-property
    - node_modules\base\node_modules\define-property
  define-property@2.0.2:
    - node_modules\micromatch\node_modules\define-property
    - node_modules\nanomatch\node_modules\define-property
    - node_modules\to-regex\node_modules\define-property
  del@4.1.1:
    - node_modules\del
  delayed-stream@1.0.0:
    - node_modules\delayed-stream
  delegate@3.2.0:
    - node_modules\delegate
  depd@1.1.2:
    - node_modules\serve-index\node_modules\depd
  depd@2.0.0:
    - node_modules\depd
  des.js@1.1.0:
    - node_modules\des.js
  destroy@1.2.0:
    - node_modules\destroy
  detect-libc@1.0.3:
    - node_modules\detect-libc
  detect-node@2.1.0:
    - node_modules\detect-node
  diffie-hellman@5.0.3:
    - node_modules\diffie-hellman
  dijkstrajs@1.0.3:
    - node_modules\dijkstrajs
  dir-glob@2.2.2:
    - node_modules\dir-glob
  dns-equal@1.0.0:
    - node_modules\dns-equal
  dns-packet@1.3.4:
    - node_modules\dns-packet
  dns-txt@2.0.2:
    - node_modules\dns-txt
  doctrine@3.0.0:
    - node_modules\doctrine
  dom-converter@0.2.0:
    - node_modules\dom-converter
  dom-serializer@0.2.2:
    - node_modules\dom-serializer
  dom-serializer@1.4.1:
    - node_modules\domutils\node_modules\dom-serializer
  domain-browser@1.2.0:
    - node_modules\domain-browser
  domelementtype@1.3.1:
    - node_modules\svgo\node_modules\domelementtype
  domelementtype@2.3.0:
    - node_modules\domelementtype
  domhandler@4.3.1:
    - node_modules\domhandler
  domutils@1.7.0:
    - node_modules\svgo\node_modules\domutils
  domutils@2.8.0:
    - node_modules\domutils
  dot-case@3.0.4:
    - node_modules\dot-case
  dot-prop@5.3.0:
    - node_modules\dot-prop
  dotenv-expand@5.1.0:
    - node_modules\dotenv-expand
  dotenv-expand@8.0.3:
    - node_modules\vite-plugin-html\node_modules\dotenv-expand
  dotenv@10.0.0:
    - node_modules\dotenv
  dotenv@16.5.0:
    - node_modules\vite-plugin-html\node_modules\dotenv
  dotenv@8.6.0:
    - node_modules\@vue\cli-service\node_modules\dotenv
  dunder-proto@1.0.1:
    - node_modules\dunder-proto
  duplexer@0.1.2:
    - node_modules\duplexer
  duplexify@3.7.1:
    - node_modules\duplexify
  eastasianwidth@0.2.0:
    - node_modules\eastasianwidth
  easy-stack@1.0.1:
    - node_modules\easy-stack
  ecc-jsbn@0.1.2:
    - node_modules\ecc-jsbn
  ee-first@1.1.1:
    - node_modules\ee-first
  ejs@2.7.4:
    - node_modules\webpack-bundle-analyzer\node_modules\ejs
  ejs@3.1.10:
    - node_modules\ejs
  electron-to-chromium@1.5.166:
    - node_modules\electron-to-chromium
  elliptic@6.6.1:
    - node_modules\elliptic
  emoji-regex@7.0.3:
    - node_modules\webpack-dev-server\node_modules\emoji-regex
    - node_modules\table\node_modules\emoji-regex
  emoji-regex@8.0.0:
    - node_modules\emoji-regex
  emoji-regex@9.2.2:
    - node_modules\@isaacs\cliui\node_modules\emoji-regex
  emojis-list@2.1.0:
    - node_modules\html-webpack-plugin\node_modules\emojis-list
  emojis-list@3.0.0:
    - node_modules\emojis-list
  encodeurl@1.0.2:
    - node_modules\send\node_modules\encodeurl
  encodeurl@2.0.0:
    - node_modules\encodeurl
  end-of-stream@1.4.4:
    - node_modules\end-of-stream
  enhanced-resolve@4.5.0:
    - node_modules\enhanced-resolve
  entities@2.2.0:
    - node_modules\entities
  entities@4.5.0:
    - node_modules\@vue\compiler-core\node_modules\entities
  errno@0.1.8:
    - node_modules\errno
  error-ex@1.3.2:
    - node_modules\error-ex
  error-stack-parser@2.1.4:
    - node_modules\error-stack-parser
  es-abstract@1.24.0:
    - node_modules\es-abstract
  es-array-method-boxes-properly@1.0.0:
    - node_modules\es-array-method-boxes-properly
  es-define-property@1.0.1:
    - node_modules\es-define-property
  es-errors@1.3.0:
    - node_modules\es-errors
  es-object-atoms@1.1.1:
    - node_modules\es-object-atoms
  es-set-tostringtag@2.1.0:
    - node_modules\es-set-tostringtag
  es-to-primitive@1.3.0:
    - node_modules\es-to-primitive
  esbuild-windows-64@0.15.18:
    - node_modules\esbuild-windows-64
  esbuild@0.15.18:
    - node_modules\esbuild
  escalade@3.2.0:
    - node_modules\escalade
  escape-html@1.0.3:
    - node_modules\escape-html
  escape-string-regexp@1.0.5:
    - node_modules\escape-string-regexp
  eslint-loader@2.2.1(eslint@6.8.0)(webpack@4.47.0):
    - node_modules\eslint-loader
  eslint-plugin-vue@7.20.0(eslint@6.8.0):
    - node_modules\eslint-plugin-vue
  eslint-scope@4.0.3:
    - node_modules\webpack\node_modules\eslint-scope
  eslint-scope@5.1.1:
    - node_modules\eslint-scope
  eslint-utils@1.4.3:
    - node_modules\eslint-utils
  eslint-utils@2.1.0:
    - node_modules\eslint-plugin-vue\node_modules\eslint-utils
  eslint-visitor-keys@1.3.0:
    - node_modules\eslint-visitor-keys
  eslint@6.8.0:
    - node_modules\eslint
  espree@6.2.1:
    - node_modules\espree
  esprima@4.0.1:
    - node_modules\esprima
  esquery@1.6.0:
    - node_modules\esquery
  esrecurse@4.3.0:
    - node_modules\esrecurse
  estraverse@4.3.0:
    - node_modules\webpack\node_modules\estraverse
    - node_modules\eslint-scope\node_modules\estraverse
  estraverse@5.3.0:
    - node_modules\estraverse
  estree-walker@2.0.2:
    - node_modules\estree-walker
  esutils@2.0.3:
    - node_modules\esutils
  etag@1.8.1:
    - node_modules\etag
  event-pubsub@4.3.0:
    - node_modules\event-pubsub
  eventemitter3@4.0.7:
    - node_modules\eventemitter3
  events@3.3.0:
    - node_modules\events
  eventsource@2.0.2:
    - node_modules\eventsource
  evp_bytestokey@1.0.3:
    - node_modules\evp_bytestokey
  execa@0.8.0:
    - node_modules\yorkie\node_modules\execa
  execa@1.0.0:
    - node_modules\execa
  execa@3.4.0:
    - node_modules\default-gateway\node_modules\execa
  expand-brackets@2.1.4(supports-color@6.1.0):
    - node_modules\expand-brackets
  express@4.21.2(supports-color@6.1.0):
    - node_modules\express
  extend-shallow@2.0.1:
    - node_modules\extend-shallow
    - node_modules\micromatch\node_modules\braces\node_modules\extend-shallow
    - node_modules\micromatch\node_modules\fill-range\node_modules\extend-shallow
  extend-shallow@3.0.2:
    - node_modules\nanomatch\node_modules\extend-shallow
    - node_modules\regex-not\node_modules\extend-shallow
    - node_modules\to-regex\node_modules\extend-shallow
    - node_modules\split-string\node_modules\extend-shallow
    - node_modules\micromatch\node_modules\extend-shallow
  extend@3.0.2:
    - node_modules\extend
  external-editor@3.1.0:
    - node_modules\external-editor
  extglob@2.0.4(supports-color@6.1.0):
    - node_modules\extglob
  extsprintf@1.3.0:
    - node_modules\extsprintf
  fast-deep-equal@3.1.3:
    - node_modules\fast-deep-equal
  fast-glob@2.2.7:
    - node_modules\fast-glob
  fast-glob@3.3.3:
    - node_modules\vite-plugin-html\node_modules\fast-glob
  fast-json-stable-stringify@2.1.0:
    - node_modules\fast-json-stable-stringify
  fast-levenshtein@2.0.6:
    - node_modules\fast-levenshtein
  fastq@1.19.1:
    - node_modules\fastq
  faye-websocket@0.11.4:
    - node_modules\faye-websocket
  figgy-pudding@3.5.2:
    - node_modules\figgy-pudding
  figures@3.2.0:
    - node_modules\figures
  file-entry-cache@5.0.1:
    - node_modules\file-entry-cache
  file-loader@4.3.0(webpack@4.47.0):
    - node_modules\file-loader
  filelist@1.0.4:
    - node_modules\filelist
  filesize@3.6.1:
    - node_modules\filesize
  fill-range@4.0.0:
    - node_modules\chokidar\node_modules\fill-range
    - node_modules\micromatch\node_modules\fill-range
  fill-range@7.1.1:
    - node_modules\fill-range
  finalhandler@1.3.1(supports-color@6.1.0):
    - node_modules\finalhandler
  find-cache-dir@0.1.1:
    - node_modules\loader-fs-cache\node_modules\find-cache-dir
  find-cache-dir@2.1.0:
    - node_modules\copy-webpack-plugin\node_modules\find-cache-dir
    - node_modules\terser-webpack-plugin\node_modules\find-cache-dir
  find-cache-dir@3.3.2:
    - node_modules\find-cache-dir
  find-up@1.1.2:
    - node_modules\loader-fs-cache\node_modules\find-up
  find-up@3.0.0:
    - node_modules\webpack-dev-server\node_modules\find-up
    - node_modules\pkg-dir\node_modules\find-up
  find-up@4.1.0:
    - node_modules\find-up
  flat-cache@2.0.1:
    - node_modules\flat-cache
  flatted@2.0.2:
    - node_modules\flatted
  flush-write-stream@1.1.1:
    - node_modules\flush-write-stream
  follow-redirects@1.15.9(debug@4.4.1):
    - node_modules\http-proxy\node_modules\follow-redirects
  follow-redirects@1.5.10:
    - node_modules\follow-redirects
  for-each@0.3.5:
    - node_modules\for-each
  for-in@1.0.2:
    - node_modules\for-in
  foreground-child@3.3.1:
    - node_modules\foreground-child
  forever-agent@0.6.1:
    - node_modules\forever-agent
  form-data@2.3.3:
    - node_modules\form-data
  forwarded@0.2.0:
    - node_modules\forwarded
  fragment-cache@0.2.1:
    - node_modules\fragment-cache
  fresh@0.5.2:
    - node_modules\fresh
  from2@2.3.0:
    - node_modules\from2
  fs-extra@10.1.0:
    - node_modules\vite-plugin-html\node_modules\fs-extra
  fs-extra@7.0.1:
    - node_modules\fs-extra
  fs-write-stream-atomic@1.0.10:
    - node_modules\fs-write-stream-atomic
  fs.realpath@1.0.0:
    - node_modules\fs.realpath
  function-bind@1.1.2:
    - node_modules\function-bind
  function.prototype.name@1.1.8:
    - node_modules\function.prototype.name
  functional-red-black-tree@1.0.1:
    - node_modules\functional-red-black-tree
  functions-have-names@1.2.3:
    - node_modules\functions-have-names
  gdt-jsapi@1.9.51:
    - node_modules\gdt-jsapi
  gensync@1.0.0-beta.2:
    - node_modules\gensync
  get-caller-file@2.0.5:
    - node_modules\get-caller-file
  get-intrinsic@1.3.0:
    - node_modules\get-intrinsic
  get-proto@1.0.1:
    - node_modules\get-proto
  get-stream@3.0.0:
    - node_modules\yorkie\node_modules\get-stream
  get-stream@4.1.0:
    - node_modules\get-stream
  get-stream@5.2.0:
    - node_modules\default-gateway\node_modules\get-stream
  get-symbol-description@1.1.0:
    - node_modules\get-symbol-description
  get-value@2.0.6:
    - node_modules\get-value
  getpass@0.1.7:
    - node_modules\getpass
  glob-parent@3.1.0:
    - node_modules\copy-webpack-plugin\node_modules\glob-parent
    - node_modules\fast-glob\node_modules\glob-parent
    - node_modules\chokidar\node_modules\glob-parent
  glob-parent@5.1.2:
    - node_modules\glob-parent
  glob-to-regexp@0.3.0:
    - node_modules\glob-to-regexp
  glob@10.4.5:
    - node_modules\rimraf\node_modules\glob
  glob@7.2.3:
    - node_modules\glob
  globals@11.12.0:
    - node_modules\globals
  globals@12.4.0:
    - node_modules\eslint\node_modules\globals
  globalthis@1.0.4:
    - node_modules\globalthis
  globby@6.1.0:
    - node_modules\del\node_modules\globby
  globby@7.1.1:
    - node_modules\copy-webpack-plugin\node_modules\globby
  globby@9.2.0:
    - node_modules\globby
  good-listener@1.2.2:
    - node_modules\good-listener
  gopd@1.2.0:
    - node_modules\gopd
  graceful-fs@4.2.11:
    - node_modules\graceful-fs
  gzip-size@5.1.1:
    - node_modules\gzip-size
  handle-thing@2.0.1:
    - node_modules\handle-thing
  har-schema@2.0.0:
    - node_modules\har-schema
  har-validator@5.1.5:
    - node_modules\har-validator
  has-bigints@1.1.0:
    - node_modules\has-bigints
  has-flag@3.0.0:
    - node_modules\has-flag
  has-flag@4.0.0:
    - node_modules\chalk\node_modules\has-flag
    - node_modules\@soda\friendly-errors-webpack-plugin\node_modules\has-flag
  has-property-descriptors@1.0.2:
    - node_modules\has-property-descriptors
  has-proto@1.2.0:
    - node_modules\has-proto
  has-symbols@1.1.0:
    - node_modules\has-symbols
  has-tostringtag@1.0.2:
    - node_modules\has-tostringtag
  has-value@0.3.1:
    - node_modules\unset-value\node_modules\has-value
  has-value@1.0.0:
    - node_modules\has-value
  has-values@0.1.4:
    - node_modules\unset-value\node_modules\has-values
  has-values@1.0.0:
    - node_modules\has-values
  has@1.0.4:
    - node_modules\has
  hash-base@3.0.5:
    - node_modules\hash-base
  hash-sum@1.0.2:
    - node_modules\hash-sum
  hash-sum@2.0.0:
    - node_modules\@vue\cli-service\node_modules\hash-sum
    - node_modules\vue-loader-v16\node_modules\hash-sum
  hash.js@1.1.7:
    - node_modules\hash.js
  hasown@2.0.2:
    - node_modules\hasown
  he@1.2.0:
    - node_modules\he
  hex-color-regex@1.1.0:
    - node_modules\hex-color-regex
  highlight.js@10.7.3:
    - node_modules\highlight.js
  hmac-drbg@1.0.1:
    - node_modules\hmac-drbg
  hoopy@0.1.4:
    - node_modules\hoopy
  hosted-git-info@2.8.9:
    - node_modules\hosted-git-info
  hpack.js@2.1.6:
    - node_modules\hpack.js
  hsl-regex@1.0.0:
    - node_modules\hsl-regex
  hsla-regex@1.0.0:
    - node_modules\hsla-regex
  html-entities@1.4.0:
    - node_modules\html-entities
  html-minifier-terser@6.1.0:
    - node_modules\html-minifier-terser
  html-minifier@3.5.21:
    - node_modules\html-minifier
  html-tags@2.0.0:
    - node_modules\html-tags
  html-webpack-plugin@3.2.0(webpack@4.47.0):
    - node_modules\html-webpack-plugin
  htmlparser2@6.1.0:
    - node_modules\htmlparser2
  http-deceiver@1.2.7:
    - node_modules\http-deceiver
  http-errors@1.6.3:
    - node_modules\serve-index\node_modules\http-errors
  http-errors@2.0.0:
    - node_modules\http-errors
  http-parser-js@0.5.10:
    - node_modules\http-parser-js
  http-proxy-middleware@0.19.1(debug@4.4.1)(supports-color@6.1.0):
    - node_modules\webpack-dev-server\node_modules\http-proxy-middleware
  http-proxy-middleware@1.3.1(debug@4.4.1):
    - node_modules\http-proxy-middleware
  http-proxy@1.18.1(debug@4.4.1):
    - node_modules\http-proxy
  http-signature@1.2.0:
    - node_modules\http-signature
  https-browserify@1.0.0:
    - node_modules\https-browserify
  human-signals@1.1.1:
    - node_modules\human-signals
  iconv-lite@0.4.24:
    - node_modules\iconv-lite
  icss-utils@4.1.1:
    - node_modules\icss-utils
  ieee754@1.2.1:
    - node_modules\ieee754
  iferr@0.1.5:
    - node_modules\iferr
  ignore@3.3.10:
    - node_modules\copy-webpack-plugin\node_modules\ignore
  ignore@4.0.6:
    - node_modules\ignore
  immutable@5.1.2:
    - node_modules\immutable
  import-cwd@2.1.0:
    - node_modules\import-cwd
  import-fresh@2.0.0:
    - node_modules\import-fresh
  import-fresh@3.3.1:
    - node_modules\eslint\node_modules\import-fresh
  import-from@2.1.0:
    - node_modules\import-from
  import-local@2.0.0:
    - node_modules\import-local
  imurmurhash@0.1.4:
    - node_modules\imurmurhash
  indexes-of@1.0.1:
    - node_modules\indexes-of
  infer-owner@1.0.4:
    - node_modules\infer-owner
  inflight@1.0.6:
    - node_modules\inflight
  inherits@2.0.3:
    - node_modules\util\node_modules\inherits
    - node_modules\node-libs-browser\node_modules\inherits
    - node_modules\serve-index\node_modules\inherits
  inherits@2.0.4:
    - node_modules\inherits
  inquirer@7.3.3:
    - node_modules\inquirer
  internal-ip@4.3.0:
    - node_modules\internal-ip
  internal-slot@1.1.0:
    - node_modules\internal-slot
  ip-regex@2.1.0:
    - node_modules\ip-regex
  ip@1.1.9:
    - node_modules\ip
  ipaddr.js@1.9.1:
    - node_modules\ipaddr.js
  is-absolute-url@2.1.0:
    - node_modules\is-absolute-url
  is-absolute-url@3.0.3:
    - node_modules\webpack-dev-server\node_modules\is-absolute-url
  is-accessor-descriptor@1.0.1:
    - node_modules\is-accessor-descriptor
  is-arguments@1.2.0:
    - node_modules\is-arguments
  is-array-buffer@3.0.5:
    - node_modules\is-array-buffer
  is-arrayish@0.2.1:
    - node_modules\is-arrayish
  is-arrayish@0.3.2:
    - node_modules\simple-swizzle\node_modules\is-arrayish
  is-async-function@2.1.1:
    - node_modules\is-async-function
  is-bigint@1.1.0:
    - node_modules\is-bigint
  is-binary-path@1.0.1:
    - node_modules\chokidar\node_modules\is-binary-path
  is-binary-path@2.1.0:
    - node_modules\is-binary-path
  is-boolean-object@1.2.2:
    - node_modules\is-boolean-object
  is-buffer@1.1.6:
    - node_modules\is-buffer
  is-callable@1.2.7:
    - node_modules\is-callable
  is-ci@1.2.1:
    - node_modules\is-ci
  is-color-stop@1.1.0:
    - node_modules\is-color-stop
  is-core-module@2.16.1:
    - node_modules\is-core-module
  is-data-descriptor@1.0.1:
    - node_modules\is-data-descriptor
  is-data-view@1.0.2:
    - node_modules\is-data-view
  is-date-object@1.1.0:
    - node_modules\is-date-object
  is-descriptor@0.1.7:
    - node_modules\define-property\node_modules\is-descriptor
  is-descriptor@1.0.3:
    - node_modules\is-descriptor
  is-directory@0.3.1:
    - node_modules\is-directory
  is-docker@2.2.1:
    - node_modules\is-docker
  is-extendable@0.1.1:
    - node_modules\is-extendable
  is-extendable@1.0.1:
    - node_modules\nanomatch\node_modules\is-extendable
    - node_modules\regex-not\node_modules\is-extendable
    - node_modules\to-regex\node_modules\is-extendable
    - node_modules\split-string\node_modules\is-extendable
    - node_modules\mixin-deep\node_modules\is-extendable
    - node_modules\micromatch\node_modules\extend-shallow\node_modules\is-extendable
  is-extglob@2.1.1:
    - node_modules\is-extglob
  is-finalizationregistry@1.1.1:
    - node_modules\is-finalizationregistry
  is-fullwidth-code-point@2.0.0:
    - node_modules\is-fullwidth-code-point
  is-fullwidth-code-point@3.0.0:
    - node_modules\string-width\node_modules\is-fullwidth-code-point
    - node_modules\string-width-cjs\node_modules\is-fullwidth-code-point
  is-generator-function@1.1.0:
    - node_modules\is-generator-function
  is-glob@3.1.0:
    - node_modules\copy-webpack-plugin\node_modules\glob-parent\node_modules\is-glob
    - node_modules\fast-glob\node_modules\glob-parent\node_modules\is-glob
    - node_modules\chokidar\node_modules\glob-parent\node_modules\is-glob
  is-glob@4.0.3:
    - node_modules\is-glob
  is-map@2.0.3:
    - node_modules\is-map
  is-negative-zero@2.0.3:
    - node_modules\is-negative-zero
  is-number-object@1.1.1:
    - node_modules\is-number-object
  is-number@3.0.0:
    - node_modules\is-number
  is-number@7.0.0:
    - node_modules\to-regex-range\node_modules\is-number
  is-obj@2.0.0:
    - node_modules\is-obj
  is-path-cwd@2.2.0:
    - node_modules\is-path-cwd
  is-path-in-cwd@2.1.0:
    - node_modules\is-path-in-cwd
  is-path-inside@2.1.0:
    - node_modules\is-path-inside
  is-plain-obj@1.1.0:
    - node_modules\sort-keys\node_modules\is-plain-obj
  is-plain-obj@3.0.0:
    - node_modules\is-plain-obj
  is-plain-object@2.0.4:
    - node_modules\is-plain-object
  is-regex@1.2.1:
    - node_modules\is-regex
  is-resolvable@1.1.0:
    - node_modules\is-resolvable
  is-set@2.0.3:
    - node_modules\is-set
  is-shared-array-buffer@1.0.4:
    - node_modules\is-shared-array-buffer
  is-stream@1.1.0:
    - node_modules\is-stream
  is-stream@2.0.1:
    - node_modules\default-gateway\node_modules\is-stream
  is-string@1.1.1:
    - node_modules\is-string
  is-symbol@1.1.1:
    - node_modules\is-symbol
  is-typed-array@1.1.15:
    - node_modules\is-typed-array
  is-typedarray@1.0.0:
    - node_modules\is-typedarray
  is-weakmap@2.0.2:
    - node_modules\is-weakmap
  is-weakref@1.1.1:
    - node_modules\is-weakref
  is-weakset@2.0.4:
    - node_modules\is-weakset
  is-windows@1.0.2:
    - node_modules\is-windows
  is-wsl@1.1.0:
    - node_modules\is-wsl
  is-wsl@2.2.0:
    - node_modules\clipboardy\node_modules\is-wsl
  isarray@1.0.0:
    - node_modules\isarray
  isarray@2.0.5:
    - node_modules\safe-array-concat\node_modules\isarray
    - node_modules\safe-push-apply\node_modules\isarray
    - node_modules\which-builtin-type\node_modules\isarray
  isexe@2.0.0:
    - node_modules\isexe
  isobject@2.1.0:
    - node_modules\unset-value\node_modules\has-value\node_modules\isobject
  isobject@3.0.1:
    - node_modules\isobject
  isstream@0.1.2:
    - node_modules\isstream
  jackspeak@3.4.3:
    - node_modules\jackspeak
  jake@10.9.2:
    - node_modules\jake
  javascript-stringify@2.1.0:
    - node_modules\javascript-stringify
  js-base64@3.7.7:
    - node_modules\js-base64
  js-cookie@3.0.5:
    - node_modules\js-cookie
  js-message@1.0.7:
    - node_modules\js-message
  js-tokens@4.0.0:
    - node_modules\js-tokens
  js-yaml@3.14.1:
    - node_modules\js-yaml
  jsbn@0.1.1:
    - node_modules\jsbn
  jsencrypt@3.3.2:
    - node_modules\jsencrypt
  jsesc@3.0.2:
    - node_modules\regjsparser\node_modules\jsesc
  jsesc@3.1.0:
    - node_modules\jsesc
  json-parse-better-errors@1.0.2:
    - node_modules\json-parse-better-errors
  json-parse-even-better-errors@2.3.1:
    - node_modules\json-parse-even-better-errors
  json-schema-traverse@0.4.1:
    - node_modules\json-schema-traverse
  json-schema@0.4.0:
    - node_modules\json-schema
  json-stable-stringify-without-jsonify@1.0.1:
    - node_modules\json-stable-stringify-without-jsonify
  json-stringify-safe@5.0.1:
    - node_modules\json-stringify-safe
  json5@0.5.1:
    - node_modules\html-webpack-plugin\node_modules\json5
  json5@1.0.2:
    - node_modules\loader-utils\node_modules\json5
  json5@2.2.3:
    - node_modules\json5
  jsonfile@4.0.0:
    - node_modules\jsonfile
  jsonfile@6.1.0:
    - node_modules\vite-plugin-html\node_modules\jsonfile
  jsonpath-plus@7.2.0:
    - node_modules\jsonpath-plus
  jsprim@1.4.2:
    - node_modules\jsprim
  killable@1.0.1:
    - node_modules\killable
  kind-of@3.2.2:
    - node_modules\kind-of
  kind-of@4.0.0:
    - node_modules\has-values\node_modules\kind-of
  kind-of@6.0.3:
    - node_modules\micromatch\node_modules\kind-of
    - node_modules\nanomatch\node_modules\kind-of
  launch-editor-middleware@2.10.0:
    - node_modules\launch-editor-middleware
  launch-editor@2.10.0:
    - node_modules\launch-editor
  levn@0.3.0:
    - node_modules\levn
  lines-and-columns@1.2.4:
    - node_modules\lines-and-columns
  loader-fs-cache@1.0.3:
    - node_modules\loader-fs-cache
  loader-runner@2.4.0:
    - node_modules\loader-runner
  loader-utils@0.2.17:
    - node_modules\html-webpack-plugin\node_modules\loader-utils
  loader-utils@1.4.2:
    - node_modules\loader-utils
  loader-utils@2.0.4:
    - node_modules\babel-loader\node_modules\loader-utils
    - node_modules\vue-loader-v16\node_modules\loader-utils
  locate-path@3.0.0:
    - node_modules\webpack-dev-server\node_modules\locate-path
    - node_modules\pkg-dir\node_modules\locate-path
  locate-path@5.0.0:
    - node_modules\locate-path
  lodash.debounce@4.0.8:
    - node_modules\lodash.debounce
  lodash.defaultsdeep@4.6.1:
    - node_modules\lodash.defaultsdeep
  lodash.kebabcase@4.1.1:
    - node_modules\lodash.kebabcase
  lodash.mapvalues@4.6.0:
    - node_modules\lodash.mapvalues
  lodash.memoize@4.1.2:
    - node_modules\lodash.memoize
  lodash.transform@4.6.0:
    - node_modules\lodash.transform
  lodash.uniq@4.5.0:
    - node_modules\lodash.uniq
  lodash@4.17.21:
    - node_modules\lodash
  log-symbols@2.2.0:
    - node_modules\log-symbols
  loglevel@1.9.2:
    - node_modules\loglevel
  lower-case@1.1.4:
    - node_modules\lower-case
  lower-case@2.0.2:
    - node_modules\pascal-case\node_modules\lower-case
    - node_modules\dot-case\node_modules\lower-case
  lru-cache@10.4.3:
    - node_modules\path-scurry\node_modules\lru-cache
  lru-cache@4.1.5:
    - node_modules\yorkie\node_modules\lru-cache
    - node_modules\@vue\component-compiler-utils\node_modules\lru-cache
  lru-cache@5.1.1:
    - node_modules\lru-cache
  magic-string@0.26.7:
    - node_modules\@vitejs\plugin-legacy\node_modules\magic-string
  magic-string@0.30.17:
    - node_modules\magic-string
  make-dir@2.1.0:
    - node_modules\copy-webpack-plugin\node_modules\make-dir
    - node_modules\terser-webpack-plugin\node_modules\make-dir
  make-dir@3.1.0:
    - node_modules\make-dir
  map-cache@0.2.2:
    - node_modules\map-cache
  map-visit@1.0.0:
    - node_modules\map-visit
  math-intrinsics@1.1.0:
    - node_modules\math-intrinsics
  md5.js@1.3.5:
    - node_modules\md5.js
  mdn-data@2.0.14:
    - node_modules\csso\node_modules\mdn-data
  mdn-data@2.0.4:
    - node_modules\mdn-data
  media-typer@0.3.0:
    - node_modules\media-typer
  memory-fs@0.4.1:
    - node_modules\memory-fs
  memory-fs@0.5.0:
    - node_modules\enhanced-resolve\node_modules\memory-fs
  merge-descriptors@1.0.3:
    - node_modules\merge-descriptors
  merge-source-map@1.1.0:
    - node_modules\merge-source-map
  merge-stream@2.0.0:
    - node_modules\merge-stream
  merge2@1.4.1:
    - node_modules\merge2
  methods@1.1.2:
    - node_modules\methods
  micromatch@3.1.10(supports-color@6.1.0):
    - node_modules\micromatch
  micromatch@4.0.8:
    - node_modules\vite-plugin-html\node_modules\micromatch
    - node_modules\@parcel\watcher\node_modules\micromatch
    - node_modules\http-proxy-middleware\node_modules\micromatch
  miller-rabin@4.0.1:
    - node_modules\miller-rabin
  mime-db@1.52.0:
    - node_modules\mime-db
  mime-db@1.54.0:
    - node_modules\compressible\node_modules\mime-db
  mime-types@2.1.35:
    - node_modules\mime-types
  mime@1.6.0:
    - node_modules\send\node_modules\mime
  mime@2.6.0:
    - node_modules\mime
  mimic-fn@1.2.0:
    - node_modules\mimic-fn
  mimic-fn@2.1.0:
    - node_modules\onetime\node_modules\mimic-fn
  mini-css-extract-plugin@0.9.0(webpack@4.47.0):
    - node_modules\mini-css-extract-plugin
  minimalistic-assert@1.0.1:
    - node_modules\minimalistic-assert
  minimalistic-crypto-utils@1.0.1:
    - node_modules\minimalistic-crypto-utils
  minimatch@3.1.2:
    - node_modules\minimatch
  minimatch@5.1.6:
    - node_modules\filelist\node_modules\minimatch
  minimatch@9.0.5:
    - node_modules\rimraf\node_modules\minimatch
  minimist@1.2.8:
    - node_modules\minimist
  minipass@3.3.6:
    - node_modules\@vue\cli-service\node_modules\minipass
  minipass@7.1.2:
    - node_modules\minipass
  mississippi@3.0.0:
    - node_modules\mississippi
  mitt@3.0.1:
    - node_modules\mitt
  mixin-deep@1.3.2:
    - node_modules\mixin-deep
  mkdirp@0.5.6:
    - node_modules\mkdirp
  move-concurrently@1.0.1:
    - node_modules\move-concurrently
  ms@2.0.0:
    - node_modules\follow-redirects\node_modules\ms
    - node_modules\express\node_modules\ms
    - node_modules\compression\node_modules\ms
    - node_modules\serve-index\node_modules\ms
    - node_modules\snapdragon\node_modules\ms
    - node_modules\body-parser\node_modules\ms
    - node_modules\finalhandler\node_modules\ms
    - node_modules\expand-brackets\node_modules\ms
    - node_modules\send\node_modules\debug\node_modules\ms
  ms@2.1.3:
    - node_modules\ms
  multicast-dns-service-types@1.1.0:
    - node_modules\multicast-dns-service-types
  multicast-dns@6.2.3:
    - node_modules\multicast-dns
  mute-stream@0.0.8:
    - node_modules\mute-stream
  mz@2.7.0:
    - node_modules\mz
  nanoid@3.3.11:
    - node_modules\nanoid
  nanomatch@1.2.13(supports-color@6.1.0):
    - node_modules\nanomatch
  natural-compare@1.4.0:
    - node_modules\natural-compare
  negotiator@0.6.3:
    - node_modules\negotiator
  negotiator@0.6.4:
    - node_modules\compression\node_modules\negotiator
  neo-async@2.6.2:
    - node_modules\neo-async
  nice-try@1.0.5:
    - node_modules\nice-try
  no-case@2.3.2:
    - node_modules\no-case
  no-case@3.0.4:
    - node_modules\pascal-case\node_modules\no-case
    - node_modules\dot-case\node_modules\no-case
  node-addon-api@7.1.1:
    - node_modules\node-addon-api
  node-forge@0.10.0:
    - node_modules\node-forge
  node-html-parser@5.4.2:
    - node_modules\node-html-parser
  node-libs-browser@2.2.1:
    - node_modules\node-libs-browser
  node-releases@2.0.19:
    - node_modules\node-releases
  normalize-package-data@2.5.0:
    - node_modules\normalize-package-data
  normalize-path@1.0.0:
    - node_modules\yorkie\node_modules\normalize-path
  normalize-path@2.1.1:
    - node_modules\chokidar\node_modules\anymatch\node_modules\normalize-path
  normalize-path@3.0.0:
    - node_modules\normalize-path
  normalize-range@0.1.2:
    - node_modules\normalize-range
  normalize-url@1.9.1:
    - node_modules\mini-css-extract-plugin\node_modules\normalize-url
  normalize-url@3.3.0:
    - node_modules\normalize-url
  npm-run-path@2.0.2:
    - node_modules\npm-run-path
  npm-run-path@4.0.1:
    - node_modules\default-gateway\node_modules\npm-run-path
  nprogress@0.2.0:
    - node_modules\nprogress
  nth-check@1.0.2:
    - node_modules\nth-check
  nth-check@2.1.1:
    - node_modules\css-select\node_modules\nth-check
  num2fraction@1.2.2:
    - node_modules\num2fraction
  oauth-sign@0.9.0:
    - node_modules\oauth-sign
  object-assign@4.1.1:
    - node_modules\object-assign
  object-copy@0.1.0:
    - node_modules\object-copy
  object-hash@1.3.1:
    - node_modules\object-hash
  object-inspect@1.13.4:
    - node_modules\object-inspect
  object-is@1.1.6:
    - node_modules\object-is
  object-keys@1.1.1:
    - node_modules\object-keys
  object-visit@1.0.1:
    - node_modules\object-visit
  object.assign@4.1.7:
    - node_modules\object.assign
  object.getownpropertydescriptors@2.1.8:
    - node_modules\object.getownpropertydescriptors
  object.pick@1.3.0:
    - node_modules\object.pick
  object.values@1.2.1:
    - node_modules\object.values
  obuf@1.1.2:
    - node_modules\obuf
  on-finished@2.4.1:
    - node_modules\on-finished
  on-headers@1.0.2:
    - node_modules\on-headers
  once@1.4.0:
    - node_modules\once
  onetime@2.0.1:
    - node_modules\restore-cursor\node_modules\onetime
  onetime@5.1.2:
    - node_modules\onetime
  open@6.4.0:
    - node_modules\open
  opener@1.5.2:
    - node_modules\opener
  opn@5.5.0:
    - node_modules\opn
  optionator@0.8.3:
    - node_modules\optionator
  ora@3.4.0:
    - node_modules\ora
  os-browserify@0.3.0:
    - node_modules\os-browserify
  os-tmpdir@1.0.2:
    - node_modules\os-tmpdir
  own-keys@1.0.1:
    - node_modules\own-keys
  p-finally@1.0.0:
    - node_modules\p-finally
  p-finally@2.0.1:
    - node_modules\default-gateway\node_modules\p-finally
  p-limit@2.3.0:
    - node_modules\p-limit
  p-locate@3.0.0:
    - node_modules\webpack-dev-server\node_modules\p-locate
    - node_modules\pkg-dir\node_modules\p-locate
  p-locate@4.1.0:
    - node_modules\p-locate
  p-map@2.1.0:
    - node_modules\p-map
  p-retry@3.0.1:
    - node_modules\p-retry
  p-try@2.2.0:
    - node_modules\p-try
  package-json-from-dist@1.0.1:
    - node_modules\package-json-from-dist
  pako@1.0.11:
    - node_modules\pako
  parallel-transform@1.2.0:
    - node_modules\parallel-transform
  param-case@2.1.1:
    - node_modules\param-case
  param-case@3.0.4:
    - node_modules\html-minifier-terser\node_modules\param-case
  parent-module@1.0.1:
    - node_modules\parent-module
  parse-asn1@5.1.7:
    - node_modules\parse-asn1
  parse-json@4.0.0:
    - node_modules\cosmiconfig\node_modules\parse-json
  parse-json@5.2.0:
    - node_modules\parse-json
  parse5-htmlparser2-tree-adapter@6.0.1:
    - node_modules\parse5-htmlparser2-tree-adapter
  parse5@5.1.1:
    - node_modules\parse5
  parse5@6.0.1:
    - node_modules\parse5-htmlparser2-tree-adapter\node_modules\parse5
  parseurl@1.3.3:
    - node_modules\parseurl
  pascal-case@3.1.2:
    - node_modules\pascal-case
  pascalcase@0.1.1:
    - node_modules\pascalcase
  path-browserify@0.0.1:
    - node_modules\path-browserify
  path-dirname@1.0.2:
    - node_modules\path-dirname
  path-exists@2.1.0:
    - node_modules\loader-fs-cache\node_modules\path-exists
  path-exists@3.0.0:
    - node_modules\webpack-dev-server\node_modules\path-exists
    - node_modules\pkg-dir\node_modules\path-exists
  path-exists@4.0.0:
    - node_modules\path-exists
  path-is-absolute@1.0.1:
    - node_modules\path-is-absolute
  path-is-inside@1.0.2:
    - node_modules\path-is-inside
  path-key@2.0.1:
    - node_modules\path-key
  path-key@3.1.1:
    - node_modules\default-gateway\node_modules\path-key
    - node_modules\foreground-child\node_modules\path-key
  path-parse@1.0.7:
    - node_modules\path-parse
  path-scurry@1.11.1:
    - node_modules\path-scurry
  path-to-regexp@0.1.12:
    - node_modules\path-to-regexp
  path-type@3.0.0:
    - node_modules\path-type
  path@0.12.7:
    - node_modules\path
  pathe@0.2.0:
    - node_modules\pathe
  pbkdf2@3.1.2:
    - node_modules\pbkdf2
  performance-now@2.1.0:
    - node_modules\performance-now
  picocolors@0.2.1:
    - node_modules\autoprefixer\node_modules\picocolors
    - node_modules\postcss\node_modules\picocolors
  picocolors@1.1.1:
    - node_modules\picocolors
  picomatch@2.3.1:
    - node_modules\picomatch
  pify@2.3.0:
    - node_modules\del\node_modules\globby\node_modules\pify
  pify@3.0.0:
    - node_modules\path-type\node_modules\pify
    - node_modules\copy-webpack-plugin\node_modules\globby\node_modules\pify
  pify@4.0.1:
    - node_modules\pify
  pinia@2.3.1(vue@3.5.16):
    - node_modules\pinia
  pinkie-promise@2.0.1:
    - node_modules\pinkie-promise
  pinkie@2.0.4:
    - node_modules\pinkie
  pkg-dir@1.0.0:
    - node_modules\loader-fs-cache\node_modules\pkg-dir
  pkg-dir@3.0.0:
    - node_modules\pkg-dir
  pkg-dir@4.2.0:
    - node_modules\find-cache-dir\node_modules\pkg-dir
  pngjs@5.0.0:
    - node_modules\pngjs
  pnp-webpack-plugin@1.7.0:
    - node_modules\pnp-webpack-plugin
  portfinder@1.0.37(supports-color@6.1.0):
    - node_modules\portfinder
  posix-character-classes@0.1.1:
    - node_modules\posix-character-classes
  possible-typed-array-names@1.1.0:
    - node_modules\possible-typed-array-names
  postcss-calc@7.0.5:
    - node_modules\postcss-calc
  postcss-colormin@4.0.3:
    - node_modules\postcss-colormin
  postcss-convert-values@4.0.1:
    - node_modules\postcss-convert-values
  postcss-discard-comments@4.0.2:
    - node_modules\postcss-discard-comments
  postcss-discard-duplicates@4.0.2:
    - node_modules\postcss-discard-duplicates
  postcss-discard-empty@4.0.1:
    - node_modules\postcss-discard-empty
  postcss-discard-overridden@4.0.1:
    - node_modules\postcss-discard-overridden
  postcss-load-config@2.1.2:
    - node_modules\postcss-load-config
  postcss-loader@3.0.0:
    - node_modules\postcss-loader
  postcss-merge-longhand@4.0.11:
    - node_modules\postcss-merge-longhand
  postcss-merge-rules@4.0.3:
    - node_modules\postcss-merge-rules
  postcss-minify-font-values@4.0.2:
    - node_modules\postcss-minify-font-values
  postcss-minify-gradients@4.0.2:
    - node_modules\postcss-minify-gradients
  postcss-minify-params@4.0.2:
    - node_modules\postcss-minify-params
  postcss-minify-selectors@4.0.2:
    - node_modules\postcss-minify-selectors
  postcss-modules-extract-imports@2.0.0:
    - node_modules\postcss-modules-extract-imports
  postcss-modules-local-by-default@3.0.3:
    - node_modules\postcss-modules-local-by-default
  postcss-modules-scope@2.2.0:
    - node_modules\postcss-modules-scope
  postcss-modules-values@3.0.0:
    - node_modules\postcss-modules-values
  postcss-normalize-charset@4.0.1:
    - node_modules\postcss-normalize-charset
  postcss-normalize-display-values@4.0.2:
    - node_modules\postcss-normalize-display-values
  postcss-normalize-positions@4.0.2:
    - node_modules\postcss-normalize-positions
  postcss-normalize-repeat-style@4.0.2:
    - node_modules\postcss-normalize-repeat-style
  postcss-normalize-string@4.0.2:
    - node_modules\postcss-normalize-string
  postcss-normalize-timing-functions@4.0.2:
    - node_modules\postcss-normalize-timing-functions
  postcss-normalize-unicode@4.0.1:
    - node_modules\postcss-normalize-unicode
  postcss-normalize-url@4.0.1:
    - node_modules\postcss-normalize-url
  postcss-normalize-whitespace@4.0.2:
    - node_modules\postcss-normalize-whitespace
  postcss-ordered-values@4.1.2:
    - node_modules\postcss-ordered-values
  postcss-reduce-initial@4.0.3:
    - node_modules\postcss-reduce-initial
  postcss-reduce-transforms@4.0.2:
    - node_modules\postcss-reduce-transforms
  postcss-selector-parser@3.1.2:
    - node_modules\postcss-minify-selectors\node_modules\postcss-selector-parser
    - node_modules\postcss-merge-rules\node_modules\postcss-selector-parser
    - node_modules\stylehacks\node_modules\postcss-selector-parser
  postcss-selector-parser@6.1.2:
    - node_modules\postcss-selector-parser
  postcss-svgo@4.0.3:
    - node_modules\postcss-svgo
  postcss-unique-selectors@4.0.1:
    - node_modules\postcss-unique-selectors
  postcss-value-parser@3.3.1:
    - node_modules\postcss-value-parser
  postcss-value-parser@4.2.0:
    - node_modules\autoprefixer\node_modules\postcss-value-parser
    - node_modules\css-loader\node_modules\postcss-value-parser
    - node_modules\postcss-modules-local-by-default\node_modules\postcss-value-parser
    - node_modules\postcss-calc\node_modules\postcss-value-parser
  postcss@7.0.39:
    - node_modules\postcss
  postcss@8.5.4:
    - node_modules\@vue\compiler-sfc\node_modules\postcss
    - node_modules\vite\node_modules\postcss
  prelude-ls@1.1.2:
    - node_modules\prelude-ls
  prepend-http@1.0.4:
    - node_modules\prepend-http
  prettier@2.8.8:
    - node_modules\prettier
  pretty-error@2.1.2:
    - node_modules\pretty-error
  process-nextick-args@2.0.1:
    - node_modules\process-nextick-args
  process@0.11.10:
    - node_modules\process
  progress@2.0.3:
    - node_modules\progress
  promise-inflight@1.0.1(bluebird@3.7.2):
    - node_modules\promise-inflight
  proxy-addr@2.0.7:
    - node_modules\proxy-addr
  prr@1.0.1:
    - node_modules\prr
  pseudomap@1.0.2:
    - node_modules\pseudomap
  psl@1.15.0:
    - node_modules\psl
  public-encrypt@4.0.3:
    - node_modules\public-encrypt
  pump@2.0.1:
    - node_modules\pumpify\node_modules\pump
  pump@3.0.2:
    - node_modules\pump
  pumpify@1.5.1:
    - node_modules\pumpify
  punycode@1.4.1:
    - node_modules\node-libs-browser\node_modules\punycode
    - node_modules\url\node_modules\punycode
  punycode@2.3.1:
    - node_modules\punycode
  q@1.5.1:
    - node_modules\q
  qrcode@1.5.4:
    - node_modules\qrcode
  qs@6.13.0:
    - node_modules\express\node_modules\qs
    - node_modules\body-parser\node_modules\qs
  qs@6.14.0:
    - node_modules\qs
  qs@6.5.3:
    - node_modules\request\node_modules\qs
  query-string@4.3.4:
    - node_modules\query-string
  querystring-es3@0.2.1:
    - node_modules\querystring-es3
  querystringify@2.2.0:
    - node_modules\querystringify
  queue-microtask@1.2.3:
    - node_modules\queue-microtask
  randombytes@2.1.0:
    - node_modules\randombytes
  randomfill@1.0.4:
    - node_modules\randomfill
  range-parser@1.2.1:
    - node_modules\range-parser
  raw-body@2.5.2:
    - node_modules\raw-body
  read-pkg@5.2.0:
    - node_modules\read-pkg
  readable-stream@2.3.8:
    - node_modules\readable-stream
  readable-stream@3.6.2:
    - node_modules\spdy-transport\node_modules\readable-stream
  readdirp@2.2.1(supports-color@6.1.0):
    - node_modules\chokidar\node_modules\readdirp
  readdirp@3.6.0:
    - node_modules\watchpack\node_modules\readdirp
  readdirp@4.1.2:
    - node_modules\readdirp
  reflect.getprototypeof@1.0.10:
    - node_modules\reflect.getprototypeof
  regenerate-unicode-properties@10.2.0:
    - node_modules\regenerate-unicode-properties
  regenerate@1.4.2:
    - node_modules\regenerate
  regenerator-runtime@0.13.11:
    - node_modules\regenerator-runtime
  regex-not@1.0.2:
    - node_modules\regex-not
  regexp.prototype.flags@1.5.4:
    - node_modules\regexp.prototype.flags
  regexpp@2.0.1:
    - node_modules\regexpp
  regexpu-core@6.2.0:
    - node_modules\regexpu-core
  regjsgen@0.8.0:
    - node_modules\regjsgen
  regjsparser@0.12.0:
    - node_modules\regjsparser
  relateurl@0.2.7:
    - node_modules\relateurl
  remove-trailing-separator@1.1.0:
    - node_modules\remove-trailing-separator
  renderkid@2.0.7:
    - node_modules\renderkid
  repeat-element@1.1.4:
    - node_modules\repeat-element
  repeat-string@1.6.1:
    - node_modules\repeat-string
  request@2.88.2:
    - node_modules\request
  require-directory@2.1.1:
    - node_modules\require-directory
  require-main-filename@2.0.0:
    - node_modules\require-main-filename
  requires-port@1.0.0:
    - node_modules\requires-port
  resolve-cwd@2.0.0:
    - node_modules\resolve-cwd
  resolve-from@3.0.0:
    - node_modules\resolve-from
  resolve-from@4.0.0:
    - node_modules\eslint\node_modules\resolve-from
  resolve-url@0.2.1:
    - node_modules\resolve-url
  resolve@1.22.10:
    - node_modules\resolve
  restore-cursor@2.0.0:
    - node_modules\restore-cursor
  restore-cursor@3.1.0:
    - node_modules\inquirer\node_modules\restore-cursor
  ret@0.1.15:
    - node_modules\ret
  retry@0.12.0:
    - node_modules\retry
  reusify@1.1.0:
    - node_modules\reusify
  rgb-regex@1.0.1:
    - node_modules\rgb-regex
  rgba-regex@1.0.0:
    - node_modules\rgba-regex
  rimraf@2.6.3:
    - node_modules\flat-cache\node_modules\rimraf
  rimraf@2.7.1:
    - node_modules\eslint-loader\node_modules\rimraf
    - node_modules\cacache\node_modules\rimraf
    - node_modules\del\node_modules\rimraf
    - node_modules\move-concurrently\node_modules\rimraf
    - node_modules\copy-concurrently\node_modules\rimraf
  rimraf@5.0.10:
    - node_modules\rimraf
  ripemd160@2.0.2:
    - node_modules\ripemd160
  rollup@2.79.2:
    - node_modules\rollup
  run-async@2.4.1:
    - node_modules\run-async
  run-parallel@1.2.0:
    - node_modules\run-parallel
  run-queue@1.0.3:
    - node_modules\run-queue
  rxjs@6.6.7:
    - node_modules\rxjs
  safe-array-concat@1.1.3:
    - node_modules\safe-array-concat
  safe-buffer@5.1.2:
    - node_modules\readable-stream\node_modules\safe-buffer
  safe-buffer@5.2.1:
    - node_modules\safe-buffer
  safe-push-apply@1.0.0:
    - node_modules\safe-push-apply
  safe-regex-test@1.1.0:
    - node_modules\safe-regex-test
  safe-regex@1.1.0:
    - node_modules\safe-regex
  safer-buffer@2.1.2:
    - node_modules\safer-buffer
  sass@1.89.2:
    - node_modules\sass
  sax@1.2.4:
    - node_modules\sax
  schema-utils@1.0.0:
    - node_modules\schema-utils
  schema-utils@2.7.1:
    - node_modules\cache-loader\node_modules\schema-utils
    - node_modules\babel-loader\node_modules\schema-utils
    - node_modules\css-loader\node_modules\schema-utils
    - node_modules\file-loader\node_modules\schema-utils
    - node_modules\url-loader\node_modules\schema-utils
  screenfull@5.2.0:
    - node_modules\screenfull
  select-hose@2.0.0:
    - node_modules\select-hose
  select@1.1.2:
    - node_modules\select
  selfsigned@1.10.14:
    - node_modules\selfsigned
  semver@5.7.2:
    - node_modules\copy-webpack-plugin\node_modules\semver
    - node_modules\terser-webpack-plugin\node_modules\semver
    - node_modules\cross-spawn\node_modules\semver
    - node_modules\normalize-package-data\node_modules\semver
  semver@6.3.1:
    - node_modules\semver
  send@0.19.0(supports-color@6.1.0):
    - node_modules\send
  serialize-javascript@4.0.0:
    - node_modules\serialize-javascript
  serve-index@1.9.1(supports-color@6.1.0):
    - node_modules\serve-index
  serve-static@1.16.2(supports-color@6.1.0):
    - node_modules\serve-static
  set-blocking@2.0.0:
    - node_modules\set-blocking
  set-function-length@1.2.2:
    - node_modules\set-function-length
  set-function-name@2.0.2:
    - node_modules\set-function-name
  set-proto@1.0.0:
    - node_modules\set-proto
  set-value@2.0.1:
    - node_modules\set-value
  setimmediate@1.0.5:
    - node_modules\setimmediate
  setprototypeof@1.1.0:
    - node_modules\serve-index\node_modules\setprototypeof
  setprototypeof@1.2.0:
    - node_modules\setprototypeof
  sha.js@2.4.11:
    - node_modules\sha.js
  shebang-command@1.2.0:
    - node_modules\shebang-command
  shebang-command@2.0.0:
    - node_modules\default-gateway\node_modules\shebang-command
    - node_modules\foreground-child\node_modules\shebang-command
  shebang-regex@1.0.0:
    - node_modules\shebang-regex
  shebang-regex@3.0.0:
    - node_modules\default-gateway\node_modules\shebang-regex
    - node_modules\foreground-child\node_modules\shebang-regex
  shell-quote@1.8.3:
    - node_modules\shell-quote
  side-channel-list@1.0.0:
    - node_modules\side-channel-list
  side-channel-map@1.0.1:
    - node_modules\side-channel-map
  side-channel-weakmap@1.0.2:
    - node_modules\side-channel-weakmap
  side-channel@1.1.0:
    - node_modules\side-channel
  signal-exit@3.0.7:
    - node_modules\signal-exit
  signal-exit@4.1.0:
    - node_modules\foreground-child\node_modules\signal-exit
  simple-swizzle@0.2.2:
    - node_modules\simple-swizzle
  slash@1.0.0:
    - node_modules\slash
  slash@2.0.0:
    - node_modules\globby\node_modules\slash
  slice-ansi@2.1.0:
    - node_modules\slice-ansi
  snapdragon-node@2.1.1:
    - node_modules\snapdragon-node
  snapdragon-util@3.0.1:
    - node_modules\snapdragon-util
  snapdragon@0.8.2(supports-color@6.1.0):
    - node_modules\snapdragon
  sockjs-client@1.6.1(supports-color@6.1.0):
    - node_modules\sockjs-client
  sockjs@0.3.24:
    - node_modules\sockjs
  sort-keys@1.1.2:
    - node_modules\sort-keys
  source-list-map@2.0.1:
    - node_modules\source-list-map
  source-map-js@1.2.1:
    - node_modules\source-map-js
  source-map-resolve@0.5.3:
    - node_modules\source-map-resolve
  source-map-support@0.5.21:
    - node_modules\source-map-support
  source-map-url@0.4.1:
    - node_modules\source-map-url
  source-map@0.5.7:
    - node_modules\snapdragon\node_modules\source-map
  source-map@0.6.1:
    - node_modules\source-map
  source-map@0.7.4:
    - node_modules\@types\webpack-sources\node_modules\source-map
  sourcemap-codec@1.4.8:
    - node_modules\sourcemap-codec
  spdx-correct@3.2.0:
    - node_modules\spdx-correct
  spdx-exceptions@2.5.0:
    - node_modules\spdx-exceptions
  spdx-expression-parse@3.0.1:
    - node_modules\spdx-expression-parse
  spdx-license-ids@3.0.21:
    - node_modules\spdx-license-ids
  spdy-transport@3.0.0(supports-color@6.1.0):
    - node_modules\spdy-transport
  spdy@4.0.2(supports-color@6.1.0):
    - node_modules\spdy
  split-string@3.1.0:
    - node_modules\split-string
  sprintf-js@1.0.3:
    - node_modules\sprintf-js
  sshpk@1.18.0:
    - node_modules\sshpk
  ssri@6.0.2:
    - node_modules\ssri
  ssri@8.0.1:
    - node_modules\@vue\cli-service\node_modules\ssri
  stable@0.1.8:
    - node_modules\stable
  stackframe@1.3.4:
    - node_modules\stackframe
  static-extend@0.1.2:
    - node_modules\static-extend
  statuses@1.5.0:
    - node_modules\serve-index\node_modules\statuses
  statuses@2.0.1:
    - node_modules\statuses
  stop-iteration-iterator@1.1.0:
    - node_modules\stop-iteration-iterator
  stream-browserify@2.0.2:
    - node_modules\stream-browserify
  stream-each@1.2.3:
    - node_modules\stream-each
  stream-http@2.8.3:
    - node_modules\stream-http
  stream-shift@1.0.3:
    - node_modules\stream-shift
  strict-uri-encode@1.1.0:
    - node_modules\strict-uri-encode
  string-width@3.1.0:
    - node_modules\table\node_modules\string-width
    - node_modules\webpack-dev-server\node_modules\string-width
  string-width@4.2.3:
    - node_modules\string-width
    - node_modules\string-width-cjs
  string-width@5.1.2:
    - node_modules\@isaacs\cliui\node_modules\string-width
  string.prototype.trim@1.2.10:
    - node_modules\string.prototype.trim
  string.prototype.trimend@1.0.9:
    - node_modules\string.prototype.trimend
  string.prototype.trimstart@1.0.8:
    - node_modules\string.prototype.trimstart
  string_decoder@1.1.1:
    - node_modules\readable-stream\node_modules\string_decoder
  string_decoder@1.3.0:
    - node_modules\string_decoder
  strip-ansi@3.0.1:
    - node_modules\webpack-dev-server\node_modules\strip-ansi
    - node_modules\renderkid\node_modules\strip-ansi
  strip-ansi@5.2.0:
    - node_modules\eslint\node_modules\strip-ansi
    - node_modules\table\node_modules\strip-ansi
    - node_modules\ora\node_modules\strip-ansi
    - node_modules\webpack-dev-server\node_modules\cliui\node_modules\strip-ansi
    - node_modules\webpack-dev-server\node_modules\string-width\node_modules\strip-ansi
    - node_modules\webpack-dev-server\node_modules\wrap-ansi\node_modules\strip-ansi
  strip-ansi@6.0.1:
    - node_modules\strip-ansi
    - node_modules\strip-ansi-cjs
  strip-ansi@7.1.0:
    - node_modules\@isaacs\cliui\node_modules\strip-ansi
  strip-eof@1.0.0:
    - node_modules\strip-eof
  strip-final-newline@2.0.0:
    - node_modules\strip-final-newline
  strip-indent@2.0.0:
    - node_modules\strip-indent
  strip-json-comments@3.1.1:
    - node_modules\strip-json-comments
  stylehacks@4.0.3:
    - node_modules\stylehacks
  supports-color@5.5.0:
    - node_modules\eslint\node_modules\supports-color
    - node_modules\@vue\cli-shared-utils\node_modules\supports-color
    - node_modules\webpack-bundle-analyzer\node_modules\supports-color
    - node_modules\ora\node_modules\supports-color
    - node_modules\log-symbols\node_modules\supports-color
    - node_modules\svgo\node_modules\supports-color
    - node_modules\coa\node_modules\supports-color
  supports-color@6.1.0:
    - node_modules\supports-color
  supports-color@7.2.0:
    - node_modules\chalk\node_modules\supports-color
    - node_modules\@soda\friendly-errors-webpack-plugin\node_modules\supports-color
  supports-preserve-symlinks-flag@1.0.0:
    - node_modules\supports-preserve-symlinks-flag
  svg-tags@1.0.0:
    - node_modules\svg-tags
  svgo@1.3.2:
    - node_modules\svgo
  systemjs@6.15.1:
    - node_modules\systemjs
  table@5.4.6:
    - node_modules\table
  tapable@1.1.3:
    - node_modules\tapable
  terser-webpack-plugin@1.4.6(webpack@4.47.0):
    - node_modules\terser-webpack-plugin
  terser@4.8.1:
    - node_modules\terser-webpack-plugin\node_modules\terser
  terser@5.42.0:
    - node_modules\terser
  text-table@0.2.0:
    - node_modules\text-table
  thenify-all@1.6.0:
    - node_modules\thenify-all
  thenify@3.3.1:
    - node_modules\thenify
  thread-loader@2.1.3(webpack@4.47.0):
    - node_modules\thread-loader
  through2@2.0.5:
    - node_modules\through2
  through@2.3.8:
    - node_modules\through
  thunky@1.1.0:
    - node_modules\thunky
  timers-browserify@2.0.12:
    - node_modules\timers-browserify
  timsort@0.3.0:
    - node_modules\timsort
  tiny-emitter@2.1.0:
    - node_modules\tiny-emitter
  tmp@0.0.33:
    - node_modules\tmp
  to-arraybuffer@1.0.1:
    - node_modules\to-arraybuffer
  to-object-path@0.3.0:
    - node_modules\to-object-path
  to-regex-range@2.1.1:
    - node_modules\micromatch\node_modules\to-regex-range
    - node_modules\chokidar\node_modules\to-regex-range
  to-regex-range@5.0.1:
    - node_modules\to-regex-range
  to-regex@3.0.2:
    - node_modules\to-regex
  toidentifier@1.0.1:
    - node_modules\toidentifier
  toposort@1.0.7:
    - node_modules\toposort
  tough-cookie@2.5.0:
    - node_modules\tough-cookie
  tryer@1.0.1:
    - node_modules\tryer
  ts-pnp@1.2.0:
    - node_modules\ts-pnp
  tslib@1.14.1:
    - node_modules\rxjs\node_modules\tslib
  tslib@2.8.1:
    - node_modules\tslib
  tty-browserify@0.0.0:
    - node_modules\tty-browserify
  tunnel-agent@0.6.0:
    - node_modules\tunnel-agent
  tweetnacl@0.14.5:
    - node_modules\tweetnacl
  type-check@0.3.2:
    - node_modules\type-check
  type-fest@0.21.3:
    - node_modules\ansi-escapes\node_modules\type-fest
  type-fest@0.6.0:
    - node_modules\type-fest
  type-fest@0.8.1:
    - node_modules\eslint\node_modules\type-fest
  type-is@1.6.18:
    - node_modules\type-is
  typed-array-buffer@1.0.3:
    - node_modules\typed-array-buffer
  typed-array-byte-length@1.0.3:
    - node_modules\typed-array-byte-length
  typed-array-byte-offset@1.0.4:
    - node_modules\typed-array-byte-offset
  typed-array-length@1.0.7:
    - node_modules\typed-array-length
  typedarray@0.0.6:
    - node_modules\typedarray
  uglify-js@3.4.10:
    - node_modules\uglify-js
  unbox-primitive@1.1.0:
    - node_modules\unbox-primitive
  undici-types@7.8.0:
    - node_modules\undici-types
  unicode-canonical-property-names-ecmascript@2.0.1:
    - node_modules\unicode-canonical-property-names-ecmascript
  unicode-match-property-ecmascript@2.0.0:
    - node_modules\unicode-match-property-ecmascript
  unicode-match-property-value-ecmascript@2.2.0:
    - node_modules\unicode-match-property-value-ecmascript
  unicode-property-aliases-ecmascript@2.1.0:
    - node_modules\unicode-property-aliases-ecmascript
  union-value@1.0.1:
    - node_modules\union-value
  uniq@1.0.1:
    - node_modules\uniq
  uniqs@2.0.0:
    - node_modules\uniqs
  unique-filename@1.1.1:
    - node_modules\unique-filename
  unique-slug@2.0.2:
    - node_modules\unique-slug
  universalify@0.1.2:
    - node_modules\fs-extra\node_modules\universalify
  universalify@2.0.1:
    - node_modules\universalify
  unpipe@1.0.0:
    - node_modules\unpipe
  unquote@1.1.1:
    - node_modules\unquote
  unset-value@1.0.0:
    - node_modules\unset-value
  upath@1.2.0:
    - node_modules\upath
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    - node_modules\update-browserslist-db
  upper-case@1.1.3:
    - node_modules\upper-case
  uri-js@4.4.1:
    - node_modules\uri-js
  urix@0.1.0:
    - node_modules\urix
  url-loader@2.3.0(file-loader@4.3.0(webpack@4.47.0))(webpack@4.47.0):
    - node_modules\url-loader
  url-parse@1.5.10:
    - node_modules\url-parse
  url@0.11.4:
    - node_modules\url
  use@3.1.1:
    - node_modules\use
  util-deprecate@1.0.2:
    - node_modules\util-deprecate
  util.promisify@1.0.0:
    - node_modules\html-webpack-plugin\node_modules\util.promisify
  util.promisify@1.0.1:
    - node_modules\util.promisify
  util@0.10.4:
    - node_modules\util
  util@0.11.1:
    - node_modules\node-libs-browser\node_modules\util
  utila@0.4.0:
    - node_modules\utila
  utils-merge@1.0.1:
    - node_modules\utils-merge
  uuid@3.4.0:
    - node_modules\uuid
  uuid@8.3.2:
    - node_modules\sockjs\node_modules\uuid
  v8-compile-cache@2.4.0:
    - node_modules\v8-compile-cache
  validate-npm-package-license@3.0.4:
    - node_modules\validate-npm-package-license
  vary@1.1.2:
    - node_modules\vary
  vendors@1.0.4:
    - node_modules\vendors
  verror@1.10.0:
    - node_modules\verror
  vite-plugin-banner@0.1.3:
    - node_modules\vite-plugin-banner
  vite-plugin-html@3.2.2(vite@3.2.11(@types/node@24.0.0)(sass@1.89.2)(terser@5.42.0)):
    - node_modules\vite-plugin-html
  vite-plugin-importer@0.2.5:
    - node_modules\vite-plugin-importer
  vite@3.2.11(@types/node@24.0.0)(sass@1.89.2)(terser@5.42.0):
    - node_modules\vite
  vm-browserify@1.1.2:
    - node_modules\vm-browserify
  vue-clipboard3@2.0.0:
    - node_modules\vue-clipboard3
  vue-demi@0.14.10(vue@3.5.16):
    - node_modules\vue-demi
  vue-eslint-parser@7.11.0(eslint@6.8.0):
    - node_modules\vue-eslint-parser
  vue-hot-reload-api@2.3.4:
    - node_modules\vue-hot-reload-api
  vue-loader@15.11.1(@vue/compiler-sfc@3.5.16)(cache-loader@4.1.0(webpack@4.47.0))(css-loader@3.6.0(webpack@4.47.0))(ejs@3.1.10)(lodash@4.17.21)(webpack@4.47.0):
    - node_modules\vue-loader
  vue-loader@16.8.3(@vue/compiler-sfc@3.5.16)(vue@3.5.16)(webpack@4.47.0):
    - node_modules\vue-loader-v16
  vue-router@4.5.1(vue@3.5.16):
    - node_modules\vue-router
  vue-style-loader@4.1.3:
    - node_modules\vue-style-loader
  vue-template-es2015-compiler@1.9.1:
    - node_modules\vue-template-es2015-compiler
  vue@3.5.16:
    - node_modules\vue
  watchpack-chokidar2@2.0.1:
    - node_modules\watchpack-chokidar2
  watchpack@1.7.5:
    - node_modules\watchpack
  wbuf@1.7.3:
    - node_modules\wbuf
  wcwidth@1.0.1:
    - node_modules\wcwidth
  webpack-bundle-analyzer@3.9.0:
    - node_modules\webpack-bundle-analyzer
  webpack-chain@6.5.1:
    - node_modules\webpack-chain
  webpack-dev-middleware@3.7.3(webpack@4.47.0):
    - node_modules\webpack-dev-middleware
  webpack-dev-server@3.11.3(webpack@4.47.0):
    - node_modules\webpack-dev-server
  webpack-log@2.0.0:
    - node_modules\webpack-log
  webpack-merge@4.2.2:
    - node_modules\webpack-merge
  webpack-sources@1.4.3:
    - node_modules\webpack-sources
  webpack@4.47.0:
    - node_modules\webpack
  websocket-driver@0.7.4:
    - node_modules\websocket-driver
  websocket-extensions@0.1.4:
    - node_modules\websocket-extensions
  which-boxed-primitive@1.1.1:
    - node_modules\which-boxed-primitive
  which-builtin-type@1.2.1:
    - node_modules\which-builtin-type
  which-collection@1.0.2:
    - node_modules\which-collection
  which-module@2.0.1:
    - node_modules\which-module
  which-typed-array@1.1.19:
    - node_modules\which-typed-array
  which@1.3.1:
    - node_modules\which
  which@2.0.2:
    - node_modules\default-gateway\node_modules\which
    - node_modules\foreground-child\node_modules\which
  word-wrap@1.2.5:
    - node_modules\word-wrap
  worker-farm@1.7.0:
    - node_modules\worker-farm
  wrap-ansi@5.1.0:
    - node_modules\webpack-dev-server\node_modules\wrap-ansi
  wrap-ansi@6.2.0:
    - node_modules\wrap-ansi
  wrap-ansi@7.0.0:
    - node_modules\wrap-ansi-cjs
    - node_modules\cli-highlight\node_modules\wrap-ansi
  wrap-ansi@8.1.0:
    - node_modules\@isaacs\cliui\node_modules\wrap-ansi
  wrappy@1.0.2:
    - node_modules\wrappy
  write@1.0.3:
    - node_modules\write
  ws@6.2.3:
    - node_modules\ws
  xtend@4.0.2:
    - node_modules\xtend
  y18n@4.0.3:
    - node_modules\y18n
  y18n@5.0.8:
    - node_modules\cli-highlight\node_modules\y18n
  yallist@2.1.2:
    - node_modules\yorkie\node_modules\yallist
    - node_modules\@vue\component-compiler-utils\node_modules\yallist
  yallist@3.1.1:
    - node_modules\yallist
  yallist@4.0.0:
    - node_modules\@vue\cli-service\node_modules\yallist
  yargs-parser@13.1.2:
    - node_modules\webpack-dev-server\node_modules\yargs-parser
  yargs-parser@18.1.3:
    - node_modules\yargs-parser
  yargs-parser@20.2.9:
    - node_modules\cli-highlight\node_modules\yargs-parser
  yargs@13.3.2:
    - node_modules\webpack-dev-server\node_modules\yargs
  yargs@15.4.1:
    - node_modules\yargs
  yargs@16.2.0:
    - node_modules\cli-highlight\node_modules\yargs
  yorkie@2.0.0:
    - node_modules\yorkie
ignoredBuilds:
  - gifsicle
  - jpegtran-bin
  - cwebp-bin
  - optipng-bin
  - mozjpeg
  - pngquant-bin
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@10.7.1
pendingBuilds: []
prunedAt: Tue, 10 Jun 2025 08:18:33 GMT
publicHoistPattern:
  - '*'
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/android-arm@0.15.18'
  - '@esbuild/linux-loong64@0.14.54'
  - '@esbuild/linux-loong64@0.15.18'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - bindings@1.5.0
  - esbuild-android-64@0.14.54
  - esbuild-android-64@0.15.18
  - esbuild-android-arm64@0.14.54
  - esbuild-android-arm64@0.15.18
  - esbuild-darwin-64@0.14.54
  - esbuild-darwin-64@0.15.18
  - esbuild-darwin-arm64@0.14.54
  - esbuild-darwin-arm64@0.15.18
  - esbuild-freebsd-64@0.14.54
  - esbuild-freebsd-64@0.15.18
  - esbuild-freebsd-arm64@0.14.54
  - esbuild-freebsd-arm64@0.15.18
  - esbuild-linux-32@0.14.54
  - esbuild-linux-32@0.15.18
  - esbuild-linux-64@0.14.54
  - esbuild-linux-64@0.15.18
  - esbuild-linux-arm64@0.14.54
  - esbuild-linux-arm64@0.15.18
  - esbuild-linux-arm@0.14.54
  - esbuild-linux-arm@0.15.18
  - esbuild-linux-mips64le@0.14.54
  - esbuild-linux-mips64le@0.15.18
  - esbuild-linux-ppc64le@0.14.54
  - esbuild-linux-ppc64le@0.15.18
  - esbuild-linux-riscv64@0.14.54
  - esbuild-linux-riscv64@0.15.18
  - esbuild-linux-s390x@0.14.54
  - esbuild-linux-s390x@0.15.18
  - esbuild-netbsd-64@0.14.54
  - esbuild-netbsd-64@0.15.18
  - esbuild-openbsd-64@0.14.54
  - esbuild-openbsd-64@0.15.18
  - esbuild-sunos-64@0.14.54
  - esbuild-sunos-64@0.15.18
  - esbuild-windows-32@0.14.54
  - esbuild-windows-32@0.15.18
  - esbuild-windows-arm64@0.14.54
  - esbuild-windows-arm64@0.15.18
  - file-uri-to-path@1.0.0
  - fsevents@1.2.13
  - fsevents@2.3.3
  - nan@2.22.2
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\asec-platform\frontend\portal\node_modules\.pnpm
virtualStoreDirMaxLength: 60
