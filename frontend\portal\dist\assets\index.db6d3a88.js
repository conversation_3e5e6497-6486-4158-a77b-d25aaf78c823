/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
import{x as e,r as a,J as s,K as l,N as t,b as n,u as o,D as c,h as i,o as r,d as u,e as d,j as p,w as m,f as v,_ as f,F as g,i as b,g as h,k as y,t as _,I as k,B as w,M as x}from"./index.a5cb1178.js";const W={class:"person"},S={class:"header-right"},C={class:"search-controls"},D={class:"el-row"},U={class:"el-recent-data"},I={class:"el-recent-item"},O={class:"el-recent-item"},z={class:"el-recent-item"},A=["title"],T={class:"category-title-wrapper"},E=["title"],F=["onClick"],N={key:0,class:"status-badge"},P={class:"app-content"},V={class:"app-icon"},$={class:"tooltip-content"},B={key:0},J={key:1},q={class:"app-details"},L=["title"],M={key:0,class:"app-desc"},j=f(Object.assign({name:"AppPage"},{setup(f){const j=a(""),R=a(null),X=a([]),H=a([]),G=a("0"),K=a(!1),Q=a("standard"),Y=s([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),Z=a(null),ee=a(!1),ae=(e,a="success",s=3e3)=>{x({message:e,type:a,duration:s})},se=async e=>new Promise(((a,s)=>{let l,t=!1;(async()=>{try{const n=await new Promise(((e,a)=>{if(Z.value&&Z.value.readyState===WebSocket.OPEN)return void e(Z.value);const s=new WebSocket("ws://localhost:50001");ee.value=!0,s.onopen=()=>{console.log("WebSocket Connected"),Z.value=s,ee.value=!1,e(s)},s.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&ae(a,"error")},s.onclose=()=>{console.log("WebSocket Disconnected"),Z.value=null,ee.value=!1},s.onerror=e=>{console.error("WebSocket Error:",e),ee.value=!1,a(e)},setTimeout((()=>{ee.value&&(ee.value=!1,s.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};l=setTimeout((()=>{t||(n.close(),s(new Error("启动超时：未收到响应")))}),3e3),n.onmessage=e=>{t=!0,clearTimeout(l);const n=e.data;n.startsWith("Ok")?a():s(new Error(n))},n.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(n){clearTimeout(l),s(n)}})()}));l((()=>{Z.value&&(Z.value.close(),Z.value=null)}));const le=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let s=0;for(let l=0;l<e.length;l++)s+=e.charCodeAt(l);return a[s%a.length]},te=()=>{K.value=!0},ne=e=>{R.value=parseInt(e),H.value=e?X.value.filter((a=>a.id===parseInt(e))):X.value},oe=()=>{if(!j.value)return void(H.value=X.value);const e=j.value.toLowerCase().trim();H.value=X.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))},ce=async()=>{try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(console.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",e),X.value=e,H.value=e,e.length>0&&(R.value=e[0].id,G.value=e[0].id.toString())}}catch(a){console.error("API调用出错:",a)}};t((()=>{ce()}));const ie=n(),re=o().query;let ue=null;try{if(!c.isClient()){const e=new XMLHttpRequest;e.open("GET",document.location,!1),e.send(null),ue=e.getResponseHeader("X-Corp-ID")}}catch(pe){console.warn("无法获取 X-Corp-ID header，使用默认值:",pe)}const de={action:0,msg:{token:ie.token.accessToken,refreshToken:ie.token.refreshToken,realm:ue||"default"},platform:document.location.hostname};{const e=re.wp||50001,s=a({}),l=a(`ws://127.0.0.1:${e}`),t=navigator.platform;0!==t.indexOf("Mac")&&"MacIntel"!==t||(l.value=`wss://127.0.0.1:${e}`);const n=()=>{s.value=new WebSocket(l.value),s.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(de))},s.value.onmessage=e=>{console.log(e),c()},s.value.onerror=()=>{console.log("socket连接错误:"+l.value),window.location.href=`asecagent://?web=${JSON.stringify(de)}`}},o=e=>{console.log(e,"0"),s.value.send(e)},c=()=>{console.log("socket断开链接"),s.value.close()};console.log(`asecagent://?web=${JSON.stringify(de)}`),n()}return(e,a)=>{const s=i("base-icon"),l=i("base-input"),t=i("base-button"),n=i("base-option"),o=i("base-select"),c=i("base-header"),f=i("base-menu-item"),x=i("base-tooltip"),R=i("base-menu"),Z=i("base-aside"),ee=i("base-avatar"),ie=i("base-main"),re=i("base-container");return r(),u("div",null,[d("div",W,[p(c,{class:"app-header"},{default:m((()=>[a[5]||(a[5]=d("div",{class:"header-left"},[d("h1",{class:"page-title"},"我的应用")],-1)),d("div",S,[d("div",C,[p(s,{class:"search-icon",name:"search"}),p(l,{class:"search-input",modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:oe,clearable:""},null,8,["modelValue"]),p(t,{class:"refresh-btn",icon:"Refresh",size:"small",onClick:ce},{default:m((()=>a[4]||(a[4]=[d("svg",{class:"icon refresh-btn-icon","aria-hidden":"true"},[d("use",{"xlink:href":"#icon-search"})],-1)]))),_:1,__:[4]}),p(o,{class:"view-select",modelValue:Q.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value=e),size:"small"},{default:m((()=>[(r(!0),u(g,null,b(Y,(e=>(r(),h(n,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])])])),_:1,__:[5]}),p(c,{class:"app-header"},{default:m((()=>[d("div",D,[a[10]||(a[10]=d("span",{class:"el-recent-access"},"最新访问",-1)),d("span",U,[d("span",I,[a[6]||(a[6]=y(" 最新访问1 ")),p(s,{class:"el-recent-icon",name:"close",size:"8px"})]),d("span",O,[a[7]||(a[7]=y(" 最新访问2 ")),p(s,{class:"el-recent-icon",name:"close",size:"8px"})]),d("span",z,[a[8]||(a[8]=y(" 最新访问3 ")),p(s,{class:"el-recent-icon",name:"close",size:"8px"})]),a[9]||(a[9]=d("svg",{class:"icon el-recent-clear","aria-hidden":"true",title:"清空"},[d("use",{"xlink:href":"#icon-qingkong"})],-1))])])])),_:1}),v(" 主体内容区域：使用 el-container 实现左右布局 "),p(re,null,{default:m((()=>[v(" 左侧分类导航 "),p(Z,{width:"96px",class:"category-aside"},{default:m((()=>[p(R,{class:"category-menu",mode:"vertical","background-color":"#ffffff",onSelect:ne,"default-active":G.value},{default:m((()=>[p(f,{class:"category-menu-item",index:"-1",onClick:a[2]||(a[2]=e=>ne(-1))},{default:m((()=>a[11]||(a[11]=[y(" 收藏 ")]))),_:1,__:[11]}),p(f,{class:"category-menu-item",index:"0",onClick:a[3]||(a[3]=e=>ne(null))},{default:m((()=>[p(x,{effect:"light",placement:"right",content:"全部",disabled:!0},{default:m((()=>a[12]||(a[12]=[d("span",{class:"category-menu-text"},"全部",-1)]))),_:1,__:[12]},8,["disabled"])])),_:1}),(r(!0),u(g,null,b(X.value,(e=>(r(),h(f,{key:e.id,index:e.id.toString()},{default:m((()=>[p(x,{effect:"light",placement:"right",content:e.name,disabled:e.name.length<=6},{default:m((()=>[d("span",{class:"category-menu-text",title:e.name},_(e.name),9,A)])),_:2},1032,["content","disabled"])])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),v(" 右侧应用列表 "),p(ie,{class:"app-main"},{default:m((()=>[(r(!0),u(g,null,b(H.value,(e=>(r(),u("div",{key:e.id,class:"category-section"},[v(" 分类标题 "),d("div",T,[d("h3",{class:"category-title",title:e.name},_(e.name),9,E),e.name.length>20?(r(),h(x,{key:0,effect:"light",placement:"top",content:e.name},{default:m((()=>[p(s,{name:"info",class:"category-title-info",size:"14px"})])),_:2},1032,["content"])):v("v-if",!0)]),v(" 应用列表 "),d("div",{class:k(["apps-grid",`view-${Q.value}`])},[(r(!0),u(g,null,b(e.apps,(e=>(r(),u("div",{key:e.id,class:k(["app-item",{disabled:!e.WebUrl||e.maint}]),onClick:a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const a=e.WebUrl.substring(3);try{ae("正在启动爱尔企业浏览器...","info"),await se(a),ae("启动成功","success")}catch(pe){ae("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)},[e.maint?(r(),u("div",N," 维护中 ")):v("v-if",!0),d("div",P,[d("div",V,[p(x,{effect:"light",placement:"bottom"},{content:m((()=>[d("div",$,[e.WebUrl?(r(),u("span",B,_(e.WebUrl),1)):(r(),u("span",J,"暂无访问地址"))])])),default:m((()=>[p(ee,{shape:"square",size:"compact"===Q.value?40:48,src:e.icon,onError:te,style:w(!e.icon||K.value?`background-color: ${le(e.app_name)} !important`:"")},{default:m((()=>[y(_(!e.icon||K.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["size","src","style"])])),_:2},1024)]),d("div",q,[d("div",{class:"app-name",title:e.app_name},_(e.app_name),9,L),"standard"===Q.value?(r(),u("div",M,_(e.app_desc||"应用程序"),1)):v("v-if",!0)])])],10,F)))),128))],2)])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]);export{j as default};
