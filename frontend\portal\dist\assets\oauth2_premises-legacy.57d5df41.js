/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var o,t,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.toStringTag||"@@toStringTag";function s(e,i,r,a){var s=i&&i.prototype instanceof u?i:u,l=Object.create(s.prototype);return n(l,"_invoke",function(e,n,i){var r,a,s,u=0,l=i||[],d=!1,p={p:0,n:0,v:o,a:f,f:f.bind(o,4),d:function(e,n){return r=e,a=0,s=o,p.n=n,c}};function f(e,n){for(a=e,s=n,t=0;!d&&u&&!i&&t<l.length;t++){var i,r=l[t],f=p.p,g=r[2];e>3?(i=g===n)&&(s=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=o):r[0]<=f&&((i=e<2&&f<r[1])?(a=0,p.v=n,p.n=r[1]):f<g&&(i=e<3||r[0]>n||n>g)&&(r[4]=e,r[5]=n,p.n=g,a=0))}if(i||e>1)return c;throw d=!0,n}return function(i,l,g){if(u>1)throw TypeError("Generator is already running");for(d&&1===l&&f(l,g),a=l,s=g;(t=a<2?o:s)||!d;){r||(a?a<3?(a>1&&(p.n=-1),f(a,s)):p.n=s:p.v=s);try{if(u=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=o}else if((t=(d=p.n<0)?s:e.call(n,p))!==c)break}catch(t){r=o,a=1,s=t}finally{u=1}}return{value:t,done:d}}}(e,r,a),!0),l}var c={};function u(){}function l(){}function d(){}t=Object.getPrototypeOf;var p=[][r]?t(t([][r]())):(n(t={},r,(function(){return this})),t),f=d.prototype=u.prototype=Object.create(p);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,n(e,a,"GeneratorFunction")),e.prototype=Object.create(f),e}return l.prototype=d,n(f,"constructor",d),n(d,"constructor",l),l.displayName="GeneratorFunction",n(d,a,"GeneratorFunction"),n(f),n(f,a,"Generator"),n(f,r,(function(){return this})),n(f,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:s,m:g}})()}function n(e,o,t,i){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}n=function(e,o,t,i){if(o)r?r(e,o,{value:t,enumerable:!i,configurable:!i,writable:!i}):e[o]=t;else{var a=function(o,t){n(e,o,(function(e){return this._invoke(o,t,e)}))};a("next",0),a("throw",1),a("return",2)}},n(e,o,t,i)}function o(e,n,o,t,i,r,a){try{var s=e[r](a),c=s.value}catch(e){return void o(e)}s.done?n(c):Promise.resolve(c).then(t,i)}function t(e){return function(){var n=this,t=arguments;return new Promise((function(i,r){var a=e.apply(n,t);function s(e){o(a,i,r,s,c,"next",e)}function c(e){o(a,i,r,s,c,"throw",e)}s(void 0)}))}}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}System.register(["./index-legacy.4bb28e53.js"],(function(n,o){"use strict";var r,a,s,c,u,l,d,p,f,g,v,m,h,I=document.createElement("style");return I.textContent=".premises-page{height:100%;background-color:#fff;background-size:cover}.ui-logo{padding-top:10px}.ui-logo img{width:140px;margin:0 auto}.ui-logo p{text-align:center;font-size:30px;font-family:PingFang SC,PingFang SC-Regular;line-height:42px;margin-top:30px;color:@title-color}.premises-tip{line-height:33px;text-align:center;margin-left:38px}.bind-title{padding:20px 60px 30px;color:#fff;font-size:30px;line-height:42px;text-align:center}.submit-btn-wrapper{padding:80px 60px 30px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}\n",document.head.appendChild(I),{setters:[function(e){r=e._,a=e.u,s=e.r,c=e.N,u=e.h,l=e.o,d=e.d,p=e.e,f=e.k,g=e.t,v=e.j,m=e.w,h=e.f}],execute:function(){var o,I="function"==typeof Buffer,b="function"==typeof TextDecoder?new TextDecoder:void 0,k=("function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),A=(o={},k.forEach((function(e,n){return o[e]=n})),o),P=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,w=String.fromCharCode.bind(String),y="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(e){return new Uint8Array(Array.prototype.slice.call(e,0))},S=function(e){return e.replace(/[^A-Za-z0-9\+\/]/g,"")},C=function(e){for(var n,o,t,i,r="",a=e.length%3,s=0;s<e.length;){if((o=e.charCodeAt(s++))>255||(t=e.charCodeAt(s++))>255||(i=e.charCodeAt(s++))>255)throw new TypeError("invalid character found");r+=k[(n=o<<16|t<<8|i)>>18&63]+k[n>>12&63]+k[n>>6&63]+k[63&n]}return a?r.slice(0,a-3)+"===".substring(a):r},T=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,x=function(e){switch(e.length){case 4:var n=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return w((n>>>10)+55296)+w(56320+(1023&n));case 3:return w((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return w((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},R=function(e){return e.replace(T,x)},O=function(e){if(e=e.replace(/\s+/g,""),!P.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));for(var n,o,t,i="",r=0;r<e.length;)n=A[e.charAt(r++)]<<18|A[e.charAt(r++)]<<12|(o=A[e.charAt(r++)])<<6|(t=A[e.charAt(r++)]),i+=64===o?w(n>>16&255):64===t?w(n>>16&255,n>>8&255):w(n>>16&255,n>>8&255,255&n);return i},E="function"==typeof atob?function(e){return atob(S(e))}:I?function(e){return Buffer.from(e,"base64").toString("binary")}:O,D=I?function(e){return y(Buffer.from(e,"base64"))}:function(e){return y(E(e).split("").map((function(e){return e.charCodeAt(0)})))},N=I?function(e){return Buffer.from(e,"base64").toString("utf8")}:b?function(e){return b.decode(D(e))}:function(e){return R(E(e))},B=function(e){return S(e.replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})))},_=function(e){return N(B(e))},M=_;function L(){return L=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var o=arguments[n];for(var t in o)({}).hasOwnProperty.call(o,t)&&(e[t]=o[t])}return e},L.apply(null,arguments)}function F(e,n,o,t,i,r,a){try{var s=e[r](a),c=s.value}catch(e){return void o(e)}s.done?n(c):Promise.resolve(c).then(t,i)}function j(e){return function(){var n=this,o=arguments;return new Promise((function(t,i){var r=e.apply(n,o);function a(e){F(r,t,i,a,s,"next",e)}function s(e){F(r,t,i,a,s,"throw",e)}a(void 0)}))}}var z={exports:{}},W={exports:{}};!function(e){e.exports=function(e,n){this.v=e,this.k=n},e.exports.__esModule=!0,e.exports.default=e.exports}(W);var H={exports:{}},U={exports:{}};!function(e){function n(o,t,i,r){var a=Object.defineProperty;try{a({},"",{})}catch(o){a=0}e.exports=n=function(e,o,t,i){if(o)a?a(e,o,{value:t,enumerable:!i,configurable:!i,writable:!i}):e[o]=t;else{var r=function(o,t){n(e,o,(function(e){return this._invoke(o,t,e)}))};r("next",0),r("throw",1),r("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,n(o,t,i,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(U),function(e){var n=U.exports;function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,i,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",s=r.toStringTag||"@@toStringTag";function c(e,o,r,a){var s=o&&o.prototype instanceof l?o:l,c=Object.create(s.prototype);return n(c,"_invoke",function(e,n,o){var r,a,s,c=0,l=o||[],d=!1,p={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,n){return r=e,a=0,s=t,p.n=n,u}};function f(e,n){for(a=e,s=n,i=0;!d&&c&&!o&&i<l.length;i++){var o,r=l[i],f=p.p,g=r[2];e>3?(o=g===n)&&(s=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=t):r[0]<=f&&((o=e<2&&f<r[1])?(a=0,p.v=n,p.n=r[1]):f<g&&(o=e<3||r[0]>n||n>g)&&(r[4]=e,r[5]=n,p.n=g,a=0))}if(o||e>1)return u;throw d=!0,n}return function(o,l,g){if(c>1)throw TypeError("Generator is already running");for(d&&1===l&&f(l,g),a=l,s=g;(i=a<2?t:s)||!d;){r||(a?a<3?(a>1&&(p.n=-1),f(a,s)):p.n=s:p.v=s);try{if(c=2,r){if(a||(o="next"),i=r[o]){if(!(i=i.call(r,s)))throw TypeError("iterator result is not an object");if(!i.done)return i;s=i.value,a<2&&(a=0)}else 1===a&&(i=r.return)&&i.call(r),a<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),a=1);r=t}else if((i=(d=p.n<0)?s:e.call(n,p))!==u)break}catch(i){r=t,a=1,s=i}finally{c=1}}return{value:i,done:d}}}(e,r,a),!0),c}var u={};function l(){}function d(){}function p(){}i=Object.getPrototypeOf;var f=[][a]?i(i([][a]())):(n(i={},a,(function(){return this})),i),g=p.prototype=l.prototype=Object.create(f);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,n(e,s,"GeneratorFunction")),e.prototype=Object.create(g),e}return d.prototype=p,n(g,"constructor",p),n(p,"constructor",d),d.displayName="GeneratorFunction",n(p,s,"GeneratorFunction"),n(g),n(g,s,"Generator"),n(g,a,(function(){return this})),n(g,"toString",(function(){return"[object Generator]"})),(e.exports=o=function(){return{w:c,m:v}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports}(H);var V={exports:{}},G={exports:{}},J={exports:{}};!function(e){var n=W.exports,o=U.exports;e.exports=function e(t,i){function r(e,o,a,s){try{var c=t[e](o),u=c.value;return u instanceof n?i.resolve(u.v).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):i.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return r("throw",e,a,s)}))}catch(t){s(t)}}var a;this.next||(o(e.prototype),o(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",(function(){return this}))),o(this,"_invoke",(function(e,n,o){function t(){return new i((function(n,t){r(e,o,n,t)}))}return a=a?a.then(t,t):t()}),!0)},e.exports.__esModule=!0,e.exports.default=e.exports}(J),function(e){var n=H.exports,o=J.exports;e.exports=function(e,t,i,r,a){return new o(n().w(e,t,i,r),a||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports}(G),function(e){var n=G.exports;e.exports=function(e,o,t,i,r){var a=n(e,o,t,i,r);return a.next().then((function(e){return e.done?e.value:a.next()}))},e.exports.__esModule=!0,e.exports.default=e.exports}(V);var K={exports:{}};!function(e){e.exports=function(e){var n=Object(e),o=[];for(var t in n)o.unshift(t);return function e(){for(;o.length;)if((t=o.pop())in n)return e.value=t,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports}(K);var q={exports:{}},Q={exports:{}};!function(e){function n(o){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(o)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(Q),function(e){var n=Q.exports.default;e.exports=function(e){if(null!=e){var o=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],t=0;if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&t>=e.length&&(e=void 0),{value:e&&e[t++],done:!e}}}}throw new TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports}(q),function(e){var n=W.exports,o=H.exports,t=V.exports,i=G.exports,r=J.exports,a=K.exports,s=q.exports;function c(){var u=o(),l=u.m(c),d=(Object.getPrototypeOf?Object.getPrototypeOf(l):l.__proto__).constructor;function p(e){var n="function"==typeof e&&e.constructor;return!!n&&(n===d||"GeneratorFunction"===(n.displayName||n.name))}var f={throw:1,return:2,break:3,continue:3};function g(e){var n,o;return function(t){n||(n={stop:function(){return o(t.a,2)},catch:function(){return t.v},abrupt:function(e,n){return o(t.a,f[e],n)},delegateYield:function(e,i,r){return n.resultName=i,o(t.d,s(e),r)},finish:function(e){return o(t.f,e)}},o=function(e,o,i){t.p=n.prev,t.n=n.next;try{return e(o,i)}finally{n.next=t.n}}),n.resultName&&(n[n.resultName]=t.v,n.resultName=void 0),n.sent=t.v,n.next=t.n;try{return e.call(this,n)}finally{t.p=n.prev,t.n=n.next}}}return(e.exports=c=function(){return{wrap:function(e,n,o,t){return u.w(g(e),n,o,t&&t.reverse())},isGeneratorFunction:p,mark:u.m,awrap:function(e,o){return new n(e,o)},AsyncIterator:r,async:function(e,n,o,r,a){return(p(n)?i:t)(g(e),n,o,r,a)},keys:a,values:s}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=c,e.exports.__esModule=!0,e.exports.default=e.exports}(z);var Y,Z,X,$,ee,ne,oe=z.exports(),te=oe;try{regeneratorRuntime=oe}catch(Mi){"object"===("undefined"==typeof globalThis?"undefined":i(globalThis))?globalThis.regeneratorRuntime=oe:Function("r","regeneratorRuntime = r")(oe)}function ie(e){return"undefined"===e}function re(){return!ie("undefined"==typeof my?"undefined":i(my))&&null!==my&&!ie(i(my.alert))}(Z=Y||(Y={})).CANCEL="-1",Z.SUCCESS="0",Z.API_UNDEFINED="1",Z.INVALID_PARAMS="2",Z.UNKNOWN_ERROR="3",Z.UNAUTHORIZED_CALL="4",Z.WRONG_CORP_ID="5",Z.CREATE_CHAT_FAILED="6",Z.UNAUTHORIZED_API="7",Z.INVALID_CORP_ID="8",Z.SERVER_RESPONSE_ERROR="9",Z.WRONG_DEVICE_INFO="10",Z.UPLOAD_FAIL="11",Z.PROCESS_FAIL="12",Z.DUPLICATED_CALL="13",Z.TOO_LARGE_PIC="14",Z.REQUEST_REJECT_OR_INSECURE_REQUEST="15",Z.PC_NOT_ALLOWED_TO_OPEN_SIDE_PANE_OR_MODAL="21",Z.PC_CLOSE_SIDE_PANE_OR_MODAL="22",Z.UNAUTHORIZED_PARAMS="23",Z.GESTURE_PASSWORD_DOES_NOT_EXIST="24",Z.NETWORK_ERROR="25",function(e){e.MOBILE="mobile",e.PC="pc",e.MINI_APP="mini",e.UNKNOWN="unknown"}(X||(X={})),function(e){e.ANDROID="android",e.IOS="ios",e.UNKNOW="unknow"}($||($={})),function(e){e.UPDATE_NETWORK_STATUS="DINGGOV_ON_NETWORK_TYPE_CHANGED",e.UPDATE_LOCATION="DINGGOV_GEO_LOCATION_UPDATE",e.UPDATE_TRACE="DINGGOV_TRACE_UPDATE",e.ON_SHAKE="onShake"}(ee||(ee={})),function(e){e.isDingTalk="DingTalk",e.isMpaas="mPaaS",e.isUnknow="unknow"}(ne||(ne={}));var ae=navigator&&(navigator.swuserAgent||navigator.userAgent)||"",se=function(){function e(){this.readyFnStack=[],this.generalEventCallbackStack={},this.apiList={},this.continuousCallbackStack={},this.isH5Mobile=null,this.appType=null,this.platformType=null,this.aliBridge=window&&window.navigator&&window.AlipayJSBridge,this.isReady=!1,this.init(),console.warn("请将 gdt-jsapi 版本请升级到 1.9.24 版本以上的最新版本，谢谢")}var n=e.prototype;return n.h5AndroidbridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(n,o){var t=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),e.execReadyFn()}catch(e){}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?t():(document.addEventListener("runtimeready",(function(){t()}),!1),document.addEventListener("runtimefailed",(function(t){var i=t&&t.detail||{errorCode:Y.INVALID_PARAMS,errorMessage:"unknown nuvajs bootstrap error"};e.handleBridgeResponse(i,n,o)}),!1))}))},n.h5IosBridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(n,o){if("undefined"!=typeof WebViewJavascriptBridge)try{WebViewJavascriptBridge.init((function(e,n){})),e.execReadyFn()}catch(e){}else document.addEventListener("WebViewJavascriptBridgeReady",(function(){try{WebViewJavascriptBridge&&WebViewJavascriptBridge.init((function(e,n){})),e.execReadyFn()}catch(e){}}),!1)}))},n.init=function(){var e=this,n=this.getAppType(),o=this.getContainerType();if(n===X.PC&&window.dingtalk&&!window.dingtalk.isRegister&&(window.dingtalk.isRegister=!0,window.dingtalk.callbackStack={},window.dingtalk.event.register((function(n,o){if(e.continuousCallbackStack[n])e.continuousCallbackStack[n](o);else if(o){var t=""+o.msgId;"openapi.event.emit"===n?(console.log("dingtalk receive event:",o,"identifer is",t),window.dingtalk.callbackStack[t]&&(window.dingtalk.callbackStack[t](o),delete window.dingtalk.callbackStack[t])):"im.fileTask.addNewTask"===n||"im.fileTask.updateTask"===n?(o.msgId||o.taskId)&&"function"==typeof e.continuousCallbackStack[o.msgId||o.taskId]&&e.continuousCallbackStack[o.msgId||o.taskId](n,o):e.generalEventCallbackStack[n]&&e.generalEventCallbackStack[n].forEach((function(n){n.call(e,o)}))}}))),n===X.MOBILE){if(o===ne.isDingTalk)this.platformType===$.ANDROID?!this.h5BridgeReadyPromise&&this.h5AndroidbridgeInit():this.platformType===$.IOS&&!this.h5BridgeReadyPromise&&this.h5IosBridgeInit();else if(o===ne.isMpaas&&n===X.MOBILE)if(window.AlipayJSBridge)this.execReadyFn();else{var t=setTimeout((function(){console.warn("window.AlipayJSBridge 未初始化完毕，走到兜底逻辑",e.isReady,window.AlipayJSBridge),e.isReady||e.execReadyFn.call(e)}),5200);document.addEventListener("AlipayJSBridgeReady",(function(){e.isReady||(clearTimeout(t),e.execReadyFn.call(e))}),!1)}}else setTimeout((function(){e.execReadyFn()}))},n.execReadyFn=function(){this.isReady=!0;for(var e=this.readyFnStack.shift();e;)e&&e(this),e=this.readyFnStack.shift()},n.onReady=function(e){this.isReady?e&&e(this):this.readyFnStack.push(e)},n.setCurrentInvoker=function(e){this.currentInvoker=e},n.getCurrentInvoker=function(){return this.currentInvoker},n.getBridge=function(){return this.aliBridge},n.getContainerType=function(){return/TaurusApp/g.test(ae)?/DingTalk/g.test(ae)?ne.isDingTalk:ne.isMpaas:/DingTalk/g.test(ae)?ne.isDingTalk:/mPaaSClient/g.test(ae)||/Nebula/g.test(ae)?ne.isMpaas:ne.isUnknow},n.getAppType=function(){return this.appType||(this.isMobile()?this.appType=X.MOBILE:window&&window.navigator&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("dingtalk-win")>=0&&window.navigator.userAgent.indexOf("TaurusApp")>=0?this.appType=X.PC:re()?this.appType=X.MINI_APP:(console.warn("检测到页面在非专有钉钉客户端中打开，JSAPI 调用可能不会生效！"),this.appType=X.UNKNOWN)),this.appType},n.isMobile=function(){var e=/iPhone|iPad|iPod|iOS/i.test(ae),n=/Android/i.test(ae),o=window&&window.navigator&&window.navigator.userAgent||"";return null!==this.isH5Mobile?this.isH5Mobile:o&&o.indexOf("dingtalk-win")>=0?(this.isH5Mobile=!1,!1):!(!o||!(o.includes("mPaaSClient")||o.includes("Nebula")||o.includes("DingTalk"))||(this.isH5Mobile=!0,this.platformType=e?$.IOS:n?$.ANDROID:$.UNKNOW,0))},n.registerEvent=function(e,n){var o=this;if("function"==typeof n)return this.getAppType()===X.PC?(this.generalEventCallbackStack[e]||(this.generalEventCallbackStack[e]=[]),this.generalEventCallbackStack[e].push(n),function(){var t=o.generalEventCallbackStack[e].findIndex((function(e){return e===n}));o.generalEventCallbackStack[e].splice(t,1)}):this.getAppType()===X.MOBILE?(document.addEventListener(e,n,!1),function(){document.removeEventListener(e,n)}):void 0;console.error("callback 参数应该为函数")},n.registerClientAPI=function(e,n){this.apiList[e]=n},n.registerAPI=function(e,n){if(this.isMobile(),"object"==i(n)){var o=n,t=this.getAppType();this.registerClientAPI(e,o[t])}else this.registerClientAPI(e,n)},n.invokeMiniApp=function(){var e=j(te.mark((function e(n,o){var t=this;return te.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o={}),e.abrupt("return",new Promise((function(e,i){o=L({_apiName:n},o);var r=t.apiList[n],a=t.getContainerType();if(!r)return console.warn("API: "+n+"，未注册"),i("API: "+n+"，未注册");if(a===ne.isMpaas){if("function"==typeof r)return void r.call(null,o,{context:my,resolve:e,reject:i,methodName:n});my.call(n,o,(function(n){t.handleBridgeResponse(n,e,i)}))}else if(a===ne.isDingTalk){if("function"==typeof r)return void r.call(null,o,{context:dd.dtBridge,resolve:e,reject:i,methodName:n,containerType:a,appType:X.MINI_APP});dd.dtBridge({m:"taurus.common."+n,args:o,onSuccess:function(n){t.handleBridgeResponse(n,e,i)},onFail:function(n){t.handleBridgeResponse(n,e,i)}})}})));case 2:case"end":return e.stop()}}),e)})));return function(n,o){return e.apply(this,arguments)}}(),n.invokeMobile=function(){var e=j(te.mark((function e(n,o,t){var i=this;return te.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o={}),e.abrupt("return",new Promise((function(e,r){o=L({_apiName:n},o);var a=i.apiList[n],s=i.getContainerType();if(!a)return console.warn("API: "+n+"，未注册"),r("API: "+n+"，未注册");if(s===ne.isDingTalk){if(i.platformType===$.IOS){var c=Object.assign({},o);if(!0===c.watch&&"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(null!=t&&t.dingTalkAPIName?null==t?void 0:t.dingTalkAPIName:"taurus.common."+n,(function(e,n){"function"==typeof o.onSuccess&&o.onSuccess.call(null,e),n&&n({errorCode:"0",errorMessage:"success"})})),"function"==typeof a)return void a.call(null,o,{context:window.WebViewJavascriptBridge,resolve:e,reject:r,methodName:n,containerType:s,appType:X.MOBILE,platformType:$.IOS,watch:c.watch});void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("taurus.common."+n,Object.assign({},c),(function(n){!c.watch&&i.handleBridgeResponse(n||{},e,r)}))}else if(i.platformType===$.ANDROID){var u=n.split("."),l=u.pop()||"",d=u.join(".")||"taurus.common";if("function"==typeof a)return void a.call(null,o,{context:window.WebViewJavascriptBridgeAndroid,resolve:e,reject:r,methodName:n,containerType:s,appType:X.MOBILE,platformType:$.ANDROID});"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid((function(n){i.handleBridgeResponse(n,e,r)}),(function(n){i.handleBridgeResponse(n,e,r)}),d,l,o)}}else if(s===ne.isMpaas){if("function"==typeof a)return void a.call(null,o,{context:AlipayJSBridge,resolve:e,reject:r,methodName:n});AlipayJSBridge.call(n,o,(function(n){i.handleBridgeResponse(n,e,r)}))}})));case 2:case"end":return e.stop()}}),e)})));return function(n,o,t){return e.apply(this,arguments)}}(),n.findFitMsgId=function(e){var n,o;return null!==(n=window.dingtalk)&&void 0!==n&&null!==(o=n.callbackStack)&&void 0!==o&&o[e]?this.findFitMsgId(e+1):e},n.invokePC=function(){var e=j(te.mark((function e(n,o,t){var i=this;return te.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o={}),void 0===t&&(t={msgId:1}),e.abrupt("return",new Promise((function(e,r){try{o=L({_apiName:n},o);var a=i.findFitMsgId(Date.now()),s=t.pcClientAPIName||n;if(t.msgId=a,!window.dingtalk)return Promise.reject(new Error("请在钉钉容器内使用 JSAPI"));i.apiList[n]?i.apiList[n].call(null,o,t):(console.info("invoke bridge api:",s,a,o),window.dingtalk.platform.invokeAPI(a,s,o)),window.dingtalk&&window.dingtalk.isRegister&&!window.dingtalk.callbackStack&&(window.dingtalk.callbackStack={}),window.dingtalk.callbackStack[""+a]=function(n){var o=n;return o.body?e(o.body):e(o)}}catch(e){r(e)}})));case 3:case"end":return e.stop()}}),e)})));return function(n,o,t){return e.apply(this,arguments)}}(),n.handleBridgeResponse=function(e,n,o){e&&e.errorCode?e.errorCode===Y.SUCCESS?n(e.result):(console.warn("API 调用失败",e),o(e)):e&&"false"===e.success?o(e):n(e)},n.invoke=function(){var e=j(te.mark((function e(n,o,t){var i;return te.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===o&&(o={}),(i=this.getAppType())!==X.MOBILE){e.next=8;break}if(this.isReady){e.next=5;break}return e.abrupt("return",Promise.reject("错误：请在 dd.ready() 回调中使用 JSAPI，当前调用函数："+n));case 5:return e.abrupt("return",this.invokeMobile(n,o,t));case 8:if(i!==X.PC){e.next=12;break}return e.abrupt("return",this.invokePC(n,o,t));case 12:if(i!==X.MINI_APP){e.next=16;break}return e.abrupt("return",this.invokeMiniApp(n,o));case 16:return e.abrupt("return",Promise.reject("错误：未在钉钉运行环境下调用该 API，无效，请检查运行环境"));case 17:case"end":return e.stop()}}),e,this)})));return function(n,o,t){return e.apply(this,arguments)}}(),n.existEventListener=function(e){return!!this.continuousCallbackStack[e]},n.registerContinuesEvent=function(e,n){this.continuousCallbackStack[e]=n},n.removeContinuesEvent=function(e){this.existEventListener(e)&&(this.continuousCallbackStack[e](),delete this.continuousCallbackStack[e])},e}();re()||(window._invoker=window._invoker||new se);var ce=re()?new se:window._invoker;function ue(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.alert",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"taurus.common","alert",e):s===$.IOS&&i.callHandler("taurus.common.alert",Object.assign({},e),(function(e){o(e)}))}else i&&i.call("alert",e,(function(){o()}))}function le(e){return ce.invoke("alert",e)}function de(e){return ce.invoke("authConfig",e)}function pe(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.bizContactDepartmentsPickerExternal",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"taurus.common","bizContactDepartmentsPickerExternal",e):s===$.IOS&&i.callHandler("taurus.common.bizContactDepartmentsPickerExternal",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("bizContactDepartmentsPickerExternal",e,(function(e){ce.handleBridgeResponse(e,o,t)}))}function fe(e){return ce.invoke("bizContactDepartmentsPickerExternal",e)}function ge(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.bizCustomContactChooseExternal",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"taurus.common","bizCustomContactChooseExternal",e):s===$.IOS&&i.callHandler("taurus.common.bizCustomContactChooseExternal",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("bizCustomContactChooseExternal",e,(function(e){ce.handleBridgeResponse(e,o,t)}))}function ve(e){return ce.invoke("bizCustomContactChooseExternal",e)}function me(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.bizCustomContactMultipleChooseExternal",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"taurus.common","bizCustomContactMultipleChooseExternal",e):s===$.IOS&&i.callHandler("taurus.common.bizCustomContactMultipleChooseExternal",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("bizCustomContactMultipleChooseExternal",e,(function(e){ce.handleBridgeResponse(e,o,t)}))}function he(e){return ce.invoke("bizCustomContactMultipleChooseExternal",e)}function Ie(e){return ce.invoke("callPhone",e)}function be(){return ce.invoke("version")}ce.registerAPI("alert",{mini:ue,mobile:ue}),le.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("authConfig",{mini:!0,mobile:!0}),de.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},ce.registerAPI("bizContactDepartmentsPickerExternal",{mini:pe,mobile:pe,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPickerEx",e)}}),fe.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"},ce.registerAPI("bizCustomContactChooseExternal",{mini:ge,mobile:ge,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.customContact.chooseEx",e)}}),ve.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"},ce.registerAPI("bizCustomContactMultipleChooseExternal",{mini:me,mobile:me,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.customContact.multipleChooseEx",e)}}),he.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"},ce.registerAPI("callPhone",{mini:!0,mobile:!0}),Ie.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("version",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"version",{})}});function ke(e,n){return"number"!=typeof e&&(e=0),"number"!=typeof n&&(n=0),e>n?1:e<n?-1:0}function Ae(e,n){void 0===e&&(e=""),void 0===n&&(n="");var o=/^\d+(\.\d+){2,3}$/;if(!o.test(e)||!o.test(n))throw new Error("请传入正确的版本号格式");for(var t=(""+e).split(".").map((function(e){return parseInt(e,10)})),i=(""+n).split(".").map((function(e){return parseInt(e,10)})),r=Math.max(t.length,i.length),a=0,s=0;s<r&&0===(a=ke(t[s],i[s]));s++);return a}var Pe,we,ye,Se,Ce,Te,xe,Re=navigator&&navigator.userAgent||"";function Oe(){return(Oe=j(te.mark((function e(n){var o,t,i,r;return te.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(xi[n]){e.next=2;break}return e.abrupt("return",!1);case 2:return e.next=4,be();case 4:return o=e.sent,t=o.version,i=xi[n].version,r=Re.indexOf("Android")>-1||Re.indexOf("Adr")>-1?"android":Re.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)?"ios":/(windows)/i.test(navigator.userAgent)?"pc":"unknown",e.abrupt("return",!(!i||!i[r])&&Ae(t,i[r])>0);case 9:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Ee(){return ce.invoke("checkVPNAppInstalled")}function De(){return ce.invoke("checkVPNAppOnline")}function Ne(e){return ce.invoke("chooseContact",e)}function Be(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.chooseContactWithComplexPicker",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"taurus.common","chooseContactWithComplexPicker",e):s===$.IOS&&i.callHandler("taurus.common.chooseContactWithComplexPicker",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("chooseContactWithComplexPicker",e,(function(n){n.error&&n.error.toString()===Y.API_UNDEFINED?i.call("complexPicker",e,(function(e){ce.handleBridgeResponse(e,o,t)})):ce.handleBridgeResponse(n,o,t)}))}function _e(e){return ce.invoke("chooseContactWithComplexPicker",e)}function Me(e){return ce.invoke("chooseDateRangeWithCalendar",e)}function Le(e){return ce.invoke("chooseDayWithCalendar",e)}function Fe(e){return ce.invoke("chooseDepartments",e)}function je(e){return ce.invoke("chooseFile",e)}function ze(e){return ce.invoke("chooseHalfDayWithCalendar",e)}function We(e){return ce.invoke("dgChooseImage",L({},e,{_apiName:"chooseImage"}))}function He(e){return ce.invoke("chooseInterconnectionChat",e)}function Ue(e){return new Promise((function(n,o){my.chooseImage(L({},e,{success:function(e){n(e)},fail:function(e){o(e)}}))}))}function Ve(){return ce.invoke("chooseSpaceDir")}function Ge(e){return ce.invoke("chooseTimeWithCalendar",e)}function Je(e){return ce.invoke("chooseVideo",e)}function Ke(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"biz.navigation.close",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"biz.navigation","close",e):s===$.IOS&&i.callHandler("biz.navigation.close",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("popWindow",e,(function(e){ce.handleBridgeResponse(e,o,t)}))}function qe(e){return ce.invoke("closePage",L({},e,{_apiName:"closePage"}))}function Qe(e){return ce.invoke("complexPickerAdmin",e)}function Ye(e){return ce.invoke("confirm",e)}function Ze(e){return ce.invoke("copyToClipboard",e)}function Xe(e){return ce.invoke("createChatGroup",e)}function $e(e){return ce.invoke("createDing",e)}function en(e){return ce.invoke("createDingV2",e)}function nn(e,n){void 0===e&&(e={});var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"biz.conference.createVideoConf",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"biz.conference","createVideoConf",e):s===$.IOS&&i.callHandler("biz.conference.createVideoConf",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("createVideoConf",e,(function(){o()}))}function on(e){return ce.invoke("createVideoConf",e)}function tn(e){return ce.invoke("createVideoMeeting",e)}function rn(e){return ce.invoke("dealWithBackAction",e)}function an(){return ce.invoke("disableClosePage")}function sn(){return ce.invoke("disablePullToRefresh",{_apiName:"disablePullToRefresh"})}function cn(){return ce.invoke("disableWebviewBounce",{_apiName:"disableWebviewBounce"})}function un(e){return ce.invoke("downloadAudio",e)}ce.registerAPI("checkVPNAppInstalled",{mini:!0,mobile:!0}),Ee.version={android:"1.6.0",ios:"1.6.0"},ce.registerAPI("checkVPNAppOnline",{mini:!0,mobile:!0}),De.version={android:"1.6.0",ios:"1.6.0"},(we=Pe||(Pe={}))[we.DEFAULT=1]="DEFAULT",we[we.NEW=2]="NEW",function(e){e[e.GLOBAL_ORG=1]="GLOBAL_ORG",e[e.FRIEND=2]="FRIEND",e[e.GROUP=4]="GROUP",e[e.RECOMMEND=5]="RECOMMEND",e[e.SPECIAL_ATTENTION=7]="SPECIAL_ATTENTION",e[e.LOAD_GROUP_PERSON=8]="LOAD_GROUP_PERSON",e[e.ORG=9]="ORG"}(ye||(ye={})),function(e){e.PHONE_HIDE="PHONE_HIDE",e.CHAT_INVALID="CHAT_INVALID",e.GROUP_CHAT_PULL_INVALID="GROUP_CHAT_PULL_INVALID",e.APP_DING_INVALID="APP_DING_INVALID",e.PHONE_DING_INVALID="PHONE_DING_INVALID",e.SMS_DING_INVALID="SMS_DING_INVALID",e.AUDIO_VIDEO_HIDE="AUDIO_VIDEO_HIDE"}(Se||(Se={})),ce.registerAPI("chooseContact",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.choose",e)}}),Ne.version={pc:"1.1.0"},ce.registerAPI("chooseContactWithComplexPicker",{mini:Be,mobile:Be,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPicker",e)}}),_e.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"},ce.registerAPI("chooseDateRangeWithCalendar",{mini:!0,mobile:!0}),Me.version={android:"1.3.10",ios:"1.3.10"},ce.registerAPI("chooseDayWithCalendar",{mini:!0,mobile:!0}),Le.version={android:"1.3.10",ios:"1.3.10"},ce.registerAPI("chooseDepartments",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPicker",e)}}),Fe.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"},ce.registerAPI("chooseFile",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseFile",e)}}),je.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},ce.registerAPI("chooseHalfDayWithCalendar",{mini:!0,mobile:!0}),ze.version={android:"1.3.10",ios:"1.3.10"},function(e){e[e.image=0]="image",e[e.video=1]="video"}(Ce||(Ce={})),ce.registerAPI("dgChooseImage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgChooseImage",e)}}),We.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},ce.registerAPI("chooseInterconnectionChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"chooseInterconnectionChat",e)}}),He.version={pc:"2.9.0",ios:"2.9.0",android:"2.9.0"},ce.registerAPI("chooseImage",{mini:!0}),Ue.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("chooseSpaceDir",{mini:!0,mobile:!0,pc:function(e,n){void 0===e&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseSpaceDir",e)}}),Ve.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"},ce.registerAPI("chooseTimeWithCalendar",{mini:!0,mobile:!0}),Ge.version={android:"1.3.10",ios:"1.3.10"},ce.registerAPI("chooseVideo",{mini:!0,mobile:!0}),Je.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("closePage",{mini:Ke,mobile:Ke,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.quit",e)}}),qe.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},function(e){e.CODE="code",e.ACCOUNTID="accountId"}(Te||(Te={})),function(e){e.CODE="code",e.id="id"}(xe||(xe={})),ce.registerAPI("complexPickerAdmin",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPickerAdmin",e)}}),Qe.version={pc:"2.8.0"},ce.registerAPI("confirm",{mini:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};r===ne.isDingTalk?i({m:"taurus.common.confirm",args:a,onSuccess:function(e){var n={errorCode:Y.SUCCESS,result:{buttonIndex:e.ok?0:1}};ce.handleBridgeResponse(n,o,t)},onFail:function(e){ce.handleBridgeResponse(e,o,t)}}):i&&i.call("confirm",a,(function(e){var n={errorCode:Y.SUCCESS,result:{buttonIndex:e.ok?0:1}};ce.handleBridgeResponse(n,o,t)}))},mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType,s={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};r?a===$.ANDROID?i&&i((function(e){var n={errorCode:Y.SUCCESS,result:{buttonIndex:e.ok?0:1}};ce.handleBridgeResponse(n,o,t)}),(function(e){ce.handleBridgeResponse(e,o,t)}),"taurus.common","confirm",s):a===$.IOS&&i.callHandler("taurus.common.confirm",Object.assign({},s),(function(e){ce.handleBridgeResponse(e,o,t)})):i&&i.call("confirm",s,(function(e){var n={errorCode:Y.SUCCESS,result:{buttonIndex:e.ok?0:1}};ce.handleBridgeResponse(n,o,t)}))},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.confirm",e)}}),Ye.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},ce.registerAPI("copyToClipboard",{mini:!0,mobile:!0}),Ze.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("createChatGroup",{mini:!0,mobile:!0}),Xe.version={android:"1.3.0",ios:"1.3.0",pc:"1.3.0"},ce.registerAPI("createDing",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.create",e)}}),$e.version={android:"1.3.9",ios:"1.3.9",pc:"1.3.9"},ce.registerAPI("createDingV2",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.createV2",e)}}),en.version={android:"2.7.0",ios:"2.7.0",pc:"2.7.0"},ce.registerAPI("createVideoConf",{mini:nn,mobile:nn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.createVideoConf",L({},e))}}),on.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"},ce.registerAPI("createVideoMeeting",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.meeting.create",L({isVideoConference:!0},e))}}),tn.version={android:"*******",ios:"*******",pc:"1.9.4"},ce.registerAPI("dealWithBackAction",{mobile:!0}),rn.version={android:"1.2.0.10"},ce.registerAPI("disableClosePage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.disableClosePage",{})}}),an.version={pc:"3.4.0"},ce.registerAPI("disablePullToRefresh",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;r?a===$.ANDROID?i&&i((function(){o()}),(function(){t()}),"ui.pullToRefresh","disable",{}):a===$.IOS&&i.callHandler("ui.pullToRefresh.disable",Object.assign({},{}),(function(e){o(e)})):i&&i.call("pullRefresh",{pullRefresh:!1},(function(){o()}))}}),sn.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("disableWebviewBounce",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;r?a===$.ANDROID?i&&i((function(){o()}),(function(){t()}),"ui.webViewBounce","disable",{}):a===$.IOS&&i.callHandler("ui.webViewBounce.disable",Object.assign({},{}),(function(e){o(e)})):i&&i.call("bounce",{enable:!1},(function(e){o(e)}))}}),cn.version={ios:"1.3.0"},ce.registerAPI("downloadAudio",{mini:!0,mobile:!0}),un.version={android:"1.3.0",ios:"1.3.0"};var ln,dn,pn;function fn(e){return ce.invoke("downloadFile",e)}function gn(){return ce.invoke("enablePullToRefresh",{_apiName:"enablePullToRefresh"})}function vn(){return ce.invoke("enableVpn")}function mn(){return ce.invoke("enableWebviewBounce",{_apiName:"enableWebviewBounce"})}function hn(e){return ce.invoke("exclusiveInvoke",e)}function In(e){return ce.invoke("faceComparison",e)}function bn(e){return ce.invoke("faceRecognition",e)}function kn(e){return ce.invoke("getAppInstallStatus",e)}function An(e){return ce.invoke("getAuthCode",e)}function Pn(){return ce.invoke("getConfig",{})}function wn(){return ce.getContainerType()}function yn(){return ce.invoke("getDeviceId",{})}function Sn(){return ce.invoke("getFromClipboard")}function Cn(e){return ce.invoke("getGeolocation",e)}function Tn(e){return ce.invoke("getGeolocationStatus",e)}function xn(){return ce.invoke("getHotspotInfo")}function Rn(){return ce.invoke("getLanguageSetting")}function On(){return ce.invoke("getLoginUser")}function En(){return ce.invoke("getNetworkType")}function Dn(){return ce.invoke("getPhoneInfo")}function Nn(){return ce.invoke("getProxyInfo",{})}function Bn(e){return ce.invoke("getStorageItem",e)}function _n(e){return ce.invoke("getTraceStatus",e)}function Mn(){return ce.invoke("getUUID")}ce.registerAPI("downloadFile",{mini:function(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.context;i===ne.isDingTalk?r&&r({m:"taurus.common.downloadFile",args:e,onSuccess:function(e){ce.handleBridgeResponse(e,o,t)},onFail:function(e){ce.handleBridgeResponse(e,o,t)}}):r&&r.call("downloadFile",e,(function(e){e.error?t(e):o(e)}))},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.downloadFile",e),ce.registerContinuesEvent(n.msgId,(function(o,t){"im.fileTask.addNewTask"===o&&(ce.removeContinuesEvent(n.msgId),ce.registerContinuesEvent(t.taskId,(function(n,o){if("im.fileTask.updateTask"===n){var t=o.doneSize,i=o.fileName,r=o.filePath,a=o.fileSize,s=o.speed;e.onProgress({doneSize:t,fileName:i,filePath:r,fileSize:a,speed:s}),1===o.status&&ce.removeContinuesEvent(o.taskId)}})))}))}}),fn.version={pc:"1.3.5"},ce.registerAPI("enablePullToRefresh",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;r?a===$.ANDROID?i&&i((function(){o()}),(function(){t()}),"ui.pullToRefresh","enable",{}):a===$.IOS&&i.callHandler("ui.pullToRefresh.enable",Object.assign({},{}),(function(){o()})):i&&i.call("pullRefresh",{pullRefresh:!0},(function(){o()}))}}),gn.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("enableVpn",{mini:!0,mobile:!0}),vn.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("enableWebviewBounce",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;r?a===$.ANDROID?i&&i((function(){o()}),(function(){t()}),"taurus.common","bounce",{enable:!0}):a===$.IOS&&i.callHandler("taurus.common.bounce",Object.assign({},{enable:!0}),(function(e){o(e)})):i&&i.call("bounce",{enable:!0},(function(e){o(e)}))}}),mn.version={ios:"1.3.0"},ce.registerAPI("exclusiveInvoke",{mini:!0,mobile:!0}),hn.version={ios:"1.9.5",android:"1.9.5"},ce.registerAPI("faceComparison",{mobile:!0,mini:!0}),In.version={android:"2.4.0",ios:"2.4.0"},function(e){e.PNG="png",e.JPG="jpg"}(ln||(ln={})),ce.registerAPI("faceRecognition",{mobile:!0,mini:!0}),bn.version={android:"2.4.0",ios:"2.4.0"},ce.registerAPI("getAppInstallStatus",{mini:!0,mobile:!0}),kn.version={android:"2.1.10",ios:"2.1.10"},ce.registerAPI("getAuthCode",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"runtime.permission.requestAuthCode",e)},mobile:!0,mini:!0}),An.version={android:"1.0.0",ios:"1.0.0",pc:"1.0.0"},ce.registerAPI("getConfig",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getConfig",e)}}),Pn.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},ce.registerAPI("getDeviceId",{mobile:!0,mini:!0}),yn.version={android:"2.5.0",ios:"2.5.0"},ce.registerAPI("getFromClipboard",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"util.clipboardData.getData",e)}}),Sn.version={android:"2.3.1",ios:"2.3.1",pc:"2.6.10"},ce.registerAPI("getGeolocation",{mini:!0,mobile:!0}),Cn.version={android:"1.2.0",ios:"1.2.0"},ce.registerAPI("getGeolocationStatus",{mobile:!0,mini:!0}),Tn.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("getHotspotInfo",{mobile:!0,mini:!0}),xn.version={android:"1.3.5",ios:"1.3.5"},ce.registerAPI("getLanguageSetting",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getLanguageSetting",e)}}),Rn.version={android:"1.4.0",ios:"1.4.0",pc:"1.4.0"},ce.registerAPI("getLoginUser",{mobile:!0,mini:!0}),On.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("getNetworkType",{mobile:!0,mini:!0}),En.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("getPhoneInfo",{mini:!0,mobile:!0}),Dn.version={android:"1.3.5",ios:"1.3.5"},(pn=dn||(dn={})).SOCKS5="SOCKS5",pn.HTTP="HTTP",ce.registerAPI("getProxyInfo",{pc:function(e,n){void 0===e&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"net.util.getProxyInfo",e)}}),Nn.version={pc:"2.10.0"},ce.registerAPI("getStorageItem",{mobile:!0,mini:!0}),Bn.version={android:"*******",ios:"*******"},ce.registerAPI("getTraceStatus",{mobile:!0}),_n.version={android:"1.3.4",ios:"1.3.4"},ce.registerAPI("getUUID",{mobile:!0,mini:!0}),Mn.version={android:"1.3.5",ios:"1.3.5"};var Ln,Fn,jn,zn,Wn=/TaurusApp\((\S*)\/(\S*)\)/;function Hn(){var e=ce.getAppType();return e===X.PC||e===X.MOBILE?function(){if(window&&window.navigator){var e=window.navigator.userAgent;if(e){var n=e.match(Wn);return Promise.resolve({group:"TaurusApp",name:n[1],version:n[2]})}return Promise.reject("调用错误：无法检测到当下环境的 userAgent，请确保在政务钉钉客户端 H5 容器下调用。")}}():e===X.MINI_APP?ce.invoke("getUserAgent",{}):void 0}function Un(e){return ce.invoke("getWaterMarkConfig",e)}ce.registerAPI("getUserAgent",{mobile:!0,mini:!0,pc:!0}),Hn.version={android:"1.6.2",ios:"1.6.2",pc:"1.6.2"},(zn=Ln||(Ln={})).off="0",zn.on="1",function(e){e[e.off=0]="off",e[e.on=1]="on"}(Fn||(Fn={})),function(e){e[e.name=1]="name",e[e.id=2]="id",e[e.custom=3]="custom"}(jn||(jn={})),ce.registerAPI("getWaterMarkConfig",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfig",e)},mini:!0,mobile:!0});var Vn,Gn="h5Page",Jn=[Gn,"meetingDetail","docPreview"],Kn=!ie("undefined"==typeof my?"undefined":i(my))&&null!==my&&!ie(i(my.alert));Kn&&(Vn=my.getSystemInfoSync());var qn,Qn,Yn,Zn,Xn=Kn?Vn.platform:navigator.userAgent,$n=Kn?Vn.screenWidth:window.screen.width,eo=(Kn?Vn.pixelRatio:window.devicePixelRatio)||2,no=Kn?Promise.resolve(""):"",oo=function(){function e(e){void 0===e&&(e={}),this.options=L({texts:[""],width:50,height:50,textRotate:-10,textColor:"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"normal",opacity:90,canvas:[],fontSize:14},e),this.options.width*=this.options.fontSize/12,this.options.height*=this.options.fontSize/12,this.options.deg=this.options.textRotate*Math.PI/180,this.options.cosDeg=Math.cos(this.options.deg),this.options.absSinDeg=Math.abs(Math.sin(this.options.deg))}var n=e.prototype;return n.init=function(){var e=this,n=null,o=null;Kn?o=my.createCanvasContext("canvasBg"):(n=this.createCanvas(),o=n.getContext("2d")),this.calcTextSize();var t=this.options,i=t.allItemsWidth,r=t.drawItems,a=t.height,s=t.containerComp,c=Math.ceil($n/i),u=new Array(c).fill(r).reduce((function(e,n){return e.concat(n)}),[]),l=function(){e.setCanvasStyle(o),e.drawText(o,u),o.translate(0,a),e.drawText(o,u.reverse(),!0)};if(Kn)return new Promise((function(e){s.setState({width:i*c,height:2*a},(function(){setTimeout((function(){l(),o.draw(),e(o.toDataURL("image/png"))}),0)}))}));n.width=i*c,n.height=2*a,n.style.display="none",l();var d=n.toDataURL("image/png");return this.destroy(),d},n.calcTextSize=function(){var e=0,n=0,o=this.options;o.drawItems=[].map.call(o.texts,(function(t){var i,r,a,s;if(Kn){for(var c=0,u=0;u<t.length;u+=1)c+=/[\uff00-\uffff]/.test(t[u])?1:.5;i=1.1*o.fontSize*c,r=1.2*o.fontSize}else{var l=(a='<span style="font:'+o.fontSize+"px "+o.textFont+';visibility:hidden;">'+t+"</span>",(s=document.createElement("div")).innerHTML=a.trim(),s.firstChild);document.body.appendChild(l),i=l.offsetWidth,r=l.offsetHeight,document.body.removeChild(l)}return e=Math.max(e,i),o.fontHeight||(o.fontHeight=r),n+=Math.ceil(o.cosDeg*(o.width<i?i:o.width)),{txt:t,width:i,height:r}})),e>o.width&&(o.width=e);var t=e*o.absSinDeg+o.fontHeight*o.cosDeg;t>o.height&&(o.height=t),o.maxItemWidth=e,o.allItemsWidth=n},n.setCanvasStyle=function(e){var n=this.options,o=n.deg,t=n.absSinDeg,i=n.height,r=n.fontHeight,a=n.fontStyle,s=n.fontSize,c=n.textFont,u=n.textColor,l=n.opacity;e.rotate(o);var d=t*(i-r);e.translate(-d,0),e.font=a+" "+s+"px "+c,e.fillStyle=u,e.textAlign="left",e.textBaseline="bottom",e.globalAlpha=l},n.drawText=function(e,n,o){void 0===o&&(o=!1);var t=this.options,i=t.maxItemWidth,r=t.width,a=t.height,s=t.deg,c=t.cosDeg,u=t.absSinDeg;n.forEach((function(n,t){var l=c*(i-n.width)/2,d=r*c*t,p=Math.abs(d*Math.tan(s))+a;e.fillText(n.txt,d+(o?c*(r-n.width)/2:l),p+(o?u*(r-n.width)/2:0))}))},n.createCanvas=function(){var e=document.createElement("canvas");return this.options.canvas.push(e),e},n.destroy=function(){this.options.canvas.forEach((function(e){e.remove(),e=null}))},e}();function to(e,n){var o=JSON.parse(e),t=o.watermark||o;if(!t||"0"===String(t.watermarkStatus))return no;if(!Array.isArray(t.targetPages)||!t.targetPages.some((function(e){return e.name===n&&"1"===String(e.value)})))return no;var i=[];if(Array.isArray(t.contentType)){var r="";t.contentType.includes(1)&&(r+=t.userName+" "),t.contentType.includes(2)&&(r+=(t.account||"").slice(-4)),r&&i.push(r),t.contentType.includes(0)&&t.contentCustom&&i.push(t.contentCustom)}if(!i.length)return no;var a,s,c=/Android|Adr|SymbianOS|Windows\s*Phone|Mobile/.test(Xn),u=/iPhone|iPad|iPod|Mac\s*OS.*Mobile|iOS/.test(Xn),l="0"===String(t.watermarkShowDensity);return u?l?(a=114,s=66):(a=86,s=45):c?l?(a=47*eo,s=40*eo):(a=25*eo,s=25*eo):l?(a=300,s=126):(a=194,s=106),new oo({containerComp:this,texts:i,width:a,height:s,textRotate:-10,textColor:{0:"#FF0000",1:"#000000",2:"#0000FF"}[t.fontColor]||"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"0"===String(t.fontStyle)?"normal":"bold",opacity:(120-parseInt(t.fontDiaphaneity,10))/100,fontSize:{0:12,1:16,2:28}[t.fontSize]||16}).init()}function io(e,n){if(void 0===e&&(e={}),void 0===n&&(n=Gn),!Jn.includes(n))throw new Error("第二个可选参数，仅能为“h5Page”或“meetingDetail”");try{return to.call(this,JSON.stringify(e),n)}catch(e){throw e}}function ro(e,n){return void 0===e&&(e=""),new Promise((function(o,t){Un({pageInfo:e}).then((function(e){try{var i=io(e,n);o(i)}catch(e){t(e)}}))}))}function ao(e){return ce.invoke("getWaterMarkConfigV2",e)}ro.version={android:"1.1.0",ios:"1.1.0",pc:"1.1.0"},function(e){e[e.ENABLE=1]="ENABLE",e[e.DISABLE=0]="DISABLE"}(qn||(qn={})),ce.registerAPI("getWaterMarkConfigV2",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfigV2",e)}}),ao.version={android:"2.8.0",ios:"2.8.0",pc:"2.8.0"},(Yn=Qn||(Qn={}))[Yn.DISABLE=0]="DISABLE",Yn[Yn.ENABLE=1]="ENABLE",function(e){e.IMSESSIONLIST="imSessionList",e.DOCPREVIEW="docPreview",e.H5PAGEOTHER="h5PageOther",e.MEETINGDETAIL="meetingDetail",e.H5PAGEBASIC="h5PageBasic",e.SELECTIONCOMPONENT="selectionComponent",e.CONTACTLIST="contactList",e.CONTACTDETAIL="contactDetail",e.CHAT="chat",e.SECRETCHAT="secretChat",e.CAMERA="camera"}(Zn||(Zn={}));var so,co,uo={1:"normal",2:"bold",3:"italic"};!function(e){e[e.LOOSE=0]="LOOSE",e[e.NORMAL=1]="NORMAL",e[e.DENSE=2]="DENSE"}(so||(so={})),function(e){e[e.RIGHT=0]="RIGHT",e[e.LEFT=1]="LEFT"}(co||(co={}));var lo,po,fo,go=function(){function e(e){this.options=Object.assign({texts:"",width:50,height:50,tiltAngle:-15,fontColor:"#171A1D",textFont:"PingFangSC-Regular,system-ui,sans-serif",transparency:90,canvas:[],fontSize:13,tWidth:0,tHeight:0,deg:-15},e,{width:e.leftAndRightSpacing,height:e.upAndDownSpacing}),this.options.deg=this.options.tiltAngle*Math.PI/180}var n=e.prototype;return n.init=function(){var e,n,o,t,i,r,a,s,c,u=null;return c=(u=this.createCanvas()).getContext("2d"),u.width=(null===(e=window)||void 0===e||null===(n=e.screen)||void 0===n?void 0:n.width)||(null===(o=document)||void 0===o||null===(t=o.documentElement)||void 0===t?void 0:t.clientWidth)||749,u.height=(null===(i=window)||void 0===i||null===(r=i.screen)||void 0===r?void 0:r.height)||(null===(a=document)||void 0===a||null===(s=a.documentElement)||void 0===s?void 0:s.clientHeight)||326,this.calcTextSize(),this.setCanvasStyle(c),this.drawText(c),u.toDataURL("image/png")},n.calcTextSize=function(){var e,n,o=this.options,t="exclusiveDingTalkWaterMarkCustomClass"+100*Math.random(),i=(e='<span id="'+t+'" style="font:'+o.fontSize+"px "+o.textFont+';visibility:hidden;display:inline-block;">'+o.texts+"</span>",(n=document.createElement("div")).innerHTML=e.trim(),n.firstChild);document.body.appendChild(i);var r=document.getElementById(t),a=Math.max(r.clientWidth,o.texts.length*o.fontSize*1.3)||200,s=Math.min(r.clientHeight,1.3*o.fontSize)||16;o.tWidth=a,o.tHeight=s,document.body.removeChild(i)},n.setCanvasStyle=function(e){var n=this.options,o=n.deg,t=n.fontStyle,i=n.fontSize,r=n.textFont,a=n.fontColor,s=n.transparency;e.rotate(o),e.font=t+" "+i+"px "+r,e.fillStyle=a,e.textAlign="left",e.textBaseline="bottom",e.globalAlpha=(100-s)/100},n.fillContent=function(e,n){for(var o=this.options,t=o.width,i=o.height,r=o.texts,a=o.tWidth,s=o.tHeight,c=0;c<40;c++)for(var u=c*i+s,l=0;l<40;l++){var d;d=c%2==0?e===co.RIGHT?(a+t)*l:(a+t)*l+a+t:e===co.RIGHT?(a+t)*l+t:(a+t)*l+a,n.fillText(r,e===co.RIGHT?d:-d,u)}},n.drawText=function(e){this.fillContent(co.RIGHT,e),this.fillContent(co.LEFT,e)},n.createCanvas=function(){var e=document.createElement("canvas");return this.options.canvas.push(e),e},e}();function vo(e,n){var o,t,i,r,a,s,c,u;void 0===n&&(n=Zn.H5PAGEOTHER);var l=null;try{l=JSON.parse(e)}catch(e){l={}}var d=null===(o=l)||void 0===o||null===(t=o.watermark)||void 0===t?void 0:t.ruleContent,p=null===(i=l)||void 0===i?void 0:i.userInfo;if((null==d?void 0:d.enable)===Qn.DISABLE||(null==d?void 0:d.enable)===Qn.ENABLE&&(null==d||null===(r=d.effectPage)||void 0===r?void 0:r[n])!==Qn.ENABLE)return"";var f,g="";return(null==d||null===(a=d.watermarkContent)||void 0===a?void 0:a.enableUsername)===Qn.ENABLE&&(g+=null==p?void 0:p.userName),(null==d||null===(s=d.watermarkContent)||void 0===s?void 0:s.enablePhoneNumber)===Qn.ENABLE&&(g+=" "+(null==p?void 0:p.lastFourPhoneNo)),null!=d&&null!==(c=d.watermarkContent)&&void 0!==c&&c.customCopy&&(g+=" "+(null==d||null===(f=d.watermarkContent)||void 0===f?void 0:f.customCopy)),g.length?new go(Object.assign({texts:g,textFont:"PingFangSC-Regular,system-ui,sans-serif"},null==d?void 0:d.watermarkStyle,{fontStyle:uo[null==d||null===(u=d.watermarkStyle)||void 0===u?void 0:u.fontStyle]})).init():""}function mo(e,n){void 0===n&&(n=Zn.H5PAGEOTHER);try{return vo.call(null,JSON.stringify(e),n)}catch(e){return""}}function ho(){return ce.invoke("getWifiStatus")}function Io(){return ce.invoke("getWorkbenchContext")}function bo(){return ce.invoke("h5PageBack",{_apiName:"goBack"})}function ko(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"device.notification.hidePreloader",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"device.notification","hidePreloader",e):s===$.IOS&&i.callHandler("device.notification.hidePreloader",Object.assign({},e),(function(e){o(e)}))}else i&&i.call("hideLoading",e,(function(){o()}))}function Ao(){return ce.invoke("hideLoading")}function Po(){return ce.invoke("hideOptionMenu")}function wo(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.appType,s=n.context,c=Object.assign(e,{hidden:!0});if(i){var u=function(){e.onSuccess&&e.onSuccess(),o()},l=function(){e.onFail&&e.onFail(),t()};a===X.MINI_APP?s&&s({m:"biz.navigation.hideBar",args:c,onSuccess:u,onFail:l}):r===$.ANDROID?s&&s(u,l,"biz.navigation","hideBar",c):r===$.IOS&&s.callHandler("biz.navigation.hideBar",Object.assign({},c),(function(){o()}))}else s&&s.call("hideTitlebar",c,(function(){o()}))}function yo(){return ce.invoke("hideTitlebar")}function So(e){return ce.invoke("isDownloadFileExist",e)}function Co(e,n){void 0===e&&(e={});var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"biz.conference.joinScheduleConf",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"biz.conference","joinScheduleConf",e):s===$.IOS&&i.callHandler("biz.conference.joinScheduleConf",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("joinScheduleConf",e,(function(){o()}))}function To(e){return ce.invoke("joinScheduleConf",e)}function xo(e,n){void 0===e&&(e={});var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"biz.conference.joinVideoConf",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"biz.conference","joinVideoConf",e):s===$.IOS&&i.callHandler("biz.conference.joinVideoConf",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("joinVideoConf",e,(function(){o()}))}function Ro(e){return ce.invoke("joinVideoConf",e)}function Oo(e){return ce.invoke("joinVideoMeeting",e)}function Eo(e){return ce.invoke("locateOnMap",e)}function Do(){return ce.invoke("onAudioPlayEnd")}function No(e){return ce.invoke("onRecordAudioEnd",e)}function Bo(e){return ce.invoke("openApiInvoker",e)}function _o(e){return ce.invoke("openApp",e)}function Mo(e){return ce.invoke("openBrowser",e)}function Lo(e){return ce.invoke("openChat",e)}function Fo(e){return ce.invoke("openDownloadFile",e)}function jo(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;be().then((function(n){var c=-1!==Ae(n.version,"1.6.2");if(r){var u=function(e){ce.handleBridgeResponse(e,o,t)},l=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:c?"taurus.common.openLink":"taurus.common.pushWindow",args:e,onSuccess:u,onFail:l}):s===$.ANDROID?i&&i(u,l,"taurus.common",c?"openLink":"pushWindow",e):s===$.IOS&&i.callHandler(c?"taurus.common.openLink":"taurus.common.pushWindow",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call(c?"openLink":"pushWindow",e,(function(e){ce.handleBridgeResponse(e,o,t)}))}))}function zo(e){return ce.invoke("openLink",e)}function Wo(e){return ce.invoke("openPage",e)}function Ho(e){return ce.invoke("dgOpenApp",L({},e,{_apiName:"openSchemeUrl"}))}function Uo(e){return ce.invoke("openSlidePanel",e)}function Vo(){return ce.invoke("openWatermarkCamera")}function Go(e){return ce.invoke("pauseAudio",e)}function Jo(e){return ce.invoke("pickChat",e)}function Ko(e){return ce.invoke("pickGroupChat",e)}function qo(e,n){void 0===e&&(e={});var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:s===$.ANDROID?"taurus.common.pickGroupConversation":"internal.chat.pickGroupConversation",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"taurus.common","pickGroupConversation",e):s===$.IOS&&i.callHandler("internal.chat.pickGroupConversation",Object.assign({},e),(function(e){o(e)}))}else i&&i.call("pickGroupConversation",e,(function(){o()}))}function Qo(e){return void 0===e&&(e={owner:!1}),ce.invoke("pickGroupConversation",e)}function Yo(e){return ce.invoke("playAudio",e)}function Zo(e){return ce.invoke("previewDoc",e)}function Xo(e){return ce.invoke("previewImage",e)}function $o(e){return ce.invoke("printFile",e)}function et(e){return ce.invoke("printNativeLog",e)}function nt(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType,c={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};if(r){var u=function(e){ce.handleBridgeResponse(e,o,t)},l=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.prompt",args:c,onSuccess:u,onFail:l}):s===$.ANDROID?i&&i(u,l,"taurus.common","prompt",c):s===$.IOS&&i.callHandler("taurus.common.prompt",Object.assign({},c),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("prompt",c,(function(e){var n={errorCode:Y.SUCCESS,result:{buttonIndex:e.ok?0:1,value:e.inputValue}};ce.handleBridgeResponse(n,o,t)}))}function ot(e){return ce.invoke("prompt",e)}function tt(e){return ce.invoke("pushWindow",e)}function it(e){return ce.invoke("readImageToBase64",e)}function rt(e){return ce.invoke("reduceImageSize",e)}function at(e){return ce.invoke("removeStorageItem",e)}function st(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.appType,s=n.context;if(i){var c=function(){e.onSuccess&&e.onSuccess(),o()},u=function(){e.onFail&&e.onFail(),t()};a===X.MINI_APP?s&&s({m:"biz.navigation.replace",args:e,onSuccess:c,onFail:u}):r===$.ANDROID?s&&s(c,u,"biz.navigation","replace",e):r===$.IOS&&s.callHandler("taurus.common.replacePage",Object.assign({},e),(function(){o()}))}else s&&s.call("replacePage",e,(function(){o()}))}function ct(e){return ce.invoke("replacePage",e)}function ut(){return ce.invoke("resetView")}function lt(e){return ce.invoke("resumeAudio",e)}function dt(e){return ce.invoke("rotateView",e)}function pt(e){return ce.invoke("scan",e)}function ft(e){return ce.invoke("searchOnMap",e)}function gt(e,n){var o=n.resolve,t=n.context;t&&t.call("sendOutData",function(e){return L({},e,{actionId:"",actionType:"0"})}(e),(function(){o()}))}function vt(e){return ce.invoke("cardSendOutData",e)}function mt(e){return ce.invoke("setLocalScreenShotPolicy",e)}function ht(e){return ce.invoke("setNavIcon",e)}function It(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType,c=n.watch;if(r){var u=function(n){e.onSuccess&&e.onSuccess(),ce.handleBridgeResponse(n,o,t)},l=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"biz.navigation.setLeft",args:e,onSuccess:u,onFail:l}):s===$.ANDROID?i&&i(u,l,"biz.navigation","setLeft",e):s===$.IOS&&i.callHandler("biz.navigation.setLeft",Object.assign({},e),(function(e){!c&&o(e)}))}else i&&i.call("setNavLeftText",e,(function(){o()}))}function bt(e){var n=wn();return ce.invoke("setNavLeftText",n===ne.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,{dingTalkAPIName:n===ne.isDingTalk?"biz.navigation.setLeft":null})}function kt(e){var n=wn();return ce.invoke("setOptionMenu",n===ne.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,n===ne.isDingTalk?{dingTalkAPIName:"biz.navigation.setRight"}:null)}function At(e){return ce.invoke("setProxyInfo",e)}function Pt(e){return ce.invoke("setStorageItem",e)}function wt(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"biz.navigation.setTitle",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"biz.navigation","setTitle",e):s===$.IOS&&i.callHandler("biz.navigation.setTitle",Object.assign({},e),(function(e){ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("setTitle",e,(function(){o()}))}function yt(e){return ce.invoke("setTitle",e)}function St(e){return ce.invoke("shareFileToMessage",e)}function Ct(e){return ce.invoke("shareImageToMessage",e)}function Tt(e){return ce.invoke("shareToMessage",e)}function xt(){return ce.invoke("shootVideo")}function Rt(e){return ce.invoke("showActionSheet",e)}function Ot(e){return ce.invoke("showCallMenu",e)}function Et(e){return ce.invoke("showDatePicker",e)}function Dt(e){return ce.invoke("showDateTimePicker",e)}function Nt(e){return ce.invoke("showExtendModal",e)}function Bt(e){return ce.invoke("showHomeBottomTab",e)}function _t(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){ce.handleBridgeResponse(e,o,t)},u=function(e){ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"device.notification.showPreloader",args:e,onSuccess:c,onFail:u}):s===$.ANDROID?i&&i(c,u,"device.notification","showPreloader",e):s===$.IOS&&i.callHandler("device.notification.showPreloader",Object.assign({},e),(function(e){o(e)}))}else i&&i.call("showLoading",e,(function(){o()}))}function Mt(e){return ce.invoke("showLoading",e)}function Lt(e){return ce.invoke("showModal",e)}function Ft(e){return ce.invoke("showMultiSelect",e)}function jt(e){return ce.invoke("showOnMap",e)}function zt(){return ce.invoke("showOptionMenu")}function Wt(e){return ce.invoke("showPlainInputUponKeyboard",e)}function Ht(e){return ce.invoke("showQuickCallMenu",e)}function Ut(e){return ce.invoke("showSelect",e)}function Vt(e){return ce.invoke("showSignature",e)}function Gt(e){return ce.invoke("showSocialShare",e)}function Jt(e){return ce.invoke("showTimePicker",e)}function Kt(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.appType,s=n.context,c=Object.assign(e,{hidden:!1});if(i){var u=function(){e.onSuccess&&e.onSuccess(),o()},l=function(){e.onFail&&e.onFail(),t()};a===X.MINI_APP?s&&s({m:"biz.navigation.hideBar",args:c,onSuccess:u,onFail:l}):r===$.ANDROID?s&&s(u,l,"biz.navigation","hideBar",c):r===$.IOS&&s.callHandler("biz.navigation.hideBar",Object.assign({},c),(function(){o()}))}else s&&s.call("showTitlebar",c,(function(){o()}))}function qt(){return ce.invoke("showTitlebar")}function Qt(e){return ce.invoke("startFaceRecognition",e)}function Yt(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.platformType,a=n.containerType,s=n.appType,c=ce.registerEvent(ee.UPDATE_LOCATION,(function(n){var o=n.data;o.errorCode!==Y.SUCCESS?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)}));if(a){var u=function(n){ce.registerContinuesEvent(e.sceneId,c),ce.handleBridgeResponse(n,o,t)},l=function(n){ce.registerContinuesEvent(e.sceneId,c),ce.handleBridgeResponse(n,o,t)};s===X.MINI_APP?(console.log("taurus.common.startGeolocation",e),i&&i({m:"taurus.common.startGeolocation",args:e,onSuccess:u,onFail:l})):r===$.ANDROID?i&&i(u,l,"taurus.common","startGeolocation",e):r===$.IOS&&i.callHandler("taurus.common.startGeolocation",Object.assign({},e),(function(n){ce.registerContinuesEvent(e.sceneId,c),ce.handleBridgeResponse(n,o,t)}))}else i&&i.call("startGeolocation",e,(function(n){ce.registerContinuesEvent(e.sceneId,c),ce.handleBridgeResponse(n,o,t)}))}function Zt(e){return ce.invoke("startGeolocation",e)}function Xt(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.appType,s=n.platformType,c=ce.registerEvent(ee.UPDATE_NETWORK_STATUS,(function(n){var o=n.data;o.errorCode!==Y.SUCCESS?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)}));if(r){var u=function(e){ce.registerContinuesEvent(e.result.requestId,c),ce.handleBridgeResponse(e,o,t)},l=function(e){ce.registerContinuesEvent(e.result.requestId,c),ce.handleBridgeResponse(e,o,t)};a===X.MINI_APP?i&&i({m:"taurus.common.startListenNetworkStatus",args:e,onSuccess:u,onFail:l}):s===$.ANDROID?i&&i(u,l,"taurus.common","startListenNetworkStatus",e):s===$.IOS&&i.callHandler("taurus.common.startListenNetworkStatus",Object.assign({},e),(function(e){ce.registerContinuesEvent(e.result.requestId,c),ce.handleBridgeResponse(e,o,t)}))}else i&&i.call("startListenNetworkStatus",e,(function(e){ce.registerContinuesEvent(e.result.requestId,c),ce.handleBridgeResponse(e,o,t)}))}function $t(e){return ce.invoke("startListenNetworkStatus",e)}function ei(e){return ce.invoke("startRecordAudio",e)}function ni(e){return ce.invoke("startTraceReport",e)}function oi(e){return ce.invoke("startVPNApp",e)}function ti(e){return ce.invoke("startWatchShake",e)}function ii(e){return ce.invoke("stopAudio",e)}function ri(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.appType,s=n.context;if(i){var c=function(n){ce.removeContinuesEvent(e.sceneId),ce.handleBridgeResponse(n,o,t)},u=function(n){ce.removeContinuesEvent(e.sceneId),ce.handleBridgeResponse(n,o,t)};a===X.MINI_APP?s&&s({m:"taurus.common.stopGeolocation",args:e,onSuccess:c,onFail:u}):r===$.ANDROID?s&&s(c,u,"taurus.common","stopGeolocation",e):r===$.IOS&&s.callHandler("taurus.common.stopGeolocation",Object.assign({},e),(function(n){ce.removeContinuesEvent(e.sceneId),ce.handleBridgeResponse(n,o,t)}))}else s&&s.call("stopGeolocation",e,(function(n){ce.removeContinuesEvent(e.sceneId),ce.handleBridgeResponse(n,o,t)}))}function ai(e){return ce.invoke("stopGeolocation",e)}function si(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.appType,a=n.platformType,s=n.context;if(i){var c=function(n){ce.removeContinuesEvent(e.requestId),ce.handleBridgeResponse(n,o,t)},u=function(n){ce.removeContinuesEvent(e.requestId),ce.handleBridgeResponse(n,o,t)};r===X.MINI_APP?s&&s({m:"taurus.common.stopListenNetworkStatus",args:e,onSuccess:c,onFail:u}):a===$.ANDROID?s&&s(c,u,"taurus.common","stopListenNetworkStatus",e):a===$.IOS&&s.callHandler("taurus.common.stopListenNetworkStatus",Object.assign({},e),(function(n){ce.removeContinuesEvent(e.requestId),ce.handleBridgeResponse(n,o,t)}))}else s&&s.call("stopListenNetworkStatus",e,(function(n){ce.removeContinuesEvent(e.requestId),ce.handleBridgeResponse(n,o,t)}))}function ci(e){return ce.invoke("stopListenNetworkStatus",e)}function ui(){return ce.invoke("stopPullToRefresh",{_apiName:"stopPullToRefresh"})}function li(e){return ce.invoke("stopRecordAudio",e)}function di(e){return ce.invoke("stopTraceReport",e)}function pi(e){return ce.invoke("stopVPNApp",e)}function fi(){return ce.invoke("stopWatchShake")}function gi(e){return ce.invoke("subscribe",e)}function vi(){return ce.invoke("takePhoto")}function mi(){return ce.invoke("testProxy",{})}function hi(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType,s=n.appType,c={type:"error"===e.icon?"fail":"success"===e.icon?"success":"none",content:e.text,duration:1e3*e.duration,taurusToastStyle:e.taurusToastStyle};if(r){var u=function(){ce.handleBridgeResponse({errorCode:Y.SUCCESS,result:{}},o,t)},l=function(e){ce.handleBridgeResponse(e,o,t)};s===X.MINI_APP?i&&i({m:"taurus.common.toast",args:c,onSuccess:u,onFail:l}):a===$.ANDROID?i&&i(u,l,"taurus.common","toast",c):a===$.IOS&&i.callHandler("taurus.common.toast",Object.assign({},c),(function(){ce.handleBridgeResponse({errorCode:Y.SUCCESS,result:{}},o,t)}))}else i&&i.call("toast",c,(function(){ce.handleBridgeResponse({errorCode:Y.SUCCESS,result:{}},o,t)}))}function Ii(e){return ce.invoke("toast",e)}function bi(){return ce.invoke("unlockWithSecurityVerification")}function ki(e){return ce.invoke("unsubscribe",e)}function Ai(e){return ce.invoke("dgUploadFile",L({},e,{_apiName:"uploadFile"}))}function Pi(e){return ce.invoke("uploadFileByType",e)}function wi(e){return new Promise((function(n,o){my.uploadFile(L({},e,{success:function(e){n(e)},fail:function(e){o(e)}}))}))}function yi(e){return ce.invoke("uploadRemoteFileToDisk",e)}function Si(e){return ce.invoke("ut",e)}function Ci(e){return ce.invoke("vibrate",e)}ce.registerAPI("getWifiStatus",{mobile:!0,mini:!0}),ho.version={android:"1.3.5",ios:"1.3.5"},ce.registerAPI("getWorkbenchContext",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWorkbenchContext",e)}}),Io.version={android:"2.1.10",ios:"2.1.10"},ce.registerAPI("h5PageBack",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;r?a===$.ANDROID?i&&i((function(e){ce.handleBridgeResponse(e,o,t)}),(function(e){ce.handleBridgeResponse(e,o,t)}),"biz.navigation","goBack",e):a===$.IOS&&i.callHandler("biz.navigation.goBack",Object.assign({},e),(function(e){o(e)})):i&&i.call("h5PageBack",{_apiName:"goBack"},(function(){o()}))}}),bo.version={android:"1.3.0",ios:"1.3.9"},ce.registerAPI("hideLoading",{mini:ko,mobile:ko}),Ao.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("hideOptionMenu",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=(n.appType,n.platformType);if(r){var s={show:!1,control:!0,text:""};a===$.ANDROID?i&&i((function(e){ce.handleBridgeResponse(e,o,t)}),(function(e){ce.handleBridgeResponse(e,o,t)}),"biz.navigation","setRight",s):a===$.IOS&&i.callHandler("biz.navigation.setRight",Object.assign({},s),(function(e){o(e)}))}else i&&i.call("hideOptionMenu",e,(function(){o()}))}}),Po.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("hideTitlebar",{mini:wo,mobile:wo}),yo.version={android:"2.1.0",ios:"2.1.0"},ce.registerAPI("isDownloadFileExist",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.isLocalFileExist",e)}}),So.version={pc:"1.3.5"},ce.registerAPI("joinScheduleConf",{mini:Co,mobile:Co,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinScheduleConf",L({},e))}}),To.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"},ce.registerAPI("joinVideoConf",{mini:xo,mobile:xo,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinVideoConf",L({},e))}}),Ro.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"},ce.registerAPI("joinVideoMeeting",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinVideoMeeting",L({},e))}}),Oo.version={android:"3.9.0",ios:"3.9.0",pc:"3.9.0"},ce.registerAPI("locateOnMap",{mobile:!0,mini:!0}),Eo.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("onAudioPlayEnd",{mini:!0,mobile:!0}),Do.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("onRecordAudioEnd",{mini:!0,mobile:!0}),No.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("openApiInvoker",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"openApiInvoker",e)}}),Bo.version={ios:"3.0.1",android:"3.0.1",pc:"3.0.1"},ce.registerAPI("openApp",{mini:!0,mobile:!0}),_o.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("openBrowser",{mini:!0,mobile:!0}),Mo.version={android:"1.2.3"},ce.registerAPI("openChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"internal.chat.toConversation",{cid:e.chatId})}}),Lo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},ce.registerAPI("openDownloadFile",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLocalFile",e)}}),Fo.version={pc:"1.3.5"},ce.registerAPI("openLink",{mini:jo,mobile:jo,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLink",e)}}),zo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},ce.registerAPI("openPage",{mini:!0,mobile:!0}),Wo.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("dgOpenApp",{mobile:!0,mini:!0}),Ho.version={android:"*******",ios:"*******"},ce.registerAPI("openSlidePanel",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openSlidePanel",e)}}),Uo.version={pc:"1.3.5"},ce.registerAPI("openWatermarkCamera",{mobile:!0,mini:!0}),Vo.version={android:"1.3.7",ios:"1.3.7"},ce.registerAPI("pauseAudio",{mini:!0,mobile:!0}),Go.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("pickChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.chat.pickConversation",e)}}),Jo.version={android:"1.2.0",ios:"1.2.0",pc:"2.9.0"},ce.registerAPI("pickChatByCorpId",{mini:!0,mobile:!0}),ce.registerAPI("pickGroupChat",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.pickGroupChat",e)}}),Ko.version={pc:"2.10.30"},ce.registerAPI("pickGroupConversation",{mini:qo,mobile:qo}),Qo.version={android:"2.8.0",ios:"2.8.0"},ce.registerAPI("playAudio",{mini:!0,mobile:!0}),Yo.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("previewDoc",{mini:!0,mobile:!0}),Zo.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("previewImage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.previewImage",e)}}),Xo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},ce.registerAPI("printFile",{mini:!0,mobile:!0}),$o.version={android:"2.2.10"},ce.registerAPI("printNativeLog",{mini:!0,mobile:!0}),et.version={android:"1.9.4",ios:"1.9.4"},ce.registerAPI("prompt",{mini:nt,mobile:nt,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.prompt",e)}}),ot.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},ce.registerAPI("pushWindow",{mini:!0,mobile:!0}),tt.version={android:"2.9.7",ios:"2.9.7"},ce.registerAPI("readImageToBase64",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"readImageToBase64",e)}}),it.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"},function(e){e[e.ADJUST_BY_NET=0]="ADJUST_BY_NET",e[e.LOW_QUALITY=1]="LOW_QUALITY",e[e.MID_QUALITY=2]="MID_QUALITY",e[e.HIGH_QUALITY=3]="HIGH_QUALITY",e[e.NOT_COMPRESSED=4]="NOT_COMPRESSED",e[e.CUSTOM=5]="CUSTOM"}(lo||(lo={})),ce.registerAPI("reduceImageSize",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"reduceImageSize",e)}}),rt.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"},ce.registerAPI("removeStorageItem",{mobile:!0,mini:!0}),at.version={android:"*******",ios:"*******"},ce.registerAPI("replacePage",{mini:st,mobile:st}),ct.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("resetView",{mini:!0,mobile:!0}),ut.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("resumeAudio",{mini:!0,mobile:!0}),lt.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("rotateView",{mini:!0,mobile:!0}),dt.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("scan",{mini:!0,mobile:!0}),pt.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("searchOnMap",{mini:!0,mobile:!0}),ft.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("cardSendOutData",{mini:gt,mobile:gt}),vt.version={android:"2.5.0",ios:"2.5.0"},function(e){e.DEFAULT="0",e.DISABLEALL="1",e.ENABLEALL="2"}(po||(po={})),ce.registerAPI("setLocalScreenShotPolicy",{mini:!0,mobile:!0}),mt.version={android:"2.12.12",ios:"2.12.12"},ce.registerAPI("setNavIcon",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.context;i?r===$.ANDROID?a&&a((function(n){e.onSuccess&&e.onSuccess(),o()}),(function(e){t()}),"biz.navigation","setIcon",e):r===$.IOS&&a.callHandler("biz.navigation.setIcon",Object.assign({},e),(function(e){o()})):a&&a.call("setNavIcon",e,(function(e){o()}))}}),ht.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("setNavLeftText",{mini:It,mobile:It,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setLeft",e)}}),bt.version={ios:"1.2.0",pc:"1.2.0"},ce.registerAPI("setOptionMenu",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;if(r){var s,c,u={text:e.title,show:void 0===e.show||e.show,control:void 0===e.control||e.control};if(a===$.ANDROID)i&&i((function(n){e.onSuccess&&e.onSuccess(n),o(n)}),(function n(o){e.onFail&&e.onFail(o),t(n)}),"biz.navigation",(null==e||null===(s=e.menus)||void 0===s?void 0:s.length)>1?"setMenu":"setRight",(null==e||null===(c=e.menus)||void 0===c?void 0:c.length)>1?e:u);else if(a===$.IOS){var l,d;i.callHandler((null==e||null===(l=e.menus)||void 0===l?void 0:l.length)>1?"biz.navigation.setMenu":"biz.navigation.setRight",Object.assign({},(null==e||null===(d=e.menus)||void 0===d?void 0:d.length)>1?e:u),(function(){o()}))}}else i&&i.call("setOptionMenu",e,(function(){o()}))}}),kt.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("setProxyInfo",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"net.util.setProxyInfo",e)}}),At.version={pc:"2.10.0"},ce.registerAPI("setStorageItem",{mobile:!0,mini:!0}),Pt.version={android:"*******",ios:"*******"},function(e){e.TRUE="true",e.FALSE="false"}(fo||(fo={})),ce.registerAPI("setTitle",{mini:wt,mobile:wt,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setTitle",e)}}),yt.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},ce.registerAPI("shareFileToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareFileToMessage",e)}}),St.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"},ce.registerAPI("shareImageToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareImageToMessage",e)}}),Ct.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"},ce.registerAPI("shareToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.share",e)}}),Tt.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"},ce.registerAPI("shootVideo",{mini:!0,mobile:!0}),xt.version={android:"1.3.5",ios:"1.3.5"},ce.registerAPI("showActionSheet",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.actionSheet",e)}}),Rt.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},ce.registerAPI("showCallMenu",{mini:!0,mobile:!0}),Ot.version={android:"1.3.9",ios:"1.3.9"},ce.registerAPI("showDatePicker",{mobile:!0,mini:!0}),Et.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("showDateTimePicker",{mini:!0,mobile:!0}),Dt.version={android:"1.3.10",ios:"1.3.10"},ce.registerAPI("showExtendModal",{mini:!0,mobile:!0}),Nt.version={android:"1.3.5",ios:"1.3.5"},ce.registerAPI("showHomeBottomTab",{mobile:!0}),Bt.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("showLoading",{mini:_t,mobile:_t}),Mt.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("showModal",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openModal",e)}}),Lt.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"},ce.registerAPI("showMultiSelect",{mini:!0,mobile:!0}),Ft.version={android:"1.3.10",ios:"1.3.10"},ce.registerAPI("showOnMap",{mini:!0,mobile:!0}),jt.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("showOptionMenu",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.platformType;n.containerType?r===$.ANDROID?i&&i((function(){o()}),(function(){t()}),"taurus.common","showOptionMenu",e):r===$.IOS&&i.callHandler("taurus.common.showOptionMenu",Object.assign({},e),(function(){o()})):i&&i.call("showOptionMenu",e,(function(){o()}))}}),zt.version={android:"1.1.0",ios:"1.1.0"},ce.registerAPI("showPlainInputUponKeyboard",{mobile:!0,mini:!0}),Wt.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("showQuickCallMenu",{mini:!0,mobile:!0}),Ht.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("showSelect",{mini:!0,mobile:!0}),Ut.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("showSignature",{mobile:!0}),Vt.version={android:"1.3.4"},ce.registerAPI("showSocialShare",{mini:!0,mobile:!0}),Gt.version={android:"1.2.0.10",ios:"1.2.0.10"},ce.registerAPI("showTimePicker",{mobile:!0,mini:!0}),Jt.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("showTitlebar",{mini:Kt,mobile:Kt}),qt.version={android:"2.1.0",ios:"2.1.0"},ce.registerAPI("startFaceRecognition",{mini:!0,mobile:!0}),Qt.version={android:"1.8.2",ios:"1.8.2"},ce.registerAPI("startGeolocation",{mobile:Yt,mini:Yt}),Zt.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("startListenNetworkStatus",{mobile:Xt,mini:Xt}),$t.version={android:"*******",ios:"*******"},ce.registerAPI("startRecordAudio",{mini:!0,mobile:!0}),ei.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("startTraceReport",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType,s=n.appType,c=ce.registerEvent(ee.UPDATE_TRACE,(function(n){var o=n.data;o.errorCode&&o.errorCode!==Y.SUCCESS?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result||o)}));if(r){var u=function(n){ce.registerContinuesEvent(e.traceId,c),ce.handleBridgeResponse(n,o,t)},l=function(n){ce.registerContinuesEvent(e.traceId,c),ce.handleBridgeResponse(n,o,t)};s===X.MINI_APP?i&&i({m:"taurus.common.startTraceReport",args:e,onSuccess:u,onFail:l}):a===$.ANDROID?i&&i(u,l,"taurus.common","startTraceReport",e):a===$.IOS&&i.callHandler("taurus.common.startTraceReport",Object.assign({},e),(function(n){ce.registerContinuesEvent(e.traceId,c),ce.handleBridgeResponse(n,o,t)}))}else i&&i.call("startTraceReport",e,(function(n){ce.registerContinuesEvent(e.traceId,c),ce.handleBridgeResponse(n,o,t)}))},mini:!0}),ni.version={android:"1.3.4",ios:"1.3.4"},ce.registerAPI("startVPNApp",{mini:!0,mobile:!0}),oi.version={android:"1.6.0",ios:"1.6.0"},ce.registerAPI("startWatchShake",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType,s=ce.registerEvent(ee.ON_SHAKE,(function(){e.onSuccess&&e.onSuccess()}));r?a===$.ANDROID?i&&i((function(n){e.onSuccess&&e.onSuccess(),o()}),(function(e){t()}),"taurus.common","startWatchShake",e):a===$.IOS&&i.callHandler("taurus.common.startWatchShake",Object.assign({},e),(function(e){o()})):i&&i.call("startWatchShake",e,(function(e){ce.registerContinuesEvent("shake",s),ce.handleBridgeResponse(e,o,t)}))}}),ti.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("stopAudio",{mini:!0,mobile:!0}),ii.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("stopGeolocation",{mobile:ri,mini:ri}),ai.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("stopListenNetworkStatus",{mini:si,mobile:si}),ci.version={android:"*******",ios:"*******"},ce.registerAPI("stopPullToRefresh",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType;r?a===$.ANDROID?i&&i((function(){o()}),(function(){t()}),"ui.pullToRefresh","stop",e):a===$.IOS&&i.callHandler("ui.pullToRefresh.stop",Object.assign({},e),(function(){o()})):i&&i.call("restorePullToRefresh",e,(function(){o()}))}}),ui.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("stopRecordAudio",{mini:!0,mobile:!0}),li.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("stopTraceReport",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.context;i?r===$.ANDROID?a&&a((function(n){ce.removeContinuesEvent(e.traceId),ce.handleBridgeResponse(n,o,t)}),(function(n){ce.removeContinuesEvent(e.traceId),ce.handleBridgeResponse(n,o,t)}),"taurus.common","stopTraceReport",e):r===$.IOS&&a.callHandler("taurus.common.stopTraceReport",Object.assign({},e),(function(n){ce.removeContinuesEvent(e.traceId),ce.handleBridgeResponse(n,o,t)})):a&&a.call("stopTraceReport",e,(function(n){ce.removeContinuesEvent(e.traceId),ce.handleBridgeResponse(n,o,t)}))}}),di.version={android:"1.3.4",ios:"1.3.4"},ce.registerAPI("stopVPNApp",{mini:!0,mobile:!0}),pi.version={android:"1.6.0",ios:"1.6.0"},ce.registerAPI("stopWatchShake",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.containerType,r=n.platformType,a=n.context;i?r===$.ANDROID?a&&a((function(n){e.onSuccess&&e.onSuccess(),o()}),(function(e){t()}),"taurus.common","stopWatchShake",e):r===$.IOS&&a.callHandler("taurus.common.stopWatchShake",Object.assign({},e),(function(e){o()})):a&&a.call("stopWatchShake",e,(function(e){ce.removeContinuesEvent("shake"),ce.handleBridgeResponse(e,o,t)}))}}),fi.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("subscribe",{mobile:function(e,n){var o=n.resolve,t=n.reject,i=n.context,r=n.containerType,a=n.platformType,s=!1;r?a===$.ANDROID?i&&i((function(n){s?(e.onSuccess||e.onFail)&&("0"!==n.errorCode?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)):(s=!0,ce.handleBridgeResponse(n,o,t))}),(function(n){s?e.onFail&&e.onFail(n):(s=!0,ce.handleBridgeResponse(n,o,t))}),"taurus.common","subscribe",e):a===$.IOS&&i.callHandler("taurus.common.subscribe",Object.assign({},e),(function(n){s?(e.onSuccess||e.onFail)&&("0"!==n.errorCode?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)):(s=!0,ce.handleBridgeResponse(n,o,t))})):i&&i.call("subscribe",e,(function(n){s?(e.onSuccess||e.onFail)&&("0"!==n.errorCode?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)):(s=!0,ce.handleBridgeResponse(n,o,t))}))}}),gi.version={android:"1.6.0",ios:"1.6.0"},ce.registerAPI("takePhoto",{mini:!0,mobile:!0}),vi.version={android:"1.3.5",ios:"1.3.5"},ce.registerAPI("testProxy",{pc:function(e,n){void 0===e&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"net.util.testProxy",e)}}),mi.version={pc:"2.10.0"},ce.registerAPI("toast",{mobile:hi,mini:hi,pc:function(e,n){var o=e.icon,t=e.text,i=e.duration,r=e.delay;window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.toast",{type:o,text:t,duration:i,delay:r})}}),Ii.version={android:"1.3.2",ios:"1.3.2"},ce.registerAPI("unlockWithSecurityVerification",{mini:!0,mobile:!0}),bi.version={android:"*******",ios:"*******"},ce.registerAPI("unsubscribe",{mobile:!0}),ki.version={android:"1.6.0",ios:"1.6.0"},ce.registerAPI("dgUploadFile",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgUploadFile",e)}}),Ai.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},ce.registerAPI("uploadFileByType",{mini:!0,mobile:!0}),Pi.version={android:"1.3.0",ios:"1.3.0"},ce.registerAPI("uploadFile",{mini:!0}),wi.version={android:"1.6.2",ios:"1.6.2"},ce.registerAPI("uploadRemoteFileToDisk",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.uploadRemoteFileToDisk",e)}}),yi.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"},ce.registerAPI("ut",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.ut",e)}}),Si.version={pc:"1.3.10"},ce.registerAPI("vibrate",{mini:!0,mobile:!0}),Ci.version={android:"1.3.1",ios:"1.3.1"};var Ti={alert:le,authConfig:de,bizContactDepartmentsPickerExternal:fe,bizCustomContactChooseExternal:ve,bizCustomContactMultipleChooseExternal:he,callPhone:Ie,canIUse:function(e){return Oe.apply(this,arguments)},checkVPNAppInstalled:Ee,checkVPNAppOnline:De,chooseContact:Ne,chooseContactWithComplexPicker:_e,chooseDateRangeWithCalendar:Me,chooseDayWithCalendar:Le,chooseDepartments:Fe,chooseFile:je,chooseHalfDayWithCalendar:ze,chooseImage:We,chooseInterconnectionChat:He,chooseLocalImage:Ue,chooseSpaceDir:Ve,chooseTimeWithCalendar:Ge,chooseVideo:Je,closePage:qe,complexPickerAdmin:Qe,confirm:Ye,copyToClipboard:Ze,createChatGroup:Xe,createDing:$e,createDingV2:en,createVideoConf:on,createVideoMeeting:tn,dealWithBackAction:rn,disableClosePage:an,disablePullToRefresh:sn,disableWebviewBounce:cn,downloadAudio:un,downloadFile:fn,enablePullToRefresh:gn,enableVpn:vn,enableWebviewBounce:mn,exclusiveInvoke:hn,faceComparison:In,faceRecognition:bn,getAppInstallStatus:kn,getAuthCode:An,getConfig:Pn,getContainerType:wn,getDeviceId:yn,getFromClipboard:Sn,getGeolocation:Cn,getGeolocationStatus:Tn,getHotspotInfo:xn,getLanguageSetting:Rn,getLoginUser:On,getNetworkType:En,getPhoneInfo:Dn,getProxyInfo:Nn,getStorageItem:Bn,getTraceStatus:_n,getUUID:Mn,getUserAgent:Hn,getWaterMark:ro,getWaterMarkConfigV2:ao,getWaterMarkV2:function(e){return new Promise((function(n,o){be().then((function(t){-1!==Ae(t.version,"2.8.0")?ao({pageInfo:e}).then((function(t){try{var i=mo(t,e);n(i)}catch(e){o(e)}})):Un({pageInfo:e}).then((function(t){try{var i=io(t,e);n(i)}catch(e){o(e)}}))})).catch((function(){Un({pageInfo:e}).then((function(t){try{var i=io(t,e);n(i)}catch(e){o(e)}}))}))}))},getWifiStatus:ho,getWorkbenchContext:Io,goBack:bo,hideLoading:Ao,hideOptionMenu:Po,hideTitleBar:yo,isDownloadFileExist:So,joinScheduleConf:To,joinVideoConf:Ro,joinVideoMeeting:Oo,locateOnMap:Eo,on:function(e,n){return ce.registerEvent(e,n)},onAudioPlayEnd:Do,onRecordAudioEnd:No,openApiInvoker:Bo,openApp:_o,openBrowser:Mo,openChat:Lo,openDownloadFile:Fo,openLink:zo,openPage:Wo,openSchemeUrl:Ho,openSlidePanel:Uo,openWatermarkCamera:Vo,pauseAudio:Go,pickChat:Jo,pickChatByCorpId:function(e){return ce.invoke("pickChatByCorpId",e)},pickGroupChat:Ko,pickGroupConversation:Qo,playAudio:Yo,previewDoc:Zo,previewImage:Xo,printFile:$o,printNativeLog:et,prompt:ot,pushWindow:tt,readImageToBase64:it,ready:function(e){"function"==typeof e?ce.onReady(e):console.error("dd.ready's param must be function! ")},reduceImageSize:rt,removeStorageItem:at,replacePage:ct,resetView:ut,resumeAudio:lt,rotateView:dt,scan:pt,searchOnMap:ft,sendOutData:vt,setLocalScreenShotPolicy:mt,setNavIcon:ht,setNavLeftText:bt,setOptionMenu:kt,setProxyInfo:At,setStorageItem:Pt,setTitle:yt,shareFileToMessage:St,shareImageToMessage:Ct,shareToMessage:Tt,shootVideo:xt,showActionSheet:Rt,showCallMenu:Ot,showDatePicker:Et,showDateTimePicker:Dt,showExtendModal:Nt,showHomeBottomTab:Bt,showLoading:Mt,showModal:Lt,showMultiSelect:Ft,showOnMap:jt,showOptionMenu:zt,showPlainInputUponKeyboard:Wt,showQuickCallMenu:Ht,showSelect:Ut,showSignature:Vt,showSocialShare:Gt,showTimePicker:Jt,showTitleBar:qt,startFaceRecognition:Qt,startGeolocation:Zt,startListenNetworkStatus:$t,startRecordAudio:ei,startTraceReport:ni,startVPNApp:oi,startWatchShake:ti,stopAudio:ii,stopGeolocation:ai,stopListenNetworkStatus:ci,stopPullToRefresh:ui,stopRecordAudio:li,stopTraceReport:di,stopVPNApp:pi,stopWatchShake:fi,subscribe:gi,takePhoto:vi,testProxy:mi,toast:Ii,unlockWithSecurityVerification:bi,unsubscribe:ki,uploadFile:Ai,uploadFileByType:Pi,uploadLocalFile:wi,uploadRemoteFileToDisk:yi,ut:Si,version:be,vibrate:Ci};if(ce.getAppType()===X.MINI_APP)Ti=new Proxy(Ti,{get:function(e,n,o){return n in Ti?Reflect.get(e,n,o):function(e,n){if(e)return function(o){return"function"==typeof o||n.includes("Sync")||n.startsWith("create")?e(o):new Promise((function(n,t){e(L({},o,{success:function(e){n(e)},fail:function(e){t(e)}}))}))}}(Reflect.get(my,n,o),n)}});else{window.dd&&console.warn("已经存在 window.dd 变量，引入 gdt-jsapi 会修改 window.dd 的值。");try{Object.defineProperty(window,"dd",{value:Ti,writable:!0})}catch(pn){console.error(pn)}window.gdt&&console.warn("已经存在 window.gdt 变量，引入 gdt-jsapi 会修改 window.gdt 的值。");try{Object.defineProperty(window,"gdt",{value:Ti,writable:!0})}catch(pn){console.error(pn)}}var xi=Ti,Ri={name:"OAuth2Premises",setup:function(){var n=a(),o=s(""),i=s("专有钉钉"),r=s(!1),u=s(!0),l=s(""),d=s({callback_url:"",state:""}),p=function(){var i=t(e().m((function t(){var i,a;return e().w((function(e){for(;;)switch(e.n){case 0:return u.value=window.self===window.top,r.value=!0,e.n=1,f();case 1:i=e.v,r.value=!1,n.query.state&&(a=M(n.query.state),d.value=JSON.parse(a),d.value.state=n.query.state),0===i.errorCode?window.location.href=g(i.data):o.value=v();case 2:return e.a(2)}}),t)})));return function(){return i.apply(this,arguments)}}(),f=function(){return new Promise((function(e,o){xi.ready((function(){xi.getAuthCode({corpId:n.query.corpId}).then((function(n){e({errorCode:0,data:n})})).catch((function(n){e({errorCode:1,data:n})}))}))}))},g=function(e){var o=[];return e.state=n.query.state||"",Object.keys(e).forEach((function(n){o.push("".concat(n,"=").concat(e[n]))})),d.value.callback_url+"?"+o.join("&")},v=function(){var e=d.value.callback_url+"?state="+d.value.state;return"taurus://taurusclient/action/open_app?type=1&offline=false&url=".concat(encodeURIComponent(e))};return c((function(){p()})),{url:o,appName:i,loading:r,isAuthState:u,authKey:l,routeHandle:function(){if(console.log("isAuthState:",u.value,"url:",o.value),!u.value&&!r.value){var e={event:"wakeup-app",params:{url:o.value,authKey:l.value}};console.log("peurl",o.value,d.value.state),window.parent.postMessage(e,window.location.origin)}}}}},Oi={class:"premises-page"},Ei={style:{"text-align":"center"}},Di={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},Ni={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},Bi={class:"submit-btn-wrapper"},_i={key:0,class:"premises-tip"};n("default",r(Ri,[["render",function(e,n,o,t,i,r){var a=u("base-button");return l(),d("div",Oi,[p("div",Ei,[p("span",Di,[(l(),d("svg",Ni,n[0]||(n[0]=[p("use",{"xlink:href":"#icon-auth-zhezhending"},null,-1)]))),f(" "+g(t.appName),1)])]),p("div",Bi,[v(a,{type:"primary",size:"large",class:"login_submit_button","native-type":"submit",onClick:t.routeHandle},{default:m((function(){return[f(g(t.isAuthState?"正在获取授权信息":"授权登录"),1)]})),_:1},8,["onClick"])]),t.isAuthState?h("v-if",!0):(l(),d("span",_i,"若打开失败，请先安装"+g(t.appName)+"App",1))])}],["__file","D:/asec-platform/frontend/portal/src/view/login/oauth2/oauth2_premises.vue"]]))}}}))}();
