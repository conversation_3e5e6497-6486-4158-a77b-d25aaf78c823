/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
!function(){function e(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(e){if("string"==typeof e)return t(e,n);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?t(e,n):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function c(n,r,i,a){var c=r&&r.prototype instanceof u?r:u,s=Object.create(c.prototype);return o(s,"_invoke",function(n,o,r){var i,a,c,u=0,s=r||[],d=!1,f={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,a=0,c=e,f.n=n,l}};function p(n,o){for(a=n,c=o,t=0;!d&&u&&!r&&t<s.length;t++){var r,i=s[t],p=f.p,m=i[2];n>3?(r=m===o)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=p&&((r=n<2&&p<i[1])?(a=0,f.v=o,f.n=i[1]):p<m&&(r=n<3||i[0]>o||o>m)&&(i[4]=n,i[5]=o,f.n=m,a=0))}if(r||n>1)return l;throw d=!0,o}return function(r,s,m){if(u>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,m),a=s,c=m;(t=a<2?e:c)||!d;){i||(a?a<3?(a>1&&(f.n=-1),p(a,c)):f.n=c:f.v=c);try{if(u=2,i){if(a||(r="next"),t=i[r]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=i.return)&&t.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+r+"' method"),a=1);i=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){i=e,a=1,c=t}finally{u=1}}return{value:t,done:d}}}(n,i,a),!0),s}var l={};function u(){}function s(){}function d(){}t=Object.getPrototypeOf;var f=[][i]?t(t([][i]())):(o(t={},i,(function(){return this})),t),p=d.prototype=u.prototype=Object.create(f);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,o(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=d,o(p,"constructor",d),o(d,"constructor",s),s.displayName="GeneratorFunction",o(d,a,"GeneratorFunction"),o(p),o(p,a,"Generator"),o(p,i,(function(){return this})),o(p,"toString",(function(){return"[object Generator]"})),(n=function(){return{w:c,m:m}})()}function o(e,t,n,r){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}o=function(e,t,n,r){if(t)i?i(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var a=function(t,n){o(e,t,(function(e){return this._invoke(t,n,e)}))};a("next",0),a("throw",1),a("return",2)}},o(e,t,n,r)}function r(e,t,n,o,r,i,a){try{var c=e[i](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,r)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))}}System.register(["./index-legacy.21dbeba9.js","./ASD-legacy.b6ffb1bc.js","./index-legacy.5dacdf50.js","./index-browser-esm-legacy.6966c248.js","./index-legacy.1a25660e.js","./menuItem-legacy.173ecb86.js","./asyncSubmenu-legacy.07446070.js"],(function(t,o){"use strict";var r,a,c,l,u,s,d,f,p,m,v,g,h,b,y,x,w,k,C,I,j,_,S,O,F,z,A,T,U,N,M,E,G,R,B,D=document.createElement("style");return D.textContent='@charset "UTF-8";@media screen and (min-width: 320px) and (max-width: 750px){.el-header,.layout-cont .main-cont .breadcrumb{padding:0 5px}.layout-cont .right-box{margin-right:5px}.el-main .admin-box{margin-left:0;margin-right:0}.el-main .big.admin-box{padding:0}.el-main .big .bottom .chart-player{height:auto!important;margin-bottom:15px}.el-main .big .bottom .todoapp{background-color:#fff;padding-bottom:10px}.card .car-left,.card .car-right{width:100%;height:100%}.card{padding-left:5px;padding-right:5px}.card .text{width:100%}.card .text h4{white-space:break-spaces}.shadow{margin-left:4px;margin-right:4px}.shadow .grid-content{margin-bottom:10px;padding:0}.el-dialog{width:90%}.el-transfer .el-transfer-panel{width:40%;display:inline-block}.el-transfer .el-transfer__buttons{padding:0 5px;display:inline-block}}.dark{background-color:#273444!important;color:#fff!important}.light{background-color:#fff!important;color:#000!important}.icon-rizhi1 span{margin-left:5px}.day-select{height:23px;width:88px;margin-left:15px}.day-select div{height:23px;width:88px}.day-select div input{height:23px;width:50px;font-size:12px;color:#2972c8}.right-box{margin-top:9px}.hidelogoimg{overflow:hidden!important;width:54px!important;padding-left:9px!important}.hidelogoimg .logoimg{margin-left:7px}*,*:before,*:after{box-sizing:border-box}.layout-wrapper{display:flex;min-height:100vh}.shadow-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:999;display:none}.shadowBg{display:block!important}.header-row{display:flex;width:100%}.header-col{flex:1}.header-content{display:flex;align-items:center;padding:0}.header-menu-col{flex:0 0 auto}.breadcrumb-col{flex:1;padding:0 20px}.user-col{flex:0 0 auto;min-width:200px}.breadcrumb{display:flex;align-items:center;gap:8px}.breadcrumb-item{display:flex;align-items:center;color:#606266;font-size:14px}.breadcrumb-item:not(:last-child):after{content:"/";margin:0 8px;color:#c0c4cc}.dropdown{position:relative;display:inline-block}.dropdown-menu{position:absolute;top:100%;right:0;background-color:#fff;border:1px solid #e4e7ed;border-radius:4px;box-shadow:0 2px 12px rgba(0,0,0,.1);z-index:1000;min-width:120px;padding:4px 0}.dropdown-item{display:flex;align-items:center;padding:8px 16px;cursor:pointer;transition:background-color .3s;font-size:14px;color:#606266}.dropdown-item:hover{background-color:#f5f7fa}.dropdown-item .icon{margin-right:8px;font-size:14px}\n',document.head.appendChild(D),{setters:[function(e){r=e._,a=e.b,c=e.a,l=e.u,u=e.V,s=e.r,d=e.O,f=e.R,p=e.c,m=e.W,v=e.p,g=e.h,h=e.G,b=e.o,y=e.g,x=e.w,w=e.e,k=e.J,C=e.j,I=e.B,j=e.f,_=e.d,S=e.T,O=e.F,F=e.i,z=e.k,A=e.t,T=e.m,U=e.U,N=e.H,M=e.X,E=e.Y,G=e.A},function(e){R=e._},function(e){B=e.default},function(){},function(){},function(){},function(){}],execute:function(){/*! js-cookie v3.0.5 | MIT */function o(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)e[o]=n[o]}return e}var D=function e(t,n){function r(e,r,i){if("undefined"!=typeof document){"number"==typeof(i=o({},n,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var c in i)i[c]&&(a+="; "+c,!0!==i[c]&&(a+="="+i[c].split(";")[0]));return document.cookie=e+"="+t.write(r,e)+a}}return Object.create({set:r,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],o={},r=0;r<n.length;r++){var i=n[r].split("="),a=i.slice(1).join("=");try{var c=decodeURIComponent(i[0]);if(o[c]=t.read(a,c),e===c)break}catch(l){}}return e?o[e]:o}},remove:function(e,t){r(e,"",o({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,o({},this.attributes,t))},withConverter:function(t){return e(o({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),P={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},J={class:"header-row"},V={class:"header-col"},$={class:"header-cont"},H={class:"header-content pd-0"},W={class:"breadcrumb-col"},X={class:"breadcrumb"},Y={class:"user-col"},q={class:"right-box"},K={class:"dp-flex justify-content-center align-items height-full width-full"},Q={class:"header-avatar",style:{cursor:"pointer"}},Z={style:{"margin-right":"9px",color:"#252631"}},ee={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},te={key:0,class:"dropdown-menu"},ne=Object.assign({name:"Layout"},{setup:function(t){var o=a(),r=c(),ne=l(),oe=u(),re=s(!0),ie=s(!1),ae=s(!1),ce=s("7"),le=function(){document.body.clientWidth;ae.value=!1,ie.value=!1,re.value=!0};le();var ue=s(!1);d((function(){f.emit("collapse",re.value),f.emit("mobile",ae.value),f.on("reload",me),f.on("showLoading",(function(){ue.value=!0})),f.on("closeLoading",(function(){ue.value=!1})),window.onresize=function(){return le(),f.emit("collapse",re.value),void f.emit("mobile",ae.value)},o.loadingInstance&&o.loadingInstance.close()})),p((function(){return"dark"===o.sideMode?"#fff":"light"===o.sideMode?"#273444":o.baseColor}));var se=p((function(){return"dark"===o.sideMode?"#273444":"light"===o.sideMode?"#fff":o.sideMode})),de=p((function(){return ne.meta.matched})),fe=s(!0),pe=null,me=function(){var e=i(n().m((function e(){return n().w((function(e){for(;;)switch(e.n){case 0:pe&&window.clearTimeout(pe),pe=window.setTimeout(i(n().m((function e(){var t;return n().w((function(e){for(;;)switch(e.n){case 0:if(!ne.meta.keepAlive){e.n=2;break}return fe.value=!1,e.n=1,m();case 1:fe.value=!0,e.n=3;break;case 2:t=ne.meta.title,r.push({name:"Reload",params:{title:t}});case 3:return e.a(2)}}),e)}))),400);case 1:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),ve=s(!1),ge=s(!1),he=function(){re.value=!re.value,ie.value=!re.value,ve.value=!re.value,f.emit("collapse",re.value)},be=function(){ge.value=!ge.value},ye=function(){r.push({name:"person"})},xe=function(){var e=i(n().m((function e(){var t,r,a,c,l,u,d;return n().w((function(e){for(;;)switch(e.n){case 0:return document.location.protocol,document.location.host,t={action:1,msg:"",platform:document.location.hostname},r=s({}),a=s("ws://127.0.0.1:50001"),0!==(c=navigator.platform).indexOf("Mac")&&"MacIntel"!==c||(a.value="wss://127.0.0.1:50001"),l=function(){r.value=new WebSocket(a.value),r.value.onopen=i(n().m((function e(){return n().w((function(e){for(;;)switch(e.n){case 0:return console.log("socket连接成功"),e.n=1,u(JSON.stringify(t));case 1:return e.a(2)}}),e)}))),r.value.onmessage=function(){var e=i(n().m((function e(t){return n().w((function(e){for(;;)switch(e.n){case 0:return console.log(t),e.n=1,d();case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),r.value.onerror=function(){console.log("socket连接错误")}},u=function(){var e=i(n().m((function e(t){return n().w((function(e){for(;;)switch(e.n){case 0:return console.log(t,"0"),e.n=1,r.value.send(t);case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),d=function(){var e=i(n().m((function e(){return n().w((function(e){for(;;)switch(e.n){case 0:return console.log("socket断开链接"),e.n=1,r.value.close();case 1:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),console.log("asecagent://?web=".concat(JSON.stringify(t))),e.n=1,o.LoginOut();case 1:l(),D.remove("asce_sms");case 2:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();return v("day",ce),function(t,n){var r=g("base-aside"),i=g("router-view"),a=g("base-main"),c=g("base-container"),l=h("loading");return b(),y(c,{class:"layout-cont"},{default:x((function(){return[w("div",{class:k([[ie.value?"openside":"hideside",ae.value?"mobile":""],"layout-wrapper"])},[w("div",{class:k([[ve.value?"shadowBg":""],"shadow-overlay"]),onClick:n[0]||(n[0]=function(e){return ve.value=!ve.value,ie.value=!!re.value,void he()})},null,2),C(r,{class:"main-cont main-left gva-aside",collapsed:re.value},{default:x((function(){return[w("div",{class:k(["tilte",[ie.value?"openlogoimg":"hidelogoimg"]]),style:I({background:se.value})},[n[3]||(n[3]=w("img",{alt:"",class:"logoimg",src:R},null,-1)),j("          <div>"),j('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),j('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),j("          </div>")],6),C(B,{class:"aside"}),w("div",{class:"footer",style:I({background:se.value})},[w("div",{class:"menu-total",onClick:he},[re.value?(b(),_("svg",P,n[4]||(n[4]=[w("use",{"xlink:href":"#icon-expand"},null,-1)]))):(b(),_("svg",L,n[5]||(n[5]=[w("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)]})),_:1},8,["collapsed"]),j(" 分块滑动功能 "),C(a,{class:"main-cont main-right"},{default:x((function(){return[C(S,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:x((function(){return[w("div",{style:I({width:"calc(100% - ".concat(ae.value?"0px":re.value?"54px":"220px",")")}),class:"topfix"},[w("div",J,[w("div",V,[w("header",$,[w("div",H,[n[10]||(n[10]=w("div",{class:"header-menu-col",style:{"z-index":"100"}},[j('                      <div class="menu-total" @click="totalCollapse">'),j('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),j('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),j("                      </div>")],-1)),w("div",W,[w("nav",X,[(b(!0),_(O,null,F(de.value.slice(1,de.value.length),(function(t){return b(),_("div",{key:t.path,class:"breadcrumb-item"},[z(A(T(U)(t.meta.topTitle||"",T(ne)))+" ",1),"总览"===t.meta.title?N((b(),_("select",{key:0,"onUpdate:modelValue":n[1]||(n[1]=function(e){return ce.value=e}),class:"day-select form-select"},e(n[6]||(n[6]=[w("option",{value:"7"},"最近7天",-1),w("option",{value:"30"},"最近30天",-1),w("option",{value:"90"},"最近90天",-1)])),512)),[[M,ce.value]]):j("v-if",!0)])})),128))])]),w("div",Y,[w("div",q,[j("                        <Search />"),w("div",{class:"dropdown",onClick:be},[w("div",K,[w("span",Q,[j(" 展示当前登录用户名 "),w("span",Z,A(T(o).userInfo.displayName?T(o).userInfo.displayName:T(o).userInfo.name),1),(b(),_("svg",ee,n[7]||(n[7]=[w("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),ge.value?(b(),_("div",te,[j(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),w("div",{class:"dropdown-item",onClick:ye},n[8]||(n[8]=[w("svg",{class:"icon","aria-hidden":"true"},[w("use",{"xlink:href":"#icon-avatar"})],-1),z(" 个人信息 ")])),w("div",{class:"dropdown-item",onClick:n[2]||(n[2]=function(e){return xe()})},n[9]||(n[9]=[w("svg",{class:"icon","aria-hidden":"true"},[w("use",{"xlink:href":"#icon-reading-lamp"})],-1),z(" 登 出 ")]))])):j("v-if",!0)]),j('                        <base-button type="text"'),j('                                   class="iconfont icon-rizhi1"'),j('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),j('                                   @click="toLog"'),j("                        >日志中心"),j("                        </base-button>")])])])])])]),j(" 当前面包屑用路由自动生成可根据需求修改 "),j('\r\n            :to="{ path: item.path }" 暂时注释不用'),j('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)]})),_:1}),fe.value?N((b(),y(i,{key:0,"element-loading-text":"正在加载中",class:"admin-box"},{default:x((function(e){var t=e.Component;return[w("div",null,[C(S,{mode:"out-in",name:"el-fade-in-linear"},{default:x((function(){return[(b(),y(E,{include:T(oe).keepAliveRouters},[(b(),y(G(t)))],1032,["include"]))]})),_:2},1024)])]})),_:1})),[[l,ue.value]]):j("v-if",!0),j("        <BottomInfo />"),j("        <setting />")]})),_:1})],2)]})),_:1})}}});t("default",r(ne,[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]))}}}))}();
