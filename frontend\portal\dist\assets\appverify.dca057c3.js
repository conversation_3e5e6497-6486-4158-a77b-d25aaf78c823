/*! 
 Build based on gin-vue-admin 
 Time : 1749732095000 */
import{_ as e,r as a,u as t,h as i,o as l,d as s,e as n,k as r,t as d,j as u,w as o,g as p,f as v,s as c,n as y,M as f,v as g}from"./index.8e61b3f9.js";const h={style:{width:"100%",height:"100%",background:"#FFFFFF"}},x={style:{width:"442px",height:"175px","padding-left":"38.5%","padding-top":"21%","text-align":"center"}},m={style:{"font-size":"24px",display:"flex","justify-content":"center","align-items":"center"}},_={class:"icon",style:{"margin-right":"10px","font-size":"14px",width:"24px",height:"24px"},"aria-hidden":"true"},w={key:0,style:{"margin-top":"20px"}},b={style:{float:"left","margin-left":"34px"}},k={key:1,style:{"margin-top":"20px"}},q=e(Object.assign({name:"Appverify"},{setup(e){const q=a(),F=a({}),z=t();(async()=>{var e;const a={user_id:z.query.user_id,idp_id:z.query.idp_id},t=await c(a);200===t.status&&(F.value=t.data.data,(null==(e=F.value)?void 0:e.notPhone)||await P())})();const j=a(60);let C;const N=()=>{clearInterval(C)},P=async()=>{const e={uniq_key:F.value.uniqKey,idp_id:z.query.idp_id},a=await y(e);200===a.status&&-1!==a.data.code?(j.value=60,C=setInterval((()=>{j.value--,0===j.value&&N()}),1e3)):(f({showClose:!0,message:a.data.msg,type:"error"}),j.value=0)},V=async()=>{const e={uniq_key:F.value.uniqKey,auth_code:q.value,user_name:F.value.userName,idp_id:z.query.idp_id,redirect_uri:"app_redirect",grant_type:"implicit",client_id:"client_portal"},a=await g(e);200===a.status&&-1!==a.data.code?location.href=z.query.redirect_url:f({showClose:!0,message:a.data.msg,type:"error"})};return(e,a)=>{var t,c,y,f;const g=i("base-input"),z=i("base-button");return l(),s("div",h,[n("div",x,[n("div",m,[(l(),s("svg",_,a[1]||(a[1]=[r("--\x3e "),n("use",{"xlink:href":"#icon-shuoming2"},null,-1)]))),a[2]||(a[2]=r("--\x3e 该应用需通过安全认证后才可继续访问 "))]),!1===(null==(t=F.value)?void 0:t.notPhone)?(l(),s("div",w,[n("span",b,"验证码已发送至您的账号("+d(null==(c=F.value)?void 0:c.userName)+")关联的手机，请注意查收",1),u(g,{modelValue:q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>q.value=e),placeholder:"请输入短信验证码",style:{float:"left","margin-left":"34px","font-size":"12px","margin-top":"12px",width:"258px",height:"32px"},class:"input-with-select"},null,8,["modelValue"]),u(z,{style:{"border-radius":"4px","font-size":"12px",float:"left","margin-top":"12px",position:"relative","margin-left":"10px",width:"92px",height:"32px"},disabled:j.value>0,onClick:P},{default:o((()=>[r("重新发送 "+d(j.value>0?`(${j.value}秒)`:""),1)])),_:1},8,["disabled"])])):(l(),s("div",k,[n("span",null,"您的账号("+d(null==(y=F.value)?void 0:y.userName)+")未关联手机号码，请联系管理员",1)])),!1===(null==(f=F.value)?void 0:f.notPhone)?(l(),p(z,{key:2,type:"primary",size:"large",style:{float:"left","margin-left":"34px",height:"44px","margin-top":"14px",width:"365px"},disabled:!q.value,onClick:V},{default:o((()=>a[3]||(a[3]=[r("确 定 ")]))),_:1,__:[3]},8,["disabled"])):v("v-if",!0)])])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/resource/appverify.vue"]]);export{q as default};
