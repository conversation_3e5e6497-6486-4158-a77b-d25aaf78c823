/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{u as O,a as R,r as o,b as T,c as x,p as l,o as r,d,e as t,f as k,g as D,L as B}from"./index.d0594432.js";import F from"./secondaryAuth.75febeaf.js";import"./verifyCode.5ac023d8.js";const J="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAATCAYAAACKsM07AAAAAXNSR0IArs4c6QAAAq5JREFUSEudk11IU2EYx//PcZtmZlREiNgHlNsZqEHmWUYXUhdFIHUh0U3eFNGneGZRXe2uiO2MLKPoqrqRLvqEIvoES3fMGw13Vkr0odCNgmVsuZ3zxFk5t7NNWS8ceJ/n/J//733eD8I8wy2rhxncCcCRRzZDRG3hgHQtnw3l9W/hIrFK/QKgcr5FABjXyqNr4WtK5NLlBYhedQ+Y76UXMfDQjAlozjAj2qsFpPsFAVyy+oTAO1NFjBEt6Kk2Y1EOfQCQnJuDgacRxTOnTSPl7MDZoa4jg0cJEOZM6HJEkU6asUsOXSIgOf8HMHSObxgJbvtk7SInwN0eOs+EMxbxbk3xPDZz1d7+zUWs9wBUPKshxoVw0HN2QYC75Y6Dq9Z8BXhVujhuW1Q1erFubDa3yTdQGp+2l83GuhGrGFakwQUBoty3D6DuHAcWB2B+OQcD00UC7R/2Sy/TBVlbJMrqc4C35zOaN894rQU9TQsAQiqAhv8CAKyT4f4YaIykzsZq5Dz9Zgnptmo2KP8jBGCDUaET3SZgaYYHUVALSHIWYP2JkWK7faKbAFd6AQM9P9loGws2Rq2LEWX1KsBHLPnJaHm08rOvKWbmU6sUZfUAwDetJiwIzRF/w6NcW+aU+5oF0IPsf9SqKdItK+AtwI2ZYvptF0pWDPnrfuUC1HYMLo4bsQmAU+/hr456NUXamgK42gdqiBJD2Sb8QlO27IDvlc05VbqRGPYMjU0oJtbvgrEsq3O21UaC9e+TWyTKoSsAjllFBJwKKx6/6O3vAhtHC7xZXZriOU5mmzNGbJzAmbfBdBP0Gq3sWVj8ses7wCsLATBoyiGUVJLoVQ+C+UaOg/qmKdJqd3tvA5Ngvo3CB9EhEuXQOwD1luoEmM7FE8s77Y7J62BuLdw9WTHwB+of7onYTsUzAAAAAElFTkSuQmCC",W=""+new URL("login_building.7b8b4335.png",import.meta.url).href;const Y={class:"login-page"},Z={class:"content"},H={class:"right-panel"},L={key:0,class:"auth-status"},G={class:"status-icon"},V={class:"icon","aria-hidden":"true",style:{height:"48px",width:"48px",color:"#0082ef"}},M={key:1,class:"auth-waiting"},j={class:"waiting-icon"},z={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},$={name:"Status"},oe=Object.assign($,{setup(X){const C=O(),b=R(),h=o({});let{code:A,state:a}=C.query;const{auth_type:I,redirect_url:c,type:v,wp:i}=C.query,m=window.location.host,f=window.location.protocol,N=T(),y=o(null),p=o(!1),K=o([]),g=o(!1),w=o(""),U=o(""),_=o(Array.isArray(a)?a[0]:a),S=o(""),u=o("phone"),q=o(!0),P=x(()=>[{type:"sms",name:"\u77ED\u4FE1\u9A8C\u8BC1",icon:"duanxin",available:u.value==="phone"},{type:"email",name:"\u90AE\u7BB1\u9A8C\u8BC1",icon:"email",available:u.value==="email"}]),E=async s=>{y.value=B.service({fullscreen:!0,text:"\u8BA4\u8BC1\u6210\u529F\uFF0C\u6B63\u5728\u8DF3\u8F6C..."});try{let e=c||"/";if(s.clientParams){const n=new URLSearchParams;n.set("type",s.clientParams.type),s.clientParams.wp&&n.set("wp",s.clientParams.wp),e+=(e.includes("?")?"&":"?")+n.toString()}window.location.href=e}finally{y.value?.close()}},Q=()=>{p.value=!1,g.value=!1;const s=new URLSearchParams;s.set("idp_id",Array.isArray(a)?a[0]:a),c&&s.set("redirect",encodeURIComponent(c)),v==="client"&&(s.set("type","client"),i&&s.set("wp",i));const e=`/login?${s.toString()}`;b.push(e)};return(async()=>{y.value=B.service({fullscreen:!0,text:"\u767B\u5F55\u4E2D\uFF0C\u8BF7\u7A0D\u5019..."});try{const s={clientId:"client_portal",grantType:"implicit",redirect_uri:`${f}//${m}/#/status`,idpId:Array.isArray(a)?a[0]:a,authWeb:{authWebCode:Array.isArray(A)?A[0]:A}},e=await N.LoginIn(s,I,_.value);if(e.code!==-1)e.isSecondary&&(g.value=e.isSecondary,K.value=e.secondary,U.value=e.secondary[0].id,w.value=e.uniqKey,S.value=e.userName,u.value=e.contactType,q.value=e.hasContactInfo||!1,h.value.uniqKey=e.uniqKey,h.value.name=e.secondary[0].name,h.value.notPhone=e.notPhone,p.value=!0);else{let n=`${f}//${m}/#/login?idp_id=${Array.isArray(a)?a[0]:a}`;c&&(n+=`&redirect=${c}`),v==="client"&&(n+="&type=client",i&&(n+=`&wp=${i}`)),location.href=n}}catch(s){console.error("\u767B\u5F55\u5904\u7406\u5931\u8D25:",s);let e=`${f}//${m}/#/login?idp_id=${Array.isArray(a)?a[0]:a}`;v==="client"&&(e+="&type=client",i&&(e+=`&wp=${i}`)),location.href=e}finally{y.value?.close()}})(),x(()=>K.value.filter(s=>s.id!==U.value)),l("userName",S),l("isSecondary",g),l("contactType",u),l("hasContactInfo",q),l("uniqKey",w),l("last_id",_),(s,e)=>(r(),d("div",Y,[e[8]||(e[8]=t("div",{class:"header"},[t("img",{src:J,alt:"\u516C\u53F8logo",class:"logo"}),t("span",{class:"header-text"},"ASec\u5B89\u5168\u5E73\u53F0")],-1)),t("div",Z,[e[7]||(e[7]=t("div",{class:"left-panel"},[t("img",{src:W,alt:"\u5BA3\u4F20\u56FE",class:"image"})],-1)),t("div",H,[p.value?(r(),d("div",M,[t("div",j,[(r(),d("svg",z,e[4]||(e[4]=[t("use",{"xlink:href":"#icon-auth-verify_code"},null,-1)])))]),e[5]||(e[5]=t("h4",{class:"waiting-title"},"\u9700\u8981\u8FDB\u884C\u5B89\u5168\u9A8C\u8BC1",-1)),e[6]||(e[6]=t("p",{class:"waiting-message"},"\u8BF7\u5B8C\u6210\u4E8C\u6B21\u8EAB\u4EFD\u9A8C\u8BC1\u4EE5\u786E\u4FDD\u8D26\u6237\u5B89\u5168",-1))])):(r(),d("div",L,[t("div",G,[(r(),d("svg",V,e[0]||(e[0]=[t("use",{"xlink:href":"#icon-auth-qiyewx"},null,-1)])))]),e[1]||(e[1]=t("h3",{class:"status-title"},"\u6B63\u5728\u767B\u5F55",-1)),e[2]||(e[2]=t("p",{class:"status-message"},"\u6B63\u5728\u5904\u7406\u4FE1\u606F...",-1)),e[3]||(e[3]=t("div",{class:"loading-dots"},[t("span"),t("span"),t("span")],-1))]))])]),p.value?(r(),k(F,{key:0,"auth-info":{uniqKey:w.value,contactType:u.value,hasContactInfo:q.value},"auth-id":U.value,"user-name":S.value,"last-id":_.value,"auth-methods":P.value,onVerificationSuccess:E,onCancel:Q},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):D("",!0)]))}});export{oe as default};
