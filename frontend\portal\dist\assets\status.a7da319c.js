/*! 
 Build based on gin-vue-admin 
 Time : 1749610601000 */
import{u as a,a as e,r as s,b as t,c as l,p as i,o as n,d as o,e as r,f as c,g as u,L as d}from"./index.d0594432.js";import y from"./secondaryAuth.75febeaf.js";import"./verifyCode.5ac023d8.js";const A=""+new URL("login_building.7b8b4335.png",import.meta.url).href,h={class:"login-page"},v={class:"content"},p={class:"right-panel"},g={key:0,class:"auth-status"},m={class:"status-icon"},w={class:"icon","aria-hidden":"true",style:{height:"48px",width:"48px",color:"#0082ef"}},f={key:1,class:"auth-waiting"},U={class:"waiting-icon"},q={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},C=Object.assign({name:"Status"},{setup(C){const K=a(),S=e(),b=s({});let{code:x,state:I}=K.query;const{auth_type:P,redirect_url:B,type:E,wp:Q}=K.query,N=window.location.host,O=window.location.protocol,T=t(),D=s(null),F=s(!1),J=s([]),R=s(!1),W=s(""),Y=s(""),Z=s(Array.isArray(I)?I[0]:I),k=s(""),H=s("phone"),j=s(!0),G=l((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===H.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===H.value}])),L=async a=>{var e;D.value=d.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let e=B||"/";if(a.clientParams){const s=new URLSearchParams;s.set("type",a.clientParams.type),a.clientParams.wp&&s.set("wp",a.clientParams.wp),e+=(e.includes("?")?"&":"?")+s.toString()}window.location.href=e}finally{null==(e=D.value)||e.close()}},M=()=>{F.value=!1,R.value=!1;const a=new URLSearchParams;a.set("idp_id",Array.isArray(I)?I[0]:I),B&&a.set("redirect",encodeURIComponent(B)),"client"===E&&(a.set("type","client"),Q&&a.set("wp",Q));const e=`/login?${a.toString()}`;S.push(e)};return(async()=>{var a;D.value=d.service({fullscreen:!0,text:"登录中，请稍候..."});try{const a={clientId:"client_portal",grantType:"implicit",redirect_uri:`${O}//${N}/#/status`,idpId:Array.isArray(I)?I[0]:I,authWeb:{authWebCode:Array.isArray(x)?x[0]:x}},e=await T.LoginIn(a,P,Z.value);if(-1!==e.code)e.isSecondary&&(R.value=e.isSecondary,J.value=e.secondary,Y.value=e.secondary[0].id,W.value=e.uniqKey,k.value=e.userName,H.value=e.contactType,j.value=e.hasContactInfo||!1,b.value.uniqKey=e.uniqKey,b.value.name=e.secondary[0].name,b.value.notPhone=e.notPhone,F.value=!0);else{let a=`${O}//${N}/#/login?idp_id=${Array.isArray(I)?I[0]:I}`;B&&(a+=`&redirect=${B}`),"client"===E&&(a+="&type=client",Q&&(a+=`&wp=${Q}`)),location.href=a}}catch(e){console.error("登录处理失败:",e);let a=`${O}//${N}/#/login?idp_id=${Array.isArray(I)?I[0]:I}`;"client"===E&&(a+="&type=client",Q&&(a+=`&wp=${Q}`)),location.href=a}finally{null==(a=D.value)||a.close()}})(),l((()=>J.value.filter((a=>a.id!==Y.value)))),i("userName",k),i("isSecondary",R),i("contactType",H),i("hasContactInfo",j),i("uniqKey",W),i("last_id",Z),(a,e)=>(n(),o("div",h,[e[8]||(e[8]=r("div",{class:"header"},[r("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAATCAYAAACKsM07AAAAAXNSR0IArs4c6QAAAq5JREFUSEudk11IU2EYx//PcZtmZlREiNgHlNsZqEHmWUYXUhdFIHUh0U3eFNGneGZRXe2uiO2MLKPoqrqRLvqEIvoES3fMGw13Vkr0odCNgmVsuZ3zxFk5t7NNWS8ceJ/n/J//733eD8I8wy2rhxncCcCRRzZDRG3hgHQtnw3l9W/hIrFK/QKgcr5FABjXyqNr4WtK5NLlBYhedQ+Y76UXMfDQjAlozjAj2qsFpPsFAVyy+oTAO1NFjBEt6Kk2Y1EOfQCQnJuDgacRxTOnTSPl7MDZoa4jg0cJEOZM6HJEkU6asUsOXSIgOf8HMHSObxgJbvtk7SInwN0eOs+EMxbxbk3xPDZz1d7+zUWs9wBUPKshxoVw0HN2QYC75Y6Dq9Z8BXhVujhuW1Q1erFubDa3yTdQGp+2l83GuhGrGFakwQUBoty3D6DuHAcWB2B+OQcD00UC7R/2Sy/TBVlbJMrqc4C35zOaN894rQU9TQsAQiqAhv8CAKyT4f4YaIykzsZq5Dz9Zgnptmo2KP8jBGCDUaET3SZgaYYHUVALSHIWYP2JkWK7faKbAFd6AQM9P9loGws2Rq2LEWX1KsBHLPnJaHm08rOvKWbmU6sUZfUAwDetJiwIzRF/w6NcW+aU+5oF0IPsf9SqKdItK+AtwI2ZYvptF0pWDPnrfuUC1HYMLo4bsQmAU+/hr456NUXamgK42gdqiBJD2Sb8QlO27IDvlc05VbqRGPYMjU0oJtbvgrEsq3O21UaC9e+TWyTKoSsAjllFBJwKKx6/6O3vAhtHC7xZXZriOU5mmzNGbJzAmbfBdBP0Gq3sWVj8ses7wCsLATBoyiGUVJLoVQ+C+UaOg/qmKdJqd3tvA5Ngvo3CB9EhEuXQOwD1luoEmM7FE8s77Y7J62BuLdw9WTHwB+of7onYTsUzAAAAAElFTkSuQmCC",alt:"公司logo",class:"logo"}),r("span",{class:"header-text"},"ASec安全平台")],-1)),r("div",v,[e[7]||(e[7]=r("div",{class:"left-panel"},[r("img",{src:A,alt:"宣传图",class:"image"})],-1)),r("div",p,[F.value?(n(),o("div",f,[r("div",U,[(n(),o("svg",q,e[4]||(e[4]=[r("use",{"xlink:href":"#icon-auth-verify_code"},null,-1)])))]),e[5]||(e[5]=r("h4",{class:"waiting-title"},"需要进行安全验证",-1)),e[6]||(e[6]=r("p",{class:"waiting-message"},"请完成二次身份验证以确保账户安全",-1))])):(n(),o("div",g,[r("div",m,[(n(),o("svg",w,e[0]||(e[0]=[r("use",{"xlink:href":"#icon-auth-qiyewx"},null,-1)])))]),e[1]||(e[1]=r("h3",{class:"status-title"},"正在登录",-1)),e[2]||(e[2]=r("p",{class:"status-message"},"正在处理信息...",-1)),e[3]||(e[3]=r("div",{class:"loading-dots"},[r("span"),r("span"),r("span")],-1))]))])]),F.value?(n(),c(y,{key:0,"auth-info":{uniqKey:W.value,contactType:H.value,hasContactInfo:j.value},"auth-id":Y.value,"user-name":k.value,"last-id":Z.value,"auth-methods":G.value,onVerificationSuccess:L,onCancel:M},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):u("",!0)]))}});export{C as default};
