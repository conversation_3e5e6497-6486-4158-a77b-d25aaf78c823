/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{_ as a,h as s,o as e,d as t,f as o,j as r,e as i,g as l}from"./index.f6c71253.js";import n from"./header.a1179103.js";import u from"./menu.73a1b64a.js";import"./ASD.492c8837.js";const c={class:"layout-page"},m={class:"layout-wrap"},d={id:"layoutMain",class:"layout-main"},f=a(Object.assign({name:"Client"},{setup:a=>(a,f)=>{const p=s("router-view");return e(),t("div",c,[o("公共顶部菜单-"),r(n),i("div",m,[o("公共侧边栏菜单"),r(u),i("div",d,[o("主流程路由渲染点"),(e(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{f as default};
