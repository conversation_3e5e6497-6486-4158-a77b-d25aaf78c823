/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
import{_ as e,h as t,o as i,d as s,g as n,w as a,k as o,u as r}from"./index.4f1b43e7.js";const c={class:"sso-warpper"},d=["src"];const u={props:{auth_id:{type:String,default:function(){return""}},auth_info:{type:Object,default:function(){return{}}}},data:()=>({iframeSrc:"",isListen:!1,isThirdBack:!1,isAutoLogin:!1,isListenShowApp:!1,route:r(),loading:!1}),computed:{isForceBrowser(){return"cas"===this.auth_info.authType?1===parseInt(this.auth_info.casOpenType):1===parseInt(this.auth_info.oauth2OpenType)}},watch:{auth_id:{handler(e){this.init()},deep:!0,immediate:!0}},mounted(){},destroyed(){this.unListenGoBack(),this.clearLoading(),this.isListen&&this.removeEvent(window,"message",this.listenHandle)},methods:{init(){this.isForceBrowser||this.clickSubmit()},async clickSubmit(){const e=function(e=64){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";let i="";for(let s=0;s<e;s++)i+=t.charAt(Math.floor(66*Math.random()));return i}();sessionStorage.setItem("oauth2_code_verifier",e);const t=await async function(e){const t=(new TextEncoder).encode(e),i=await window.crypto.subtle.digest("SHA-256",t);return btoa(String.fromCharCode(...new Uint8Array(i))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}(e);this.submit({code_challenge:encodeURIComponent(t),code_challenge_method:"S256"})},async submit(e){var t,i;let s="/auth/login/v1/callback/"+this.auth_id;if(e){const n=[];for(const t in e)n.push(t+"="+encodeURIComponent(e[t]));if(s+="?"+n.join("&"),null==(t=this.route.query)?void 0:t.redirect){const e=null==(i=this.route.query)?void 0:i.redirect;s+="&redirect=/"+encodeURIComponent(e)}}if(this.isForceBrowser)window.location.href=s;else{if(this.isListen)return void(s.includes("code=")||s.includes("token=")||s.includes("auth_success=true")?window.location.href=s:this.iframeSrc=s);this.iframeSrc=s,console.log("iframe初始地址",this.iframeSrc),this.isListen=!0,this.addEvent(window,"message",this.listenHandle)}},async listenHandle(e){console.log("sso触发监听");const t=e.data.event;this.isThirdAppWakeup(t)?this.wakeupApp(e):e.data&&this.submit(e.data)},addEvent(e,t,i){e.addEventListener?e.addEventListener(t,i,!1):e.attachEvent&&e.attachEvent("on"+t,(function(){i.call(e,window.event)}))},removeEvent(e,t,i){e.removeEventListener?e.removeEventListener(t,i):e.detachEvent&&e.detachEvent("on"+t,i)},isThirdAppWakeup:e=>"wakeup-app"===e,wakeupApp(e){const t=e.data.params.url;t&&(window.location.href=t)},clearLoading(){this.loading&&(this.loading.clear(),this.loading=!1)}}},h=e(Object.assign(u,{__name:"oauth2",setup:e=>(e,r)=>{const u=t("base-button");return i(),s("div",c,[e.isForceBrowser?(i(),n(u,{key:0,type:"primary",size:"large",class:"login_submit_button",onClick:e.clickSubmit},{default:a((()=>r[0]||(r[0]=[o("授权登录")]))),_:1,__:[0]},8,["onClick"])):(i(),s("iframe",{key:1,src:e.iframeSrc,frameborder:"0",class:"sso-iframe"},null,8,d))])}}),[["__scopeId","data-v-5a770c39"],["__file","D:/asec-platform/frontend/portal/src/view/login/oauth2/oauth2.vue"]]);export{h as default};
