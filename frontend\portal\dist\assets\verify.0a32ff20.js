/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{b as u,q as p,o as d,d as f}from"./index.d0594432.js";const h={name:"Verify"},w=Object.assign(h,{setup(m){const s=location.href.split("?")[1],c=new URLSearchParams(s),e=Object.fromEntries(c.entries()),n=u(),a=document.location.protocol+"//"+document.location.host,o=new URLSearchParams;e.type==="client"&&(o.set("type","client"),e.wp&&o.set("wp",e.wp));const i={method:"GET",url:`${a}/auth/user/v1/redirect_verify?redirect_url=${e.redirect_url}`,headers:{Accept:"application/json, text/plain, */*",Authorization:`${n.token.tokenType} ${n.token.accessToken}`}};return p.request(i).then(function(t){if(t.status===200){let r=t.data.url;if(o.toString()){const l=r.includes("?")?"&":"?";r+=l+o.toString()}window.location.href=r}}).catch(function(t){console.error(t)}),(t,r)=>(d(),f("div"))}});export{w as default};
