/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{_ as e,u as r,a,b as t,r as s,I as i,p as c,f as l,L as o,M as n}from"./index.44d6e232.js";const u=e(Object.assign({name:"WxOAuthCallback"},{setup(e){const u=r(),p=a(),y=t(),{code:d,state:_,auth_type:f,redirect_url:h}=u.query,m=s(Array.isArray(_)?_[0]:_),x=s("");return i((async()=>{const e=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(e,"qiyewx_oauth",m.value)?await p.push({name:"verify",query:{redirect_url:h}}):n.error("登录失败，请重试")}catch(r){console.error("登录过程出错:",r),n.error("登录过程出错，请重试")}finally{e.close()}})),c("userName",x),(e,r)=>l(" 空模板，因为所有逻辑都在 script 中处理 ")}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wx_oauth_callback.vue"]]);export{u as default};
