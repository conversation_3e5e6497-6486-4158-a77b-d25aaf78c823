/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function n(e){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?t(Object(i),!0).forEach((function(t){o(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):t(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function o(t,n,o){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var i=o.call(t,n||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o,t}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function l(n,o,i,a){var l=o&&o.prototype instanceof c?o:c,s=Object.create(l.prototype);return r(s,"_invoke",function(n,o,i){var r,a,l,c=0,s=i||[],u=!1,p={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return r=t,a=0,l=e,p.n=n,d}};function h(n,o){for(a=n,l=o,t=0;!u&&c&&!i&&t<s.length;t++){var i,r=s[t],h=p.p,f=r[2];n>3?(i=f===o)&&(l=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=e):r[0]<=h&&((i=n<2&&h<r[1])?(a=0,p.v=o,p.n=r[1]):h<f&&(i=n<3||r[0]>o||o>f)&&(r[4]=n,r[5]=o,p.n=f,a=0))}if(i||n>1)return d;throw u=!0,o}return function(i,s,f){if(c>1)throw TypeError("Generator is already running");for(u&&1===s&&h(s,f),a=s,l=f;(t=a<2?e:l)||!u;){r||(a?a<3?(a>1&&(p.n=-1),h(a,l)):p.n=l:p.v=l);try{if(c=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(u=p.n<0)?l:n.call(o,p))!==d)break}catch(t){r=e,a=1,l=t}finally{c=1}}return{value:t,done:u}}}(n,i,a),!0),s}var d={};function c(){}function s(){}function u(){}t=Object.getPrototypeOf;var p=[][o]?t(t([][o]())):(r(t={},o,(function(){return this})),t),h=u.prototype=c.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,r(e,a,"GeneratorFunction")),e.prototype=Object.create(h),e}return s.prototype=u,r(h,"constructor",u),r(u,"constructor",s),s.displayName="GeneratorFunction",r(u,a,"GeneratorFunction"),r(h),r(h,a,"Generator"),r(h,o,(function(){return this})),r(h,"toString",(function(){return"[object Generator]"})),(i=function(){return{w:l,m:f}})()}function r(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}r=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{var a=function(t,n){r(e,t,(function(e){return this._invoke(t,n,e)}))};a("next",0),a("throw",1),a("return",2)}},r(e,t,n,o)}function a(e,t,n,o,i,r,a){try{var l=e[r](a),d=l.value}catch(e){return void n(e)}l.done?t(d):Promise.resolve(d).then(o,i)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function l(e){a(r,o,i,l,d,"next",e)}function d(e){a(r,o,i,l,d,"throw",e)}l(void 0)}))}}System.register(["./ASD-legacy.b6ffb1bc.js","./index-legacy.e6617eb1.js"],(function(e,t){"use strict";var o,r,a,d,c,s,u,p,h,f,g=document.createElement("style");return g.textContent='@charset "UTF-8";.layout-header[data-v-3a119553]{height:42px;display:flex;justify-content:space-between;align-items:center;background:linear-gradient(315deg,#536CE6,#647be9);box-shadow:0 2px 6px rgba(46,60,128,.2);color:#fff}.layout-header .header-title[data-v-3a119553]{line-height:42px;font-size:18px;font-weight:500}.layout-header .header-logo[data-v-3a119553]{height:42px;display:flex;align-items:center}.layout-header .header-logo img[data-v-3a119553]{max-width:79px;max-height:28px}.layout-header #u-electron-drag[data-v-3a119553]{display:flex;flex:1;height:100%;-webkit-app-region:drag}.layout-header .right-wrapper[data-v-3a119553]{display:flex;align-items:center;height:100%}.layout-header .right-wrapper>li[data-v-3a119553]:hover{background:#4256b8}.layout-header .right-wrapper .user-divider[data-v-3a119553]{width:1px;height:14px;margin-left:16px;margin-right:16px;background:#e6e6e6}.layout-header .right-wrapper .user-info[data-v-3a119553]{display:flex;align-items:center;height:42px;padding:0 14px;cursor:pointer}.layout-header .right-wrapper .user-info .user-face[data-v-3a119553]{width:32px;height:32px;border-radius:50%;overflow:hidden;margin-right:6px}.layout-header .right-wrapper .user-info .user-face img[data-v-3a119553]{width:100%;height:100%;display:block}.layout-header .right-wrapper .user-info .user-name[data-v-3a119553]{color:#fff;display:inline-block;max-width:100px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;word-break:break-all}.layout-header .right-wrapper .set-icon-wrapper[data-v-3a119553],.layout-header .right-wrapper .menu-msg[data-v-3a119553]{width:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;height:42px;position:relative}.layout-header .right-wrapper .set-icon-wrapper .icon-shezhi[data-v-3a119553],.layout-header .right-wrapper .menu-msg .icon-shezhi[data-v-3a119553]{color:#bac4f5;font-size:18px}.layout-header .right-wrapper .is-message[data-v-3a119553]:after{content:"";position:absolute;top:17px;right:13px;width:8px;height:8px;border-radius:50%;background:#FF4D4D}.layout-header .right-wrapper .window-operate[data-v-3a119553],.layout-header .right-wrapper #ui-headNav-header-li-msg_list[data-v-3a119553]{width:24px;height:100%;margin-left:4px;filter:brightness(1.5);display:flex;align-items:center;justify-content:center;cursor:pointer}.layout-header .right-wrapper .window-operate .iconfont[data-v-3a119553],.layout-header .right-wrapper #ui-headNav-header-li-msg_list .iconfont[data-v-3a119553]{color:#bac4f5;font-size:16px}.count-title[data-v-3a119553]{color:#686e84}.count-title i[data-v-3a119553]{font-style:normal;color:#3c404d}.el-dropdown-menu.header-count-menu[data-v-3a119553] .el-dropdown-menu__item{padding-left:40px;position:relative}.el-dropdown-menu.header-count-menu[data-v-3a119553] .el-dropdown-menu__item i{position:absolute;top:0;bottom:0;left:16px;font-size:14px}.el-dropdown-menu.header-count-menu[data-v-3a119553] .el-dropdown-menu__item .icon-qianduanruwang-yuyanqiehuan{font-size:15px}.el-dropdown-menu.header-count-menu[data-v-3a119553] .el-dropdown-menu__item .icon-qiehuanzhanghu1,.el-dropdown-menu.header-count-menu[data-v-3a119553] .el-dropdown-menu__item .icon-jisuruwang,.el-dropdown-menu.header-count-menu[data-v-3a119553] .el-dropdown-menu__item .icon-neiwaiwangqiehuan{font-size:16px}.s-title[data-v-3a119553]{margin-top:18px;margin-left:18px;font-size:13px;line-height:18px;font-weight:500;color:#3c404d}.s-content[data-v-3a119553]{padding:24px 32px 29px;font-size:13px;line-height:18px}.s-content .s-text[data-v-3a119553]{color:#686e84}.change-reg-info[data-v-3a119553]{padding-left:8px;line-height:20px;font-size:14px;font-weight:500;color:#3c404d}body .el-dialog-ip-box{width:260px}body .el-dialog-ip-box .el-message-box__content{padding:20px 15px}.s-content .el-radio{margin-right:13px}.s-content .el-radio .el-radio__label{padding-left:8px;font-size:13px;color:#3c404d;line-height:18px}#ip-info-dialog .ip-content{margin-top:24px;margin-bottom:24px;padding:0 24px;line-height:20px;font-size:14px;color:#3c404d}#ip-info-dialog .netcard-list{margin-top:16px;padding:0 24px}#ip-info-dialog .netcard-list li{display:flex;align-items:center;line-height:20px;font-size:14px;color:#3c404d;margin-bottom:10px}#ip-info-dialog .netcard-list li:last-child{margin-bottom:24px}#ip-info-dialog .netcard-list li i{font-size:16px;margin-left:16px}#ip-info-dialog .netcard-list li .icon-lianjie{color:#29cc88}#ip-info-dialog .netcard-list li .icon-duankailianjie{color:#e65353}#ip-info-dialog .el-dialog__footer button{height:40px;line-height:40px;border-bottom-right-radius:4px}.loginout-m-confirm-dialog .v-header{line-height:45px;border-bottom:1px solid #EDEDF1;padding:0 24px;font-size:16px;color:#3c404d}.loginout-m-confirm-dialog .v-header i{font-size:16px;color:#ffbf00;margin-right:6px;font-weight:400}.loginout-m-confirm-dialog .outline-tips{padding:24px;line-height:20px;color:#3c404d;font-size:14px}\n',document.head.appendChild(g),{setters:[function(e){o=e._},function(e){r=e._,a=e.h,d=e.o,c=e.d,s=e.e,u=e.j,p=e.w,h=e.t,f=e.k}],execute:function(){var g=""+new URL("avator.bd83723a.png",t.meta.url).href,m={name:"ClientHeader",data:function(){return{drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1}},computed:{isAccess:function(){return!1}},watch:{userId:function(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted:function(){},beforeDestroy:function(){},methods:{minimizeWnd:function(){agentApi.minimizeWnd()},maximizeWndOrNot:function(){this.isMaxWindow?(agentApi.normalnizeWnd(),this.isMaxWindow=!1):(agentApi.maximizeWnd(),this.isMaxWindow=!0)},dropdownVisiHandle:function(){},closeWnd:function(){if(TestQtModule("UIPlatform_Window","HideWnd"))EventBus.$emit("closeAssui"),this.$nextTick((function(){agentApi.hideWend()}));else try{this.$ipcSend("UIPlatform_Window","TerminateWnd")}catch(e){this.$message.error("操作失败，因小助手版本低。请重启小助手或电脑以升级。")}},setHandle:function(e){var t=this;return l(i().m((function n(){var o;return i().w((function(n){for(;;)switch(n.n){case 0:"changeLange"===e?(o=t.$i18n.locale,setLang("zh"===o?"en":"zh")):"changeMode"===e&&t.changeMode();case 1:return n.a(2)}}),n)})))()},userMenuHandle:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},logoutHandle:function(){var e=this;return l(i().m((function t(){var o,r,a,l;return i().w((function(t){for(;;)switch(t.n){case 0:if(e.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1!==e.logoutType){t.n=9;break}if(t.p=1,!e.isSsoAuth()){t.n=3;break}return t.n=2,ssoLogout(_.get(e.clientInfo,"accessStatus.lastAuthType"));case 2:o=t.v;case 3:return t.n=4,proxyApi.cutoffDevice({device_id:_.get(e.clientInfo,"detail.DeviceID",0),remark:"LogOut"});case 4:if(r=t.v,0===parseInt(_.get(r,"errcode"))){t.n=5;break}return _.get(r,"errmsg")||e.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),loading.destory(),t.a(2);case 5:return commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),t.n=6,agentApi.logOut({IsCredibleDevice:_.get(e.clientInfo,"detail.IsTrustDev","0")});case 6:e.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,e.isDot1xMode&&e.setClientInfo(_.merge({},e.clientInfo,{basic:{IsOnline:0}})),e.setAuthInfo(n(n({},e.authInfo),{basic:{}})),e.setClientInfo(n(n({},e.clientInfo),{accessStatus:{}})),a=(new Date).getTime(),e.$router.push({name:"message",params:{forceTo:!0},query:{t:a}}),_.isString(o)&&""!==o&&(console.log("logoutUrl:".logoutUrl),agentApi.windowOpenUrl(o)),t.n=8;break;case 7:t.p=7,l=t.v,console.error("退出登录错误",l);case 8:loading.destory();case 9:return t.a(2)}}),t,null,[[1,7]])})))()},getCountMenuWidth:function(){var e=this.isZtpUser?44:0,t=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);this.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(t)+e})},hdEventHandle:function(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer:function(){this.deviceInnerBack=!1},changeVisible:function(e){this.drawer=e}}},w={class:"layout-header"},v={id:"u-header-menu",class:"right-wrapper"},y={id:"u-avator",ref:"countMenu"},x={class:"user-info"},b={class:"user-name"};e("default",r(m,[["render",function(e,t,n,i,r,l){var m=a("el-dropdown-item"),_=a("el-dropdown-menu"),k=a("el-dropdown"),O=a("FullScreen"),S=a("el-icon"),I=a("Minus"),j=a("Close");return d(),c("div",w,[t[3]||(t[3]=s("div",{class:"header-logo"},[s("img",{src:o,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),t[4]||(t[4]=s("div",{id:"u-electron-drag"},null,-1)),s("ul",v,[s("li",y,[u(k,{id:"ui-headNav-header-div-account_info",placement:"bottom-start",onCommand:l.userMenuHandle,onVisibleChange:l.dropdownVisiHandle},{default:p((function(){return[s("div",x,[t[0]||(t[0]=s("div",{class:"user-face"},[s("img",{src:g,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),s("span",b,h(r.username),1)]),u(_,{slot:"dropdown",class:"header-count-menu"},{default:p((function(){return[u(m,{id:"ui-headNav-header-li-cancel_account",command:"lougOut"},{default:p((function(){return t[1]||(t[1]=[s("i",{class:"iconfont icon-zhuxiao"},null,-1),f("注销 ")])})),_:1,__:[1]})]})),_:1})]})),_:1},8,["onCommand","onVisibleChange"])],512),t[2]||(t[2]=s("div",{class:"user-divider"},null,-1)),u(S,{class:"window-operate",onClick:l.maximizeWndOrNot},{default:p((function(){return[u(O)]})),_:1},8,["onClick"]),u(S,{class:"window-operate",onClick:l.minimizeWnd},{default:p((function(){return[u(I)]})),_:1},8,["onClick"]),u(S,{class:"window-operate",style:{"margin-right":"16px"},onClick:l.closeWnd},{default:p((function(){return[u(j)]})),_:1},8,["onClick"])])])}],["__scopeId","data-v-3a119553"]]))}}}))}();
