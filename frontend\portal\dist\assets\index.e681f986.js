/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
import{_ as a,h as e,o as s,d as t,f as o,j as r,e as i,g as l}from"./index.9684b1fb.js";import n from"./header.9baad04e.js";import u from"./menu.7faba8e2.js";import"./ASD.492c8837.js";const d={class:"layout-page"},f={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},c=a(Object.assign({name:"Client"},{setup:a=>(a,c)=>{const p=e("router-view");return s(),t("div",d,[o("公共顶部菜单-"),r(n),i("div",f,[o("公共侧边栏菜单"),r(u),i("div",m,[o("主流程路由渲染点"),(s(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{c as default};
