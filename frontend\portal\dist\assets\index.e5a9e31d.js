/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{u as w,a as F,b as M,S as C,r as f,z as k,E as N,K as b,h as y,o as n,d,j as m,w as v,T as R,F as x,i as q,m as T,f as E,g as O,I as W}from"./index.d0594432.js";import j from"./index.2c60b68d.js";import"./menuItem.5f1822c7.js";import"./asyncSubmenu.4ad0e585.js";const z={name:"Aside"},K=Object.assign(z,{setup(D){const a=w(),B=F(),c=M(),l=C(),o=f({}),p=()=>{switch(c.sideMode){case"#fff":o.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":o.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};break}};p();const u=f("");k(()=>a,()=>{u.value=a.meta.activeName||a.name},{deep:!0}),k(()=>c.sideMode,()=>{p()});const r=f(!1);(()=>{u.value=a.meta.activeName||a.name,document.body.clientWidth<1e3&&(r.value=!r.value),b.on("collapse",i=>{r.value=i})})(),N(()=>{b.off("collapse")});const S=(e,i,_,h)=>{const t={},g={};l.routeMap[e]?.parameters&&l.routeMap[e]?.parameters.forEach(s=>{s.type==="query"?t[s.key]=s.value:g[s.key]=s.value}),e!==a.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):B.push({name:e,query:t,params:g}))};return(e,i)=>{const _=y("el-menu"),h=y("el-scrollbar");return n(),d("div",{style:W({background:T(c).sideMode})},[m(h,{style:{height:"calc(100vh - 110px)"}},{default:v(()=>[m(R,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:v(()=>[m(_,{collapse:r.value,"collapse-transition":!1,"default-active":u.value,"background-color":o.value.background,"active-text-color":o.value.active,class:"el-menu-vertical","unique-opened":"",onSelect:S},{default:v(()=>[(n(!0),d(x,null,q(T(l).asyncRouters[0].children,t=>(n(),d(x,null,[t.hidden?O("",!0):(n(),E(j,{key:t.name,"is-collapse":r.value,"router-info":t,theme:o.value},null,8,["is-collapse","router-info","theme"]))],64))),256))]),_:1},8,["collapse","default-active","background-color","active-text-color"])]),_:1})]),_:1})],4)}}});export{K as default};
