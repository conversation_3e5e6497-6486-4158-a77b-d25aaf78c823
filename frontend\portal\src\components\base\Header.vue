<template>
  <header 
    :class="[
      'base-header',
      { 'base-header--shadow': shadow },
      { 'base-header--border': border }
    ]"
    :style="headerStyle"
  >
    <div class="base-header__content">
      <slot />
    </div>
  </header>
</template>

<script>
export default {
  name: 'BaseHeader',
  props: {
    height: {
      type: String,
      default: '60px'
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    textColor: {
      type: String,
      default: '#333333'
    },
    shadow: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: false
    },
    padding: {
      type: String,
      default: '0 20px'
    }
  },
  computed: {
    headerStyle() {
      return {
        height: this.height,
        backgroundColor: this.backgroundColor,
        color: this.textColor,
        padding: this.padding
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.base-header {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
  
  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
  }
  
  &--shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &--border {
    border-bottom: 1px solid #e4e7ed;
  }
}

// 深度选择器，影响子组件样式
:deep(.el-title) {
  font-size: 18px;
  font-weight: 600;
  color: inherit;
  margin: 0;
}

:deep(.el-search) {
  display: flex;
  align-items: center;
  gap: 12px;
}

:deep(.el-search-input) {
  width: 200px;
}

:deep(.el-search-btn) {
  min-width: auto;
  padding: 8px 12px;
}

:deep(.el-search-select) {
  width: 120px;
}

:deep(.el-row) {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

:deep(.el-recent-access) {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

:deep(.el-recent-data) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-recent-item) {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

:deep(.el-recent-icon) {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.3s;
  
  &:hover {
    opacity: 1;
  }
}

:deep(.el-recent-clear) {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.3s;
  
  &:hover {
    opacity: 1;
  }
}
</style>
