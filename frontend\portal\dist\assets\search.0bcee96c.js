/*! 
 Build based on gin-vue-admin 
 Time : 1749618054000 */
import{_ as a,a as e,S as s,r as l,h as o,o as u,d as n,j as c,w as t,O as r,e as v,F as i,i as d,m as p,f as m,R as b,T as f,C as g,g as h,U as k,K as y}from"./index.49a4551d.js";import x from"./index.c7dea0e0.js";const I={class:"search-component"},_={class:"transition-box",style:{display:"inline-block"}},C={key:0,class:"user-box"},j={key:1,class:"user-box"},w={key:2,class:"user-box"},B={key:3,class:"user-box"},T=a(Object.assign({name:"BtnBox"},{setup(a){const T=e(),V=s(),q=l(""),O=()=>{T.push({name:q.value}),q.value=""},U=l(!1),F=l(!0),K=()=>{U.value=!1,setTimeout((()=>{F.value=!0}),500)},L=l(null),R=async()=>{F.value=!1,U.value=!0,await k(),L.value.focus()},S=l(!1),z=()=>{S.value=!0,y.emit("reload"),setTimeout((()=>{S.value=!1}),500)},A=()=>{window.open("https://support.qq.com/product/371961")};return(a,e)=>{const s=o("base-option"),l=o("base-select");return u(),n("div",I,[c(f,{name:"el-fade-in-linear"},{default:t((()=>[r(v("div",_,[c(l,{ref_key:"searchInput",ref:L,modelValue:q.value,"onUpdate:modelValue":e[0]||(e[0]=a=>q.value=a),filterable:"",placeholder:"请选择",onBlur:K,onChange:O},{default:t((()=>[(u(!0),n(i,null,d(p(V).routerList,(a=>(u(),m(s,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],512),[[b,U.value]])])),_:1}),F.value?(u(),n("div",C,[v("div",{class:g(["gvaIcon gvaIcon-refresh",[S.value?"reloading":""]]),onClick:z},null,2)])):h("",!0),F.value?(u(),n("div",j,[v("div",{class:"gvaIcon gvaIcon-search",onClick:R})])):h("",!0),F.value?(u(),n("div",w,[c(x,{class:"search-icon",style:{cursor:"pointer"}})])):h("",!0),F.value?(u(),n("div",B,[v("div",{class:"gvaIcon gvaIcon-customer-service",onClick:A})])):h("",!0)])}}}),[["__scopeId","data-v-97ccbcef"]]);export{T as default};
