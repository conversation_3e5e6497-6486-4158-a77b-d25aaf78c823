/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{x as ie,y as g,u as re,r as l,c as T,b as ue,z as ce,p as h,h as E,o as n,d as u,e as s,k as de,t as U,g as O,f as A,A as ve,j as J,w as q,m as pe,B as x,L as y,F as M,i as $}from"./index.d0594432.js";const me=()=>ie({url:"/auth/login/v1/user/main_idp/list",method:"get"});const _e={class:"login-page"},he={class:"content"},ye={class:"right-panel"},fe={key:0},ge={key:0,class:"title"},xe={key:1,class:"title"},we={style:{"text-align":"center"}},ke={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},Ce={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},Te=["xlink:href"],Le={key:2,class:"login_panel_form"},Pe={key:3},be=["onClick"],Se={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},Ie=["xlink:href"],Ee={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},Oe={key:1,class:"auth-waiting"},Ae={class:"waiting-icon"},qe={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},De=["xlink:href"],Ve={class:"waiting-title"},Re={name:"Login"},Ke=Object.assign(Re,{setup(Be){const j=g({loader:()=>x(()=>import("./localLogin.00f453db.js"),["./localLogin.00f453db.js","./index.d0594432.js","./index.68b46171.css","./localLogin.a927f263.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),W=g({loader:()=>x(()=>import("./wechat.82fc9960.js"),["./wechat.82fc9960.js","./index.d0594432.js","./index.68b46171.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">\u5FAE\u4FE1\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),G=g({loader:()=>x(()=>import("./feishu.d1928f9b.js"),["./feishu.d1928f9b.js","./index.d0594432.js","./index.68b46171.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">\u98DE\u4E66\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),H=g({loader:()=>x(()=>import("./dingtalk.64e6a0d2.js"),["./dingtalk.64e6a0d2.js","./index.d0594432.js","./index.68b46171.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">\u9489\u9489\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),K=g({loader:()=>x(()=>import("./oauth2.cdc437fb.js"),["./oauth2.cdc437fb.js","./index.d0594432.js","./index.68b46171.css","./oauth2.79676400.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">OAuth2\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),Q=g({loader:()=>x(()=>import("./sms.06abfe55.js"),["./sms.06abfe55.js","./index.d0594432.js","./index.68b46171.css","./sms.ef70f8fb.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">\u77ED\u4FE1\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),X=g({loader:()=>x(()=>import("./secondaryAuth.75febeaf.js"),["./secondaryAuth.75febeaf.js","./verifyCode.5ac023d8.js","./index.d0594432.js","./index.68b46171.css","./verifyCode.978f9466.css","./secondaryAuth.21aaca5f.css"],import.meta.url),loadingComponent:y,errorComponent:{template:'<div class="error-component">\u4E8C\u6B21\u8BA4\u8BC1\u7EC4\u4EF6\u52A0\u8F7D\u5931\u8D25\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5</div>'},delay:200,timeout:3e3}),_=re(),L=l(0),o=l([]),i=l("local"),v=l(""),c=l(""),w=l(""),a=l([]),f=l([]),p=l(!1),P=l(),D=l(""),z=l(!1),k=l(""),V=l(!1),C=l(""),b=l(""),R=l(""),S=l({}),B=T(()=>{const e=p.value?C.value:c.value;return o.value.filter(t=>t.id!==e)}),Y=ue();T(()=>f.value.filter(e=>e.id!==c.value));const Z=()=>{const e={};if(_.query.type&&(e.type=_.query.type),_.query.wp&&(e.wp=_.query.wp),_.query.redirect&&Object.keys(e).length===0)try{const t=decodeURIComponent(_.query.redirect);if(t.includes("?")){const d=t.substring(t.indexOf("?")+1),r=new URLSearchParams(d);r.get("type")&&(e.type=r.get("type")),r.get("wp")&&(e.wp=r.get("wp"))}}catch(t){console.warn("\u89E3\u6790redirect\u53C2\u6570\u5931\u8D25:",t)}return e};(async()=>{try{const e=Z();Object.keys(e).length>0&&(localStorage.setItem("client_params",JSON.stringify(e)),sessionStorage.setItem("client_params",JSON.stringify(e)));const t=await me();if(t.status===200){o.value=t.data.idpList;const d=_.query.idp_id||Y.loginType;if(d&&d!=="undefined"){let r=!1;for(const m of t.data.idpList)d===m.id&&(r=!0,c.value=m.id,i.value=m.type,v.value=m.templateType,a.value=m.attrs,a.value.name=m.name,a.value.authType=m.type);r||(w.value=o.value[0]?.id,c.value=o.value[0]?.id,i.value=o.value[0]?.type,v.value=o.value[0]?.templateType,a.value=o.value[0]?.attrs,a.value.name=o.value[0].name,a.value.authType=o.value[0]?.type)}else w.value=o.value[0]?.id,c.value=o.value[0]?.id,i.value=o.value[0]?.type,v.value=o.value[0]?.templateType,a.value=o.value[0]?.attrs,a.value.name=o.value[0].name,a.value.authType=o.value[0]?.type;++L.value}}catch(e){console.error(e)}})();const ee=T(()=>{switch(i.value){case"local":case"msad":case"ldap":case"web":return j;case"email":return j;case"qiyewx":return W;case"feishu":return G;case"dingtalk":return H;case"oauth2":case"cas":return K;case"sms":return Q;default:switch(v.value){case"oauth2":return K}return"local"}}),te=T(()=>[{type:"sms",name:"\u77ED\u4FE1\u9A8C\u8BC1",icon:"duanxin",available:k.value==="phone"},{type:"email",name:"\u90AE\u7BB1\u9A8C\u8BC1",icon:"email",available:k.value==="email"}]),ae=()=>{p.value=!1,f.value=[],P.value="",D.value="",k.value="",V.value=!1,C.value&&(c.value=C.value,i.value=b.value,v.value=R.value,a.value={...S.value},C.value="",b.value="",R.value="",S.value={}),++L.value,console.log("\u53D6\u6D88\u540E\u6062\u590D\u7684\u72B6\u6001:",{isSecondary:p.value,auth_id:c.value,auth_type:i.value})},le=async e=>{const t=y.service({fullscreen:!0,text:"\u8BA4\u8BC1\u6210\u529F\uFF0C\u6B63\u5728\u8DF3\u8F6C..."});try{let d=_.query.redirect_url||"/";if(e.clientParams){const r=new URLSearchParams;r.set("type",e.clientParams.type),e.clientParams.wp&&r.set("wp",e.clientParams.wp),d+=(d.includes("?")?"&":"?")+r.toString()}window.location.href=d}finally{t?.close()}},ne=()=>i.value==="cas"?parseInt(a.value.casOpenType)===1:v.value==="oauth2"?parseInt(a.value.oauth2OpenType)===1:!1,se=T(()=>["dingtalk","feishu","qiyewx"].includes(i.value)?!1:v.value==="oauth2"||i.value==="cas"?ne():!0),F=e=>{w.value=e.id,a.value=e.attrs||{},a.value.name=e.name,a.value.authType=e.type,p.value&&(a.value.uniqKey=P.value,a.value.notPhone=z.value),c.value=e.id,i.value=e.type,v.value=e.templateType,++L.value};return ce(p,async(e,t)=>{p.value&&(C.value=c.value,b.value=i.value,R.value=v.value,S.value={...a.value},console.log("\u4E8C\u6B21\u8BA4\u8BC1\u6570\u636E:",{secondary:f.value,secondaryLength:f.value.length}),f.value.length>0&&F(f.value[0]))}),h("secondary",f),h("isSecondary",p),h("uniqKey",P),h("userName",D),h("notPhone",z),h("last_id",w),h("contactType",k),h("hasContactInfo",V),(e,t)=>{const d=E("base-divider"),r=E("base-avatar"),m=E("base-carousel-item"),oe=E("base-carousel");return n(),u("div",_e,[s("div",he,[t[3]||(t[3]=s("div",{class:"left-panel"},null,-1)),s("div",ye,[p.value?(n(),u("div",Oe,[s("div",Ae,[(n(),u("svg",qe,[s("use",{"xlink:href":`#icon-auth-${b.value||i.value}`},null,8,De)]))]),s("h4",Ve,U(S.value.name||a.value.name)+" \u767B\u5F55\u6210\u529F",1),t[1]||(t[1]=s("p",{class:"waiting-message"},"\u9700\u8981\u8FDB\u884C\u5B89\u5168\u9A8C\u8BC1\u4EE5\u786E\u4FDD\u8D26\u6237\u5B89\u5168",-1)),t[2]||(t[2]=s("div",{class:"security-tips"},[s("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),s("span",null,"\u4E3A\u4E86\u60A8\u7684\u8D26\u6237\u5B89\u5168\uFF0C\u8BF7\u5B8C\u6210\u4E8C\u6B21\u8EAB\u4EFD\u9A8C\u8BC1")],-1))])):(n(),u("div",fe,[i.value==="local"?(n(),u("span",ge,"\u672C\u5730\u8D26\u53F7\u767B\u5F55")):se.value?(n(),u("span",xe,[s("div",we,[s("span",ke,[(n(),u("svg",Ce,[s("use",{"xlink:href":"#icon-auth-"+i.value},null,8,Te)])),de(" "+U(a.value.name),1)])])])):O("",!0),c.value?(n(),u("div",Le,[(n(),A(ve(ee.value),{auth_id:c.value,auth_info:a.value},null,8,["auth_id","auth_info"]))])):O("",!0),B.value.length>0?(n(),u("div",Pe,[J(d,null,{default:q(()=>t[0]||(t[0]=[s("span",{style:{color:"#929298"}}," \u5176\u4ED6\u767B\u5F55\u65B9\u5F0F ",-1)])),_:1,__:[0]}),(n(),A(oe,{key:L.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:q(()=>[(n(!0),u(M,null,$(Math.ceil(B.value.length/2),N=>(n(),A(m,{key:N,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:q(()=>[(n(!0),u(M,null,$(B.value.slice((N-1)*2,(N-1)*2+2),I=>(n(),u("div",{key:I.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:Ue=>F(I)},[s("div",null,[J(r,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:q(()=>[(n(),u("svg",Se,[s("use",{"xlink:href":"#icon-auth-"+I.type},null,8,Ie)]))]),_:2},1024)]),s("div",Ee,U(I.name),1)],8,be))),128))]),_:2},1024))),128))]),_:1}))])):O("",!0)]))])]),p.value?(n(),A(pe(X),{key:0,"auth-info":{uniqKey:P.value,contactType:k.value,hasContactInfo:V.value},"auth-id":c.value,"user-name":D.value,"last-id":w.value,"auth-methods":te.value,onVerificationSuccess:le,onCancel:ae},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):O("",!0)])}}});export{Ke as default};
