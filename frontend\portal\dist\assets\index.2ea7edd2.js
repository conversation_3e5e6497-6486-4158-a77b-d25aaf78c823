/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{x as e,_ as a,y as l,u as t,r as n,c as i,b as s,z as u,p as o,h as r,o as v,d as c,e as d,f as p,k as m,t as y,g as h,A as g,j as f,w as _,F as x,m as w,B as k,L as T,i as b}from"./index.44d6e232.js";const C={class:"login-page"},L={class:"content"},E={class:"right-panel"},O={key:0},P={key:0,class:"title"},j={key:1,class:"title"},q={style:{"text-align":"center"}},I={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},A={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},R=["xlink:href"],S={key:2,class:"login_panel_form"},D={key:3},V=["onClick"],B={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},z=["xlink:href"],K={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},N={class:"auth-waiting"},U={class:"waiting-icon"},J={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},F=["xlink:href"],M={class:"waiting-title"},$={class:"security-tips"},G=a(Object.assign({name:"Login"},{setup(a){const G=l({loader:()=>k((()=>import("./localLogin.51d61403.js")),["./localLogin.51d61403.js","./index.44d6e232.js","./index.762c2bf1.css","./lodash.def54e57.js","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),H=l({loader:()=>k((()=>import("./wechat.b7023fc6.js")),["./wechat.b7023fc6.js","./index.44d6e232.js","./index.762c2bf1.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Q=l({loader:()=>k((()=>import("./feishu.fc4a7938.js")),["./feishu.fc4a7938.js","./index.44d6e232.js","./index.762c2bf1.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),W=l({loader:()=>k((()=>import("./dingtalk.d32d419c.js")),["./dingtalk.d32d419c.js","./index.44d6e232.js","./index.762c2bf1.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),X=l({loader:()=>k((()=>import("./oauth2.ccf39fa9.js")),["./oauth2.ccf39fa9.js","./index.44d6e232.js","./index.762c2bf1.css","./oauth2.03d0b5c4.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Y=l({loader:()=>k((()=>import("./sms.8a5ec2c1.js")),["./sms.8a5ec2c1.js","./index.44d6e232.js","./index.762c2bf1.css","./sms.844b2c56.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Z=l({loader:()=>k((()=>import("./secondaryAuth.2a2c334b.js")),["./secondaryAuth.2a2c334b.js","./index.44d6e232.js","./index.762c2bf1.css","./verifyCode.699c4e38.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css"],import.meta.url),loadingComponent:T,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ee=t(),ae=n(0),le=n([]),te=n("local"),ne=n(""),ie=n(""),se=n(""),ue=n([]),oe=n([]),re=n(!1),ve=n(),ce=n(""),de=n(!1),pe=n(""),me=n(!1),ye=n(""),he=n(""),ge=n(""),fe=n({}),_e=i((()=>{const e=re.value?ye.value:ie.value;return le.value.filter((a=>a.id!==e))})),xe=s();i((()=>oe.value.filter((e=>e.id!==ie.value))));(async()=>{var a,l,t,n,i,s,u,o,r,v,c,d;try{const p=(()=>{const e={};if(ee.query.type&&(e.type=ee.query.type),ee.query.wp&&(e.wp=ee.query.wp),ee.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(ee.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){le.value=m.data.idpList;const e=ee.query.idp_id||xe.loginType;if(e&&"undefined"!==e){let u=!1;for(const a of m.data.idpList)e===a.id&&(u=!0,ie.value=a.id,te.value=a.type,ne.value=a.templateType,ue.value=a.attrs,ue.value.name=a.name,ue.value.authType=a.type);u||(se.value=null==(a=le.value[0])?void 0:a.id,ie.value=null==(l=le.value[0])?void 0:l.id,te.value=null==(t=le.value[0])?void 0:t.type,ne.value=null==(n=le.value[0])?void 0:n.templateType,ue.value=null==(i=le.value[0])?void 0:i.attrs,ue.value.name=le.value[0].name,ue.value.authType=null==(s=le.value[0])?void 0:s.type)}else se.value=null==(u=le.value[0])?void 0:u.id,ie.value=null==(o=le.value[0])?void 0:o.id,te.value=null==(r=le.value[0])?void 0:r.type,ne.value=null==(v=le.value[0])?void 0:v.templateType,ue.value=null==(c=le.value[0])?void 0:c.attrs,ue.value.name=le.value[0].name,ue.value.authType=null==(d=le.value[0])?void 0:d.type;++ae.value}}catch(p){console.error(p)}})();const we=i((()=>{switch(te.value){case"local":case"msad":case"ldap":case"web":case"email":return G;case"qiyewx":return H;case"feishu":return Q;case"dingtalk":return W;case"oauth2":case"cas":return X;case"sms":return Y;default:return"oauth2"===ne.value?X:"local"}})),ke=i((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===pe.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===pe.value}])),Te=()=>{re.value=!1,oe.value=[],ve.value="",ce.value="",pe.value="",me.value=!1,ye.value&&(ie.value=ye.value,te.value=he.value,ne.value=ge.value,ue.value={...fe.value},ye.value="",he.value="",ge.value="",fe.value={}),++ae.value,console.log("取消后恢复的状态:",{isSecondary:re.value,auth_id:ie.value,auth_type:te.value})},be=async e=>{const a=T.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=ee.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},Ce=i((()=>!["dingtalk","feishu","qiyewx"].includes(te.value)&&("oauth2"!==ne.value&&"cas"!==te.value||("cas"===te.value?1===parseInt(ue.value.casOpenType):"oauth2"===ne.value&&1===parseInt(ue.value.oauth2OpenType))))),Le=e=>{se.value=e.id,ue.value=e.attrs||{},ue.value.name=e.name,ue.value.authType=e.type,re.value&&(ue.value.uniqKey=ve.value,ue.value.notPhone=de.value),ie.value=e.id,te.value=e.type,ne.value=e.templateType,++ae.value};return u(re,(async(e,a)=>{re.value&&(ye.value=ie.value,he.value=te.value,ge.value=ne.value,fe.value={...ue.value},console.log("二次认证数据:",{secondary:oe.value,secondaryLength:oe.value.length}),oe.value.length>0&&Le(oe.value[0]))})),o("secondary",oe),o("isSecondary",re),o("uniqKey",ve),o("userName",ce),o("notPhone",de),o("last_id",se),o("contactType",pe),o("hasContactInfo",me),(e,a)=>{const l=r("base-divider"),t=r("base-avatar"),n=r("base-carousel-item"),i=r("base-carousel"),s=r("base-icon");return v(),c("div",C,[d("div",L,[a[3]||(a[3]=d("div",{class:"left-panel"},[p(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),p('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),p(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),d("div",E,[p(" 正常登录状态 "),re.value?(v(),c(x,{key:1},[p(" 二次认证等待状态 "),d("div",N,[d("div",U,[(v(),c("svg",J,[d("use",{"xlink:href":`#icon-auth-${he.value||te.value}`},null,8,F)]))]),d("h4",M,y(fe.value.name||ue.value.name)+" 登录成功",1),a[2]||(a[2]=d("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),d("div",$,[f(s,{name:"shield",style:{color:"#67c23a"}}),a[1]||(a[1]=d("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(v(),c("div",O,["local"===te.value?(v(),c("span",P,"本地账号登录")):Ce.value?(v(),c("span",j,[d("div",q,[d("span",I,[(v(),c("svg",A,[d("use",{"xlink:href":"#icon-auth-"+te.value},null,8,R)])),m(" "+y(ue.value.name),1)])])])):p("v-if",!0),ie.value?(v(),c("div",S,[p(' <component :is="getLoginType"></component> '),(v(),h(g(we.value),{auth_id:ie.value,auth_info:ue.value},null,8,["auth_id","auth_info"])),p(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):p("v-if",!0),_e.value.length>0?(v(),c("div",D,[f(l,null,{default:_((()=>a[0]||(a[0]=[d("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)]))),_:1,__:[0]}),(v(),h(i,{key:ae.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:_((()=>[(v(!0),c(x,null,b(Math.ceil(_e.value.length/2),(e=>(v(),h(n,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:_((()=>[(v(!0),c(x,null,b(_e.value.slice(2*(e-1),2*(e-1)+2),(e=>(v(),c("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:a=>Le(e)},[d("div",null,[f(t,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:_((()=>[(v(),c("svg",B,[d("use",{"xlink:href":"#icon-auth-"+e.type},null,8,z)]))])),_:2},1024)]),d("div",K,y(e.name),1)],8,V)))),128))])),_:2},1024)))),128))])),_:1}))])):p("v-if",!0)]))])]),p(" 二次认证弹窗 "),re.value?(v(),h(w(Z),{key:0,"auth-info":{uniqKey:ve.value,contactType:pe.value,hasContactInfo:me.value},"auth-id":ie.value,"user-name":ce.value,"last-id":se.value,"auth-methods":ke.value,onVerificationSuccess:be,onCancel:Te},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):p("v-if",!0)])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]);export{G as default};
