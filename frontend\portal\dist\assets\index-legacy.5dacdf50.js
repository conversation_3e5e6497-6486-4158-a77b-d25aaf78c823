/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
System.register(["./index-legacy.21dbeba9.js","./index-legacy.1a25660e.js","./menuItem-legacy.173ecb86.js","./asyncSubmenu-legacy.07446070.js"],(function(e,n){"use strict";var t,a,u,l,o,r,c,i,f,d,s,v,m,p,h,g,b,y,x,k,_,T,j=document.createElement("style");return j.textContent='@charset "UTF-8";.el-sub-menu__title:hover,.el-menu-item:hover{background:transparent}.el-scrollbar .el-scrollbar__view{height:100%}.menu-info .menu-contorl{line-height:52px;font-size:20px;display:table-cell;vertical-align:middle}\n',document.head.appendChild(j),{setters:[function(e){t=e._,a=e.u,u=e.a,l=e.b,o=e.V,r=e.r,c=e.z,i=e.N,f=e.R,d=e.h,s=e.o,v=e.d,m=e.j,p=e.w,h=e.T,g=e.F,b=e.i,y=e.m,x=e.g,k=e.f,_=e.B},function(e){T=e.default},function(){},function(){}],execute:function(){var n=Object.assign({name:"Aside"},{setup:function(e){var n=a(),t=u(),j=l(),w=o(),F=r({}),B=function(){switch(j.sideMode){case"#fff":F.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":F.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};B();var M=r("");c((function(){return n}),(function(){M.value=n.meta.activeName||n.name}),{deep:!0}),c((function(){return j.sideMode}),(function(){B()}));var q=r(!1);M.value=n.meta.activeName||n.name,document.body.clientWidth<1e3&&(q.value=!q.value),f.on("collapse",(function(e){q.value=e})),i((function(){f.off("collapse")}));var D=function(e,a,u,l){var o,r,c={},i={};(null===(o=w.routeMap[e])||void 0===o?void 0:o.parameters)&&(null===(r=w.routeMap[e])||void 0===r||r.parameters.forEach((function(e){"query"===e.type?c[e.key]=e.value:i[e.key]=e.value}))),e!==n.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):t.push({name:e,query:c,params:i}))};return function(e,n){var t=d("el-menu"),a=d("el-scrollbar");return s(),v("div",{style:_({background:y(j).sideMode})},[m(a,{style:{height:"calc(100vh - 110px)"}},{default:p((function(){return[m(h,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:p((function(){return[m(t,{collapse:q.value,"collapse-transition":!1,"default-active":M.value,"background-color":F.value.background,"active-text-color":F.value.active,class:"el-menu-vertical","unique-opened":"",onSelect:D},{default:p((function(){return[(s(!0),v(g,null,b(y(w).asyncRouters[0].children,(function(e){return s(),v(g,null,[e.hidden?k("v-if",!0):(s(),x(T,{key:e.name,"is-collapse":q.value,"router-info":e,theme:F.value},null,8,["is-collapse","router-info","theme"]))],64)})),256))]})),_:1},8,["collapse","default-active","background-color","active-text-color"])]})),_:1})]})),_:1})],4)}}});e("default",t(n,[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/index.vue"]]))}}}));
