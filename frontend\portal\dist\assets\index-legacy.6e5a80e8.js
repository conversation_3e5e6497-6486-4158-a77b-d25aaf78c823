/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function l(n,r,a,i){var l=r&&r.prototype instanceof c?r:c,s=Object.create(l.prototype);return o(s,"_invoke",function(n,r,a){var o,i,l,c=0,s=a||[],p=!1,d={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,n){return o=t,i=0,l=e,d.n=n,u}};function f(n,r){for(i=n,l=r,t=0;!p&&c&&!a&&t<s.length;t++){var a,o=s[t],f=d.p,v=o[2];n>3?(a=v===r)&&(l=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=f&&((a=n<2&&f<o[1])?(i=0,d.v=r,d.n=o[1]):f<v&&(a=n<3||o[0]>r||r>v)&&(o[4]=n,o[5]=r,d.n=v,i=0))}if(a||n>1)return u;throw p=!0,r}return function(a,s,v){if(c>1)throw TypeError("Generator is already running");for(p&&1===s&&f(s,v),i=s,l=v;(t=i<2?e:l)||!p;){o||(i?i<3?(i>1&&(d.n=-1),f(i,l)):d.n=l:d.v=l);try{if(c=2,o){if(i||(a="next"),t=o[a]){if(!(t=t.call(o,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,i<2&&(i=0)}else 1===i&&(t=o.return)&&t.call(o),i<2&&(l=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=e}else if((t=(p=d.n<0)?l:n.call(r,d))!==u)break}catch(t){o=e,i=1,l=t}finally{c=1}}return{value:t,done:p}}}(n,a,i),!0),s}var u={};function c(){}function s(){}function p(){}t=Object.getPrototypeOf;var d=[][r]?t(t([][r]())):(o(t={},r,(function(){return this})),t),f=p.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,o(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=p,o(f,"constructor",p),o(p,"constructor",s),s.displayName="GeneratorFunction",o(p,i,"GeneratorFunction"),o(f),o(f,i,"Generator"),o(f,r,(function(){return this})),o(f,"toString",(function(){return"[object Generator]"})),(a=function(){return{w:l,m:v}})()}function o(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}o=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){o(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},o(e,t,n,r)}function i(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){u=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(u)throw o}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t,n,r,a,o,i){try{var l=e[o](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,a)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function i(e){u(o,r,a,i,l,"next",e)}function l(e){u(o,r,a,i,l,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.60f18f5a.js"],(function(e,t){"use strict";var r,o,l,u,s,p,d,f,v,m,g,y,h,b,x,w,k,j,O,S,_,C,P,T,z,q=document.createElement("style");return q.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+');background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}.error-component{text-align:center;padding:20px;background-color:#fef2f2;border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;margin:10px 0}.error-component:before{content:"\\26a0\\fe0f";display:block;font-size:24px;margin-bottom:8px}\n',document.head.appendChild(q),{setters:[function(e){r=e.x,o=e.y,l=e.u,u=e.r,s=e.c,p=e.b,d=e.z,f=e.p,v=e.h,m=e.o,g=e.d,y=e.e,h=e.j,b=e.m,x=e.k,w=e.t,k=e.g,j=e.f,O=e.A,S=e.w,_=e.B,C=e.L,P=e.C,T=e.F,z=e.i}],execute:function(){var q={class:"login-page"},E={class:"content"},I={class:"right-panel"},L={key:0},A={key:1},U={key:0,class:"title"},G={key:1,class:"title"},D={style:{"text-align":"center"}},F={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},N={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},R=["xlink:href"],B={key:2,class:"login_panel_form"},K={key:3},H=["onClick"],J={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},M=["xlink:href"],W={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},V={key:2,class:"auth-waiting"},$={class:"waiting-icon"},Q={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},X=["xlink:href"],Y={class:"waiting-title"},Z={class:"security-tips"};e("default",Object.assign({name:"Login"},{setup:function(e){var ee=o({loader:function(){return _((function(){return t.import("./localLogin-legacy.c69663da.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=o({loader:function(){return _((function(){return t.import("./wechat-legacy.10533f16.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=o({loader:function(){return _((function(){return t.import("./feishu-legacy.ba97a1f1.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),re=o({loader:function(){return _((function(){return t.import("./dingtalk-legacy.ce52430f.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ae=o({loader:function(){return _((function(){return t.import("./oauth2-legacy.2c7a5859.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),oe=o({loader:function(){return _((function(){return t.import("./sms-legacy.16ec83e0.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ie=o({loader:function(){return _((function(){return t.import("./secondaryAuth-legacy.2e8406d4.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=o({loader:function(){return _((function(){return t.import("./serverConfig-legacy.492936ee.js")}),void 0,t.meta.url)},loadingComponent:C,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=l(),ce=u(0),se=u([]),pe=u("local"),de=u(""),fe=u(""),ve=u(""),me=u([]),ge=u([]),ye=u(!1),he=u(!1),be=u(),xe=u(""),we=u(!1),ke=u(""),je=u(!1),Oe=u(""),Se=u(""),_e=u(""),Ce=u({}),Pe=s((function(){var e=ye.value?Oe.value:fe.value;return se.value.filter((function(t){return t.id!==e}))})),Te=p();s((function(){return ge.value.filter((function(e){return e.id!==fe.value}))}));var ze=function(){var e={};if(ue.query.type&&(e.type=ue.query.type),ue.query.wp&&(e.wp=ue.query.wp),ue.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(ue.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),r=new URLSearchParams(n);r.get("type")&&(e.type=r.get("type")),r.get("wp")&&(e.wp=r.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e},qe=function(){if(P.isClient()){var e=urlHashParams?urlHashParams.get("WebUrl"):"";try{var t=new URL(e);e="".concat(t.protocol,"//").concat(t.host)}catch(n){e="",console.warn("解析 WebUrl 参数失败:",n)}if(e)return!1;if(!localStorage.getItem("server_host"))return!0}return!1},Ee=function(e){logger.log("服务器配置完成:",e),he.value=!1,Ie()},Ie=function(){var e=c(a().m((function e(){var t,n,o,l,u,c,s,p,d,f,v,m,g,y,h,b,x,w,k,j;return a().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,!qe()){e.n=1;break}return he.value=!0,e.a(2);case 1:return t=ze(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=2,r({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 2:if(200===(n=e.v).status){if(se.value=n.data.idpList,(o=ue.query.idp_id||Te.loginType)&&"undefined"!==o){l=!1,u=i(n.data.idpList);try{for(u.s();!(c=u.n()).done;)s=c.value,o===s.id&&(l=!0,fe.value=s.id,pe.value=s.type,de.value=s.templateType,me.value=s.attrs,me.value.name=s.name,me.value.authType=s.type)}catch(a){u.e(a)}finally{u.f()}l||(ve.value=null===(p=se.value[0])||void 0===p?void 0:p.id,fe.value=null===(d=se.value[0])||void 0===d?void 0:d.id,pe.value=null===(f=se.value[0])||void 0===f?void 0:f.type,de.value=null===(v=se.value[0])||void 0===v?void 0:v.templateType,me.value=null===(m=se.value[0])||void 0===m?void 0:m.attrs,me.value.name=se.value[0].name,me.value.authType=null===(g=se.value[0])||void 0===g?void 0:g.type)}else ve.value=null===(y=se.value[0])||void 0===y?void 0:y.id,fe.value=null===(h=se.value[0])||void 0===h?void 0:h.id,pe.value=null===(b=se.value[0])||void 0===b?void 0:b.type,de.value=null===(x=se.value[0])||void 0===x?void 0:x.templateType,me.value=null===(w=se.value[0])||void 0===w?void 0:w.attrs,me.value.name=se.value[0].name,me.value.authType=null===(k=se.value[0])||void 0===k?void 0:k.type;++ce.value}e.n=4;break;case 3:e.p=3,j=e.v,console.error("获取认证列表失败:",j),P.isClient()&&(he.value=!0);case 4:return e.a(2)}}),e,null,[[0,3]])})));return function(){return e.apply(this,arguments)}}();Ie();var Le=s((function(){switch(pe.value){case"local":case"msad":case"ldap":case"web":case"email":return ee;case"qiyewx":return te;case"feishu":return ne;case"dingtalk":return re;case"oauth2":case"cas":return ae;case"sms":return oe;default:return"oauth2"===de.value?ae:"local"}})),Ae=s((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===ke.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===ke.value}]})),Ue=function(){ye.value=!1,ge.value=[],be.value="",xe.value="",ke.value="",je.value=!1,Oe.value&&(fe.value=Oe.value,pe.value=Se.value,de.value=_e.value,me.value=n({},Ce.value),Oe.value="",Se.value="",_e.value="",Ce.value={}),++ce.value,console.log("取消后恢复的状态:",{isSecondary:ye.value,auth_id:fe.value,auth_type:pe.value})},Ge=function(){var e=c(a().m((function e(t){var n,r,o;return a().w((function(e){for(;;)switch(e.n){case 0:n=C.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{r=ue.query.redirect_url||"/",t.clientParams&&((o=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&o.set("wp",t.clientParams.wp),r+=(r.includes("?")?"&":"?")+o.toString()),window.location.href=r}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),De=s((function(){return!["dingtalk","feishu","qiyewx"].includes(pe.value)&&("oauth2"!==de.value&&"cas"!==pe.value||("cas"===pe.value?1===parseInt(me.value.casOpenType):"oauth2"===de.value&&1===parseInt(me.value.oauth2OpenType)))})),Fe=function(e){ve.value=e.id,me.value=e.attrs||{},me.value.name=e.name,me.value.authType=e.type,ye.value&&(me.value.uniqKey=be.value,me.value.notPhone=we.value),fe.value=e.id,pe.value=e.type,de.value=e.templateType,++ce.value};return d(ye,function(){var e=c(a().m((function e(t,r){return a().w((function(e){for(;;)switch(e.n){case 0:ye.value&&(Oe.value=fe.value,Se.value=pe.value,_e.value=de.value,Ce.value=n({},me.value),console.log("二次认证数据:",{secondary:ge.value,secondaryLength:ge.value.length}),ge.value.length>0&&Fe(ge.value[0]));case 1:return e.a(2)}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),f("secondary",ge),f("isSecondary",ye),f("uniqKey",be),f("userName",xe),f("notPhone",we),f("last_id",ve),f("contactType",ke),f("hasContactInfo",je),function(e,t){var n=v("base-divider"),r=v("base-avatar"),a=v("base-carousel-item"),o=v("base-carousel"),i=v("base-icon");return m(),g("div",q,[y("div",E,[t[3]||(t[3]=y("div",{class:"left-panel"},null,-1)),y("div",I,[he.value?(m(),g("div",L,[h(b(le),{onServerConfigured:Ee})])):ye.value?(m(),g("div",V,[y("div",$,[(m(),g("svg",Q,[y("use",{"xlink:href":"#icon-auth-".concat(Se.value||pe.value)},null,8,X)]))]),y("h4",Y,w(Ce.value.name||me.value.name)+" 登录成功",1),t[2]||(t[2]=y("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),y("div",Z,[h(i,{name:"shield",style:{color:"#67c23a"}}),t[1]||(t[1]=y("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])):(m(),g("div",A,["local"===pe.value?(m(),g("span",U,"本地账号登录")):De.value?(m(),g("span",G,[y("div",D,[y("span",F,[(m(),g("svg",N,[y("use",{"xlink:href":"#icon-auth-"+pe.value},null,8,R)])),x(" "+w(me.value.name),1)])])])):k("",!0),fe.value?(m(),g("div",B,[(m(),j(O(Le.value),{auth_id:fe.value,auth_info:me.value},null,8,["auth_id","auth_info"]))])):k("",!0),Pe.value.length>0?(m(),g("div",K,[h(n,null,{default:S((function(){return t[0]||(t[0]=[y("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)])})),_:1,__:[0]}),(m(),j(o,{key:ce.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:S((function(){return[(m(!0),g(T,null,z(Math.ceil(Pe.value.length/2),(function(e){return m(),j(a,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:S((function(){return[(m(!0),g(T,null,z(Pe.value.slice(2*(e-1),2*(e-1)+2),(function(e){return m(),g("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:function(t){return Fe(e)}},[y("div",null,[h(r,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:S((function(){return[(m(),g("svg",J,[y("use",{"xlink:href":"#icon-auth-"+e.type},null,8,M)]))]})),_:2},1024)]),y("div",W,w(e.name),1)],8,H)})),128))]})),_:2},1024)})),128))]})),_:1}))])):k("",!0)]))])]),ye.value?(m(),j(b(ie),{key:0,"auth-info":{uniqKey:be.value,contactType:ke.value,hasContactInfo:je.value},"auth-id":fe.value,"user-name":xe.value,"last-id":ve.value,"auth-methods":Ae.value,onVerificationSuccess:Ge,onCancel:Ue},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):k("",!0)])}}}))}}}))}();
