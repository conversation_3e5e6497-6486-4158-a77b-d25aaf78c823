/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as m,o,d as a,e as i,F as l,i as h,C as p,t as g}from"./index.d0594432.js";const d=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"\u63A5\u5165",icon:"icon-jieru",moduleName:"\u63A5\u5165",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"\u8BBE\u7F6E",icon:"icon-shezhi",moduleName:"\u8BBE\u7F6E",uiId:"ui-menu-client-setting"}}}];const f={name:"ClientMenu",data(){return{currentRouteCode:"101"}},computed:{computedMenu(){return this.computedMenuFun()}},mounted(){this.$router.push({path:"/client/main",query:[]})},watch:{$route:{handler(e,t){if(logger.log("\u8DEF\u7531\u53D8\u5316",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code")||e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun(){const e=[];return d&&d.forEach(t=>{if(t.meta&&t.meta.menu){const{name:s,icon:c,uiId:r}=t.meta.menu,u={name:s,icon:c,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:r};e.push(u)}}),e},changeMenu(e,t={},s=0){const c={...t,menuClick:!0};logger.log(e,c),this.$router.push({path:e,query:c}),this.currentRouteCode=this.cutOut(s)},routerInterceptor(e){const t={next:!1,stateMsg:"\u60A8\u597D\uFF0C\u7CFB\u7EDF\u6B63\u5728\u68C0\u6D4B\u60A8\u7684\u7F51\u7EDC\u73AF\u5883\uFF0C\u8BF7\u7A0D\u5019......"};return t.next=!0,t},cutOut(e){return e&&e.length?e.substr(0,3):e}}},C={class:"layout-aside"},v={class:"menu-wrapper"},M=["onClick"],k={class:"icon menu-item-icon","aria-hidden":"true"},x=["xlink:href"],y={class:"menu-item-title"};function I(e,t,s,c,r,u){return o(),a("div",C,[i("ul",v,[(o(!0),a(l,null,h(u.computedMenu,n=>(o(),a("li",{key:n.code,class:p(["menu-item",u.cutOut(n.code)===r.currentRouteCode?"active-menu-item":""]),onClick:R=>u.changeMenu(n.url,n.params,n.code)},[(o(),a("svg",k,[i("use",{"xlink:href":"#"+n.icon+(u.cutOut(n.code)===r.currentRouteCode?"-active":"")},null,8,x)])),i("div",y,g(n.name),1)],10,M))),128))])])}const O=m(f,[["render",I],["__scopeId","data-v-32753310"]]);export{O as default};
