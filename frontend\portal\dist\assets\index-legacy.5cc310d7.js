/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
System.register(["./index-legacy.f131204f.js","./header-legacy.ae6eb607.js","./menu-legacy.fafe96d5.js","./ASD-legacy.b6ffb1bc.js"],(function(e,t){"use strict";var a,n,i,l,o,u,r,f,c,d,s=document.createElement("style");return s.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(s),{setters:[function(e){a=e._,n=e.h,i=e.o,l=e.d,o=e.f,u=e.j,r=e.e,f=e.g},function(e){c=e.default},function(e){d=e.default},function(){}],execute:function(){var t={class:"layout-page"},s={class:"layout-wrap"},h={id:"layoutMain",class:"layout-main"},p=Object.assign({name:"Client"},{setup:function(e){return function(e,a){var p=n("router-view");return i(),l("div",t,[o("公共顶部菜单-"),u(c),r("div",s,[o("公共侧边栏菜单"),u(d),r("div",h,[o("主流程路由渲染点"),(i(),f(p,{key:e.$route.fullPath}))])])])}}});e("default",a(p,[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]))}}}));
