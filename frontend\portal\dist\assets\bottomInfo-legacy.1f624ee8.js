/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
System.register(["./index-legacy.50e341a4.js"],(function(t,e){"use strict";var o,n,a,i,r,p=document.createElement("style");return p.textContent='@charset "UTF-8";.bottom-info{color:#888;height:30px;line-height:12px}.bottom-info a{color:#888}.bottom-info div{display:flex;justify-content:center}.bottom-info div span{margin:0 3px}\n',document.head.appendChild(p),{setters:[function(t){o=t._,n=t.o,a=t.d,i=t.e,r=t.f}],execute:function(){var e={class:"bottom-info"};t("default",o({name:"BottomInfo"},[["render",function(t,o,p,s,f,d){return n(),a("div",e,o[0]||(o[0]=[i("div",null,[r("      <span>Powered by</span>"),r("      <span>"),r('        <a href="https://github.com/flipped-aurora/gin-vue-admin">{{ $GIN_VUE_ADMIN.appName }}</a>'),r("      </span>"),r('      <base-divider direction="vertical" />'),r("      <span>Copyright</span>"),r("      <span>"),r('        <a href="https://github.com/flipped-aurora">flipped-aurora团队</a>'),r("      </span>")],-1)]))}],["__file","D:/asec-platform/frontend/portal/src/view/layout/bottomInfo/bottomInfo.vue"]]))}}}));
