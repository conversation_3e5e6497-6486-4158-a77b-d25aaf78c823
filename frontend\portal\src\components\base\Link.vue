<template>
  <component
    :is="tag"
    :class="[
      'base-link',
      `base-link--${type}`,
      {
        'base-link--disabled': disabled,
        'base-link--underline': underline && !disabled
      }
    ]"
    :href="href"
    :target="target"
    :rel="rel"
    @click="handleClick"
  >
    <base-icon v-if="icon" :name="icon" class="base-link__icon" />
    <span class="base-link__content">
      <slot />
    </span>
    <base-icon v-if="suffixIcon" :name="suffixIcon" class="base-link__suffix-icon" />
  </component>
</template>

<script>
export default {
  name: 'BaseLink',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    underline: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    href: {
      type: String,
      default: ''
    },
    target: {
      type: String,
      default: '_self',
      validator: (value) => ['_blank', '_self', '_parent', '_top'].includes(value)
    },
    rel: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    suffixIcon: {
      type: String,
      default: ''
    }
  },
  emits: ['click'],
  computed: {
    tag() {
      return this.href ? 'a' : 'span'
    }
  },
  methods: {
    handleClick(event) {
      if (this.disabled) {
        event.preventDefault()
        return
      }
      this.$emit('click', event)
    }
  }
}
</script>

<style lang="scss" scoped>
.base-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  position: relative;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &__icon {
    margin-right: 4px;
  }
  
  &__suffix-icon {
    margin-left: 4px;
  }
  
  &__content {
    display: inline-block;
  }
  
  // 类型样式
  &--default {
    color: #606266;
    
    &:hover {
      color: #409eff;
    }
  }
  
  &--primary {
    color: #409eff;
    
    &:hover {
      color: #66b1ff;
    }
  }
  
  &--success {
    color: #67c23a;
    
    &:hover {
      color: #85ce61;
    }
  }
  
  &--warning {
    color: #e6a23c;
    
    &:hover {
      color: #ebb563;
    }
  }
  
  &--danger {
    color: #f56c6c;
    
    &:hover {
      color: #f78989;
    }
  }
  
  &--info {
    color: #909399;
    
    &:hover {
      color: #a6a9ad;
    }
  }
  
  // 下划线
  &--underline {
    &:hover {
      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        height: 0;
        bottom: 0;
        border-bottom: 1px solid currentColor;
      }
    }
  }
  
  // 禁用状态
  &--disabled {
    color: #c0c4cc !important;
    cursor: not-allowed;
    
    &:hover {
      color: #c0c4cc !important;
    }
    
    &::after {
      display: none;
    }
  }
}
</style>
