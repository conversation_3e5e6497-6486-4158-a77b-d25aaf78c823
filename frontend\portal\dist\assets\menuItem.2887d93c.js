/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{_ as e,N as t,r as a,z as o,h as n,o as r,d as i,e as l,t as s,j as u,g as m,w as c,E as d,f as p}from"./index.44d6e232.js";const f={key:0,style:{height:"34px","margin-top":"6px","margin-bottom":"6px",border:"4px","line-height":"34px","margin-left":"14px",background:"#2F3C4B","padding-left":"35px","margin-right":"29px"}},v={style:{"font-size":"12px",color:"#FFFFFF",opacity:"1"}},h={key:1,class:"gva-menu-item"},x={class:"gva-menu-item-title"},g={name:"MenuItem",setup(){}},y=e(Object.assign(g,{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON><PERSON>an},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"e04a4d90-activeBackground":y.value,"e04a4d90-normalText":I.value,"e04a4d90-hoverText":b.value,"e04a4d90-activeText":F.value})));const g=e,y=a(g.theme.activeBackground),F=a(g.theme.activeText),I=a(g.theme.normalText),k=a(g.theme.hoverBackground),b=a(g.theme.hoverText);return o((()=>g.theme),(()=>{y.value=g.theme.activeBackground,F.value=g.theme.activeText,I.value=g.theme.normalText,k.value=g.theme.hoverBackground,b.value=g.theme.hoverText})),(t,a)=>{const o=n("base-icon"),g=n("el-tooltip"),y=n("el-menu-item");return e.routerInfo.meta.isDisabled?(r(),i("div",f,[l("span",v,s(e.routerInfo.meta.title),1),u(o,{color:"#FFFFFF",size:"12px",style:{"padding-left":"17px"},name:"plus"})])):(r(),m(y,{key:1,index:e.routerInfo.name},{default:c((()=>[e.isCollapse?(r(),m(g,{key:0,class:"box-item",effect:"light",content:e.routerInfo.meta.title,placement:"right"},{default:c((()=>[e.routerInfo.meta.icon?(r(),i("i",{key:0,class:d(["iconfont",e.routerInfo.meta.icon])},null,2)):p("v-if",!0)])),_:1},8,["content"])):(r(),i("div",h,[e.routerInfo.meta.icon?(r(),i("i",{key:0,class:d(["iconfont",e.routerInfo.meta.icon])},null,2)):p("v-if",!0),l("span",x,s(e.routerInfo.meta.title),1)]))])),_:1},8,["index"]))}}}),[["__scopeId","data-v-e04a4d90"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/menuItem.vue"]]);export{y as default};
