<template>
  <div :class="dividerClass">
    <span v-if="$slots.default" :class="contentClass">
      <slot></slot>
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  direction: {
    type: String,
    default: 'horizontal',
    validator: (value) => ['horizontal', 'vertical'].includes(value)
  },
  contentPosition: {
    type: String,
    default: 'center',
    validator: (value) => ['left', 'center', 'right'].includes(value)
  }
})

const dividerClass = computed(() => {
  const classes = ['divider']
  
  if (props.direction === 'vertical') {
    classes.push('divider-vertical')
  } else {
    classes.push('divider-horizontal')
  }
  
  return classes.join(' ')
})

const contentClass = computed(() => {
  const classes = ['divider-content']
  
  if (props.direction === 'horizontal') {
    classes.push(`divider-content-${props.contentPosition}`)
  }
  
  return classes.join(' ')
})
</script>

<style scoped>
.divider-horizontal {
  position: relative;
  margin: 24px 0;
  border-top: 1px solid #e8e8e8;
}

.divider-horizontal .divider-content {
  position: absolute;
  top: 50%;
  background-color: #fff;
  padding: 0 16px;
  color: #606266;
  font-size: 14px;
}

.divider-content-left {
  left: 5%;
}

.divider-content-center {
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.divider-content-right {
  right: 5%;
}

.divider-vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  background-color: #e8e8e8;
  vertical-align: middle;
  margin: 0 8px;
}
</style>
