/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{u as M,a as B,r as u,b as F,c as H,z as y,E as U,K as p,h as J,o as _,d as V,j as z,w as T,F as D,i as $,f as K,e as m,I,m as w,k as Q,t as X,Q as Y,H as G,O as W,R as Z}from"./index.d0594432.js";import{J as ee}from"./index-browser-esm.c2d3b5c9.js";const ae={class:"router-history"},se=["tab"],te={name:"HistoryComponent"},ue=Object.assign(te,{setup(le){const f=M(),i=B(),l=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),s=u([]),r=u(""),c=u(!1),h=F(),g=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),x=u(0),O=u(0),k=u(!1),q=u(!1),v=u(""),d=H(()=>ee("$..defaultRouter[0]",h.userInfo)[0]||"dashboard"),E=e=>{if(s.value.length===1&&f.name===d.value)return!1;let a="";if(e.srcElement.nodeName==="SPAN"?a=e.srcElement.offsetParent.id:a=e.srcElement.id,a){c.value=!0;let t;k.value?t=54:t=220,q.value&&(t=0),x.value=e.clientX-t,O.value=e.clientY+10,v.value=a.substring(4)}},b=()=>{s.value=[{name:d.value,meta:{title:"\u603B\u89C8"},query:{},params:{}}],i.push({name:d.value}),c.value=!1,sessionStorage.setItem("historys",JSON.stringify(s.value))},R=()=>{let e;const a=s.value.findIndex(n=>(l(n)===v.value&&(e=n),l(n)===v.value)),t=s.value.findIndex(n=>l(n)===r.value);s.value.splice(0,a),a>t&&i.push(e),sessionStorage.setItem("historys",JSON.stringify(s.value))},j=()=>{let e;const a=s.value.findIndex(n=>(l(n)===v.value&&(e=n),l(n)===v.value)),t=s.value.findIndex(n=>l(n)===r.value);s.value.splice(a+1,s.value.length),a<t&&i.push(e),sessionStorage.setItem("historys",JSON.stringify(s.value))},P=()=>{let e;s.value=s.value.filter(a=>(l(a)===v.value&&(e=a),l(a)===v.value)),i.push(e),sessionStorage.setItem("historys",JSON.stringify(s.value))},A=(e,a)=>{if(e.name!==a.name||Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const t in e.query)if(e.query[t]!==a.query[t])return!1;for(const t in e.params)if(e.params[t]!==a.params[t])return!1;return!0},N=e=>{if(!s.value.some(a=>A(a,e))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,s.value.push(a)}window.sessionStorage.setItem("activeValue",l(e))},S=u({});y(()=>s.value,()=>{S.value={},s.value.forEach(e=>{S.value[l(e)]=e})});const L=e=>{const a=S.value[e];i.push({name:a.name,query:a.query,params:a.params})},C=e=>{const a=s.value.findIndex(t=>l(t)===e);l(f)===e&&(s.value.length===1?i.push({name:d.value}):a<s.value.length-1?i.push({name:s.value[a+1].name,query:s.value[a+1].query,params:s.value[a+1].params}):i.push({name:s.value[a-1].name,query:s.value[a-1].query,params:s.value[a-1].params})),s.value.splice(a,1)};return y(()=>c.value,()=>{c.value?document.body.addEventListener("click",()=>{c.value=!1}):document.body.removeEventListener("click",()=>{c.value=!1})}),y(()=>f,(e,a)=>{e.name==="Login"||e.name==="Reload"||(s.value=s.value.filter(t=>!t.meta.closeTab),N(e),sessionStorage.setItem("historys",JSON.stringify(s.value)),r.value=window.sessionStorage.getItem("activeValue"))},{deep:!0}),y(()=>s.value,()=>{sessionStorage.setItem("historys",JSON.stringify(s.value))},{deep:!0}),(()=>{p.on("closeThisPage",()=>{C(g(f))}),p.on("closeAllPage",()=>{b()}),p.on("mobile",a=>{q.value=a}),p.on("collapse",a=>{k.value=a});const e=[{name:d.value,meta:{title:"\u603B\u89C8"},query:{},params:{}}];s.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?r.value=window.sessionStorage.getItem("activeValue"):r.value=l(f),N(f),window.sessionStorage.getItem("needCloseAll")==="true"&&(b(),window.sessionStorage.removeItem("needCloseAll"))})(),U(()=>{p.off("collapse"),p.off("mobile")}),(e,a)=>{const t=J("el-tab-pane"),n=J("el-tabs");return _(),V("div",ae,[z(n,{modelValue:r.value,"onUpdate:modelValue":a[0]||(a[0]=o=>r.value=o),closable:!(s.value.length===1&&e.$route.name===d.value),type:"card",onContextmenu:a[1]||(a[1]=G(o=>E(o),["prevent"])),onTabChange:L,onTabRemove:C},{default:T(()=>[(_(!0),V(D,null,$(s.value,o=>(_(),K(t,{key:g(o),label:o.meta.title,name:g(o),tab:o,class:"gva-tab"},{label:T(()=>[m("span",{tab:o,style:I({color:r.value===g(o)?w(h).activeColor:"#333"})},[m("i",{class:"dot",style:I({backgroundColor:r.value===g(o)?w(h).activeColor:"#ddd"})},null,4),Q(" "+X(w(Y)(o.meta.title,o)),1)],12,se)]),_:2},1032,["label","name","tab"]))),128))]),_:1},8,["modelValue","closable"]),W(m("ul",{style:I({left:x.value+"px",top:O.value+"px"}),class:"contextmenu"},[m("li",{onClick:b},"\u5173\u95ED\u6240\u6709"),m("li",{onClick:R},"\u5173\u95ED\u5DE6\u4FA7"),m("li",{onClick:j},"\u5173\u95ED\u53F3\u4FA7"),m("li",{onClick:P},"\u5173\u95ED\u5176\u4ED6")],4),[[Z,c.value]])])}}});export{ue as default};
