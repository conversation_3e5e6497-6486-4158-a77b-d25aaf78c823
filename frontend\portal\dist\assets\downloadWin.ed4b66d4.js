/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
import{_ as e,O as a,r as o,N as l,P as t,h as n,o as s,g as i,w as d,j as r,I as c,e as u,B as v,T as m,d as p,k as g,f as w,M as f}from"./index.9684b1fb.js";import{_ as b}from"./ASD.492c8837.js";import{g as h}from"./browser.9273b95e.js";const x={style:{background:"'#273444'"}},y={class:"downloadWin"},k={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},_={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},F={key:1,class:"download-complete"},T=e(Object.assign({name:"downloadWin"},{setup(e){a((e=>({"dc46818e-activeBackground":e.activeBackground,"dc46818e-normalText":e.normalText})));const T=o(!1),z=o(!0),B=o(!1),L=o("1"),j=o({});j.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const E=o(!1),S=o(0),C=o(!1);let O=0;const R=()=>{const e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(B.value=!1,z.value=!1,T.value=!0):(B.value=!1,z.value=!0,T.value=!1)};R();const W=o(!1);l((()=>{t.emit("collapse",T.value),t.emit("mobile",B.value),t.on("showLoading",(()=>{W.value=!0})),t.on("closeLoading",(()=>{W.value=!1})),window.onresize=()=>(R(),t.emit("collapse",T.value),void t.emit("mobile",B.value))}));const D=o("#1f2a36"),M=o(!1),U=()=>{T.value=!T.value,z.value=!T.value,M.value=!T.value,t.emit("collapse",T.value)},$=e=>100===e?"下载完成":`${e}%`,q=async(e,a)=>{try{const o=await P(e);H(o,a)}catch(o){if(O<3&&"网络连接超时"===o.message)return O++,q(e,a);throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${o.message}`)}},P=e=>new Promise(((a,o)=>{const l=new XMLHttpRequest;l.open("GET",e,!0),l.responseType="blob",l.timeout=3e5;let t=Date.now();l.onprogress=e=>{if(e.lengthComputable){const a=e.loaded/e.total*100;S.value=Math.round(a)}else{const a=(Date.now()-t)/1e3,o=60*(e.loaded/a),l=e.loaded/o*100;S.value=Math.min(99,Math.round(l))}},l.onload=()=>{200===l.status?a(l.response):o(new Error(`HTTP 错误: ${l.status}`))},l.onerror=()=>{o(new Error("网络错误"))},l.ontimeout=()=>{o(new Error("网络连接超时"))},l.send()})),H=(e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const o=document.createElement("a"),l=document.querySelector("body");o.href=window.URL.createObjectURL(e),o.download=a,o.style.display="none",l.appendChild(o),o.click(),l.removeChild(o),window.URL.revokeObjectURL(o.href)}};return(e,a)=>{const o=n("base-row"),l=n("base-icon"),t=n("base-menu-item"),R=n("base-menu"),W=n("base-scrollbar"),P=n("base-aside"),H=n("el-link"),I=n("el-progress"),A=n("base-main"),G=n("base-container");return s(),i(G,{class:"layout-cont"},{default:d((()=>[r(G,{class:c([z.value?"openside":"hideside",B.value?"mobile":""])},{default:d((()=>[r(o,{class:c([M.value?"shadowBg":""]),onClick:a[0]||(a[0]=e=>(M.value=!M.value,z.value=!!T.value,void U()))},null,8,["class"]),r(P,{class:"main-cont main-left gva-aside"},{default:d((()=>[u("div",{class:c(["tilte",[z.value?"openlogoimg":"hidelogoimg"]]),style:v({background:D.value})},a[2]||(a[2]=[u("img",{alt:"",class:"logoimg",src:b},null,-1)]),6),u("div",x,[r(W,{height:"calc(100vh - 110px)"},{default:d((()=>[r(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:d((()=>[r(R,{collapse:T.value,"collapse-transition":!1,"default-active":L.value,"background-color":j.value.background,"active-text-color":j.value.activeText,mode:"vertical","unique-opened":!0},{default:d((()=>[r(t,{index:"1"},{default:d((()=>[r(l,{name:"xiazai",size:"16px"}),a[3]||(a[3]=u("span",null,"客户端下载",-1))])),_:1,__:[3]})])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})]),u("div",{class:"footer",style:v({background:D.value})},[u("div",{class:"menu-total",onClick:U},[T.value?(s(),i(l,{key:0,color:"#FFFFFF",size:"14px",name:"expand"})):(s(),i(l,{key:1,color:"#FFFFFF",size:"14px",name:"fold"}))])],4)])),_:1}),r(A,{class:"main-cont main-right client"},{default:d((()=>[u("div",y,[u("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:a[1]||(a[1]=e=>(async e=>{if("windows"===e){E.value=!0,S.value=0,C.value=!1,O=0;try{const a=await h({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);{const e=window.location.port,o=new URL(a.data.data.download_url);let l;e?o.toString().includes("asec-deploy")?l=a.data.data.download_url:(o.port=e,l=o.toString()):(o.port="",l=o.toString());const t=e?a.data.data.latest_filename.replace(/@(\d+)/,`@${e}`):a.data.data.latest_filename;await q(l,t),C.value=!0,f({type:"success",message:"下载完成"})}}catch(a){f({type:"error",message:a.message||"下载失败，请联系管理员"})}finally{E.value=!1,setTimeout((()=>{C.value=!1}),3e3)}}})("windows"))},[(s(),p("svg",k,a[4]||(a[4]=[u("use",{"xlink:href":"#icon-windows"},null,-1)]))),(s(),p("svg",_,a[5]||(a[5]=[u("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[8]||(a[8]=u("br",null,null,-1)),r(H,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:d((()=>a[6]||(a[6]=[g(" Windows客户端 ")]))),_:1,__:[6]}),r(H,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:d((()=>a[7]||(a[7]=[g(" 点击下载Windows客户端 ")]))),_:1,__:[7]}),E.value?(s(),i(I,{key:0,percentage:S.value,format:$,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):w("v-if",!0),C.value?(s(),p("div",F,"下载完成")):w("v-if",!0)])])])),_:1})])),_:1},8,["class"])])),_:1})}}}),[["__scopeId","data-v-dc46818e"],["__file","D:/asec-platform/frontend/portal/src/view/login/downloadWin.vue"]]);export{T as default};
