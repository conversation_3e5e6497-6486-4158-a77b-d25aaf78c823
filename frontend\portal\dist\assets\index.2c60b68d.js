/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import c from"./menuItem.5f1822c7.js";import i from"./asyncSubmenu.4ad0e585.js";import{c as m,h as f,o as t,f as l,w as d,d as h,F as p,i as C,g as u,A as I}from"./index.d0594432.js";const y={name:"AsideComponent"},B=Object.assign(y,{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:<PERSON>olean},theme:{default:function(){return{}},type:Object}},setup(e){const n=e,a=m(()=>n.routerInfo.children&&n.routerInfo.children.filter(o=>!o.hidden).length?i:c);return(o,k)=>{const s=f("AsideComponent");return e.routerInfo.hidden?u("",!0):(t(),l(I(a.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:d(()=>[e.routerInfo.children&&e.routerInfo.children.length?(t(!0),h(p,{key:0},C(e.routerInfo.children,r=>(t(),l(s,{key:r.name,"is-collapse":!1,"router-info":r,theme:e.theme},null,8,["router-info","theme"]))),128)):u("",!0)]),_:1},8,["is-collapse","theme","router-info"]))}}});export{B as default};
