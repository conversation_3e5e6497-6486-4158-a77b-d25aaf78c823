<template>
  <div class="layout-header">
    <div class="header-logo">
      <!--如果图片加载失败就隐藏-->
      <img src="@/assets/ASD.png" alt="" onload="this.style.display = 'block'" onerror="this.style.display = 'none' ">
    </div>
    <div id="u-electron-drag" />
    <ul id="u-header-menu" class="right-wrapper">
      <li id="u-avator" ref="countMenu">
        <el-dropdown id="ui-headNav-header-div-account_info" placement="bottom-start" @command="userMenuHandle" @visible-change="dropdownVisiHandle">
          <div class="user-info">
            <div class="user-face">
              <img src="@/assets/avator.png" alt="" onload="this.style.display = 'block'" onerror="this.style.display = 'none' ">
            </div>
            <span class="user-name">{{ username }}</span>
          </div>
          <el-dropdown-menu slot="dropdown" class="header-count-menu">
            <el-dropdown-item id="ui-headNav-header-li-cancel_account" command="lougOut">
              <i class="iconfont icon-zhuxiao" />注销
            </el-dropdown-item>

          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <div class="user-divider"></div>
      <base-icon class="window-operate" name="fullscreen" @click="maximizeWndOrNot" />
      <base-icon class="window-operate" name="minus" @click="minimizeWnd" />
      <base-icon class="window-operate" name="close" style="margin-right: 16px;" @click="closeWnd" />
    </ul>
  </div>
</template>
<script>
export default {
  name: 'ClientHeader',
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      Qty: 0,
      drawerTitle: '',
      username: 'duanyc',
      countCommand: 'changePassword',
      drawerSize: 424,
      showLogout: false,
      logoutMsg: '',
      logoutType: 1, // 1账户 2客户端
      isMaxWindow: false, // 当前窗口是最最大化还是正常形态
      deviceInnerBack: false,
      avator: '',
      showSwitch: false,
      IsolateType: '',
      IsOpenIsolate: 0,
      drawerData: {},
      showIpDialog: false,
      ipText: '',
      netCardList: [],
      isDox8021x: false
    }
  },
  computed: {
    // 是否已登录
    isAccess() {
      return false
    }
  },
  watch: {
    userId(newVal, oldVal) {
      console.log('用户id变动', newVal, oldVal)
      console.debug('用户id变动')
    }
  },
  mounted() {
    //this.addEventBus()

    // 获取内外网配置
    //this.getSwNetworkData()
    //this.autochangeMode()
    //this.getCountMenuWidth() // 小助手初始化时网络变动header还没渲染监听不到，这里补掉，bugID=16790
  },
  beforeDestroy() {
    //EventBus.$off('openPassword')
  },
  methods: {
    // 最小化
    minimizeWnd() {
      agentApi.minimizeWnd()
    },
    maximizeWndOrNot() {
      if (this.isMaxWindow) {
        agentApi.normalnizeWnd()
        this.isMaxWindow = false
      } else {
        agentApi.maximizeWnd()
        this.isMaxWindow = true
      }
    },
    dropdownVisiHandle() {

    },
    closeWnd() {
      if (!TestQtModule('UIPlatform_Window', 'HideWnd')) { // 客户端不升级没有该接口
        try {
          this.$ipcSend('UIPlatform_Window', 'TerminateWnd')
        } catch (error) {
          this.$message.error('操作失败，因小助手版本低。请重启小助手或电脑以升级。')
        }
        return
      }
      EventBus.$emit('closeAssui')
      this.$nextTick(() => {
        agentApi.hideWend()
      })
    },

    async setHandle(command) {
      if (command === 'changeLange') {
        const locale = this.$i18n.locale
        setLang(locale === 'zh' ? 'en' : 'zh')
      } else if (command === 'changeMode') {
        this.changeMode()
      }
    },
    userMenuHandle(command, data = {}) {
      this.countCommand = command
      switch (command) {
        case 'changePassword':
          // 如果返回失败，则不需要展示
          if (!this.changePasswordHandle(data)) {
            return
          }
          break
        case 'myDevice':
          this.drawerSize = 500
          this.drawerTitle = ''
          break
        case 'changeCount':
          this.drawerSize = 581
          this.drawerTitle = ''
          break
        case 'lougOut':
          this.logoutMsg = '注销后会取消自动身份认证功能，您确定要注销吗？'
          this.showLogout = true
          this.logoutType = 1
          break
        case 'switchNetwork':
          this.showSwitch = true
          break
      }
      if (command !== 'lougOut' && command !== 'switchNetwork') {
        this.drawer = true
      }
    },
    // 注销
    async logoutHandle() {
      this.showLogout = false

      loading.start({ msg: i18n.t('header.logouting') })
      // 非可信设备 登出
      if (this.logoutType === 1) {
        try {
          // sso退出登录
          let logoutUrl
          if (this.isSsoAuth()) {
            logoutUrl = await ssoLogout(_.get(this.clientInfo, 'accessStatus.lastAuthType'))
          }
          // 注销账户的操作
          const ret = await proxyApi.cutoffDevice({
            device_id: _.get(this.clientInfo, 'detail.DeviceID', 0),
            remark: 'LogOut'
          })
          if (parseInt(_.get(ret, 'errcode')) !== 0) {
            if (!_.get(ret, 'errmsg')) {
              this.$message.error('注销失败！可能是因为网络不可用，或者服务器繁忙。')
            }
            loading.destory()
            return
          }
          commonUtil.setLoginRet({ token: '', UserID: '', LoginRet: '0' })

          await agentApi.logOut({
            IsCredibleDevice: _.get(this.clientInfo, 'detail.IsTrustDev', '0')
          })
          this.setGateInfos({ state: 2, gateWayMap: {}, total: 0, VPNStatus: 0 })
          clearToken()
          // 审核和安检后审核的标记删除，再审核后全部跳转到欢迎页。
          localStorage.removeItem('auditNextStatus')
          localStorage.removeItem('auditCheckNextStatus')
          // 取消自动登录
          authIndex.config.AutoLogin = -1

          // 如是8021.模式,直接设置为离线
          if (this.isDot1xMode) {
            this.setClientInfo(_.merge({}, this.clientInfo, { basic: { IsOnline: 0 }}))
          }

          this.setAuthInfo({ ...this.authInfo, ...{ basic: {}}})
          // 先清空
          this.setClientInfo({ ...this.clientInfo, ...{ accessStatus: {}}})

          // 这里跳转的时候，加个时间戳，这样从/access/message跳转到message的时候可以强制刷新一下
          const timestamp = new Date().getTime()
          this.$router.push({ name: 'message', params: { forceTo: true }, query: { t: timestamp }})
          if (_.isString(logoutUrl) && logoutUrl !== '') {
            console.log('logoutUrl:'.logoutUrl)
            agentApi.windowOpenUrl(logoutUrl)
          }
        } catch (error) {
          console.error('退出登录错误', error)
        }
        loading.destory()
      }
    },
    /* 获取顶部头像昵称总长度*/
    getCountMenuWidth() {
      const addWidth = this.isZtpUser ? 44 : 0
      const width = parseInt(document.getElementById('u-avator') ? document.getElementById('u-avator').offsetWidth : 0)
      this.$ipcSend('UIPlatform_Window', 'SetTitleDimension', { nHeight: 50, nNameWidth: parseFloat(width) + addWidth })
    },
    hdEventHandle(data) {
      switch (data.type) {
        case 'router' :
          this.userMenuHandle(data.val)
          break
      }
    },
    closeDrawer() {
      this.deviceInnerBack = false
    },
    changeVisible(isClose) {
      this.drawer = isClose
    },
  }
}
</script>

<style lang="scss" scoped>
.layout-header {
  height: 42px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
  box-shadow: 0px 2px 6px 0px rgba(46, 60, 128, 0.2);
  color: $light-color;
  .header-title {
    line-height: 42px;
    font-size: 18px;
    font-weight: 500;
  }
  .header-logo{
    height: 42px;
    display: flex;
    align-items: center;
    img{
      max-width: 79px;
      max-height: 28px;
    }
  }
  #u-electron-drag{
    display: flex;
    flex: 1;
    height: 100%;
    -webkit-app-region: drag;
  }
  .right-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    & > li:hover {
      background: $--color-primary-dark-2;
    }
    .user-divider {
      width: 1px;
      height: 14px;
      margin-left: 16px;
      margin-right: 16px;
      background: #e6e6e6;
    }
    .user-info {
      display: flex;
      align-items: center;
      height: 42px;
      padding: 0 14px;
      cursor: pointer;
      .user-face {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 6px;
        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
      .user-name {
        color: $light-color;
        display: inline-block;
        max-width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
    .set-icon-wrapper, .menu-msg {
      width: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      height: 42px;
      position: relative;
      .icon-shezhi {
        color: $icon-color;
        font-size: 18px;
      }
    }
    .is-message{
      &::after {
          content: '';
          position: absolute;
          top: 17px;
          right: 13px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: $error-1;
        }
    }
    .window-operate, #ui-headNav-header-li-msg_list {
      width: 24px;
      height: 100%;
      margin-left: 4px;
      filter: brightness(1.5);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      .iconfont {
        color: $icon-color;
        font-size: 16px;
      }
    }
  }
}

.count-title {
  color: $default-color;
  i {
    font-style: normal;
    color: $title-color;
  }
}
.el-dropdown-menu.header-count-menu ::v-deep .el-dropdown-menu__item{
  padding-left: 40px;
  position: relative;
  i{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 16px;
    font-size: 14px;
  }
  .icon-qianduanruwang-yuyanqiehuan{
    font-size: 15px;
  }
  .icon-qiehuanzhanghu1, .icon-jisuruwang{
    font-size: 16px;
  }
  .icon-neiwaiwangqiehuan{
    font-size: 16px;
  }
}

.s-title{
  margin-top: 18px;
  margin-left: 18px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 500;
  color: $title-color;
}
.s-content{
  padding: 24px 32px 29px 32px;
  font-size: 13px;
  line-height: 18px;
  .s-text{
    color: $default-color
  }
}
.change-reg-info{
  padding-left: 8px;
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: $title-color;
}
</style>
<style lang="scss">
body .el-dialog-ip-box {
  width: 260px;
  .el-message-box__content {
    padding: 20px 15px;
  }
}
.s-content .el-radio{
  margin-right: 13px;
  .el-radio__label{
    padding-left: 8px;
    font-size: 13px;
    color: $title-color;
    line-height: 18px;
  }
}
#ip-info-dialog{
  .ip-content{
    margin-top: 24px;
    margin-bottom: 24px;
    padding: 0 24px;
    line-height: 20px;
    font-size: 14px;
    color: $title-color;
  }
  .netcard-list{
    margin-top: 16px;
    padding: 0 24px;
    li{
      display: flex;
      align-items: center;
      line-height: 20px;
      font-size: 14px;
      color: $title-color;
      margin-bottom: 10px;
      &:last-child{
        margin-bottom: 24px;
      }
      i{
        font-size: 16px;
        margin-left: 16px;
      }
      .icon-lianjie{
        color: $success;
      }
      .icon-duankailianjie{
        color: $error;
      }
    }
  }
  .el-dialog__footer button{
    height: 40px;
    line-height: 40px;
    border-bottom-right-radius: 4px;
  }
}

.loginout-m-confirm-dialog{
    .v-header{
        line-height: 45px;
        border-bottom: 1px solid $line-color;
        padding: 0 24px;
        font-size: 16px;
        color: $title-color;
        i{
            font-size: 16px;
            color: $yellow-1;
            margin-right: 6px;
            font-weight: 400;
        }
    }
    .outline-tips{
        padding: 24px;
        line-height: 20px;
        color: $title-color;
        font-size: 14px;
    }
}
</style>
