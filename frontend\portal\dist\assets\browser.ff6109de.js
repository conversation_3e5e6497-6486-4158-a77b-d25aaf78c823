/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{x as bt}from"./index.d0594432.js";const ye=e=>bt({url:"/console/v1/agents/version_check",method:"post",data:e});var O={},Rt=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},dt={},I={};let it;const Lt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];I.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17};I.getSymbolTotalCodewords=function(t){return Lt[t]};I.getBCHDigit=function(e){let t=0;for(;e!==0;)t++,e>>>=1;return t};I.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');it=t};I.isKanjiModeEnabled=function(){return typeof it<"u"};I.toSJIS=function(t){return it(t)};var $={};(function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+i)}}e.isValid=function(o){return o&&typeof o.bit<"u"&&o.bit>=0&&o.bit<4},e.from=function(o,n){if(e.isValid(o))return o;try{return t(o)}catch{return n}}})($);function ht(){this.buffer=[],this.length=0}ht.prototype={get:function(e){const t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)===1},put:function(e,t){for(let i=0;i<t;i++)this.putBit((e>>>t-i-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var _t=ht;function V(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}V.prototype.set=function(e,t,i,o){const n=e*this.size+t;this.data[n]=i,o&&(this.reservedBit[n]=!0)};V.prototype.get=function(e,t){return this.data[e*this.size+t]};V.prototype.xor=function(e,t,i){this.data[e*this.size+t]^=i};V.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var Dt=V,wt={};(function(e){const t=I.getSymbolSize;e.getRowColCoords=function(o){if(o===1)return[];const n=Math.floor(o/7)+2,r=t(o),s=r===145?26:Math.ceil((r-13)/(2*n-2))*2,c=[r-7];for(let u=1;u<n-1;u++)c[u]=c[u-1]-s;return c.push(6),c.reverse()},e.getPositions=function(o){const n=[],r=e.getRowColCoords(o),s=r.length;for(let c=0;c<s;c++)for(let u=0;u<s;u++)c===0&&u===0||c===0&&u===s-1||c===s-1&&u===0||n.push([r[c],r[u]]);return n}})(wt);var mt={};const Ut=I.getSymbolSize,lt=7;mt.getPositions=function(t){const i=Ut(t);return[[0,0],[i-lt,0],[0,i-lt]]};var yt={};(function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(n){return n!=null&&n!==""&&!isNaN(n)&&n>=0&&n<=7},e.from=function(n){return e.isValid(n)?parseInt(n,10):void 0},e.getPenaltyN1=function(n){const r=n.size;let s=0,c=0,u=0,a=null,l=null;for(let p=0;p<r;p++){c=u=0,a=l=null;for(let w=0;w<r;w++){let f=n.get(p,w);f===a?c++:(c>=5&&(s+=t.N1+(c-5)),a=f,c=1),f=n.get(w,p),f===l?u++:(u>=5&&(s+=t.N1+(u-5)),l=f,u=1)}c>=5&&(s+=t.N1+(c-5)),u>=5&&(s+=t.N1+(u-5))}return s},e.getPenaltyN2=function(n){const r=n.size;let s=0;for(let c=0;c<r-1;c++)for(let u=0;u<r-1;u++){const a=n.get(c,u)+n.get(c,u+1)+n.get(c+1,u)+n.get(c+1,u+1);(a===4||a===0)&&s++}return s*t.N2},e.getPenaltyN3=function(n){const r=n.size;let s=0,c=0,u=0;for(let a=0;a<r;a++){c=u=0;for(let l=0;l<r;l++)c=c<<1&2047|n.get(a,l),l>=10&&(c===1488||c===93)&&s++,u=u<<1&2047|n.get(l,a),l>=10&&(u===1488||u===93)&&s++}return s*t.N3},e.getPenaltyN4=function(n){let r=0;const s=n.data.length;for(let u=0;u<s;u++)r+=n.data[u];return Math.abs(Math.ceil(r*100/s/5)-10)*t.N4};function i(o,n,r){switch(o){case e.Patterns.PATTERN000:return(n+r)%2===0;case e.Patterns.PATTERN001:return n%2===0;case e.Patterns.PATTERN010:return r%3===0;case e.Patterns.PATTERN011:return(n+r)%3===0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2===0;case e.Patterns.PATTERN101:return n*r%2+n*r%3===0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2===0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2===0;default:throw new Error("bad maskPattern:"+o)}}e.applyMask=function(n,r){const s=r.size;for(let c=0;c<s;c++)for(let u=0;u<s;u++)r.isReserved(u,c)||r.xor(u,c,i(n,u,c))},e.getBestMask=function(n,r){const s=Object.keys(e.Patterns).length;let c=0,u=1/0;for(let a=0;a<s;a++){r(a),e.applyMask(a,n);const l=e.getPenaltyN1(n)+e.getPenaltyN2(n)+e.getPenaltyN3(n)+e.getPenaltyN4(n);e.applyMask(a,n),l<u&&(u=l,c=a)}return c}})(yt);var j={};const R=$,H=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],K=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];j.getBlocksCount=function(t,i){switch(i){case R.L:return H[(t-1)*4+0];case R.M:return H[(t-1)*4+1];case R.Q:return H[(t-1)*4+2];case R.H:return H[(t-1)*4+3];default:return}};j.getTotalCodewordsCount=function(t,i){switch(i){case R.L:return K[(t-1)*4+0];case R.M:return K[(t-1)*4+1];case R.Q:return K[(t-1)*4+2];case R.H:return K[(t-1)*4+3];default:return}};var Et={},G={};const z=new Uint8Array(512),J=new Uint8Array(256);(function(){let t=1;for(let i=0;i<255;i++)z[i]=t,J[t]=i,t<<=1,t&256&&(t^=285);for(let i=255;i<512;i++)z[i]=z[i-255]})();G.log=function(t){if(t<1)throw new Error("log("+t+")");return J[t]};G.exp=function(t){return z[t]};G.mul=function(t,i){return t===0||i===0?0:z[J[t]+J[i]]};(function(e){const t=G;e.mul=function(o,n){const r=new Uint8Array(o.length+n.length-1);for(let s=0;s<o.length;s++)for(let c=0;c<n.length;c++)r[s+c]^=t.mul(o[s],n[c]);return r},e.mod=function(o,n){let r=new Uint8Array(o);for(;r.length-n.length>=0;){const s=r[0];for(let u=0;u<n.length;u++)r[u]^=t.mul(n[u],s);let c=0;for(;c<r.length&&r[c]===0;)c++;r=r.slice(c)}return r},e.generateECPolynomial=function(o){let n=new Uint8Array([1]);for(let r=0;r<o;r++)n=e.mul(n,new Uint8Array([1,t.exp(r)]));return n}})(Et);const Ct=Et;function st(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}st.prototype.initialize=function(t){this.degree=t,this.genPoly=Ct.generateECPolynomial(this.degree)};st.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const i=new Uint8Array(t.length+this.degree);i.set(t);const o=Ct.mod(i,this.genPoly),n=this.degree-o.length;if(n>0){const r=new Uint8Array(this.degree);return r.set(o,n),r}return o};var Ft=st,pt={},L={},ut={};ut.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40};var P={};const Bt="[0-9]+",kt="[A-Z $%*+\\-./:]+";let v="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";v=v.replace(/u/g,"\\u");const zt="(?:(?![A-Z0-9 $%*+\\-./:]|"+v+`)(?:.|[\r
]))+`;P.KANJI=new RegExp(v,"g");P.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");P.BYTE=new RegExp(zt,"g");P.NUMERIC=new RegExp(Bt,"g");P.ALPHANUMERIC=new RegExp(kt,"g");const vt=new RegExp("^"+v+"$"),Vt=new RegExp("^"+Bt+"$"),Ht=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");P.testKanji=function(t){return vt.test(t)};P.testNumeric=function(t){return Vt.test(t)};P.testAlphanumeric=function(t){return Ht.test(t)};(function(e){const t=ut,i=P;e.NUMERIC={id:"Numeric",bit:1<<0,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:1<<1,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:1<<2,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:1<<3,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(r,s){if(!r.ccBits)throw new Error("Invalid mode: "+r);if(!t.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?r.ccBits[0]:s<27?r.ccBits[1]:r.ccBits[2]},e.getBestModeForData=function(r){return i.testNumeric(r)?e.NUMERIC:i.testAlphanumeric(r)?e.ALPHANUMERIC:i.testKanji(r)?e.KANJI:e.BYTE},e.toString=function(r){if(r&&r.id)return r.id;throw new Error("Invalid mode")},e.isValid=function(r){return r&&r.bit&&r.ccBits};function o(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+n)}}e.from=function(r,s){if(e.isValid(r))return r;try{return o(r)}catch{return s}}})(L);(function(e){const t=I,i=j,o=$,n=L,r=ut,s=1<<12|1<<11|1<<10|1<<9|1<<8|1<<5|1<<2|1<<0,c=t.getBCHDigit(s);function u(w,f,m){for(let y=1;y<=40;y++)if(f<=e.getCapacity(y,m,w))return y}function a(w,f){return n.getCharCountIndicator(w,f)+4}function l(w,f){let m=0;return w.forEach(function(y){const T=a(y.mode,f);m+=T+y.getBitsLength()}),m}function p(w,f){for(let m=1;m<=40;m++)if(l(w,m)<=e.getCapacity(m,f,n.MIXED))return m}e.from=function(f,m){return r.isValid(f)?parseInt(f,10):m},e.getCapacity=function(f,m,y){if(!r.isValid(f))throw new Error("Invalid QR Code version");typeof y>"u"&&(y=n.BYTE);const T=t.getSymbolTotalCodewords(f),h=i.getTotalCodewordsCount(f,m),E=(T-h)*8;if(y===n.MIXED)return E;const d=E-a(y,f);switch(y){case n.NUMERIC:return Math.floor(d/10*3);case n.ALPHANUMERIC:return Math.floor(d/11*2);case n.KANJI:return Math.floor(d/13);case n.BYTE:default:return Math.floor(d/8)}},e.getBestVersionForData=function(f,m){let y;const T=o.from(m,o.M);if(Array.isArray(f)){if(f.length>1)return p(f,T);if(f.length===0)return 1;y=f[0]}else y=f;return u(y.mode,y.getLength(),T)},e.getEncodedBits=function(f){if(!r.isValid(f)||f<7)throw new Error("Invalid QR Code version");let m=f<<12;for(;t.getBCHDigit(m)-c>=0;)m^=s<<t.getBCHDigit(m)-c;return f<<12|m}})(pt);var At={};const et=I,Tt=1<<10|1<<8|1<<5|1<<4|1<<2|1<<1|1<<0,Kt=1<<14|1<<12|1<<10|1<<4|1<<1,ft=et.getBCHDigit(Tt);At.getEncodedBits=function(t,i){const o=t.bit<<3|i;let n=o<<10;for(;et.getBCHDigit(n)-ft>=0;)n^=Tt<<et.getBCHDigit(n)-ft;return(o<<10|n)^Kt};var It={};const Jt=L;function _(e){this.mode=Jt.NUMERIC,this.data=e.toString()}_.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)};_.prototype.getLength=function(){return this.data.length};_.prototype.getBitsLength=function(){return _.getBitsLength(this.data.length)};_.prototype.write=function(t){let i,o,n;for(i=0;i+3<=this.data.length;i+=3)o=this.data.substr(i,3),n=parseInt(o,10),t.put(n,10);const r=this.data.length-i;r>0&&(o=this.data.substr(i),n=parseInt(o,10),t.put(n,r*3+1))};var Yt=_;const Ot=L,W=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function D(e){this.mode=Ot.ALPHANUMERIC,this.data=e}D.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)};D.prototype.getLength=function(){return this.data.length};D.prototype.getBitsLength=function(){return D.getBitsLength(this.data.length)};D.prototype.write=function(t){let i;for(i=0;i+2<=this.data.length;i+=2){let o=W.indexOf(this.data[i])*45;o+=W.indexOf(this.data[i+1]),t.put(o,11)}this.data.length%2&&t.put(W.indexOf(this.data[i]),6)};var $t=D;const jt=L;function U(e){this.mode=jt.BYTE,typeof e=="string"?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}U.getBitsLength=function(t){return t*8};U.prototype.getLength=function(){return this.data.length};U.prototype.getBitsLength=function(){return U.getBitsLength(this.data.length)};U.prototype.write=function(e){for(let t=0,i=this.data.length;t<i;t++)e.put(this.data[t],8)};var Gt=U;const Qt=L,qt=I;function F(e){this.mode=Qt.KANJI,this.data=e}F.getBitsLength=function(t){return t*13};F.prototype.getLength=function(){return this.data.length};F.prototype.getBitsLength=function(){return F.getBitsLength(this.data.length)};F.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let i=qt.toSJIS(this.data[t]);if(i>=33088&&i<=40956)i-=33088;else if(i>=57408&&i<=60351)i-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);i=(i>>>8&255)*192+(i&255),e.put(i,13)}};var Wt=F,Nt={exports:{}};(function(e){var t={single_source_shortest_paths:function(i,o,n){var r={},s={};s[o]=0;var c=t.PriorityQueue.make();c.push(o,0);for(var u,a,l,p,w,f,m,y,T;!c.empty();){u=c.pop(),a=u.value,p=u.cost,w=i[a]||{};for(l in w)w.hasOwnProperty(l)&&(f=w[l],m=p+f,y=s[l],T=typeof s[l]>"u",(T||y>m)&&(s[l]=m,c.push(l,m),r[l]=a))}if(typeof n<"u"&&typeof s[n]>"u"){var h=["Could not find a path from ",o," to ",n,"."].join("");throw new Error(h)}return r},extract_shortest_path_from_predecessor_list:function(i,o){for(var n=[],r=o;r;)n.push(r),i[r],r=i[r];return n.reverse(),n},find_path:function(i,o,n){var r=t.single_source_shortest_paths(i,o,n);return t.extract_shortest_path_from_predecessor_list(r,n)},PriorityQueue:{make:function(i){var o=t.PriorityQueue,n={},r;i=i||{};for(r in o)o.hasOwnProperty(r)&&(n[r]=o[r]);return n.queue=[],n.sorter=i.sorter||o.default_sorter,n},default_sorter:function(i,o){return i.cost-o.cost},push:function(i,o){var n={value:i,cost:o};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t})(Nt);(function(e){const t=L,i=Yt,o=$t,n=Gt,r=Wt,s=P,c=I,u=Nt.exports;function a(h){return unescape(encodeURIComponent(h)).length}function l(h,E,d){const g=[];let C;for(;(C=h.exec(d))!==null;)g.push({data:C[0],index:C.index,mode:E,length:C[0].length});return g}function p(h){const E=l(s.NUMERIC,t.NUMERIC,h),d=l(s.ALPHANUMERIC,t.ALPHANUMERIC,h);let g,C;return c.isKanjiModeEnabled()?(g=l(s.BYTE,t.BYTE,h),C=l(s.KANJI,t.KANJI,h)):(g=l(s.BYTE_KANJI,t.BYTE,h),C=[]),E.concat(d,g,C).sort(function(A,N){return A.index-N.index}).map(function(A){return{data:A.data,mode:A.mode,length:A.length}})}function w(h,E){switch(E){case t.NUMERIC:return i.getBitsLength(h);case t.ALPHANUMERIC:return o.getBitsLength(h);case t.KANJI:return r.getBitsLength(h);case t.BYTE:return n.getBitsLength(h)}}function f(h){return h.reduce(function(E,d){const g=E.length-1>=0?E[E.length-1]:null;return g&&g.mode===d.mode?(E[E.length-1].data+=d.data,E):(E.push(d),E)},[])}function m(h){const E=[];for(let d=0;d<h.length;d++){const g=h[d];switch(g.mode){case t.NUMERIC:E.push([g,{data:g.data,mode:t.ALPHANUMERIC,length:g.length},{data:g.data,mode:t.BYTE,length:g.length}]);break;case t.ALPHANUMERIC:E.push([g,{data:g.data,mode:t.BYTE,length:g.length}]);break;case t.KANJI:E.push([g,{data:g.data,mode:t.BYTE,length:a(g.data)}]);break;case t.BYTE:E.push([{data:g.data,mode:t.BYTE,length:a(g.data)}])}}return E}function y(h,E){const d={},g={start:{}};let C=["start"];for(let B=0;B<h.length;B++){const A=h[B],N=[];for(let b=0;b<A.length;b++){const M=A[b],k=""+B+b;N.push(k),d[k]={node:M,lastCount:0},g[k]={};for(let q=0;q<C.length;q++){const S=C[q];d[S]&&d[S].node.mode===M.mode?(g[S][k]=w(d[S].lastCount+M.length,M.mode)-w(d[S].lastCount,M.mode),d[S].lastCount+=M.length):(d[S]&&(d[S].lastCount=M.length),g[S][k]=w(M.length,M.mode)+4+t.getCharCountIndicator(M.mode,E))}}C=N}for(let B=0;B<C.length;B++)g[C[B]].end=0;return{map:g,table:d}}function T(h,E){let d;const g=t.getBestModeForData(h);if(d=t.from(E,g),d!==t.BYTE&&d.bit<g.bit)throw new Error('"'+h+'" cannot be encoded with mode '+t.toString(d)+`.
 Suggested mode is: `+t.toString(g));switch(d===t.KANJI&&!c.isKanjiModeEnabled()&&(d=t.BYTE),d){case t.NUMERIC:return new i(h);case t.ALPHANUMERIC:return new o(h);case t.KANJI:return new r(h);case t.BYTE:return new n(h)}}e.fromArray=function(E){return E.reduce(function(d,g){return typeof g=="string"?d.push(T(g,null)):g.data&&d.push(T(g.data,g.mode)),d},[])},e.fromString=function(E,d){const g=p(E,c.isKanjiModeEnabled()),C=m(g),B=y(C,d),A=u.find_path(B.map,"start","end"),N=[];for(let b=1;b<A.length-1;b++)N.push(B.table[A[b]].node);return e.fromArray(f(N))},e.rawSplit=function(E){return e.fromArray(p(E,c.isKanjiModeEnabled()))}})(It);const Q=I,Z=$,Zt=_t,Xt=Dt,xt=wt,te=mt,nt=yt,ot=j,ee=Ft,Y=pt,ne=At,oe=L,X=It;function re(e,t){const i=e.size,o=te.getPositions(t);for(let n=0;n<o.length;n++){const r=o[n][0],s=o[n][1];for(let c=-1;c<=7;c++)if(!(r+c<=-1||i<=r+c))for(let u=-1;u<=7;u++)s+u<=-1||i<=s+u||(c>=0&&c<=6&&(u===0||u===6)||u>=0&&u<=6&&(c===0||c===6)||c>=2&&c<=4&&u>=2&&u<=4?e.set(r+c,s+u,!0,!0):e.set(r+c,s+u,!1,!0))}}function ie(e){const t=e.size;for(let i=8;i<t-8;i++){const o=i%2===0;e.set(i,6,o,!0),e.set(6,i,o,!0)}}function se(e,t){const i=xt.getPositions(t);for(let o=0;o<i.length;o++){const n=i[o][0],r=i[o][1];for(let s=-2;s<=2;s++)for(let c=-2;c<=2;c++)s===-2||s===2||c===-2||c===2||s===0&&c===0?e.set(n+s,r+c,!0,!0):e.set(n+s,r+c,!1,!0)}}function ue(e,t){const i=e.size,o=Y.getEncodedBits(t);let n,r,s;for(let c=0;c<18;c++)n=Math.floor(c/3),r=c%3+i-8-3,s=(o>>c&1)===1,e.set(n,r,s,!0),e.set(r,n,s,!0)}function x(e,t,i){const o=e.size,n=ne.getEncodedBits(t,i);let r,s;for(r=0;r<15;r++)s=(n>>r&1)===1,r<6?e.set(r,8,s,!0):r<8?e.set(r+1,8,s,!0):e.set(o-15+r,8,s,!0),r<8?e.set(8,o-r-1,s,!0):r<9?e.set(8,15-r-1+1,s,!0):e.set(8,15-r-1,s,!0);e.set(o-8,8,1,!0)}function ce(e,t){const i=e.size;let o=-1,n=i-1,r=7,s=0;for(let c=i-1;c>0;c-=2)for(c===6&&c--;;){for(let u=0;u<2;u++)if(!e.isReserved(n,c-u)){let a=!1;s<t.length&&(a=(t[s]>>>r&1)===1),e.set(n,c-u,a),r--,r===-1&&(s++,r=7)}if(n+=o,n<0||i<=n){n-=o,o=-o;break}}}function ae(e,t,i){const o=new Zt;i.forEach(function(u){o.put(u.mode.bit,4),o.put(u.getLength(),oe.getCharCountIndicator(u.mode,e)),u.write(o)});const n=Q.getSymbolTotalCodewords(e),r=ot.getTotalCodewordsCount(e,t),s=(n-r)*8;for(o.getLengthInBits()+4<=s&&o.put(0,4);o.getLengthInBits()%8!==0;)o.putBit(0);const c=(s-o.getLengthInBits())/8;for(let u=0;u<c;u++)o.put(u%2?17:236,8);return le(o,e,t)}function le(e,t,i){const o=Q.getSymbolTotalCodewords(t),n=ot.getTotalCodewordsCount(t,i),r=o-n,s=ot.getBlocksCount(t,i),c=o%s,u=s-c,a=Math.floor(o/s),l=Math.floor(r/s),p=l+1,w=a-l,f=new ee(w);let m=0;const y=new Array(s),T=new Array(s);let h=0;const E=new Uint8Array(e.buffer);for(let A=0;A<s;A++){const N=A<u?l:p;y[A]=E.slice(m,m+N),T[A]=f.encode(y[A]),m+=N,h=Math.max(h,N)}const d=new Uint8Array(o);let g=0,C,B;for(C=0;C<h;C++)for(B=0;B<s;B++)C<y[B].length&&(d[g++]=y[B][C]);for(C=0;C<w;C++)for(B=0;B<s;B++)d[g++]=T[B][C];return d}function fe(e,t,i,o){let n;if(Array.isArray(e))n=X.fromArray(e);else if(typeof e=="string"){let a=t;if(!a){const l=X.rawSplit(e);a=Y.getBestVersionForData(l,i)}n=X.fromString(e,a||40)}else throw new Error("Invalid data");const r=Y.getBestVersionForData(n,i);if(!r)throw new Error("The amount of data is too big to be stored in a QR Code");if(!t)t=r;else if(t<r)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+r+`.
`);const s=ae(t,i,n),c=Q.getSymbolSize(t),u=new Xt(c);return re(u,t),ie(u),se(u,t),x(u,i,0),t>=7&&ue(u,t),ce(u,s),isNaN(o)&&(o=nt.getBestMask(u,x.bind(null,u,i))),nt.applyMask(o,u),x(u,i,o),{modules:u,version:t,errorCorrectionLevel:i,maskPattern:o,segments:n}}dt.create=function(t,i){if(typeof t>"u"||t==="")throw new Error("No input text");let o=Z.M,n,r;return typeof i<"u"&&(o=Z.from(i.errorCorrectionLevel,Z.M),n=Y.from(i.version),r=nt.from(i.maskPattern),i.toSJISFunc&&Q.setToSJISFunction(i.toSJISFunc)),fe(t,n,o,r)};var Mt={},ct={};(function(e){function t(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let o=i.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+i);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(r){return[r,r]}))),o.length===6&&o.push("F","F");const n=parseInt(o.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:n&255,hex:"#"+o.slice(0,6).join("")}}e.getOptions=function(o){o||(o={}),o.color||(o.color={});const n=typeof o.margin>"u"||o.margin===null||o.margin<0?4:o.margin,r=o.width&&o.width>=21?o.width:void 0,s=o.scale||4;return{width:r,scale:r?4:s,margin:n,color:{dark:t(o.color.dark||"#000000ff"),light:t(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},e.getScale=function(o,n){return n.width&&n.width>=o+n.margin*2?n.width/(o+n.margin*2):n.scale},e.getImageWidth=function(o,n){const r=e.getScale(o,n);return Math.floor((o+n.margin*2)*r)},e.qrToImageData=function(o,n,r){const s=n.modules.size,c=n.modules.data,u=e.getScale(s,r),a=Math.floor((s+r.margin*2)*u),l=r.margin*u,p=[r.color.light,r.color.dark];for(let w=0;w<a;w++)for(let f=0;f<a;f++){let m=(w*a+f)*4,y=r.color.light;if(w>=l&&f>=l&&w<a-l&&f<a-l){const T=Math.floor((w-l)/u),h=Math.floor((f-l)/u);y=p[c[T*s+h]?1:0]}o[m++]=y.r,o[m++]=y.g,o[m++]=y.b,o[m]=y.a}}})(ct);(function(e){const t=ct;function i(n,r,s){n.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=s,r.width=s,r.style.height=s+"px",r.style.width=s+"px"}function o(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}e.render=function(r,s,c){let u=c,a=s;typeof u>"u"&&(!s||!s.getContext)&&(u=s,s=void 0),s||(a=o()),u=t.getOptions(u);const l=t.getImageWidth(r.modules.size,u),p=a.getContext("2d"),w=p.createImageData(l,l);return t.qrToImageData(w.data,r,u),i(p,a,l),p.putImageData(w,0,0),a},e.renderToDataURL=function(r,s,c){let u=c;typeof u>"u"&&(!s||!s.getContext)&&(u=s,s=void 0),u||(u={});const a=e.render(r,s,u),l=u.type||"image/png",p=u.rendererOpts||{};return a.toDataURL(l,p.quality)}})(Mt);var Pt={};const ge=ct;function gt(e,t){const i=e.a/255,o=t+'="'+e.hex+'"';return i<1?o+" "+t+'-opacity="'+i.toFixed(2).slice(1)+'"':o}function tt(e,t,i){let o=e+t;return typeof i<"u"&&(o+=" "+i),o}function de(e,t,i){let o="",n=0,r=!1,s=0;for(let c=0;c<e.length;c++){const u=Math.floor(c%t),a=Math.floor(c/t);!u&&!r&&(r=!0),e[c]?(s++,c>0&&u>0&&e[c-1]||(o+=r?tt("M",u+i,.5+a+i):tt("m",n,0),n=0,r=!1),u+1<t&&e[c+1]||(o+=tt("h",s),s=0)):n++}return o}Pt.render=function(t,i,o){const n=ge.getOptions(i),r=t.modules.size,s=t.modules.data,c=r+n.margin*2,u=n.color.light.a?"<path "+gt(n.color.light,"fill")+' d="M0 0h'+c+"v"+c+'H0z"/>':"",a="<path "+gt(n.color.dark,"stroke")+' d="'+de(s,r,n.margin)+'"/>',l='viewBox="0 0 '+c+" "+c+'"',w='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+l+' shape-rendering="crispEdges">'+u+a+`</svg>
`;return typeof o=="function"&&o(null,w),w};const he=Rt,rt=dt,St=Mt,we=Pt;function at(e,t,i,o,n){const r=[].slice.call(arguments,1),s=r.length,c=typeof r[s-1]=="function";if(!c&&!he())throw new Error("Callback required as last argument");if(c){if(s<2)throw new Error("Too few arguments provided");s===2?(n=i,i=t,t=o=void 0):s===3&&(t.getContext&&typeof n>"u"?(n=o,o=void 0):(n=o,o=i,i=t,t=void 0))}else{if(s<1)throw new Error("Too few arguments provided");return s===1?(i=t,t=o=void 0):s===2&&!t.getContext&&(o=i,i=t,t=void 0),new Promise(function(u,a){try{const l=rt.create(i,o);u(e(l,t,o))}catch(l){a(l)}})}try{const u=rt.create(i,o);n(null,e(u,t,o))}catch(u){n(u)}}O.create=rt.create;O.toCanvas=at.bind(null,St.render);O.toDataURL=at.bind(null,St.renderToDataURL);O.toString=at.bind(null,function(e,t,i){return we.render(e,i)});export{O as b,ye as g};
