<template>
  <div>
    <div class="person">
      <base-header>
        <span class="el-title">我的应用</span>
        <span class="el-search">
          <base-input
            class="el-search-input"
            v-model="searchQuery"
            placeholder="搜索应用"
            prefix-icon="Search"
            @input="handleSearch"
            clearable
            style="width: 200px"
          />
          <base-button 
            class="el-search-btn"
            icon="Refresh" 
            size="small" 
          />
          <base-select
            class="el-search-select"
            suffix-icon="CaretTop"
            v-model="viewType"
            placeholder="Select"
            size="small"
          >
            <base-option
              v-for="item in viewOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </base-select>
        </span>
        <!--
        <div class="el-row">
          <span class="el-recent-access">最新访问</span>
          <span class="el-recent-data">
            <span class="el-recent-item">
              最新访问1
              <base-icon class="el-recent-icon" name="close" />
            </span>
            <span class="el-recent-item">
              最新访问2
              <base-icon class="el-recent-icon" name="close" />
            </span>
            <span class="el-recent-item">
              最新访问3
              <base-icon class="el-recent-icon" name="close" />
            </span>
            <base-icon class="el-recent-clear" name="close" title="清空" />
          </span>
        </div>
        -->
      </base-header>
      
      <!-- 主体内容区域：使用 el-container 实现左右布局 -->
      <base-container>
        <!-- 左侧分类导航 -->
        <base-aside width="96px" class="category-aside">
          <el-menu 
            class="category-menu"
            @select="handleCategoryChange"
            :default-active="defaultActiveIndex"
          >
            <el-menu-item index="0" @click="handleCategoryChange(null)">
              全部
            </el-menu-item>
            <el-menu-item 
              v-for="category in categoryList" 
              :key="category.id"
              :index="category.id.toString()"
            >
              {{ category.name }}
            </el-menu-item>
          </el-menu>
        </base-aside>

        <!-- 右侧应用列表 -->
        <base-main class="app-main">
          <div 
            v-for="category in filteredCategories" 
            :key="category.id" 
            class="category-section"
          >
            <!-- 分类标题 -->
            <h3 class="category-title">{{ category.name }}</h3>

            <!-- 应用列表 -->
            <div class="apps-container">
              <div
                v-for="item in category.apps"
                :key="item.id"
                class="app-card"
                :class="{ 'disabled': !item.WebUrl || item.maint }"
              >
                <div 
                  v-if="item.maint" 
                  class="status-badge"
                >
                  维护中
                </div>

                <el-link
                  class="app_list"
                  :underline="false"
                  :disabled="!item.WebUrl || item.maint"
                  @click.prevent="handleAppClick(item)"
                >
                  <div class="icon-wrapper">
                    <el-tooltip effect="light" placement="bottom">
                      <template #content>
                        <div class="tooltip-content text-center">
                          <span v-if="item.WebUrl">{{ item.WebUrl }}</span>
                          <span v-else>暂无访问地址</span>
                        </div>
                      </template>
                      <base-avatar
                        shape="square"
                        :size="48"
                        :src="item.icon"
                        @error="handleIconError"
                        :style="(!item.icon || iconLoadError) ? `background-color: ${getRandomColor(item.app_name)} !important` : ''"
                      >
                        {{ (!item.icon || iconLoadError) ? item.app_name.slice(0, 2) : '' }}
                      </base-avatar>
                    </el-tooltip>
                  </div>

                  <div class="app-info">
                    <div class="app-name">
                      {{ item.app_name }}
                    </div>
                    <div class="app-remark">
                      这是一段应用程序的描述信息。
                    </div>
                  </div>
                </el-link>
              </div>
            </div>
          </div>
        </base-main>
      </base-container>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AppPage',
}
</script>
<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Message } from '@/components/base'

import { getCurrentUser } from '@/api/resource'
import { useRoute } from 'vue-router'
import { useUserStore } from '../../pinia/modules/user'

const searchQuery = ref('')
const selectedCategory = ref(null)
const categoryList = ref([])
const filteredCategories = ref([])
const defaultActiveIndex = ref('1')
const iconLoadError = ref(false)
const viewType = ref('standard')
const viewOptions = reactive([
  { 'key': 'standard', 'label': '标准视图' },
  { 'key': 'compact', 'label': '紧凑视图' },
])

// WebSocket相关状态
const ws = ref(null)
const wsConnecting = ref(false)

// 显示状态消息
const showMessage = (message, type = 'success', duration = 3000) => {
  Message({
    message,
    type,
    duration  // 使用传入的持续时间，默认3秒，特殊消息可以更长
  })
}

// 连接WebSocket
const connectWebSocket = () => {
  return new Promise((resolve, reject) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      resolve(ws.value)
      return
    }

    const wsInstance = new WebSocket('ws://localhost:50001')
    wsConnecting.value = true

    wsInstance.onopen = () => {
      console.log('WebSocket Connected')
      ws.value = wsInstance
      wsConnecting.value = false
      resolve(wsInstance)
    }

    wsInstance.onmessage = (evt) => {
      const response = evt.data
      if (response.startsWith('Ok')) {
        //showMessage('应用启动成功')
      } else if (response.startsWith('Failed')) {
        showMessage(response, 'error')
      }
    }

    wsInstance.onclose = () => {
      console.log('WebSocket Disconnected')
      ws.value = null
      wsConnecting.value = false
    }

    wsInstance.onerror = (err) => {
      console.error('WebSocket Error:', err)
      // 移除这里的 showMessage 调用
      wsConnecting.value = false
      reject(err)
    }

    setTimeout(() => {
      if (wsConnecting.value) {
        wsConnecting.value = false
        wsInstance.close()
        reject(new Error('连接超时'))
      }
    }, 5000)
  })
}

// 发送打开应用消息
const sendOpenAppMessage = async (appName) => {
  return new Promise((resolve, reject) => {
    let messageReceived = false;
    let timeoutId;

    const connectAndSend = async () => {
      try {
        const wsInstance = await connectWebSocket()
        const message = {
          action: 3,
          msg: appName
        }
        
        // 设置超时检查
        timeoutId = setTimeout(() => {
          if (!messageReceived) {
            wsInstance.close()
            reject(new Error('启动超时：未收到响应'))
          }
        }, 3000) // 3秒超时

        // 添加一次性消息监听
        wsInstance.onmessage = (evt) => {
          messageReceived = true
          clearTimeout(timeoutId)
          
          const response = evt.data
          if (response.startsWith('Ok')) {
            resolve()
          } else {
            reject(new Error(response))
          }
        }

        wsInstance.send(JSON.stringify(message))
        console.log('发送消息:', message)
      } catch (error) {
        clearTimeout(timeoutId)
        reject(error)
      }
    }

    connectAndSend()
  })
}

// 处理应用打开
const handleAppClick = async (item) => {
  if (!item.WebUrl || item.maint) return

  if (item.WebUrl.toLowerCase().startsWith('cs:')) {
    const appName = item.WebUrl.substring(3)
    try {
      showMessage('正在启动爱尔企业浏览器...', 'info')
      await sendOpenAppMessage(appName)
      showMessage('启动成功', 'success')
    } catch (error) {
      showMessage(`启动企业浏览器失败：
      检查是否已安装企业浏览器，
      如仍然无法启动，请手动运行企业浏览器访问该应用！`, 'warning', 8000)
    }
  } else {
    window.open(item.WebUrl, '_blank')
  }
}

// 组件卸载时关闭WebSocket连接
onUnmounted(() => {
  if (ws.value) {
    ws.value.close()
    ws.value = null
  }
})

// 获取随机背景色，基于应用名生成固定颜色
const getRandomColor = (appName) => {
  const colors = [
    '#71BDDF',  // 浅蓝
    '#8AB05D',  // 浅绿
    '#9571DF',  // 浅紫
    '#DF7171',  // 浅红
    '#DFC271',  // 浅黄
    '#71DFA7',  // 薄荷绿
    '#B05D8A',  // 浅粉
    '#5D8AB0'   // 深蓝
  ]
  
  let sum = 0
  for (let i = 0; i < appName.length; i++) {
    sum += appName.charCodeAt(i)
  }
  
  return colors[sum % colors.length]
}

// 处理图标加载失败
const handleIconError = () => {
  iconLoadError.value = true
}

const getTooltipContent = (item) => {
  if (item.WebUrl) {
    return `
      <div class="tooltip-content">
        <div class="tooltip-item">
          <span>${item.WebUrl}</span>
        </div>
      </div>
    `
  }
  
  return '暂无访问地址'
}

// 添加分类切换处理函数
const handleCategoryChange = (categoryId) => {
  selectedCategory.value = parseInt(categoryId)
  if (!categoryId) {
    filteredCategories.value = categoryList.value
  } else {
    filteredCategories.value = categoryList.value.filter(category => 
      category.id === parseInt(categoryId)
    )
  }
}

// 添加搜索处理函数
const handleSearch = () => {
  if (!searchQuery.value) {
    filteredCategories.value = categoryList.value
    return
  }

  const searchText = searchQuery.value.toLowerCase().trim()
  filteredCategories.value = categoryList.value
    .map(category => ({
      ...category,
      apps: category.apps.filter(app => 
        app.app_name.toLowerCase().includes(searchText)
      )
    }))
    .filter(category => category.apps.length > 0)
}

const initCategories = async() => {
  try {
    const { data: apiResponse } = await getCurrentUser()
    console.log('API返回数据:', apiResponse)
    
    if (apiResponse.code === 0 && apiResponse.data) {
      const formattedData = apiResponse.data.map((item, index) => ({
        id: index + 1,
        name: item.category,
        apps: item.apps.map(app => ({
          id: app.id,
          app_name: app.app_name,
          app_desc: app.app_type,
          icon: app.icon,
          maint: app.maintenance === 2,
          app_type: app.app_type,
          app_sites: app.app_sites,
          WebUrl: app.WebUrl  // 确保 WebUrl 字段被保留
        }))
      }))
      
      console.log('格式化后的数据:', formattedData)
      categoryList.value = formattedData
      filteredCategories.value = formattedData
      
      if (formattedData.length > 0) {
        selectedCategory.value = formattedData[0].id
        defaultActiveIndex.value = formattedData[0].id.toString()
      }
    }
  } catch (error) {
    console.error('API调用出错:', error)
  }
}

onMounted(() => {
  initCategories()
})
const userStore = useUserStore()
const route = useRoute()
const cli_query = route.query
// 获取 corpID，在本地文件环境下使用默认值
let corpID = null
try {
  // 检查是否为本地文件协议
  if (document.location.protocol === 'qrc:' || document.location.protocol !== 'file:') {
    const req = new XMLHttpRequest()
    req.open('GET', document.location, false)
    req.send(null)
    corpID = req.getResponseHeader('X-Corp-ID')
  }
} catch (error) {
  console.warn('无法获取 X-Corp-ID header，使用默认值:', error)
}
const clineData = {
  action: 0,
  msg: {
    token: userStore.token.accessToken,
    refreshToken: userStore.token.refreshToken,
    realm: corpID || 'default',
  },
  platform: document.location.hostname
}

//if (cli_query.type === 'client') 
{
  const port = cli_query.wp || 50001
  const websocket = ref({})
  const wsUrl = ref(`ws://127.0.0.1:${port}`)
  const platform = navigator.platform
  if (platform.indexOf('Mac') === 0 || platform === 'MacIntel') {
    wsUrl.value = `wss://127.0.0.1:${port}`
  }
  const initWebSocket = () => {
    websocket.value = new WebSocket(wsUrl.value)
    websocket.value.onopen = () => {
      console.log('socket连接成功')
      sendMessage(JSON.stringify(clineData))
    }
    websocket.value.onmessage = (e) => {
      console.log(e)
      closeWebSocket()
    }
    websocket.value.onerror = () => {
      console.log('socket连接错误:' + wsUrl.value)
      window.location.href = `asecagent://?web=${JSON.stringify(clineData)}`
    }
  }
  // 发送消息
  const sendMessage = (msg) => {
    console.log(msg, '0')
    websocket.value.send(msg)
  }
  // 关闭链接（在页面销毁时可销毁链接）
  const closeWebSocket = () => {
    console.log('socket断开链接')
    websocket.value.close()
  }
  console.log(`asecagent://?web=${JSON.stringify(clineData)}`)
  initWebSocket()
}

</script>
<style lang="scss" scoped>
.person {
  height: 100vh;
  background: #FFFFFF;
  border-radius: 4px;
  height: 100vh;
  max-height: calc(100vh - 68px);

  .base-header {
    display: flex;
    justify-content: space-between; 
    font-weight: Medium;
    color: #282a33;
    font-size: 16px;
    line-height: 22px;
    padding: 0 15px;
    display: flex;
    align-items: center;

    .el-title {
      padding-top: 19px;
    }
    .el-search {
      padding-top: 16px;
      display: flex;
      justify-content: space-between; 
    }
    .el-search-input {
      width: 200px;
      height: 28px;
      border-radius: 4px;
      font-size: 14px;
      > .el-input__wrapper {
        background: #f5f5f7;
      }
    }
    .el-search-btn {
      font-size: 14px !important;
      margin-left: 10px;
      min-height: 28px;
      padding: 0px;
      width: 28px;
      height: 28px;
      background: #f5f5f7;
      border-radius: 4px;
    }
    .el-search-select {
      margin-left: 10px;
      padding: 0px;
      padding-top: 3px;
      width: 80px;
      height: 20px !important;
      :deep(.el-input__wrapper) {
        background-color: transparent !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;   // 去除默认内边距
        line-height: 1 !important; // 确保行高不影响高度
        height: 20px !important;
      }
      :deep(.el-input__suffix) {
        padding: 0 !important;   // 去除默认内边距
        height: 20px !important;
      }
      :deep(.el-input__suffix-inner) {
        margin-left: -28px;
      }
      :deep(.el-input__inner) {
        height: 20px !important;
        line-height: 20px !important;
      }
    }

    .search-input {
      width: 300px;  // 设置搜索框宽度
      
      :deep(.el-input__wrapper) {
        border-radius: 4px;
        background-color: #f5f7fa;
        
        &.is-focus {
          background-color: #ffffff;
        }
      }

      :deep(.el-input__inner) {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        
        &::placeholder {
          color: #909399;
        }
      }
    }
  }

  .category-aside {
    
    .category-menu {
      border-right: none;

      .el-menu-item {
        height: 28px;
        line-height: 28px;
        margin: 4px 0;
        font-size: 14px;
        color: #606266;
        position: relative;
        transition: all 0.3s;
        cursor: pointer;
        border-radius: 4px;
        margin-left: 20px;

        // 默认状态
        &:not(.is-active) {
          background-color: transparent;
          color: #606266;
        }

        // 悬浮状态
        &:hover:not(.is-active) {
          background-color: #f0f7ff;
          color: #409EFF;
        }

        // 焦点状态
        &:focus {
          outline: none;
          background-color: #f0f7ff;
          &:not(.is-active) {
            color: #409EFF;
          }
        }

        // 选中状态
        &.is-active {
          background-color: #536ce6;;
          color: #ffffff;;
          font-weight: 500;
        }

        // 点击状态
        &:active {
          background-color: #dcedff;
        }
      }
    }
  }

  .app-main {
    padding: 12px 20px;
    overflow-y: auto;

    .category-section {
      margin-bottom: 24px;

      .category-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
        margin-top: 0;
        padding-bottom: 6px;
        width: fit-content;
        min-width: 100px;
        max-width: 200px;
        line-height: 1.2;
      }

      .apps-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, 265px);
        gap: 16px;
        padding: 16px 0;

        .app-card {
          width: 257px;
          height: 64px;
          margin-right: 8px;
          background: #f7f7fa;
          border: 1px solid #f2f2f5;
          border-radius: 4px;
          position: relative;
          display: flex;
          justify-content: center;

          :deep(.el-link) {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
            padding: 0;
          }

          :deep(.el-link__inner) {
            width: 100% !important;
            height: 100% !important;
          }

          .icon-wrapper {
            width: 48px;
            height: 48px;
            margin-left: 13px;
            display: flex;
            align-items: center;

            :deep(.el-avatar) {
              width: 48px;
              height: 48px;
              color: #ffffff;
              font-size: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
              
              &.default-avatar {
                background-color: #f0f2f5 !important;
                color: #909399;
              }
            }
          }

          .app-info {
            width: 100%;
            display: flex;
            flex-direction: column; /* 子元素垂直排列 */
            justify-content: center; /* 垂直方向上居中对齐 */
            
            .app-name {
              font-weight: bold; /* 加粗 */
              height: 20px;
              width: 56px;
              font-size: 14px;
              font-family: PingFang SC, PingFang SC-Medium;
              font-weight: bold; 
              text-align: left;
              color: #282a33;
              line-height: 20px;
              margin-left: 12px;
              margin-top: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .app-remark {
              margin-left: 12px;
              width: 168px;
              height: 17px;
              font-size: 12px;
              font-family: PingFang SC, PingFang SC-Regular;
              font-weight: Regular;
              text-align: left;
              color: #686e84;
              line-height: 17px;
            }
          }

          .status-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            padding: 1px 6px;
            border-radius: 8px;
            font-size: 11px;
            background-color: #D3C5A5;
            color: #000000;
            font-weight: 500;
            z-index: 2;
          }

          // 正常状态（可点击）
          :deep(.el-link) {
            
            &:hover {
              .icon-wrapper {
                transform: scale(1.05);
              }
            }
          }

          // 维护中状态（不可点击）
          &.disabled {
           :deep(.el-link) {
              cursor: not-allowed;
              
              &:hover {
                .icon-wrapper {
                  transform: none;
                }
              }
            }

            .icon-wrapper {
              opacity: 0.6;
            }

            :deep(.el-avatar) {
              filter: grayscale(100%);
            }
          }
        }
      }
    }

    // 第一个分类区域特殊处理
    .category-section:first-child {
      margin-top: 0;
    }

    .el-recent-access {
      margin-left: 4px;
      margin-top: 3px;
      width: 56px;
      height: 20px;
      font-size: 14px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: Regular;
      text-align: left;
      color: #282a33;
      line-height: 20px;
    }
    .el-recent-data {
      margin-left: 15px;
      height:20px;
    }
    .el-recent-item {
      margin-top: 2px;
      margin-right: 6px;
      height: 25px;
      padding: 4px 0px 4px 6px;
      background: #f5f6fe;
      border-radius: 4px;
      font-size: 12px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: Regular;
      color: #536ce6;
    }
    .el-recent-icon {
      width: 8px;
      height: 8px;
      margin: 8px 6px 8px 5px;
    }
    .el-recent-clear {
      opacity: 0.6;
      margin-top: 6px;
      position: absolute;
      width: 14px;
      height: 14px;
      right: 6px;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1439px) {
  .apps-container {
    grid-template-columns: repeat(auto-fill, 180px);
    .app-card {
      width: 180px;
      height: 200px;
    }
  }
}

@media screen and (max-width: 1023px) {
  .apps-container {
    grid-template-columns: repeat(auto-fill, 200px);
    .app-card {
      width: 200px;
      height: 220px;
    }
  }
}

@media screen and (max-width: 767px) {
  .apps-container {
    grid-template-columns: repeat(2, 1fr);
    .app-card {
      width: 100%;
      height: auto;
      aspect-ratio: 1/1.125;
    }
  }
}

.tooltip-content {
  width: 200px;
  text-align: center;
}

.web-link {
  color: #409EFF;
  text-decoration: none;
  word-break: break-all;
}

.web-link:hover {
  text-decoration: underline;
}
.el-select__popper {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 2px 20px 0px rgba(46,60,128,0.10);
  margin-left: -10px !important; 
  right: 15px;
  top: 110px;
  max-width: 88px;
  .el-select-dropdown__item {
    width: 72px;
    height: 28px;
    border-radius: 4px;
    margin-left: 7px;
    margin-bottom: 4px;
    padding: 0 8px 0 8px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    line-height: 20px;
    display: flex;
    align-items: center; 
    background: #f5f5f7 !important;
  }
  .el-select-dropdown__item.selected {
    color: #ffffff;
    background: #536ce6 !important;
  }
}
</style>
<style scoped>
.text-center {
  text-align: center;
}

.web-link {
  color: #409EFF;
  text-decoration: none;
}

.web-link:hover {
  text-decoration: underline;
}
</style>
<style>
.el-message {
  white-space: pre-line !important;
  line-height: 1.5 !important;
  padding: 12px 20px !important;
}
</style>
