<template>
  <div>
    <div class="person">
      <base-header class="app-header">
        <div class="header-left">
          <h1 class="page-title">我的应用</h1>
        </div>
        <div class="header-right">
          <div class="search-controls">
            <base-icon class="search-icon" name="search" />
            <base-input
              class="search-input"
              v-model="searchQuery"
              placeholder="搜索应用"
              prefix-icon="Search"
              @input="handleSearch"
              clearable
            />
            <base-button
              class="refresh-btn"
              icon="Refresh"
              size="small"
              @click="initCategories"
            >
              <svg class="icon refresh-btn-icon" aria-hidden="true"><use xlink:href="#icon-search" /></svg>
            </base-button>
            <base-select
              class="view-select"
              v-model="viewType"
              size="small"
            >
              <base-option
                v-for="item in viewOptions"
                :key="item.key"
                :label="item.label"
                :value="item.key"
              />
            </base-select>
          </div>
        </div>
      </base-header>

      <base-header class="app-header"> 
        <div class="el-row">
          <span class="el-recent-access">最新访问</span>
          <span class="el-recent-data">
            <span class="el-recent-item">
              最新访问1
              <base-icon class="el-recent-icon" name="close" size="8px" />
            </span>
            <span class="el-recent-item">
              最新访问2
              <base-icon class="el-recent-icon" name="close" size="8px" />
            </span>
            <span class="el-recent-item">
              最新访问3
              <base-icon class="el-recent-icon" name="close" size="8px" />
            </span>
            <svg class="icon el-recent-clear" aria-hidden="true" title="清空"><use xlink:href="#icon-qingkong" /></svg>
          </span>
        </div>
      </base-header>
      
      <!-- 主体内容区域：使用 el-container 实现左右布局 -->
      <base-container>
        <!-- 左侧分类导航 -->
        <base-aside width="96px" class="category-aside">
          <base-menu
            class="category-menu"
            mode="vertical"
            background-color="#ffffff"
            @select="handleCategoryChange"
            :default-active="defaultActiveIndex"
          >
            <base-menu-item class="category-menu-item" index="-1" @click="handleCategoryChange(-1)">
              收藏 
            </base-menu-item>
            <base-menu-item class="category-menu-item" index="0" @click="handleCategoryChange(null)">
              全部
            </base-menu-item>
            <base-menu-item
              v-for="category in categoryList"
              :key="category.id"
              :index="category.id.toString()"
            >
              <base-tooltip
                effect="light"
                placement="right"
                :content="category.name"
                :disabled="category.name.length <= 5"
              >
                <span class="category-menu-text">{{ category.name.length <= 5 ? category.name : (category.name.substring(0, 4) + '...') }}</span>
              </base-tooltip>
            </base-menu-item>
          </base-menu>
        </base-aside>

        <!-- 右侧应用列表 -->
        <base-main class="app-main">
          <div 
            v-for="category in filteredCategories" 
            :key="category.id" 
            class="category-section"
          >
            <!-- 分类标题 -->
            <div class="category-title-wrapper">
              <h3 class="category-title" :title="category.name">
                {{ category.name }}
              </h3>
              <base-tooltip
                v-if="category.name.length > 20"
                effect="light"
                placement="top"
                :content="category.name"
              >
                <base-icon name="info" class="category-title-info" size="14px" />
              </base-tooltip>
            </div>

            <!-- 应用列表 -->
            <div class="apps-grid" :class="`view-${viewType}`">
              <div
                v-for="item in category.apps"
                :key="item.id"
                class="app-item"
                :class="{ 'disabled': !item.WebUrl || item.maint }"
                @click="handleAppClick(item)"
              >
                <div
                  v-if="item.maint"
                  class="status-badge"
                >
                  维护中
                </div>

                <div class="app-content">
                  <div class="app-icon">
                    <base-tooltip effect="light" placement="bottom">
                      <template #content>
                        <div class="tooltip-content">
                          <span v-if="item.WebUrl">{{ item.WebUrl }}</span>
                          <span v-else>暂无访问地址</span>
                        </div>
                      </template>
                      <base-avatar
                        shape="square"
                        :size="viewType === 'compact' ? 40 : 48"
                        :src="item.icon"
                        @error="handleIconError"
                        :style="(!item.icon || iconLoadError) ? `background-color: ${getRandomColor(item.app_name)} !important` : ''"
                      >
                        {{ (!item.icon || iconLoadError) ? item.app_name.slice(0, 2) : '' }}
                      </base-avatar>
                    </base-tooltip>
                  </div>

                  <div class="app-details">
                    <div class="app-name" :title="item.app_name">
                      {{ item.app_name }}
                    </div>
                    <div v-if="viewType === 'standard'" class="app-desc">
                      {{ item.app_desc || '应用程序' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </base-main>
      </base-container>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AppPage',
}
</script>
<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Message } from '@/components/base'
import agentApi from '@/api/agentApi'

import { getCurrentUser } from '@/api/resource'
import { useRoute } from 'vue-router'
import { useUserStore } from '../../pinia/modules/user'

const searchQuery = ref('')
const selectedCategory = ref(null)
const categoryList = ref([])
const filteredCategories = ref([])
const defaultActiveIndex = ref('0')
const iconLoadError = ref(false)
const viewType = ref('standard')
const viewOptions = reactive([
  { 'key': 'standard', 'label': '标准视图' },
  { 'key': 'compact', 'label': '紧凑视图' },
])

// WebSocket相关状态
const ws = ref(null)
const wsConnecting = ref(false)

// 显示状态消息
const showMessage = (message, type = 'success', duration = 3000) => {
  Message({
    message,
    type,
    duration  // 使用传入的持续时间，默认3秒，特殊消息可以更长
  })
}

// 连接WebSocket
const connectWebSocket = () => {
  return new Promise((resolve, reject) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      resolve(ws.value)
      return
    }

    const wsInstance = new WebSocket('ws://localhost:50001')
    wsConnecting.value = true

    wsInstance.onopen = () => {
      console.log('WebSocket Connected')
      ws.value = wsInstance
      wsConnecting.value = false
      resolve(wsInstance)
    }

    wsInstance.onmessage = (evt) => {
      const response = evt.data
      if (response.startsWith('Ok')) {
        //showMessage('应用启动成功')
      } else if (response.startsWith('Failed')) {
        showMessage(response, 'error')
      }
    }

    wsInstance.onclose = () => {
      console.log('WebSocket Disconnected')
      ws.value = null
      wsConnecting.value = false
    }

    wsInstance.onerror = (err) => {
      console.error('WebSocket Error:', err)
      // 移除这里的 showMessage 调用
      wsConnecting.value = false
      reject(err)
    }

    setTimeout(() => {
      if (wsConnecting.value) {
        wsConnecting.value = false
        wsInstance.close()
        reject(new Error('连接超时'))
      }
    }, 5000)
  })
}

// 发送打开应用消息
const sendOpenAppMessage = async (appName) => {
  return new Promise((resolve, reject) => {
    let messageReceived = false;
    let timeoutId;

    const connectAndSend = async () => {
      try {
        const wsInstance = await connectWebSocket()
        const message = {
          action: 3,
          msg: appName
        }
        
        // 设置超时检查
        timeoutId = setTimeout(() => {
          if (!messageReceived) {
            wsInstance.close()
            reject(new Error('启动超时：未收到响应'))
          }
        }, 3000) // 3秒超时

        // 添加一次性消息监听
        wsInstance.onmessage = (evt) => {
          messageReceived = true
          clearTimeout(timeoutId)
          
          const response = evt.data
          if (response.startsWith('Ok')) {
            resolve()
          } else {
            reject(new Error(response))
          }
        }

        wsInstance.send(JSON.stringify(message))
        console.log('发送消息:', message)
      } catch (error) {
        clearTimeout(timeoutId)
        reject(error)
      }
    }

    connectAndSend()
  })
}

// 处理应用打开
const handleAppClick = async (item) => {
  if (!item.WebUrl || item.maint) return

  if (item.WebUrl.toLowerCase().startsWith('cs:')) {
    const appName = item.WebUrl.substring(3)
    try {
      showMessage('正在启动爱尔企业浏览器...', 'info')
      await sendOpenAppMessage(appName)
      showMessage('启动成功', 'success')
    } catch (error) {
      showMessage(`启动企业浏览器失败：
      检查是否已安装企业浏览器，
      如仍然无法启动，请手动运行企业浏览器访问该应用！`, 'warning', 8000)
    }
  } else {
    window.open(item.WebUrl, '_blank')
  }
}

// 组件卸载时关闭WebSocket连接
onUnmounted(() => {
  if (ws.value) {
    ws.value.close()
    ws.value = null
  }
})

// 获取随机背景色，基于应用名生成固定颜色
const getRandomColor = (appName) => {
  const colors = [
    '#71BDDF',  // 浅蓝
    '#8AB05D',  // 浅绿
    '#9571DF',  // 浅紫
    '#DF7171',  // 浅红
    '#DFC271',  // 浅黄
    '#71DFA7',  // 薄荷绿
    '#B05D8A',  // 浅粉
    '#5D8AB0'   // 深蓝
  ]
  
  let sum = 0
  for (let i = 0; i < appName.length; i++) {
    sum += appName.charCodeAt(i)
  }
  
  return colors[sum % colors.length]
}

// 处理图标加载失败
const handleIconError = () => {
  iconLoadError.value = true
}

const getTooltipContent = (item) => {
  if (item.WebUrl) {
    return `
      <div class="tooltip-content">
        <div class="tooltip-item">
          <span>${item.WebUrl}</span>
        </div>
      </div>
    `
  }
  
  return '暂无访问地址'
}

// 添加分类切换处理函数
const handleCategoryChange = (categoryId) => {
  selectedCategory.value = parseInt(categoryId)
  if (!categoryId) {
    filteredCategories.value = categoryList.value
  } else {
    filteredCategories.value = categoryList.value.filter(category => 
      category.id === parseInt(categoryId)
    )
  }
}

// 添加搜索处理函数
const handleSearch = () => {
  if (!searchQuery.value) {
    filteredCategories.value = categoryList.value
    return
  }

  const searchText = searchQuery.value.toLowerCase().trim()
  filteredCategories.value = categoryList.value
    .map(category => ({
      ...category,
      apps: category.apps.filter(app => 
        app.app_name.toLowerCase().includes(searchText)
      )
    }))
    .filter(category => category.apps.length > 0)
}

const initCategories = async() => {
  try {
    const { data: apiResponse } = await getCurrentUser()
    console.log('API返回数据:', apiResponse)
    
    if (apiResponse.code === 0 && apiResponse.data) {
      const formattedData = apiResponse.data.map((item, index) => ({
        id: index + 1,
        name: item.category,
        apps: item.apps.map(app => ({
          id: app.id,
          app_name: app.app_name,
          app_desc: app.app_type,
          icon: app.icon,
          maint: app.maintenance === 2,
          app_type: app.app_type,
          app_sites: app.app_sites,
          WebUrl: app.WebUrl  // 确保 WebUrl 字段被保留
        }))
      }))
      
      console.log('格式化后的数据:', formattedData)
      categoryList.value = formattedData
      filteredCategories.value = formattedData
      
      if (formattedData.length > 0) {
        selectedCategory.value = formattedData[0].id
        defaultActiveIndex.value = formattedData[0].id.toString()
      }
    }
  } catch (error) {
    console.error('API调用出错:', error)
  }
}

onMounted(() => {
  initCategories()
})
const userStore = useUserStore()
const route = useRoute()
const cli_query = route.query
// 获取 corpID，在本地文件环境下使用默认值
let corpID = null
try {
  // 检查是否为本地文件协议
  if (!agentApi.isClient()) {
    const req = new XMLHttpRequest()
    req.open('GET', document.location, false)
    req.send(null)
    corpID = req.getResponseHeader('X-Corp-ID')
  }
} catch (error) {
  console.warn('无法获取 X-Corp-ID header，使用默认值:', error)
}
const clineData = {
  action: 0,
  msg: {
    token: userStore.token.accessToken,
    refreshToken: userStore.token.refreshToken,
    realm: corpID || 'default',
  },
  platform: document.location.hostname
}

//if (cli_query.type === 'client') 
{
  const port = cli_query.wp || 50001
  const websocket = ref({})
  const wsUrl = ref(`ws://127.0.0.1:${port}`)
  const platform = navigator.platform
  if (platform.indexOf('Mac') === 0 || platform === 'MacIntel') {
    wsUrl.value = `wss://127.0.0.1:${port}`
  }
  const initWebSocket = () => {
    websocket.value = new WebSocket(wsUrl.value)
    websocket.value.onopen = () => {
      console.log('socket连接成功')
      sendMessage(JSON.stringify(clineData))
    }
    websocket.value.onmessage = (e) => {
      console.log(e)
      closeWebSocket()
    }
    websocket.value.onerror = () => {
      console.log('socket连接错误:' + wsUrl.value)
      window.location.href = `asecagent://?web=${JSON.stringify(clineData)}`
    }
  }
  // 发送消息
  const sendMessage = (msg) => {
    console.log(msg, '0')
    websocket.value.send(msg)
  }
  // 关闭链接（在页面销毁时可销毁链接）
  const closeWebSocket = () => {
    console.log('socket断开链接')
    websocket.value.close()
  }
  console.log(`asecagent://?web=${JSON.stringify(clineData)}`)
  initWebSocket()
}

</script>
<style lang="scss" scoped>
.person {
  height: 100vh;
  background: #FFFFFF;
  border-radius: 4px;
  height: 100vh;
  max-height: calc(100vh - 68px);

  :deep(.base-header--shadow) {
    box-shadow: none
  }

  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #ffffff;

    :deep(.base-input) {
      padding: 5px 12px 5px 36px;
    }

    .header-left {
      .page-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2329;
        line-height: 28px;
      }
    }

    .header-right {
      .search-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .search-icon {
          width: 14px;
          height: 14px;
          position: relative;
          margin-right: -37px;
          z-index: 1000;
          color: #b3b6c1 !important;
        }

        .search-input {
          width: 200px;
          height: 28px;

          :deep(.base-input__wrapper) {
            border-radius: 6px;
            background-color: #f7f8fa;
            border: 1px solid transparent;
            transition: all 0.2s;

            &:hover {
              background-color: #ffffff;
              border-color: #d0d7de;
            }

            &.is-focus {
              background-color: #ffffff;
              border-color: #536ce6;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }

          :deep(.base-input__inner) {
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            color: #1f2329;

            &::placeholder {
              color: #8a919f;
            }
          }
        }

        .refresh-btn {
          width: 28px;
          height: 28px;
          padding: 0;
          border-radius: 4px;
          background: #f5f5f7;
          color: #686e84;

          .refresh-btn-icon {
            width: 14px;
            height: 14px;
          }

          &:hover {
            background: #ffffff;
            border-color: #d0d7de;
            color: #1f2329;
          }
        }

        .view-select {
          width: 70px;
          height: 20px;

          :deep(.base-select__input) {
            padding: 0px;
            border: none;
          }

          :deep(.base-select__dropdown) {
            width: 88px;
            padding: 7px 7px 3px 7px;
          }

          :deep(.base-option) {
            padding: 4px 8px 4px 8px; 
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Regular;
            background: #f5f5f7;
            border-radius: 4px;
            margin-bottom: 4px;
          }

          :deep(.base-option.is-selected) {
            color: #ffffff;
            background: #536ce6;
            border-radius: 4px;
          }

          :deep(.base-select__wrapper) {
            border-radius: 6px;
            background-color: #f7f8fa;
            border: 1px solid transparent;
            height: 36px;

            &:hover {
              background-color: #ffffff;
              border-color: #d0d7de;
            }
          }
        }
      }
    }

    .search-input {
      width: 200px;  // 设置搜索框宽度
      height: 28px;
      
      :deep(.el-input__wrapper) {
        border-radius: 4px;
        background-color: #f5f7fa;
        
        &.is-focus {
          background-color: #ffffff;
        }
      }

      :deep(.el-input__inner) {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        
        &::placeholder {
          color: #909399;
        }
      }
    }
  }

  .category-aside {
    height: calc(100vh - 200px);
    border-bottom: none;

    :deep(.base-menu--vertical) {
      width: 100%;
    }

    .category-menu {
      border-right: none;
      background: transparent;
      padding: 0px 0px 8px 16px;

      .category-menu-item {
        width: 64px;
        height: 28px;
      }

      :deep(.base-menu-item__content) {
        padding: 0px;
        height: 28px;
        line-height: 28px;
        justify-content: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      :deep(.base-menu-item) {
        margin: 8px 4px 8px 0px;
        font-size: 14px;
        color: #4e5969;
        border-radius: 6px;
        transition: all 0.2s ease;
        cursor: pointer;

        // 默认状态
        &:not(.base-menu-item--active) {
          background-color: transparent;
          color: #4e5969;
        }

        // 悬浮状态
        &:hover:not(.base-menu-item--active) {
          background-color: #f5f5f7;
          .base-menu-item__content {
            color: #686e84;
          }
        }

        // 选中状态
        &.base-menu-item--active {
          background-color: #536ce6;
          color: #ffffff;
          font-weight: 500;
        }

        // 点击状态
        &:active {
          background-color: #3370ff;
        }
      }
    }
  }

  .app-main {
    height: calc(100vh - 188px);
    padding: 24px;
    overflow-y: auto;
    background: #ffffff;

    .category-section {
      margin-bottom: 40px;

      .category-title-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        .category-title {
          font-size: 18px;
          font-weight: 600;
          color: #1f2329;
          margin: 0;
          padding-bottom: 8px;
          border-bottom: 2px solid #f0f0f0;
          position: relative;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 40px;
            height: 2px;
            background: #536ce6;
          }
        }

        .category-title-info {
          color: #8a919f;
          cursor: help;
          flex-shrink: 0;
          margin-bottom: 8px;

          &:hover {
            color: #536ce6;
          }
        }
      }

      .apps-grid {
        display: grid;
        gap: 16px;
        padding: 20px 0;

        &.view-standard {
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          max-width: 100%;

          .app-item {
            width: 120px;
            height: 120px;

            .app-content {
              flex-direction: column;
              text-align: center;
              padding: 16px 8px;

              .app-icon {
                margin-bottom: 12px;
              }

              .app-details {
                .app-name {
                  font-size: 14px;
                  line-height: 20px;
                  margin-bottom: 4px;
                }

                .app-desc {
                  font-size: 12px;
                  color: #8a919f;
                  line-height: 16px;
                }
              }
            }
          }
        }

        &.view-compact {
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

          .app-item {
            height: 64px;

            .app-content {
              flex-direction: row;
              align-items: center;
              padding: 12px 16px;

              .app-icon {
                margin-right: 12px;
                flex-shrink: 0;
              }

              .app-details {
                flex: 1;
                text-align: left;

                .app-name {
                  font-size: 14px;
                  line-height: 20px;
                  font-weight: 500;
                }
              }
            }
          }
        }

        .app-item {
          background: #ffffff;
          border: 1px solid #e5e6eb;
          border-radius: 8px;
          position: relative;
          cursor: pointer;
          transition: all 0.2s ease;
          overflow: hidden;

          &:hover:not(.disabled) {
            border-color: #536ce6;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            transform: translateY(-2px);
          }

          .app-content {
            display: flex;
            width: 100%;
            height: 100%;

            .app-icon {
              :deep(.base-avatar) {
                color: #ffffff;
                font-size: 16px;
                font-weight: 500;
                border-radius: 8px;

                &.default-avatar {
                  background-color: #f0f2f5 !important;
                  color: #909399;
                }
              }
            }

            .app-details {
              .app-name {
                color: #1f2329;
                font-weight: 500;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100%;
              }
            }
          }

          .status-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            background-color: #ff9500;
            color: #ffffff;
            font-weight: 500;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          // 维护中状态（不可点击）
          &.disabled {
            cursor: not-allowed;
            opacity: 0.6;

            &:hover {
              border-color: #e5e6eb;
              box-shadow: none;
              transform: none;
            }

            .app-content {
              .app-icon :deep(.base-avatar) {
                filter: grayscale(100%);
              }

              .app-details .app-name {
                color: #8a919f;
              }
            }
          }
        }
      }
    }

    // 第一个分类区域特殊处理
    .category-section:first-child {
      margin-top: 0;
    }
  }
}

.app-header {
  .el-recent-access {
    margin-left: 4px;
    margin-top: 3px;
    width: 56px;
    height: 20px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: Regular;
    text-align: left;
    color: #282a33;
    line-height: 20px;
  }
  .el-recent-data {
    margin-left: 15px;
    height:20px;
  }
  .el-recent-item {
    margin-top: 2px;
    margin-right: 6px;
    height: 25px;
    padding: 4px 0px 4px 6px;
    background: #f5f6fe;
    border-radius: 4px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: Regular;
    color: #536ce6;
  }
  .el-recent-icon {
    opacity: 0.6;
    width: 8px;
    height: 8px;
    margin: 8px 6px 8px 5px;
  }
  .el-recent-clear {
    opacity: 0.6;
    margin-top: 6px;
    position: absolute;
    width: 14px;
    height: 14px;
    right: 20px;
  }
}

// 响应式布局
@media screen and (max-width: 1200px) {
  .person {
    .app-header {
      padding: 16px 20px;

      .header-right .search-controls {
        gap: 8px;

        .search-input {
          width: 200px;
          height: 28px;
        }
      }
    }

    .app-main {
      padding: 20px;
      height: calc(100vh - 188px);

      .apps-grid {
        &.view-standard {
          grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));

          .app-item {
            width: 100px;
            height: 100px;

            .app-content {
              padding: 12px 6px;

              .app-icon {
                margin-bottom: 8px;

                :deep(.base-avatar) {
                  width: 40px !important;
                  height: 40px !important;
                }
              }

              .app-details .app-name {
                font-size: 12px;
                line-height: 16px;
              }
            }
          }
        }

        &.view-compact {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .person {
    .app-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .header-left .page-title {
        font-size: 18px;
      }

      .header-right .search-controls {
        justify-content: space-between;

        .search-input {
          flex: 1;
          max-width: none;
        }
      }
    }

    .base-container {
      flex-direction: column;

      .category-aside {
        width: 100% !important;

        .category-menu {
          display: flex;
          overflow-x: auto;
          padding: 12px 16px;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */

          &::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
          }

          :deep(.base-menu-item) {
            flex-shrink: 0;
            margin: 0 4px;
            white-space: nowrap;
            min-width: 60px;
            max-width: 120px;

            .category-menu-text {
              max-width: 100px;
            }
          }
        }
      }
    }

    .app-main {
      padding: 16px;

      .category-section {
        .category-title-wrapper {
          .category-title {
            font-size: 16px;

            &::after {
              width: 30px;
            }
          }

          .category-title-info {
            display: none; /* 移动端隐藏信息图标 */
          }
        }
      }

      .apps-grid {
        &.view-standard {
          grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
          gap: 12px;

          .app-item {
            width: 80px;
            height: 90px;

            .app-content {
              padding: 8px 4px;

              .app-details {
                .app-name {
                  font-size: 11px;
                  line-height: 14px;
                }

                .app-desc {
                  display: none;
                }
              }
            }
          }
        }

        &.view-compact .app-item {
          height: 56px;

          .app-content {
            padding: 8px 12px;

            .app-icon {
              margin-right: 8px;

              :deep(.base-avatar) {
                width: 32px !important;
                height: 32px !important;
              }
            }
          }
        }
      }
    }
  }
}

.tooltip-content {
  width: 200px;
  text-align: center;
}

.web-link {
  color: #536ce6;
  text-decoration: none;
  word-break: break-all;
}

.web-link:hover {
  text-decoration: underline;
}
.el-select__popper {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 2px 20px 0px rgba(46,60,128,0.10);
  margin-left: -10px !important; 
  right: 15px;
  top: 110px;
  max-width: 88px;
  .el-select-dropdown__item {
    width: 72px;
    height: 28px;
    border-radius: 4px;
    margin-left: 7px;
    margin-bottom: 4px;
    padding: 0 8px 0 8px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC-Regular;
    line-height: 20px;
    display: flex;
    align-items: center; 
    background: #f5f5f7 !important;
  }
  .el-select-dropdown__item.selected {
    color: #ffffff;
    background: #536ce6 !important;
  }
}
</style>
<style scoped>
.text-center {
  text-align: center;
}

.web-link {
  color: #536ce6;
  text-decoration: none;
}

.web-link:hover {
  text-decoration: underline;
}
</style>
<style>
.el-message {
  white-space: pre-line !important;
  line-height: 1.5 !important;
  padding: 12px 20px !important;
}
</style>
