import { createApp, h } from 'vue'

const MessageComponent = {
  name: 'BaseMessage',
  props: {
    message: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'warning', 'info', 'error'].includes(value)
    },
    showClose: {
      type: Boolean,
      default: false
    },
    duration: {
      type: Number,
      default: 3000
    }
  },
  data() {
    return {
      visible: true
    }
  },
  mounted() {
    if (this.duration > 0) {
      setTimeout(() => {
        this.close()
      }, this.duration)
    }
  },
  methods: {
    close() {
      //this.visible = false
      /*setTimeout(() => {
        this.$el.remove()
      }, 300)*/
    },
    getIcon() {
      const icons = {
        success: '✓',
        warning: '⚠',
        error: '✕',
        info: 'ℹ'
      }
      return icons[this.type] || icons.info
    }
  },
  render() {
    if (!this.visible) return null

    return h('div', {
      class: [
        'base-message',
        `base-message--${this.type}`,
        { 'base-message--closable': this.showClose }
      ],
      style: {
        position: 'fixed',
        top: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
        padding: '9px 18px 9px 14px',
        borderRadius: '20px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        background: '#ffffff',
        color: '#fff',
        display: 'flex',
        alignItems: 'center',
        minWidth: '240px',
        maxWidth: '420px',
        backdropFilter: 'blur(8px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }
    }, [
      // 图标
      h('span', {
        style: {
          fontSize: '16px',
          marginRight: '8px',
          fontWeight: 'bold',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '20px',
          height: '20px'
        }
      }, this.getIcon()),

      // 消息内容
      h('span', {
        style: {
          flex: 1,
          color: '#3c404d',
          lineHeight: '1.4'
        }
      }, this.message),

      // 关闭按钮
      this.showClose && h('span', {
        style: {
          marginLeft: '12px',
          cursor: 'pointer',
          fontSize: '18px',
          opacity: '0.8',
          transition: 'opacity 0.2s',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '20px',
          height: '20px',
          borderRadius: '50%'
        },
        onClick: this.close,
        onMouseenter: (e) => {
          e.target.style.opacity = '1'
          e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
        },
        onMouseleave: (e) => {
          e.target.style.opacity = '0.8'
          e.target.style.backgroundColor = 'transparent'
        }
      }, '×')
    ])
  }
}

const Message = (options) => {
  if (typeof options === 'string') {
    options = { message: options }
  }
  
  const container = document.createElement('div')
  document.body.appendChild(container)
  
  const app = createApp(MessageComponent, options)
  app.mount(container)
  
  return {
    close: () => {
      app.unmount()
      document.body.removeChild(container)
    }
  }
}

// 添加快捷方法
Message.success = (message) => Message({ message, type: 'success' })
Message.warning = (message) => Message({ message, type: 'warning' })
Message.error = (message) => Message({ message, type: 'error' })
Message.info = (message) => Message({ message, type: 'info' })

export { Message }
