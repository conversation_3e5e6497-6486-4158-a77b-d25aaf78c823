/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
System.register(["./index-legacy.f131204f.js"],(function(e,n){"use strict";var t,r,u,i,o,l,a,f,c,d,s,v;return{setters:[function(e){t=e._,r=e.U,u=e.h,i=e.o,o=e.d,l=e.j,a=e.w,f=e.T,c=e.g,d=e.X,s=e.m,v=e.A}],execute:function(){var n=Object.assign({name:"RouterHolder"},{setup:function(e){var n=r();return function(e,t){var r=u("router-view");return i(),o("div",null,[l(r,null,{default:a((function(e){var t=e.Component;return[l(f,{mode:"out-in",name:"el-fade-in-linear"},{default:a((function(){return[(i(),c(d,{include:s(n).keepAliveRouters},[(i(),c(v(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}});e("default",t(n,[["__file","D:/asec-platform/frontend/portal/src/view/routerHolder.vue"]]))}}}));
