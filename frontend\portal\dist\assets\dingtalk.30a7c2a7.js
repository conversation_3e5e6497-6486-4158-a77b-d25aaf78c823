/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
import{_ as t,a as e,r as i,b as a,z as n,o,d as s,e as d,k as l,Y as r}from"./index.4f1b43e7.js";const c={style:{"text-align":"center"}},p={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},u={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},h={name:"Dingtalk",mounted(){this.loadThirdPartyScript()},methods:{loadThirdPartyScript(){const t=document.createElement("script");t.src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js",t.onload=()=>{this.doSomethingWithThirdPartyLibrary()},document.body.appendChild(t)}}},g=t(Object.assign(h,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(t){const h=e(),g=i(0),f=t;a();const y=async()=>{await(async()=>{const t={type:"dingtalk",data:{idpId:f.auth_id}},e=await r(t);if(200===e.status)return e.data.uniqKey})();const t=f.auth_info.dingtalkAppKey,e=window.location.host,i=`${window.location.protocol}//${e}`;setTimeout((()=>{window.DTFrameLogin({id:"self_defined_element",width:300,height:300},{redirect_uri:encodeURIComponent(i),client_id:t,scope:"openid",response_type:"code",state:f.auth_id,prompt:"consent"},(t=>{const{redirectUrl:e,authCode:i,state:a}=t;h.push({name:"Status",query:{code:i,state:a,auth_type:"dingtalk"},replace:!0})}),(t=>{t&&console.error("钉钉登录错误:",t)}))}),100)};return y(),n(f,(async(t,e)=>{g.value++,await y()})),(t,e)=>(o(),s("div",{key:g.value},[d("div",c,[d("span",p,[(o(),s("svg",u,e[0]||(e[0]=[d("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),e[1]||(e[1]=l(" 钉钉认证 "))])]),e[2]||(e[2]=d("div",{id:"self_defined_element",class:"self-defined-classname"},null,-1))]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/dingtalk/dingtalk.vue"]]);export{g as default};
