/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{r as C,l as f,c as D,b as z,h as b,o,d as l,e as u,t as n,m as k,j as m,w as d,g as x,f as E,_ as K,n as M,M as V,k as p}from"./index.d0594432.js";const O={class:"verify-code"},U={style:{top:"10px","margin-bottom":"25px","text-align":"center"}},L={class:"title"},P={key:0,class:"message-text"},$={key:1,class:"message-text"},A={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},F={style:{"text-align":"center"}},G={name:"VerifyCode"},H=Object.assign(G,{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}},secondaryType:{type:String,default:"sms"}},emits:["verification-success","back","cancel"],setup(s,{emit:w}){const i=C(""),_=f("userName");f("last_id"),f("isSecondary");const e=s;console.log("verifyCode\u7EC4\u4EF6\u63A5\u6536\u5230\u7684\u5C5E\u6027:",{secondaryType:e.secondaryType,authInfo:e.auth_info,canVerify:e.secondaryType==="email"?e.auth_info.hasEmail!==!1:e.auth_info.notPhone!==!1});const v=w,c=D(()=>e.auth_info.hasContactInfo!==void 0?e.auth_info.hasContactInfo:(e.secondaryType==="email",e.auth_info.hasContactInfo!==!1)),a=C(60);let h;const I=()=>{a.value=60,h=setInterval(()=>{a.value--,a.value===0&&T()},1e3)},T=()=>{clearInterval(h)},g=async()=>{if(!c.value)return;const r={uniq_key:e.auth_info.uniqKey,idp_id:e.auth_id};try{const t=await M(r);t.status===200&&t.data.code!==-1?I():(V({showClose:!0,message:t.data.msg,type:"error"}),a.value=0)}catch{V({showClose:!0,message:"\u53D1\u9001\u9A8C\u8BC1\u7801\u5931\u8D25",type:"error"}),a.value=0}};g();const N=z(),S=async()=>{const r={uniq_key:e.auth_info.uniqKey,auth_code:i.value,user_name:e.userName||_.value,idp_id:e.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},t=await N.LoginIn(r,"accessory");t.code!==-1&&v("verification-success",t)},q=()=>{v("cancel")};return(r,t)=>{const y=b("base-button"),j=b("base-input");return o(),l("div",O,[u("div",U,[u("span",L,n(s.secondaryType==="email"?"\u90AE\u4EF6\u8BA4\u8BC1":"\u77ED\u4FE1\u8BA4\u8BC1"),1)]),u("div",null,[c.value?(o(),l("div",P,"\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001\u81F3\u60A8\u8D26\u53F7("+n(s.userName||k(_))+")\u5173\u8054\u7684"+n(s.secondaryType==="email"?"\u90AE\u7BB1":"\u624B\u673A")+"\uFF0C\u8BF7\u6CE8\u610F\u67E5\u6536",1)):(o(),l("div",$,"\u60A8\u7684\u8D26\u53F7("+n(s.userName||k(_))+")\u672A\u5173\u8054"+n(s.secondaryType==="email"?"\u90AE\u7BB1":"\u624B\u673A\u53F7\u7801")+"\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01",1)),c.value?(o(),l("div",A,[m(j,{modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=B=>i.value=B),placeholder:s.secondaryType==="email"?"\u90AE\u7BB1\u9A8C\u8BC1\u7801":"\u77ED\u4FE1\u9A8C\u8BC1\u7801",class:"input-with-select"},{append:d(()=>[m(y,{type:"info",disabled:a.value>0,onClick:g},{default:d(()=>[p("\u91CD\u65B0\u53D1\u9001 "+n(a.value>0?`(${a.value}\u79D2)`:""),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","placeholder"])])):x("",!0),u("div",F,[c.value?(o(),E(y,{key:0,type:"primary",size:"large",disabled:!i.value,onClick:S},{default:d(()=>t[1]||(t[1]=[p("\u786E \u5B9A ")])),_:1,__:[1]},8,["disabled"])):x("",!0),m(y,{type:"info",size:"large",onClick:q},{default:d(()=>t[2]||(t[2]=[p("\u53D6 \u6D88 ")])),_:1,__:[2]})])])])}}}),Q=K(H,[["__scopeId","data-v-2c12ced5"]]);export{Q as default};
