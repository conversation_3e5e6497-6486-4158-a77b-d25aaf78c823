import ipcHelpers from './helper'

// 组织xml格式的请求格式
const parseXmlRequest = ({ module, action, strSerial, data }) => {
  let xmlData = ''
  const header = {
    Head: {
      'TradeCode': 'CallDllFun',
      'WaitTime': '-1',
      'PacketType': 'AuiReqstAgt',
      'Serial': strSerial,
      'Base64': '0',
      'NeedRequest': '1',
      'RequestBodyLength': '0',
      'WebCallBackFun': [],
      'WhatToDo': 'CallOneFunc',
      'WhereIsModule': module + '.dll',
      'WhatFuncToCall': action,
      'ClassName': [],
      'CallOneFuncMode': 'New'
    }
  }

  xmlData += 'LENGTH:<<<9999>>>;VERSION:01;CRYPT:00;TEXTCODE:01;SERIAL:' + strSerial + ';TYPE:AuiReqstAgt;'
  xmlData += '[<HEAD>]' + JSON.stringify(header) + '[</HEAD>]'
  xmlData += '[<REQUEST>]' + data + '[</REQUEST>]'

  const xmlDataLen = ipcHelpers.formatNum(ipcHelpers.getStrLen(xmlData), 10)
  xmlData = xmlData.replace(/<<<9999>>>/, xmlDataLen)

  return encodeURIComponent(xmlData)
}

const unpareseXmlResponese = (xmlStr) => {
  const replyStr = ipcHelpers.getSubStr(xmlStr, '[<REPLY>]', '[</REPLY>]').replace(/[\r\n\t]+/g, '')
  const headStr = ipcHelpers.getSubStr(xmlStr, '[<HEAD>]', '[</HEAD>]').replace(/[\r\n\t]+/g, '')
  const requestStr = ipcHelpers.getSubStr(xmlStr, '[<REQUEST>]', '[</REQUEST>]').replace(/[\r\n\t]+/g, '')

  return { replyStr, headStr, requestStr }
}

export {
  parseXmlRequest,
  unpareseXmlResponese
}
