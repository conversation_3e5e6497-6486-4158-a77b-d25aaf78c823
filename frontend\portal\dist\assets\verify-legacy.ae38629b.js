/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
System.register(["./index-legacy.50e341a4.js"],(function(e,t){"use strict";var n,r,o,c,i;return{setters:[function(e){n=e._,r=e.b,o=e.q,c=e.o,i=e.d}],execute:function(){var t=Object.assign({name:"Verify"},{setup:function(e){var t=location.href.split("?")[1],n=new URLSearchParams(t),a=Object.fromEntries(n.entries()),s=r(),u=document.location.protocol+"//"+document.location.host,l=new URLSearchParams;"client"===a.type&&(l.set("type","client"),a.wp&&l.set("wp",a.wp));var f={method:"GET",url:"".concat(u,"/auth/user/v1/redirect_verify?redirect_url=").concat(a.redirect_url),headers:{Accept:"application/json, text/plain, */*",Authorization:"".concat(s.token.tokenType," ").concat(s.token.accessToken)}};return o.request(f).then((function(e){if(200===e.status){var t=e.data.url;if(l.toString()){var n=t.includes("?")?"&":"?";t+=n+l.toString()}window.location.href=t}})).catch((function(e){console.error(e)})),function(e,t){return c(),i("div")}}});e("default",n(t,[["__file","D:/asec-platform/frontend/portal/src/view/login/verify.vue"]]))}}}));
