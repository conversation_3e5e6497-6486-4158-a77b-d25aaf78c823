/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{_ as e,V as a,h as s,o as t,d as n,j as o,w as r,T as l,g as u,Y as d,m as i,A as c}from"./index.44d6e232.js";const f=e(Object.assign({name:"RouterHolder"},{setup(e){const f=a();return(e,a)=>{const m=s("router-view");return t(),n("div",null,[o(m,null,{default:r((({Component:e})=>[o(l,{mode:"out-in",name:"el-fade-in-linear"},{default:r((()=>[(t(),u(d,{include:i(f).keepAliveRouters},[(t(),u(c(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/routerHolder.vue"]]);export{f as default};
