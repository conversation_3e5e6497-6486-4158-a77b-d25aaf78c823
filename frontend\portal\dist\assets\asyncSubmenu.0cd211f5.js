/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
import{_ as e,J as t,r as a,z as n,h as o,o as s,f as u,w as l,d as r,j as c,C as i,g as f,e as m,t as d,F as p,P as v}from"./index.8abc592d.js";const b={key:0,class:"gva-subMenu"},h=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON><PERSON>an},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({c8e9c8aa:y.value,"6037b64a":x.value})));const h=e,I=a(h.theme.activeBackground),x=a(h.theme.activeText),y=a(h.theme.normalText);return n((()=>h.theme),(()=>{I.value=h.theme.activeBackground,x.value=h.theme.activeText,y.value=h.theme.normalText})),(t,a)=>{const n=o("component"),h=o("el-icon"),I=o("el-sub-menu");return s(),u(I,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(p,{key:1},[e.routerInfo.meta.icon?(s(),u(h,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)],64)):(s(),r("div",b,[e.routerInfo.meta.icon?(s(),u(h,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)]))])),default:l((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-547fcaa6"]]);export{h as default};
