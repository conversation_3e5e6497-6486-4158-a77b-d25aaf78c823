/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{_ as e,c as o,h as t,o as n,g as r,w as s,d as a,F as i,i as l,f,A as u}from"./index.f6c71253.js";import c from"./menuItem.cfb7f063.js";import d from"./asyncSubmenu.9a954938.js";const m=e(Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:<PERSON>olean},theme:{default:function(){return{}},type:Object}},setup(e){const m=e,p=o((()=>m.routerInfo.children&&m.routerInfo.children.filter((e=>!e.hidden)).length?d:c));return(o,c)=>{const d=t("AsideComponent");return e.routerInfo.hidden?f("v-if",!0):(n(),r(u(p.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:s((()=>[e.routerInfo.children&&e.routerInfo.children.length?(n(!0),a(i,{key:0},l(e.routerInfo.children,(o=>(n(),r(d,{key:o.name,"is-collapse":!1,"router-info":o,theme:e.theme},null,8,["router-info","theme"])))),128)):f("v-if",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/index.vue"]]);export{m as default};
