/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,a,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",r=i.toStringTag||"@@toStringTag";function s(e,i,o,r){var s=i&&i.prototype instanceof d?i:d,l=Object.create(s.prototype);return t(l,"_invoke",function(e,t,i){var o,r,s,d=0,l=i||[],u=!1,p={p:0,n:0,v:n,a:v,f:v.bind(n,4),d:function(e,t){return o=e,r=0,s=n,p.n=t,c}};function v(e,t){for(r=e,s=t,a=0;!u&&d&&!i&&a<l.length;a++){var i,o=l[a],v=p.p,f=o[2];e>3?(i=f===t)&&(s=o[(r=o[4])?5:(r=3,3)],o[4]=o[5]=n):o[0]<=v&&((i=e<2&&v<o[1])?(r=0,p.v=t,p.n=o[1]):v<f&&(i=e<3||o[0]>t||t>f)&&(o[4]=e,o[5]=t,p.n=f,r=0))}if(i||e>1)return c;throw u=!0,t}return function(i,l,f){if(d>1)throw TypeError("Generator is already running");for(u&&1===l&&v(l,f),r=l,s=f;(a=r<2?n:s)||!u;){o||(r?r<3?(r>1&&(p.n=-1),v(r,s)):p.n=s:p.v=s);try{if(d=2,o){if(r||(i="next"),a=o[i]){if(!(a=a.call(o,s)))throw TypeError("iterator result is not an object");if(!a.done)return a;s=a.value,r<2&&(r=0)}else 1===r&&(a=o.return)&&a.call(o),r<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),r=1);o=n}else if((a=(u=p.n<0)?s:e.call(t,p))!==c)break}catch(a){o=n,r=1,s=a}finally{d=1}}return{value:a,done:u}}}(e,o,r),!0),l}var c={};function d(){}function l(){}function u(){}a=Object.getPrototypeOf;var p=[][o]?a(a([][o]())):(t(a={},o,(function(){return this})),a),v=u.prototype=d.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,t(e,r,"GeneratorFunction")),e.prototype=Object.create(v),e}return l.prototype=u,t(v,"constructor",u),t(u,"constructor",l),l.displayName="GeneratorFunction",t(u,r,"GeneratorFunction"),t(v),t(v,r,"Generator"),t(v,o,(function(){return this})),t(v,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:s,m:f}})()}function t(e,n,a,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,n,a,i){if(n)o?o(e,n,{value:a,enumerable:!i,configurable:!i,writable:!i}):e[n]=a;else{var r=function(n,a){t(e,n,(function(e){return this._invoke(n,a,e)}))};r("next",0),r("throw",1),r("return",2)}},t(e,n,a,i)}function n(e,t,n,a,i,o,r){try{var s=e[o](r),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(a,i)}System.register(["./index-legacy.e6617eb1.js"],(function(t,a){"use strict";var i,o,r,s,c,d,l,u,p,v,f,g,b,m,x=document.createElement("style");return x.textContent='@charset "UTF-8";.setting-page[data-v-c55304de]{display:flex;height:100vh;font-family:PingFang SC,PingFang SC-Regular}.sidebar[data-v-c55304de]{width:60px;background:#667eea;display:flex;flex-direction:column;align-items:center;padding:20px 0;box-shadow:2px 0 8px rgba(0,0,0,.1)}.sidebar .sidebar-menu[data-v-c55304de]{display:flex;flex-direction:column;gap:16px}.sidebar .sidebar-menu .menu-item[data-v-c55304de]{width:48px;height:48px;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:8px;cursor:pointer;transition:all .3s ease;color:rgba(255,255,255,.7);font-size:10px}.sidebar .sidebar-menu .menu-item[data-v-c55304de]:hover{background:rgba(255,255,255,.1);color:#fff}.sidebar .sidebar-menu .menu-item.active[data-v-c55304de]{background:rgba(255,255,255,.2);color:#fff}.sidebar .sidebar-menu .menu-item .menu-icon[data-v-c55304de]{width:16px;height:16px;margin-bottom:4px}.sidebar .sidebar-menu .menu-item .menu-icon[data-v-c55304de]:before{content:"";display:block;width:100%;height:100%;background:currentColor;border-radius:2px}.main-content[data-v-c55304de]{flex:1;padding:20px;overflow-y:auto}.setting-container[data-v-c55304de]{height:calc(100% - 38px);background:white;border-radius:8px;overflow:hidden;box-shadow:0 2px 12px rgba(0,0,0,.1);margin:0 auto}.tabs-header[data-v-c55304de]{display:flex;border-bottom:1px solid #e4e7ed;background:#f5f7fa}.tabs-header .tab-item[data-v-c55304de]{padding:16px 24px;cursor:pointer;color:#606266;font-size:14px;font-weight:500;border-bottom:2px solid transparent;transition:all .3s ease}.tabs-header .tab-item[data-v-c55304de]:hover{color:#409eff;background:rgba(64,158,255,.05)}.tabs-header .tab-item.active[data-v-c55304de]{color:#409eff;border-bottom-color:#409eff;background:white;position:relative}.tabs-header .tab-item.active[data-v-c55304de]:after{content:"";position:absolute;bottom:-1px;left:0;right:0;height:1px;background:white}.tabs-content[data-v-c55304de]{min-height:400px}.tabs-content .tab-panel[data-v-c55304de]{padding:32px}.tabs-content .tab-panel .setting-update[data-v-c55304de]{padding-bottom:24px;margin-bottom:32px;border-bottom:1px solid #f0f0f0}.setting-section .setting-item[data-v-c55304de]{margin-bottom:32px}.setting-section .setting-item[data-v-c55304de]:last-child{margin-bottom:0}.setting-section .setting-platformAddress[data-v-c55304de]{padding-bottom:24px;border-bottom:1px solid #f0f0f0}.setting-section .setting-label[data-v-c55304de]{display:block;font-size:14px;font-weight:500;color:#303133;margin-bottom:12px}.setting-section .setting-input[data-v-c55304de]{width:320px}.setting-section .setting-input[data-v-c55304de] .el-input__inner{height:40px;border-radius:6px}.setting-section .setting-input[data-v-c55304de] .el-input__inner:focus{border-color:#409eff}.setting-section .setting-select[data-v-c55304de]{width:200px}.setting-section .setting-select[data-v-c55304de] .el-select__wrapper{height:40px;border-radius:6px}.setting-section .checkbox-group[data-v-c55304de]{display:flex;flex-direction:column;gap:12px}.setting-section .checkbox-group .setting-checkbox[data-v-c55304de] .el-checkbox__label{font-size:14px;color:#606266}.setting-section .checkbox-group .setting-checkbox[data-v-c55304de] .el-checkbox__input.is-checked .el-checkbox__inner{background-color:#409eff;border-color:#409eff}.about-section .about-title[data-v-c55304de]{font-size:16px;font-weight:600;color:#303133;margin:0 0 24px}.version-info .version-item[data-v-c55304de]{display:flex;align-items:center;justify-content:space-between;padding:16px 0}.version-info .version-item[data-v-c55304de]:last-child{border-bottom:none}.version-info .version-item .version-label[data-v-c55304de]{font-size:14px;color:#606266;flex-shrink:0}.version-info .version-item .version-value[data-v-c55304de]{font-size:14px;color:#303133;font-weight:500}.version-info .version-item .version-value-group[data-v-c55304de]{display:flex;align-items:center;gap:12px}.version-info .version-item .version-value-group .version-value[data-v-c55304de]{font-size:14px;color:#303133;font-weight:500}.copyright[data-v-c55304de]{margin-top:32px;padding-top:20px;border-top:1px solid #f0f0f0}.copyright p[data-v-c55304de]{font-size:12px;color:#909399;margin:0;text-align:center}.setting-footer[data-v-c55304de]{padding:20px 32px;border-top:1px solid #e4e7ed;background:#f5f7fa;display:flex;justify-content:flex-end;gap:12px}.setting-footer .el-button[data-v-c55304de]{padding:8px 20px;border-radius:6px;font-size:14px}@media (max-width: 768px){.setting-page[data-v-c55304de]{flex-direction:column}.sidebar[data-v-c55304de]{width:100%;height:auto;flex-direction:row;padding:16px}.sidebar .sidebar-menu[data-v-c55304de]{flex-direction:row;justify-content:center}.main-content[data-v-c55304de]{padding:24px 32px}.setting-container[data-v-c55304de]{margin:0;border-radius:0}.tabs-content .tab-panel[data-v-c55304de]{padding:20px}.setting-footer[data-v-c55304de]{padding:16px 20px}.tabs-header .tab-item[data-v-c55304de]{padding:12px 16px;font-size:13px}.setting-section .setting-select[data-v-c55304de]{width:100%}}\n',document.head.appendChild(x),{setters:[function(e){i=e._,o=e.r,r=e.G,s=e.h,c=e.o,d=e.d,l=e.e,u=e.C,p=e.j,v=e.w,f=e.g,g=e.t,b=e.M,m=e.k}],execute:function(){var a={class:"setting-page"},x={class:"main-content"},h={class:"setting-container"},y={class:"tabs-header"},k={class:"tabs-content"},w={key:0,class:"tab-panel"},_={class:"setting-section"},j={class:"setting-item setting-platformAddress"},V={class:"setting-item"},S={class:"checkbox-group"},z={key:1,class:"tab-panel"},C={class:"setting-section setting-update"},O={class:"setting-item"},P={class:"checkbox-group"},T={class:"setting-item"},U={class:"about-section"},F={class:"version-info"},G={class:"version-item"},E={class:"version-value-group"},A={class:"version-value"},I={class:"version-item"},N={class:"version-value"},q={class:"version-item"},J={class:"version-value"},M={__name:"setting",setup:function(t){var i=o("general"),M=o(""),R=o(!1),B=o(!0),D=o(!0),H=o("daily"),K=o("2.5.0"),L=o("2025.03.21 09:00"),Q=o("2025.03.21 09:00");r((function(){X()}));var W=function(){var t,a=(t=e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:b.info("正在检查更新..."),setTimeout((function(){b.success("当前已是最新版本")}),1500);case 1:return e.a(2)}}),t)})),function(){var e=this,a=arguments;return new Promise((function(i,o){var r=t.apply(e,a);function s(e){n(r,i,o,s,c,"next",e)}function c(e){n(r,i,o,s,c,"throw",e)}s(void 0)}))});return function(){return a.apply(this,arguments)}}(),X=function(){var e=localStorage.getItem("appSettings");if(e){var t=JSON.parse(e);M.value=t.platformAddress||"",R.value=t.autoStart||!1,B.value=void 0===t.autoConnect||t.autoConnect,D.value=void 0===t.autoUpdate||t.autoUpdate,H.value=t.updateFrequency||"daily"}};return function(e,t){var n=s("base-input"),o=s("base-checkbox"),r=s("base-option"),b=s("base-select"),X=s("base-button");return c(),d("div",a,[l("div",x,[l("div",h,[l("div",y,[l("div",{class:u(["tab-item",{active:"general"===i.value}]),onClick:t[0]||(t[0]=function(e){return i.value="general"})}," 通用设置 ",2),l("div",{class:u(["tab-item",{active:"version"===i.value}]),onClick:t[1]||(t[1]=function(e){return i.value="version"})}," 版本信息 ",2)]),l("div",k,["general"===i.value?(c(),d("div",w,[l("div",_,[l("div",j,[t[7]||(t[7]=l("label",{class:"setting-label"},"平台地址",-1)),p(n,{modelValue:M.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return M.value=e}),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:""},null,8,["modelValue"])]),l("div",V,[t[10]||(t[10]=l("label",{class:"setting-label"},"启动选项",-1)),l("div",S,[p(o,{modelValue:R.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return R.value=e}),class:"setting-checkbox"},{default:v((function(){return t[8]||(t[8]=[m(" 开机自启动 ")])})),_:1,__:[8]},8,["modelValue"]),p(o,{modelValue:B.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return B.value=e}),class:"setting-checkbox"},{default:v((function(){return t[9]||(t[9]=[m(" 启动后自动连接 ")])})),_:1,__:[9]},8,["modelValue"])])])])])):f("",!0),"version"===i.value?(c(),d("div",z,[l("div",C,[l("div",O,[t[12]||(t[12]=l("label",{class:"setting-label"},"更新选项",-1)),l("div",P,[p(o,{modelValue:D.value,"onUpdate:modelValue":t[5]||(t[5]=function(e){return D.value=e}),class:"setting-checkbox"},{default:v((function(){return t[11]||(t[11]=[m(" 自动检查更新 ")])})),_:1,__:[11]},8,["modelValue"])])]),l("div",T,[t[13]||(t[13]=l("label",{class:"setting-label"},"更新检查频率",-1)),p(b,{modelValue:H.value,"onUpdate:modelValue":t[6]||(t[6]=function(e){return H.value=e}),class:"setting-select",placeholder:"请选择"},{default:v((function(){return[p(r,{label:"每天",value:"daily"}),p(r,{label:"每周",value:"weekly"}),p(r,{label:"每月",value:"monthly"})]})),_:1},8,["modelValue"])])]),l("div",U,[t[18]||(t[18]=l("h3",{class:"about-title"},"关于安全客户端",-1)),l("div",F,[l("div",G,[t[15]||(t[15]=l("span",{class:"version-label"},"当前版本",-1)),l("div",E,[l("span",A,g(K.value),1),p(X,{text:"",type:"primary",size:"small",onClick:W},{default:v((function(){return t[14]||(t[14]=[m(" 检查更新 ")])})),_:1,__:[14]})])]),l("div",I,[t[16]||(t[16]=l("span",{class:"version-label"},"构建时间",-1)),l("span",N,g(L.value),1)]),l("div",q,[t[17]||(t[17]=l("span",{class:"version-label"},"上次更新时间",-1)),l("span",J,g(Q.value),1)])]),t[19]||(t[19]=l("div",{class:"copyright"},[l("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])])):f("",!0)])])])])}}};t("default",i(M,[["__scopeId","data-v-c55304de"]]))}}}))}();
