/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{u as d,a as y,b as p,r as o,G as f,p as g,L as h,M as n}from"./index.d0594432.js";const m={name:"WxOAuthCallback"},I=Object.assign(m,{setup(b){const u=d(),c=y(),i=p(),{code:r,state:t,auth_type:v,redirect_url:l}=u.query,s=o(Array.isArray(t)?t[0]:t),_=o("");return f(async()=>{const a=h.service({fullscreen:!0,text:"\u767B\u5F55\u4E2D\uFF0C\u8BF7\u7A0D\u5019..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:s.value,authWeb:{authWebCode:Array.isArray(r)?r[0]:r}};await i.LoginIn(e,"qiyewx_oauth",s.value)===!0?await c.push({name:"verify",query:{redirect_url:l}}):n.error("\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}catch(e){console.error("\u767B\u5F55\u8FC7\u7A0B\u51FA\u9519:",e),n.error("\u767B\u5F55\u8FC7\u7A0B\u51FA\u9519\uFF0C\u8BF7\u91CD\u8BD5")}finally{a.close()}}),g("userName",_),(a,e)=>null}});export{I as default};
