/*! 
 Build based on gin-vue-admin 
 Time : 1749623364000 */
import e from"./menuItem.f17f062d.js";import t from"./asyncSubmenu.3071b051.js";import{c as o,h as n,o as r,f as s,w as u,d as l,F as a,i,g as f,A as d}from"./index.49a4551d.js";const m=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(m){const c=m,h=o((()=>c.routerInfo.children&&c.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return m.routerInfo.hidden?f("",!0):(r(),s(d(h.value),{key:0,"is-collapse":m.isCollapse,theme:m.theme,"router-info":m.routerInfo},{default:u((()=>[m.routerInfo.children&&m.routerInfo.children.length?(r(!0),l(a,{key:0},i(m.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:m.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{m as default};
