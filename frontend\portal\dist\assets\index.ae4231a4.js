/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{_ as a,h as s,o as e,d as t,f as o,j as r,e as i,g as l}from"./index.2a422357.js";import n from"./header.23ccb61d.js";import c from"./menu.abdaf066.js";import"./ASD.492c8837.js";const d={class:"layout-page"},u={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},f=a(Object.assign({name:"Client"},{setup:a=>(a,f)=>{const p=s("router-view");return e(),t("div",d,[o("公共顶部菜单-"),r(n),i("div",u,[o("公共侧边栏菜单"),r(c),i("div",m,[o("主流程路由渲染点"),(e(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{f as default};
