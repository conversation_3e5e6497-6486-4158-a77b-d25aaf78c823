/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
import{_ as e,b as a,a as o,u as s,U as t,r as i,N as n,P as l,c,V as r,p as d,h as u,o as v,g as p,w as m,e as f,I as g,j as h,B as y,f as b,d as w,T as x,F as k,i as I,k as C,t as F,m as _,R as j,G as N,W as S,X as U,A as z}from"./index.9684b1fb.js";import{_ as A}from"./ASD.492c8837.js";import M from"./index.cf94c41c.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.f344746a.js";import"./menuItem.3162ee37.js";import"./asyncSubmenu.205021d2.js";
/*! js-cookie v3.0.5 | MIT */function O(e){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var s in o)e[s]=o[s]}return e}var R=function e(a,o){function s(e,s,t){if("undefined"!=typeof document){"number"==typeof(t=O({},o,t)).expires&&(t.expires=new Date(Date.now()+864e5*t.expires)),t.expires&&(t.expires=t.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var n in t)t[n]&&(i+="; "+n,!0!==t[n]&&(i+="="+t[n].split(";")[0]));return document.cookie=e+"="+a.write(s,e)+i}}return Object.create({set:s,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var o=document.cookie?document.cookie.split("; "):[],s={},t=0;t<o.length;t++){var i=o[t].split("="),n=i.slice(1).join("=");try{var l=decodeURIComponent(i[0]);if(s[l]=a.read(n,l),e===l)break}catch(c){}}return e?s[e]:s}},remove:function(e,a){s(e,"",O({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,O({},this.attributes,a))},withConverter:function(a){return e(O({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const D={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},B={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={class:"header-row"},T={class:"header-col"},$={class:"header-cont"},E={class:"header-content pd-0"},V={class:"breadcrumb-col"},G={class:"breadcrumb"},J={class:"user-col"},W={class:"right-box"},H={class:"dp-flex justify-content-center align-items height-full width-full"},P={class:"header-avatar",style:{cursor:"pointer"}},X={style:{"margin-right":"9px",color:"#252631"}},q={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},K={key:0,class:"dropdown-menu"},Q={key:0,class:"loading-overlay"},Y=e(Object.assign({name:"Layout"},{setup(e){const O=a(),Y=o(),Z=s(),ee=t(),ae=i(!0),oe=i(!1),se=i(!1),te=i("7"),ie=()=>{document.body.clientWidth;se.value=!1,oe.value=!1,ae.value=!0};ie();const ne=i(!1);n((()=>{l.emit("collapse",ae.value),l.emit("mobile",se.value),l.on("reload",ue),l.on("showLoading",(()=>{ne.value=!0})),l.on("closeLoading",(()=>{ne.value=!1})),window.onresize=()=>(ie(),l.emit("collapse",ae.value),void l.emit("mobile",se.value)),O.loadingInstance&&O.loadingInstance.close()})),c((()=>"dark"===O.sideMode?"#fff":"light"===O.sideMode?"#273444":O.baseColor));const le=c((()=>"dark"===O.sideMode?"#273444":"light"===O.sideMode?"#fff":O.sideMode)),ce=c((()=>Z.meta.matched)),re=i(!0);let de=null;const ue=async()=>{de&&window.clearTimeout(de),de=window.setTimeout((async()=>{if(Z.meta.keepAlive)re.value=!1,await r(),re.value=!0;else{const e=Z.meta.title;Y.push({name:"Reload",params:{title:e}})}}),400)},ve=i(!1),pe=i(!1),me=()=>{ae.value=!ae.value,oe.value=!ae.value,ve.value=!ae.value,l.emit("collapse",ae.value)},fe=()=>{pe.value=!pe.value},ge=()=>{Y.push({name:"person"})};return d("day",te),(e,a)=>{const o=u("base-aside"),s=u("router-view"),t=u("base-main"),n=u("base-container");return v(),p(n,{class:"layout-cont"},{default:m((()=>[f("div",{class:g([[oe.value?"openside":"hideside",se.value?"mobile":""],"layout-wrapper"])},[f("div",{class:g([[ve.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(ve.value=!ve.value,oe.value=!!ae.value,void me()))},null,2),h(o,{class:"main-cont main-left gva-aside",collapsed:ae.value},{default:m((()=>[f("div",{class:g(["tilte",[oe.value?"openlogoimg":"hidelogoimg"]]),style:y({background:le.value})},[a[3]||(a[3]=f("img",{alt:"",class:"logoimg",src:A},null,-1)),b("          <div>"),b('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),b('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),b("          </div>")],6),h(M,{class:"aside"}),f("div",{class:"footer",style:y({background:le.value})},[f("div",{class:"menu-total",onClick:me},[ae.value?(v(),w("svg",D,a[4]||(a[4]=[f("use",{"xlink:href":"#icon-expand"},null,-1)]))):(v(),w("svg",B,a[5]||(a[5]=[f("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),b(" 分块滑动功能 "),h(t,{class:"main-cont main-right"},{default:m((()=>[h(x,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:m((()=>[f("div",{style:y({width:`calc(100% - ${se.value?"0px":ae.value?"54px":"220px"})`}),class:"topfix"},[f("div",L,[f("div",T,[f("header",$,[f("div",E,[a[10]||(a[10]=f("div",{class:"header-menu-col",style:{"z-index":"100"}},[b('                      <div class="menu-total" @click="totalCollapse">'),b('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),b('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),b("                      </div>")],-1)),f("div",V,[f("nav",G,[(v(!0),w(k,null,I(ce.value.slice(1,ce.value.length),(e=>(v(),w("div",{key:e.path,class:"breadcrumb-item"},[C(F(_(j)(e.meta.topTitle||"",_(Z)))+" ",1),"总览"===e.meta.title?N((v(),w("select",{key:0,"onUpdate:modelValue":a[1]||(a[1]=e=>te.value=e),class:"day-select form-select"},[...a[6]||(a[6]=[f("option",{value:"7"},"最近7天",-1),f("option",{value:"30"},"最近30天",-1),f("option",{value:"90"},"最近90天",-1)])],512)),[[S,te.value]]):b("v-if",!0)])))),128))])]),f("div",J,[f("div",W,[b("                        <Search />"),f("div",{class:"dropdown",onClick:fe},[f("div",H,[f("span",P,[b(" 展示当前登录用户名 "),f("span",X,F(_(O).userInfo.displayName?_(O).userInfo.displayName:_(O).userInfo.name),1),(v(),w("svg",q,a[7]||(a[7]=[f("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),pe.value?(v(),w("div",K,[b(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),f("div",{class:"dropdown-item",onClick:ge},a[8]||(a[8]=[f("svg",{class:"icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-avatar"})],-1),C(" 个人信息 ")])),f("div",{class:"dropdown-item",onClick:a[2]||(a[2]=e=>(async()=>{document.location.protocol,document.location.host;const e={action:1,msg:"",platform:document.location.hostname},a=i({}),o=i("ws://127.0.0.1:50001"),s=navigator.platform;0!==s.indexOf("Mac")&&"MacIntel"!==s||(o.value="wss://127.0.0.1:50001");const t=async e=>{console.log(e,"0"),await a.value.send(e)},n=async()=>{console.log("socket断开链接"),await a.value.close()};console.log(`asecagent://?web=${JSON.stringify(e)}`),await O.LoginOut(),a.value=new WebSocket(o.value),a.value.onopen=async()=>{console.log("socket连接成功"),await t(JSON.stringify(e))},a.value.onmessage=async e=>{console.log(e),await n()},a.value.onerror=()=>{console.log("socket连接错误")},R.remove("asce_sms")})())},a[9]||(a[9]=[f("svg",{class:"icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-reading-lamp"})],-1),C(" 登 出 ")]))])):b("v-if",!0)]),b('                        <base-button type="text"'),b('                                   class="iconfont icon-rizhi1"'),b('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),b('                                   @click="toLog"'),b("                        >日志中心"),b("                        </base-button>")])])])])])]),b(" 当前面包屑用路由自动生成可根据需求修改 "),b('\r\n            :to="{ path: item.path }" 暂时注释不用'),b('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)])),_:1}),f("div",{class:g(["router-view-container",{loading:ne.value}])},[ne.value?(v(),w("div",Q,a[11]||(a[11]=[f("div",{class:"loading-spinner"},[f("div",{class:"spinner"}),f("div",{class:"loading-text"},"正在加载中")],-1)]))):b("v-if",!0),re.value?(v(),p(s,{key:1,class:"admin-box"},{default:m((({Component:e})=>[f("div",null,[h(x,{mode:"out-in",name:"el-fade-in-linear"},{default:m((()=>[(v(),p(U,{include:_(ee).keepAliveRouters},[(v(),p(z(e)))],1032,["include"]))])),_:2},1024)])])),_:1})):b("v-if",!0)],2),b("        <BottomInfo />"),b("        <setting />")])),_:1})],2)])),_:1})}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]);export{Y as default};
