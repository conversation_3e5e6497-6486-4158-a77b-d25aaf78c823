/*! 
 Build based on gin-vue-admin 
 Time : 1749628938000 */
import{u as e,a,r as s,b as l,c as t,z as n,E as u,K as o,h as r,o as i,d as v,j as m,w as c,F as d,i as p,f as y,e as g,I as f,m as h,k as b,t as S,Q as I,H as q,O,R as w}from"./index.c733b50d.js";import{J as k}from"./index-browser-esm.c2d3b5c9.js";const N={class:"router-history"},x=["tab"],C=Object.assign({name:"HistoryComponent"},{setup(C){const J=e(),j=a(),E=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),V=s([]),A=s(""),P=s(!1),R=l(),T=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),L=s(0),H=s(0),$=s(!1),_=s(!1),z=s(""),F=t((()=>k("$..defaultRouter[0]",R.userInfo)[0]||"dashboard")),K=()=>{V.value=[{name:F.value,meta:{title:"总览"},query:{},params:{}}],j.push({name:F.value}),P.value=!1,sessionStorage.setItem("historys",JSON.stringify(V.value))},Q=()=>{let e;const a=V.value.findIndex((a=>(E(a)===z.value&&(e=a),E(a)===z.value))),s=V.value.findIndex((e=>E(e)===A.value));V.value.splice(0,a),a>s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(V.value))},U=()=>{let e;const a=V.value.findIndex((a=>(E(a)===z.value&&(e=a),E(a)===z.value))),s=V.value.findIndex((e=>E(e)===A.value));V.value.splice(a+1,V.value.length),a<s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(V.value))},X=()=>{let e;V.value=V.value.filter((a=>(E(a)===z.value&&(e=a),E(a)===z.value))),j.push(e),sessionStorage.setItem("historys",JSON.stringify(V.value))},Y=e=>{if(!V.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,V.value.push(a)}window.sessionStorage.setItem("activeValue",E(e))},B=s({});n((()=>V.value),(()=>{B.value={},V.value.forEach((e=>{B.value[E(e)]=e}))}));const D=e=>{const a=B.value[e];j.push({name:a.name,query:a.query,params:a.params})},G=e=>{const a=V.value.findIndex((a=>E(a)===e));E(J)===e&&(1===V.value.length?j.push({name:F.value}):a<V.value.length-1?j.push({name:V.value[a+1].name,query:V.value[a+1].query,params:V.value[a+1].params}):j.push({name:V.value[a-1].name,query:V.value[a-1].query,params:V.value[a-1].params})),V.value.splice(a,1)};n((()=>P.value),(()=>{P.value?document.body.addEventListener("click",(()=>{P.value=!1})):document.body.removeEventListener("click",(()=>{P.value=!1}))})),n((()=>J),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(V.value=V.value.filter((e=>!e.meta.closeTab)),Y(e),sessionStorage.setItem("historys",JSON.stringify(V.value)),A.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),n((()=>V.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(V.value))}),{deep:!0});return(()=>{o.on("closeThisPage",(()=>{G(T(J))})),o.on("closeAllPage",(()=>{K()})),o.on("mobile",(e=>{_.value=e})),o.on("collapse",(e=>{$.value=e}));const e=[{name:F.value,meta:{title:"总览"},query:{},params:{}}];V.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?A.value=window.sessionStorage.getItem("activeValue"):A.value=E(J),Y(J),"true"===window.sessionStorage.getItem("needCloseAll")&&(K(),window.sessionStorage.removeItem("needCloseAll"))})(),u((()=>{o.off("collapse"),o.off("mobile")})),(e,a)=>{const s=r("el-tab-pane"),l=r("el-tabs");return i(),v("div",N,[m(l,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),closable:!(1===V.value.length&&e.$route.name===F.value),type:"card",onContextmenu:a[1]||(a[1]=q((e=>(e=>{if(1===V.value.length&&J.name===F.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;P.value=!0,s=$.value?54:220,_.value&&(s=0),L.value=e.clientX-s,H.value=e.clientY+10,z.value=a.substring(4)}})(e)),["prevent"])),onTabChange:D,onTabRemove:G},{default:c((()=>[(i(!0),v(d,null,p(V.value,(e=>(i(),y(s,{key:T(e),label:e.meta.title,name:T(e),tab:e,class:"gva-tab"},{label:c((()=>[g("span",{tab:e,style:f({color:A.value===T(e)?h(R).activeColor:"#333"})},[g("i",{class:"dot",style:f({backgroundColor:A.value===T(e)?h(R).activeColor:"#ddd"})},null,4),b(" "+S(h(I)(e.meta.title,e)),1)],12,x)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),O(g("ul",{style:f({left:L.value+"px",top:H.value+"px"}),class:"contextmenu"},[g("li",{onClick:K},"关闭所有"),g("li",{onClick:Q},"关闭左侧"),g("li",{onClick:U},"关闭右侧"),g("li",{onClick:X},"关闭其他")],4),[[w,P.value]])])}}});export{C as default};
