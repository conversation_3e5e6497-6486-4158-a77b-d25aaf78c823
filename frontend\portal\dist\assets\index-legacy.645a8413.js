/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
System.register(["./header-legacy.45b1d338.js","./menu-legacy.35bf5163.js","./index-legacy.d09203da.js","./ASD-legacy.b6ffb1bc.js"],(function(t,e){"use strict";var a,n,i,u,o,l,d,r,c=document.createElement("style");return c.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(c),{setters:[function(t){a=t.default},function(t){n=t.default},function(t){i=t.h,u=t.o,o=t.d,l=t.j,d=t.e,r=t.f},function(){}],execute:function(){var e={class:"layout-page"},c={class:"layout-wrap"},f={id:"layoutMain",class:"layout-main"};t("default",Object.assign({name:"Client"},{setup:function(t){return function(t,s){var h=i("router-view");return u(),o("div",e,[l(a),d("div",c,[l(n),d("div",f,[(u(),r(h,{key:t.$route.fullPath}))])])])}}}))}}}));
