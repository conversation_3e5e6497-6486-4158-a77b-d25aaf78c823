/* 菜单组件样式 */
.menu {
  list-style: none;
  margin: 0;
  padding: 0;
  background-color: #263444;
  color: #fff;
}

.menu-vertical {
  width: 100%;
}

.menu-item {
  position: relative;
  display: block;
  padding: 12px 20px;
  color: #fff;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-item:hover {
  background-color: rgba(64, 158, 255, 0.08);
  color: #fff;
}

.menu-item.active {
  background-color: #4D70FF;
  color: #fff;
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #409eff;
}

.menu-item-icon {
  display: inline-block;
  width: 20px;
  text-align: center;
}

.menu-item-title {
  display: inline-block;
  transition: all 0.3s;
}

/* 折叠状态 */
.menu.collapsed .menu-item {
  padding: 12px 17px;
  text-align: center;
}

.menu.collapsed .menu-item-title {
  display: none;
}

.menu.collapsed .menu-item-icon {
  margin-right: 0;
}

/* 子菜单 */
.submenu {
  position: relative;
}

.submenu-title {
  display: block;
  padding: 12px 20px;
  color: #fff;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.submenu-title:hover {
  background-color: rgba(64, 158, 255, 0.08);
  color: #fff;
}

.submenu-title::after {
  content: '';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%) rotate(0deg);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #fff;
  transition: transform 0.3s;
}

.submenu.open .submenu-title::after {
  transform: translateY(-50%) rotate(180deg);
}

.submenu-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s;
  background-color: rgba(0, 0, 0, 0.2);
}

.submenu.open .submenu-content {
  max-height: 500px;
}

.submenu .menu-item {
  padding-left: 40px;
  border-bottom: none;
}

.submenu .menu-item:hover {
  background-color: rgba(64, 158, 255, 0.15);
}

/* 滚动条样式 */
.scrollbar {
  overflow-y: auto;
  overflow-x: hidden;
}

.scrollbar::-webkit-scrollbar {
  width: 6px;
}

.scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 轮播图组件 */
.carousel {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}

.carousel-container {
  display: flex;
  transition: transform 0.3s;
}

.carousel-item {
  flex: 0 0 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s;
}

.carousel-indicator.active {
  background-color: #409eff;
}

/* 对话框组件 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
}

.dialog-header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 500;
}

.dialog-body {
  padding: 20px;
}

.dialog-footer {
  padding: 10px 20px 20px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-text {
  margin-left: 10px;
  color: #606266;
}

/* 消息提示 */
.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 3000;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.message-success {
  background-color: #f0f9ff;
  color: #67c23a;
  border: 1px solid #c2e7b0;
}

.message-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.message-error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.message-info {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #e9e9eb;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.float-left { float: left; }
.float-right { float: right; }

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

.hidden { display: none; }
.visible { display: block; }

.margin-0 { margin: 0; }
.margin-top-10 { margin-top: 10px; }
.margin-bottom-10 { margin-bottom: 10px; }
.margin-left-10 { margin-left: 10px; }
.margin-right-10 { margin-right: 10px; }

.padding-0 { padding: 0; }
.padding-10 { padding: 10px; }
.padding-20 { padding: 20px; }

.width-100 { width: 100%; }
.height-100 { height: 100%; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }
