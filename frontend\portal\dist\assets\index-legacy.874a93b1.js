/*! 
 Build based on gin-vue-admin 
 Time : 1749637440000 */
System.register(["./index-legacy.453b6b0c.js","./index-legacy.e4d7d647.js","./menuItem-legacy.b5fc509a.js","./asyncSubmenu-legacy.13292a71.js"],(function(e,n){"use strict";var t,a,u,l,o,r,c,i,f,d,s,v,m,h,p,g,b,y,x,k,T,_=document.createElement("style");return _.textContent='@charset "UTF-8";.el-sub-menu__title:hover,.el-menu-item:hover{background:transparent}.el-scrollbar .el-scrollbar__view{height:100%}.menu-info .menu-contorl{line-height:52px;font-size:20px;display:table-cell;vertical-align:middle}\n',document.head.appendChild(_),{setters:[function(e){t=e.u,a=e.a,u=e.b,l=e.S,o=e.r,r=e.z,c=e.E,i=e.K,f=e.h,d=e.o,s=e.d,v=e.j,m=e.w,h=e.T,p=e.F,g=e.i,b=e.m,y=e.f,x=e.g,k=e.I},function(e){T=e.default},function(){},function(){}],execute:function(){e("default",Object.assign({name:"Aside"},{setup:function(e){var n=t(),_=a(),j=u(),F=l(),w=o({}),M=function(){switch(j.sideMode){case"#fff":w.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":w.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};M();var B=o("");r((function(){return n}),(function(){B.value=n.meta.activeName||n.name}),{deep:!0}),r((function(){return j.sideMode}),(function(){M()}));var S=o(!1);B.value=n.meta.activeName||n.name,document.body.clientWidth<1e3&&(S.value=!S.value),i.on("collapse",(function(e){S.value=e})),c((function(){i.off("collapse")}));var q=function(e,t,a,u){var l,o,r={},c={};(null===(l=F.routeMap[e])||void 0===l?void 0:l.parameters)&&(null===(o=F.routeMap[e])||void 0===o||o.parameters.forEach((function(e){"query"===e.type?r[e.key]=e.value:c[e.key]=e.value}))),e!==n.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):_.push({name:e,query:r,params:c}))};return function(e,n){var t=f("el-menu"),a=f("el-scrollbar");return d(),s("div",{style:k({background:b(j).sideMode})},[v(a,{style:{height:"calc(100vh - 110px)"}},{default:m((function(){return[v(h,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:m((function(){return[v(t,{collapse:S.value,"collapse-transition":!1,"default-active":B.value,"background-color":w.value.background,"active-text-color":w.value.active,class:"el-menu-vertical","unique-opened":"",onSelect:q},{default:m((function(){return[(d(!0),s(p,null,g(b(F).asyncRouters[0].children,(function(e){return d(),s(p,null,[e.hidden?x("",!0):(d(),y(T,{key:e.name,"is-collapse":S.value,"router-info":e,theme:w.value},null,8,["is-collapse","router-info","theme"]))],64)})),256))]})),_:1},8,["collapse","default-active","background-color","active-text-color"])]})),_:1})]})),_:1})],4)}}}))}}}));
