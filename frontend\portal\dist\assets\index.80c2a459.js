/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
import a from"./header.65c1e73f.js";import s from"./menu.61add1bc.js";import{h as t,o as e,d as o,j as r,e as i,f as d}from"./index.8abc592d.js";import"./ASD.492c8837.js";const l={class:"layout-page"},u={class:"layout-wrap"},c={id:"layoutMain",class:"layout-main"},m=Object.assign({name:"Client"},{setup:m=>(m,n)=>{const p=t("router-view");return e(),o("div",l,[r(a),i("div",u,[r(s),i("div",c,[(e(),d(p,{key:m.$route.fullPath}))])])])}});export{m as default};
