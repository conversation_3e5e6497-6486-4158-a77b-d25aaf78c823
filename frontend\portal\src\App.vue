<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import { inject } from 'vue'

export default {
  name: 'App',
  created() {
  },
}
</script>

<style lang="scss">
// 引入初始化样式
/* 使用轻量级 SVG 图标，已在 main.js 中全局加载 */
@import '@/style/main.scss';
@import '@/style/base.scss';
@import '@/style/mobile.scss';

#app {
  background: #eee;
  height: 100vh;
  overflow: hidden;
  font-weight: 400 !important;
}

.el-button {
  font-weight: 400 !important;
}

.el-tabs__header {
  margin: 0px !important;
}

.demo-tabs {

  .el-tabs__header {
    height: 35px !important;

    * {
      height: 35px !important;
    }
  }

  .el-tabs__nav {
    border-bottom: 1px solid var(--el-border-color-light) !important;
  }
}

.el-table__header {
  * {
    font-family: Microsoft YaHei;
  }
}

//认证策略
//搜索框全局样式
.organize-search {
  width: 200px !important;
  float: right;
  height: 32px !important;
  color: #AAAAAA;

  input {
    font-size: 12px;
    color: #252631;
  }
}

//弹窗form表单全局样式
.custom-dialog {
  .el-dialog__title {
    font-size: 16px !important;
    font-weight: 700 !important;
  }

  .el-form-item__label {
    font-size: 12px;
  }

  .el-form-item__content * {
    font-size: 12px;

    .el-radio__label {
      font-size: 12px;
    }
  }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #1890FF;
    background: #1890FF;
  }

  .el-tabs__active-bar {
    background-color: #3791CF;
  }

  .el-tabs__item.is-active {
    color: #189CFF;
  }

  .el-switch.is-checked .el-switch__core {
    background-color: #1890FF;
    --el-switch-on-color: #1890FF;
  }

  .el-switch__core {
    background: #C0C0C0;
  }

  .dialog-footer {
    button {
      height: 28px;
      width: 93px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    background: #1890FF;
    border-color: #1890FF;
  }
}

.header {
  button {
    height: 32px;
    width: 77px;
    border-radius: 4px !important;
    font-size: 12px;
    color: #2972C8;
    --el-button-bg-color: #ffffff !important;
    --el-button-border-color: #E4E4E4 !important;
    font-family: PingFangSC-Regular, PingFang SC;
  }

  .icon-shuaxin:before {
    margin-right: 5px;
  }

  .el-input {
    .el-input__icon {
      font-size: 16px;
    }
  }
}

.table-row-style {
  th.is-leaf {
    background: #FAFAFA !important;
  }
}

.risk-pagination {
  float: right;
  height: 28px;

  .el-pagination__total {
    color: #252631;
    opacity: 0.5;
  }

  .el-input__inner {
    color: #252631;
    opacity: 0.5;
  }

  .el-pagination__jump {
    color: #252631;
    opacity: 0.5;
  }

  .el-pager li.is-active + li {
    border-left: 1px solid #ddd !important;
    border-radius: 4px;
    color: #252631;
    opacity: 0.5;
  }

  * {
    height: 26px;
    line-height: 28px;
  }

  .el-pager {
    height: 28px;

    li {
      height: 28px;
      background-color: #FFFFFF !important;
    }

    .is-active {
      height: 28px;
      border: 1px solid #2972C8 !important;
      border-radius: 4px !important;
      color: #2972C8 !important;
      //background-color: #4E8DDA !important;
    }
  }

  .btn-prev {
    height: 28px;
    background-color: #FFFFFF !important;
  }

  .btn-next {
    height: 28px;
    background-color: #FFFFFF !important;
  }
}

//终端管理
.terminal {
  .table-row-style {
    th.is-leaf {
      background: #FFFFFF;
    }

    .app-table-style td {
      background-color: #FFFFFF !important;
    }
  }
}

//组织架构
.organize {
  .header {
    button {
      height: 28px;
      width: 90px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }

  .table-row-style {
    th.is-leaf {
      background: #FFFFFF;
    }

    .app-table-style td {
      background-color: #FFFFFF !important;
    }
  }

  .dialog-footer {
    button {
      height: 28px;
      width: 93px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }
}

.role {
  .header {
    button {
      height: 28px;
      width: 90px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }

  .table-row-style {
    th.is-leaf {
      background: #FFFFFF;
    }

    .app-table-style td {
      background-color: #FFFFFF !important;
    }
  }

  .dialog-footer {
    button {
      height: 28px;
      width: 93px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }
}

.application {
  .header {
    button {
      height: 28px;
      width: 90px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }

  .table-row-style {
    th.is-leaf {
      background: #FFFFFF;
    }

    .app-table-style td {
      background-color: #FFFFFF !important;
    }
  }

  .dialog-footer {
    button {
      height: 28px;
      width: 93px;
      border-radius: 5px !important;
      font-size: 12px;
    }
  }
}

.policy-tree {
  div {
    font-size: 12px;
  }
}

.custom-tree-type {
  font-size: 6px;
  margin-left: 10px;
  background: #0d84ff;
  color: #FFFFFF;
}

// 全局单选框图标换成勾勾
#app .el-radio__input.is-checked .el-radio__inner::after {
  content: '';
  width: 8px;
  height: 3px;
  border: 2px solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 2px;
  left: 1px;
  vertical-align: middle;
  transform: rotate(-45deg);
  border-radius: 0px;
  background-color: #2972C8 !important;
  background: #2972C8 !important;
}

#app .el-radio__input.is-checked .el-radio__inner {
  background-color: #2972C8 !important;
  background: #2972C8 !important;
}

#app .el-radio__input.is-checked + .el-radio__label {
  color: #252631 !important;
}

#app .el-radio {
  color: #252631 !important;
}

#app .el-form-item__label {
  color: #252631 !important;
}

#app .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #2972C8 !important;
}

#app .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #2972C8 !important;
  background: #2972C8 !important;
}

#app .el-checkbox.el-checkbox--large .el-checkbox__inner {
  border-radius: 7px;
}
</style>
