/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
!function(){function e(a){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(a)}function a(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,n)}return t}function t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach((function(a){n(e,a,r[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(r,a))}))}return e}function n(a,t,n){return(t=function(a){var t=function(a,t){if("object"!=e(a)||!a)return a;var n=a[Symbol.toPrimitive];if(void 0!==n){var r=n.call(a,t||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(a)}(a,"string");return"symbol"==e(t)?t:t+""}(t))in a?Object.defineProperty(a,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[t]=n,a}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,a,t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",p=t.toStringTag||"@@toStringTag";function i(t,n,r,p){var i=n&&n.prototype instanceof s?n:s,d=Object.create(i.prototype);return o(d,"_invoke",function(t,n,r){var o,p,i,s=0,d=r||[],l=!1,f={p:0,n:0,v:e,a:u,f:u.bind(e,4),d:function(a,t){return o=a,p=0,i=e,f.n=t,c}};function u(t,n){for(p=t,i=n,a=0;!l&&s&&!r&&a<d.length;a++){var r,o=d[a],u=f.p,g=o[2];t>3?(r=g===n)&&(i=o[(p=o[4])?5:(p=3,3)],o[4]=o[5]=e):o[0]<=u&&((r=t<2&&u<o[1])?(p=0,f.v=n,f.n=o[1]):u<g&&(r=t<3||o[0]>n||n>g)&&(o[4]=t,o[5]=n,f.n=g,p=0))}if(r||t>1)return c;throw l=!0,n}return function(r,d,g){if(s>1)throw TypeError("Generator is already running");for(l&&1===d&&u(d,g),p=d,i=g;(a=p<2?e:i)||!l;){o||(p?p<3?(p>1&&(f.n=-1),u(p,i)):f.n=i:f.v=i);try{if(s=2,o){if(p||(r="next"),a=o[r]){if(!(a=a.call(o,i)))throw TypeError("iterator result is not an object");if(!a.done)return a;i=a.value,p<2&&(p=0)}else 1===p&&(a=o.return)&&a.call(o),p<2&&(i=TypeError("The iterator does not provide a '"+r+"' method"),p=1);o=e}else if((a=(l=f.n<0)?i:t.call(n,f))!==c)break}catch(a){o=e,p=1,i=a}finally{s=1}}return{value:a,done:l}}}(t,r,p),!0),d}var c={};function s(){}function d(){}function l(){}a=Object.getPrototypeOf;var f=[][n]?a(a([][n]())):(o(a={},n,(function(){return this})),a),u=l.prototype=s.prototype=Object.create(f);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,o(e,p,"GeneratorFunction")),e.prototype=Object.create(u),e}return d.prototype=l,o(u,"constructor",l),o(l,"constructor",d),d.displayName="GeneratorFunction",o(l,p,"GeneratorFunction"),o(u),o(u,p,"Generator"),o(u,n,(function(){return this})),o(u,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:i,m:g}})()}function o(e,a,t,n){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}o=function(e,a,t,n){if(a)r?r(e,a,{value:t,enumerable:!n,configurable:!n,writable:!n}):e[a]=t;else{var p=function(a,t){o(e,a,(function(e){return this._invoke(a,t,e)}))};p("next",0),p("throw",1),p("return",2)}},o(e,a,t,n)}function p(e,a,t,n,r,o,p){try{var i=e[o](p),c=i.value}catch(e){return void t(e)}i.done?a(c):Promise.resolve(c).then(n,r)}function i(e){return function(){var a=this,t=arguments;return new Promise((function(n,r){var o=e.apply(a,t);function i(e){p(o,n,r,i,c,"next",e)}function c(e){p(o,n,r,i,c,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.4bb28e53.js"],(function(e,a){"use strict";var n,o,p,c,s,d,l,f,u,g,m,v,h,b,x,y,w,k,_,O,S,P,j,F,z=document.createElement("style");return z.textContent='@charset "UTF-8";.person[data-v-f0c977cd]{background:#FFFFFF;border-radius:4px;height:100vh;max-height:calc(100vh - 68px)}.person .app-header[data-v-f0c977cd]{display:flex;justify-content:space-between;align-items:center;padding:20px 24px;border-bottom:1px solid #f0f0f0;background:#ffffff}.person .app-header .header-left .page-title[data-v-f0c977cd]{margin:0;font-size:20px;font-weight:600;color:#1f2329;line-height:28px}.person .app-header .header-right .search-controls[data-v-f0c977cd]{display:flex;align-items:center;gap:12px}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd]{width:240px}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd] .base-input__wrapper{border-radius:6px;background-color:#f7f8fa;border:1px solid transparent;transition:all .2s}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd] .base-input__wrapper:hover{background-color:#fff;border-color:#d0d7de}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd] .base-input__wrapper.is-focus{background-color:#fff;border-color:#409eff;box-shadow:0 0 0 2px rgba(64,158,255,.1)}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd] .base-input__inner{height:36px;line-height:36px;font-size:14px;color:#1f2329}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd] .base-input__inner::placeholder{color:#8a919f}.person .app-header .header-right .search-controls .refresh-btn[data-v-f0c977cd]{width:36px;height:36px;padding:0;border-radius:6px;background:#f7f8fa;border:1px solid transparent;color:#4e5969;transition:all .2s}.person .app-header .header-right .search-controls .refresh-btn[data-v-f0c977cd]:hover{background:#ffffff;border-color:#d0d7de;color:#1f2329}.person .app-header .header-right .search-controls .view-select[data-v-f0c977cd]{width:100px}.person .app-header .header-right .search-controls .view-select[data-v-f0c977cd] .base-select__wrapper{border-radius:6px;background-color:#f7f8fa;border:1px solid transparent;height:36px}.person .app-header .header-right .search-controls .view-select[data-v-f0c977cd] .base-select__wrapper:hover{background-color:#fff;border-color:#d0d7de}.person .app-header .search-input[data-v-f0c977cd]{width:300px}.person .app-header .search-input[data-v-f0c977cd] .el-input__wrapper{border-radius:4px;background-color:#f5f7fa}.person .app-header .search-input[data-v-f0c977cd] .el-input__wrapper.is-focus{background-color:#fff}.person .app-header .search-input[data-v-f0c977cd] .el-input__inner{height:32px;line-height:32px;font-size:14px}.person .app-header .search-input[data-v-f0c977cd] .el-input__inner::placeholder{color:#909399}.person .category-aside[data-v-f0c977cd]{background:#fafbfc;border-right:1px solid #e5e6eb}.person .category-aside .category-menu[data-v-f0c977cd]{border-right:none;background:transparent;padding:16px 8px}.person .category-aside .category-menu[data-v-f0c977cd] .base-menu-item{height:36px;line-height:36px;margin:2px 0;font-size:14px;color:#4e5969;border-radius:6px;padding:0 12px;transition:all .2s ease;cursor:pointer}.person .category-aside .category-menu[data-v-f0c977cd] .base-menu-item:not(.base-menu-item--active){background-color:transparent;color:#4e5969}.person .category-aside .category-menu[data-v-f0c977cd] .base-menu-item:hover:not(.base-menu-item--active){background-color:#f2f3f5;color:#1f2329}.person .category-aside .category-menu[data-v-f0c977cd] .base-menu-item.base-menu-item--active{background-color:#409eff;color:#fff;font-weight:500}.person .category-aside .category-menu[data-v-f0c977cd] .base-menu-item:active{background-color:#3370ff}.person .app-main[data-v-f0c977cd]{padding:24px;overflow-y:auto;background:#ffffff}.person .app-main .category-section[data-v-f0c977cd]{margin-bottom:40px}.person .app-main .category-section .category-title[data-v-f0c977cd]{font-size:18px;font-weight:600;color:#1f2329;margin:0 0 16px;padding-bottom:8px;border-bottom:2px solid #f0f0f0;position:relative}.person .app-main .category-section .category-title[data-v-f0c977cd]:after{content:"";position:absolute;bottom:-2px;left:0;width:40px;height:2px;background:#409eff}.person .app-main .category-section .apps-grid[data-v-f0c977cd]{display:grid;gap:16px;padding:20px 0}.person .app-main .category-section .apps-grid.view-standard[data-v-f0c977cd]{grid-template-columns:repeat(auto-fill,minmax(120px,1fr));max-width:100%}.person .app-main .category-section .apps-grid.view-standard .app-item[data-v-f0c977cd]{width:120px;height:120px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content[data-v-f0c977cd]{flex-direction:column;text-align:center;padding:16px 8px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-icon[data-v-f0c977cd]{margin-bottom:12px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-f0c977cd]{font-size:14px;line-height:20px;margin-bottom:4px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-desc[data-v-f0c977cd]{font-size:12px;color:#8a919f;line-height:16px}.person .app-main .category-section .apps-grid.view-compact[data-v-f0c977cd]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr))}.person .app-main .category-section .apps-grid.view-compact .app-item[data-v-f0c977cd]{height:64px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content[data-v-f0c977cd]{flex-direction:row;align-items:center;padding:12px 16px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-icon[data-v-f0c977cd]{margin-right:12px;flex-shrink:0}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-details[data-v-f0c977cd]{flex:1;text-align:left}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-details .app-name[data-v-f0c977cd]{font-size:14px;line-height:20px;font-weight:500}.person .app-main .category-section .apps-grid .app-item[data-v-f0c977cd]{background:#ffffff;border:1px solid #e5e6eb;border-radius:8px;position:relative;cursor:pointer;transition:all .2s ease;overflow:hidden}.person .app-main .category-section .apps-grid .app-item[data-v-f0c977cd]:hover:not(.disabled){border-color:#409eff;box-shadow:0 4px 12px rgba(64,158,255,.15);transform:translateY(-2px)}.person .app-main .category-section .apps-grid .app-item .app-content[data-v-f0c977cd]{display:flex;width:100%;height:100%}.person .app-main .category-section .apps-grid .app-item .app-content .app-icon[data-v-f0c977cd] .base-avatar{color:#fff;font-size:16px;font-weight:500;border-radius:8px}.person .app-main .category-section .apps-grid .app-item .app-content .app-icon[data-v-f0c977cd] .base-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.person .app-main .category-section .apps-grid .app-item .app-content .app-details .app-name[data-v-f0c977cd]{color:#1f2329;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:100%}.person .app-main .category-section .apps-grid .app-item .status-badge[data-v-f0c977cd]{position:absolute;top:8px;right:8px;padding:2px 6px;border-radius:4px;font-size:10px;background-color:#ff9500;color:#fff;font-weight:500;z-index:10;box-shadow:0 2px 4px rgba(0,0,0,.1)}.person .app-main .category-section .apps-grid .app-item.disabled[data-v-f0c977cd]{cursor:not-allowed;opacity:.6}.person .app-main .category-section .apps-grid .app-item.disabled[data-v-f0c977cd]:hover{border-color:#e5e6eb;box-shadow:none;transform:none}.person .app-main .category-section .apps-grid .app-item.disabled .app-content .app-icon[data-v-f0c977cd] .base-avatar{filter:grayscale(100%)}.person .app-main .category-section .apps-grid .app-item.disabled .app-content .app-details .app-name[data-v-f0c977cd]{color:#8a919f}.person .app-main .category-section[data-v-f0c977cd]:first-child{margin-top:0}.person .app-main .el-recent-access[data-v-f0c977cd]{margin-left:4px;margin-top:3px;width:56px;height:20px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#282a33;line-height:20px}.person .app-main .el-recent-data[data-v-f0c977cd]{margin-left:15px;height:20px}.person .app-main .el-recent-item[data-v-f0c977cd]{margin-top:2px;margin-right:6px;height:25px;padding:4px 0 4px 6px;background:#f5f6fe;border-radius:4px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;color:#536ce6}.person .app-main .el-recent-icon[data-v-f0c977cd]{width:8px;height:8px;margin:8px 6px 8px 5px}.person .app-main .el-recent-clear[data-v-f0c977cd]{opacity:.6;margin-top:6px;position:absolute;width:14px;height:14px;right:6px}@media screen and (max-width: 1200px){.person .app-header[data-v-f0c977cd]{padding:16px 20px}.person .app-header .header-right .search-controls[data-v-f0c977cd]{gap:8px}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd]{width:200px}.person .app-main[data-v-f0c977cd]{padding:20px}.person .app-main .apps-grid.view-standard[data-v-f0c977cd]{grid-template-columns:repeat(auto-fill,minmax(100px,1fr))}.person .app-main .apps-grid.view-standard .app-item[data-v-f0c977cd]{width:100px;height:100px}.person .app-main .apps-grid.view-standard .app-item .app-content[data-v-f0c977cd]{padding:12px 6px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-icon[data-v-f0c977cd]{margin-bottom:8px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-icon[data-v-f0c977cd] .base-avatar{width:40px!important;height:40px!important}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-f0c977cd]{font-size:12px;line-height:16px}.person .app-main .apps-grid.view-compact[data-v-f0c977cd]{grid-template-columns:1fr}}@media screen and (max-width: 768px){.person .app-header[data-v-f0c977cd]{flex-direction:column;align-items:stretch;gap:16px}.person .app-header .header-left .page-title[data-v-f0c977cd]{font-size:18px}.person .app-header .header-right .search-controls[data-v-f0c977cd]{justify-content:space-between}.person .app-header .header-right .search-controls .search-input[data-v-f0c977cd]{flex:1;max-width:none}.person .base-container[data-v-f0c977cd]{flex-direction:column}.person .base-container .category-aside[data-v-f0c977cd]{width:100%!important}.person .base-container .category-aside .category-menu[data-v-f0c977cd]{display:flex;overflow-x:auto;padding:12px 16px}.person .base-container .category-aside .category-menu[data-v-f0c977cd] .base-menu-item{flex-shrink:0;margin:0 4px;white-space:nowrap}.person .app-main[data-v-f0c977cd]{padding:16px}.person .app-main .apps-grid.view-standard[data-v-f0c977cd]{grid-template-columns:repeat(auto-fill,minmax(80px,1fr));gap:12px}.person .app-main .apps-grid.view-standard .app-item[data-v-f0c977cd]{width:80px;height:90px}.person .app-main .apps-grid.view-standard .app-item .app-content[data-v-f0c977cd]{padding:8px 4px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-f0c977cd]{font-size:11px;line-height:14px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-desc[data-v-f0c977cd]{display:none}.person .app-main .apps-grid.view-compact .app-item[data-v-f0c977cd]{height:56px}.person .app-main .apps-grid.view-compact .app-item .app-content[data-v-f0c977cd]{padding:8px 12px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-f0c977cd]{margin-right:8px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-f0c977cd] .base-avatar{width:32px!important;height:32px!important}}.tooltip-content[data-v-f0c977cd]{width:200px;text-align:center}.web-link[data-v-f0c977cd]{color:#409eff;text-decoration:none;word-break:break-all}.el-select__popper[data-v-f0c977cd]{background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1);margin-left:-10px!important;right:15px;top:110px;max-width:88px}.el-select__popper .el-select-dropdown__item[data-v-f0c977cd]{width:72px;height:28px;border-radius:4px;margin-left:7px;margin-bottom:4px;padding:0 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;line-height:20px;display:flex;align-items:center;background:#f5f5f7!important}.el-select__popper .el-select-dropdown__item.selected[data-v-f0c977cd]{color:#fff;background:#536ce6!important}.text-center[data-v-f0c977cd]{text-align:center}.web-link[data-v-f0c977cd]{color:#409eff;text-decoration:none}.web-link[data-v-f0c977cd]:hover{text-decoration:underline}.el-message{white-space:pre-line!important;line-height:1.5!important;padding:12px 20px!important}\n',document.head.appendChild(z),{setters:[function(e){n=e.x,o=e.r,p=e.J,c=e.K,s=e.N,d=e.b,l=e.u,f=e.D,u=e.h,g=e.o,m=e.d,v=e.e,h=e.j,b=e.w,x=e.f,y=e._,w=e.F,k=e.i,_=e.g,O=e.k,S=e.t,P=e.I,j=e.B,F=e.M}],execute:function(){var a={class:"person"},z={class:"header-right"},C={class:"search-controls"},W={class:"category-title"},D=["onClick"],T={key:0,class:"status-badge"},E={class:"app-content"},U={class:"app-icon"},I={class:"tooltip-content"},A={key:0},N={key:1},R={class:"app-details"},G=["title"],V={key:0,class:"app-desc"},B=Object.assign({name:"AppPage"},{setup:function(e){var y=o(""),B=o(null),J=o([]),L=o([]),M=o("1"),q=o(!1),X=o("standard"),H=p([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),K=o(null),Y=o(!1),Q=function(e){F({message:e,type:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",duration:arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3})},Z=function(){var e=i(r().m((function e(a){return r().w((function(e){for(;;)if(0===e.n)return e.a(2,new Promise((function(e,t){var n,o=!1,p=function(){var p=i(r().m((function p(){var i,c,s;return r().w((function(r){for(;;)switch(r.n){case 0:return r.p=0,r.n=1,new Promise((function(e,a){if(K.value&&K.value.readyState===WebSocket.OPEN)e(K.value);else{var t=new WebSocket("ws://localhost:50001");Y.value=!0,t.onopen=function(){console.log("WebSocket Connected"),K.value=t,Y.value=!1,e(t)},t.onmessage=function(e){var a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&Q(a,"error")},t.onclose=function(){console.log("WebSocket Disconnected"),K.value=null,Y.value=!1},t.onerror=function(e){console.error("WebSocket Error:",e),Y.value=!1,a(e)},setTimeout((function(){Y.value&&(Y.value=!1,t.close(),a(new Error("连接超时")))}),5e3)}}));case 1:i=r.v,c={action:3,msg:a},n=setTimeout((function(){o||(i.close(),t(new Error("启动超时：未收到响应")))}),3e3),i.onmessage=function(a){o=!0,clearTimeout(n);var r=a.data;r.startsWith("Ok")?e():t(new Error(r))},i.send(JSON.stringify(c)),console.log("发送消息:",c),r.n=3;break;case 2:r.p=2,s=r.v,clearTimeout(n),t(s);case 3:return r.a(2)}}),p,null,[[0,2]])})));return function(){return p.apply(this,arguments)}}();p()})))}),e)})));return function(a){return e.apply(this,arguments)}}(),$=function(){var e=i(r().m((function e(a){var t;return r().w((function(e){for(;;)switch(e.n){case 0:if(a.WebUrl&&!a.maint){e.n=1;break}return e.a(2);case 1:if(!a.WebUrl.toLowerCase().startsWith("cs:")){e.n=6;break}return t=a.WebUrl.substring(3),e.p=2,Q("正在启动爱尔企业浏览器...","info"),e.n=3,Z(t);case 3:Q("启动成功","success"),e.n=5;break;case 4:e.p=4,e.v,Q("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3);case 5:e.n=7;break;case 6:window.open(a.WebUrl,"_blank");case 7:return e.a(2)}}),e,null,[[2,4]])})));return function(a){return e.apply(this,arguments)}}();c((function(){K.value&&(K.value.close(),K.value=null)}));var ee=function(e){for(var a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,n=0;n<e.length;n++)t+=e.charCodeAt(n);return a[t%a.length]},ae=function(){q.value=!0},te=function(e){B.value=parseInt(e),L.value=e?J.value.filter((function(a){return a.id===parseInt(e)})):J.value},ne=function(){if(y.value){var e=y.value.toLowerCase().trim();L.value=J.value.map((function(a){return t(t({},a),{},{apps:a.apps.filter((function(a){return a.app_name.toLowerCase().includes(e)}))})})).filter((function(e){return e.apps.length>0}))}else L.value=J.value},re=function(){var e=i(r().m((function e(){var a,t,o,p;return r().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,n({url:"/console/v1/application/getuserapp",method:"get"});case 1:a=e.v,t=a.data,console.log("API返回数据:",t),0===t.code&&t.data&&(o=t.data.map((function(e,a){return{id:a+1,name:e.category,apps:e.apps.map((function(e){return{id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl}}))}})),console.log("格式化后的数据:",o),J.value=o,L.value=o,o.length>0&&(B.value=o[0].id,M.value=o[0].id.toString())),e.n=3;break;case 2:e.p=2,p=e.v,console.error("API调用出错:",p);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();s((function(){re()}));var oe=d(),pe=l().query,ie=null;try{if(!f.isClient()){var ce=new XMLHttpRequest;ce.open("GET",document.location,!1),ce.send(null),ie=ce.getResponseHeader("X-Corp-ID")}}catch(ve){console.warn("无法获取 X-Corp-ID header，使用默认值:",ve)}var se={action:0,msg:{token:oe.token.accessToken,refreshToken:oe.token.refreshToken,realm:ie||"default"},platform:document.location.hostname},de=pe.wp||50001,le=o({}),fe=o("ws://127.0.0.1:".concat(de)),ue=navigator.platform;0!==ue.indexOf("Mac")&&"MacIntel"!==ue||(fe.value="wss://127.0.0.1:".concat(de));var ge=function(e){console.log(e,"0"),le.value.send(e)},me=function(){console.log("socket断开链接"),le.value.close()};return console.log("asecagent://?web=".concat(JSON.stringify(se))),le.value=new WebSocket(fe.value),le.value.onopen=function(){console.log("socket连接成功"),ge(JSON.stringify(se))},le.value.onmessage=function(e){console.log(e),me()},le.value.onerror=function(){console.log("socket连接错误:"+fe.value),window.location.href="asecagent://?web=".concat(JSON.stringify(se))},function(e,t){var n=u("base-input"),r=u("base-button"),o=u("base-option"),p=u("base-select"),i=u("base-header"),c=u("base-menu-item"),s=u("base-menu"),d=u("base-aside"),l=u("base-avatar"),f=u("base-tooltip"),F=u("base-main"),B=u("base-container");return g(),m("div",null,[v("div",a,[h(i,{class:"app-header"},{default:b((function(){return[t[3]||(t[3]=v("div",{class:"header-left"},[v("h1",{class:"page-title"},"我的应用")],-1)),v("div",z,[v("div",C,[h(n,{class:"search-input",modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return y.value=e}),placeholder:"搜索应用","prefix-icon":"Search",onInput:ne,clearable:""},null,8,["modelValue"]),h(r,{class:"refresh-btn",icon:"Refresh",size:"small",onClick:re}),h(p,{class:"view-select",modelValue:X.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return X.value=e}),placeholder:"视图",size:"small"},{default:b((function(){return[(g(!0),m(w,null,k(H,(function(e){return g(),_(o,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])]),x('\r\n        <div class="el-row">\r\n          <span class="el-recent-access">最新访问</span>\r\n          <span class="el-recent-data">\r\n            <span class="el-recent-item">\r\n              最新访问1\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问2\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问3\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <base-icon class="el-recent-clear" name="close" title="清空" />\r\n          </span>\r\n        </div>\r\n        ')]})),_:1,__:[3]}),x(" 主体内容区域：使用 el-container 实现左右布局 "),h(B,null,{default:b((function(){return[x(" 左侧分类导航 "),h(d,{width:"96px",class:"category-aside"},{default:b((function(){return[h(s,{class:"category-menu",mode:"vertical",onSelect:te,"default-active":M.value},{default:b((function(){return[h(c,{index:"0",onClick:t[2]||(t[2]=function(e){return te(null)})},{default:b((function(){return t[4]||(t[4]=[O(" 全部 ")])})),_:1,__:[4]}),(g(!0),m(w,null,k(J.value,(function(e){return g(),_(c,{key:e.id,index:e.id.toString()},{default:b((function(){return[O(S(e.name),1)]})),_:2},1032,["index"])})),128))]})),_:1},8,["default-active"])]})),_:1}),x(" 右侧应用列表 "),h(F,{class:"app-main"},{default:b((function(){return[(g(!0),m(w,null,k(L.value,(function(e){return g(),m("div",{key:e.id,class:"category-section"},[x(" 分类标题 "),v("h3",W,S(e.name),1),x(" 应用列表 "),v("div",{class:P(["apps-grid","view-".concat(X.value)])},[(g(!0),m(w,null,k(e.apps,(function(e){return g(),m("div",{key:e.id,class:P(["app-item",{disabled:!e.WebUrl||e.maint}]),onClick:function(a){return $(e)}},[e.maint?(g(),m("div",T," 维护中 ")):x("v-if",!0),v("div",E,[v("div",U,[h(f,{effect:"light",placement:"bottom"},{content:b((function(){return[v("div",I,[e.WebUrl?(g(),m("span",A,S(e.WebUrl),1)):(g(),m("span",N,"暂无访问地址"))])]})),default:b((function(){return[h(l,{shape:"square",size:"compact"===X.value?40:48,src:e.icon,onError:ae,style:j(!e.icon||q.value?"background-color: ".concat(ee(e.app_name)," !important"):"")},{default:b((function(){return[O(S(!e.icon||q.value?e.app_name.slice(0,2):""),1)]})),_:2},1032,["size","src","style"])]})),_:2},1024)]),v("div",R,[v("div",{class:"app-name",title:e.app_name},S(e.app_name),9,G),"standard"===X.value?(g(),m("div",V,S(e.app_desc||"应用程序"),1)):x("v-if",!0)])])],10,D)})),128))],2)])})),128))]})),_:1})]})),_:1})])])}}});e("default",y(B,[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]))}}}))}();
