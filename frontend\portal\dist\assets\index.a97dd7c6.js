/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
import{x as e,r as a,J as s,K as l,N as n,b as t,u as o,D as c,h as r,o as i,d as u,e as p,j as d,w as v,f as m,_ as b,F as f,i as g,g as y,k,t as h,I as _,B as w,M as W}from"./index.5ebe5a72.js";const S={class:"person"},D={class:"el-search"},x={class:"category-title"},C={class:"apps-container"},U={key:0,class:"status-badge"},I={class:"icon-wrapper"},O={class:"tooltip-content text-center"},T={key:0},A={key:1},E={class:"app-info"},F={class:"app-name"},N=b(Object.assign({name:"AppPage"},{setup(b){const N=a(""),P=a(null),V=a([]),B=a([]),J=a("1"),$=a(!1),L=a("standard"),M=s([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),j=a(null),q=a(!1),z=(e,a="success",s=3e3)=>{W({message:e,type:a,duration:s})},R=async e=>new Promise(((a,s)=>{let l,n=!1;(async()=>{try{const t=await new Promise(((e,a)=>{if(j.value&&j.value.readyState===WebSocket.OPEN)return void e(j.value);const s=new WebSocket("ws://localhost:50001");q.value=!0,s.onopen=()=>{console.log("WebSocket Connected"),j.value=s,q.value=!1,e(s)},s.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&z(a,"error")},s.onclose=()=>{console.log("WebSocket Disconnected"),j.value=null,q.value=!1},s.onerror=e=>{console.error("WebSocket Error:",e),q.value=!1,a(e)},setTimeout((()=>{q.value&&(q.value=!1,s.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};l=setTimeout((()=>{n||(t.close(),s(new Error("启动超时：未收到响应")))}),3e3),t.onmessage=e=>{n=!0,clearTimeout(l);const t=e.data;t.startsWith("Ok")?a():s(new Error(t))},t.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(t){clearTimeout(l),s(t)}})()}));l((()=>{j.value&&(j.value.close(),j.value=null)}));const X=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let s=0;for(let l=0;l<e.length;l++)s+=e.charCodeAt(l);return a[s%a.length]},H=()=>{$.value=!0},G=e=>{P.value=parseInt(e),B.value=e?V.value.filter((a=>a.id===parseInt(e))):V.value},K=()=>{if(!N.value)return void(B.value=V.value);const e=N.value.toLowerCase().trim();B.value=V.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))},Q=async()=>{try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(console.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",e),V.value=e,B.value=e,e.length>0&&(P.value=e[0].id,J.value=e[0].id.toString())}}catch(a){console.error("API调用出错:",a)}};n((()=>{Q()}));const Y=t(),Z=o().query;let ee=null;try{if(!c.isClient()){const e=new XMLHttpRequest;e.open("GET",document.location,!1),e.send(null),ee=e.getResponseHeader("X-Corp-ID")}}catch(se){console.warn("无法获取 X-Corp-ID header，使用默认值:",se)}const ae={action:0,msg:{token:Y.token.accessToken,refreshToken:Y.token.refreshToken,realm:ee||"default"},platform:document.location.hostname};{const e=Z.wp||50001,s=a({}),l=a(`ws://127.0.0.1:${e}`),n=navigator.platform;0!==n.indexOf("Mac")&&"MacIntel"!==n||(l.value=`wss://127.0.0.1:${e}`);const t=()=>{s.value=new WebSocket(l.value),s.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(ae))},s.value.onmessage=e=>{console.log(e),c()},s.value.onerror=()=>{console.log("socket连接错误:"+l.value),window.location.href=`asecagent://?web=${JSON.stringify(ae)}`}},o=e=>{console.log(e,"0"),s.value.send(e)},c=()=>{console.log("socket断开链接"),s.value.close()};console.log(`asecagent://?web=${JSON.stringify(ae)}`),t()}return(e,a)=>{const s=r("base-input"),l=r("base-button"),n=r("base-option"),t=r("base-select"),o=r("base-header"),c=r("el-menu-item"),b=r("el-menu"),W=r("base-aside"),P=r("base-avatar"),j=r("base-tooltip"),q=r("base-link"),Q=r("base-main"),Y=r("base-container");return i(),u("div",null,[p("div",S,[d(o,null,{default:v((()=>[a[3]||(a[3]=p("span",{class:"el-title"},"我的应用",-1)),p("span",D,[d(s,{class:"el-search-input",modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=e=>N.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:K,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),d(l,{class:"el-search-btn",icon:"Refresh",size:"small"}),d(t,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:L.value,"onUpdate:modelValue":a[1]||(a[1]=e=>L.value=e),placeholder:"Select",size:"small"},{default:v((()=>[(i(!0),u(f,null,g(M,(e=>(i(),y(n,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),m('\r\n        <div class="el-row">\r\n          <span class="el-recent-access">最新访问</span>\r\n          <span class="el-recent-data">\r\n            <span class="el-recent-item">\r\n              最新访问1\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问2\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问3\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <base-icon class="el-recent-clear" name="close" title="清空" />\r\n          </span>\r\n        </div>\r\n        ')])),_:1,__:[3]}),m(" 主体内容区域：使用 el-container 实现左右布局 "),d(Y,null,{default:v((()=>[m(" 左侧分类导航 "),d(W,{width:"96px",class:"category-aside"},{default:v((()=>[d(b,{class:"category-menu",onSelect:G,"default-active":J.value},{default:v((()=>[d(c,{index:"0",onClick:a[2]||(a[2]=e=>G(null))},{default:v((()=>a[4]||(a[4]=[k(" 全部 ")]))),_:1,__:[4]}),(i(!0),u(f,null,g(V.value,(e=>(i(),y(c,{key:e.id,index:e.id.toString()},{default:v((()=>[k(h(e.name),1)])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),m(" 右侧应用列表 "),d(Q,{class:"app-main"},{default:v((()=>[(i(!0),u(f,null,g(B.value,(e=>(i(),u("div",{key:e.id,class:"category-section"},[m(" 分类标题 "),p("h3",x,h(e.name),1),m(" 应用列表 "),p("div",C,[(i(!0),u(f,null,g(e.apps,(e=>(i(),u("div",{key:e.id,class:_(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(i(),u("div",U," 维护中 ")):m("v-if",!0),d(q,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const a=e.WebUrl.substring(3);try{z("正在启动爱尔企业浏览器...","info"),await R(a),z("启动成功","success")}catch(se){z("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)},{default:v((()=>[p("div",I,[d(j,{effect:"light",placement:"bottom"},{content:v((()=>[p("div",O,[e.WebUrl?(i(),u("span",T,h(e.WebUrl),1)):(i(),u("span",A,"暂无访问地址"))])])),default:v((()=>[d(P,{shape:"square",size:48,src:e.icon,onError:H,style:w(!e.icon||$.value?`background-color: ${X(e.app_name)} !important`:"")},{default:v((()=>[k(h(!e.icon||$.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["src","style"])])),_:2},1024)]),p("div",E,[p("div",F,h(e.app_name),1),a[5]||(a[5]=p("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])])),_:2},1032,["disabled","onClick"])],2)))),128))])])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]);export{N as default};
