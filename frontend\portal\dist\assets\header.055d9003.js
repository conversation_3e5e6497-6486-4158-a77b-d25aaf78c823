/*! 
 Build based on gin-vue-admin 
 Time : 1749610601000 */
import{_ as e}from"./ASD.492c8837.js";import{_ as t,h as i,o,d as s,e as a,j as n,w as r,t as d,k as l}from"./index.d0594432.js";const c=""+new URL("avator.bd83723a.png",import.meta.url).href,u={class:"layout-header"},h={id:"u-header-menu",class:"right-wrapper"},g={id:"u-avator",ref:"countMenu"},m={class:"user-info"},w={class:"user-name"};const p=t({name:"ClientHeader",data:()=>({drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1}),computed:{isAccess:()=>!1},watch:{userId(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted(){},beforeDestroy(){},methods:{minimizeWnd(){agentApi.minimizeWnd()},maximizeWndOrNot(){this.isMaxWindow?(agentApi.normalnizeWnd(),this.isMaxWindow=!1):(agentApi.maximizeWnd(),this.isMaxWindow=!0)},dropdownVisiHandle(){},closeWnd(){if(TestQtModule("UIPlatform_Window","HideWnd"))EventBus.$emit("closeAssui"),this.$nextTick((()=>{agentApi.hideWend()}));else try{this.$ipcSend("UIPlatform_Window","TerminateWnd")}catch(e){this.$message.error("操作失败，因小助手版本低。请重启小助手或电脑以升级。")}},async setHandle(e){if("changeLange"===e){const e=this.$i18n.locale;setLang("zh"===e?"en":"zh")}else"changeMode"===e&&this.changeMode()},userMenuHandle(e,t={}){switch(this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},async logoutHandle(){if(this.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1===this.logoutType){try{let e;this.isSsoAuth()&&(e=await ssoLogout(_.get(this.clientInfo,"accessStatus.lastAuthType")));const t=await proxyApi.cutoffDevice({device_id:_.get(this.clientInfo,"detail.DeviceID",0),remark:"LogOut"});if(0!==parseInt(_.get(t,"errcode")))return _.get(t,"errmsg")||this.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),void loading.destory();commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),await agentApi.logOut({IsCredibleDevice:_.get(this.clientInfo,"detail.IsTrustDev","0")}),this.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,this.isDot1xMode&&this.setClientInfo(_.merge({},this.clientInfo,{basic:{IsOnline:0}})),this.setAuthInfo({...this.authInfo,basic:{}}),this.setClientInfo({...this.clientInfo,accessStatus:{}});const i=(new Date).getTime();this.$router.push({name:"message",params:{forceTo:!0},query:{t:i}}),_.isString(e)&&""!==e&&(console.log("logoutUrl:".logoutUrl),agentApi.windowOpenUrl(e))}catch(e){console.error("退出登录错误",e)}loading.destory()}},getCountMenuWidth(){const e=this.isZtpUser?44:0,t=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);this.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(t)+e})},hdEventHandle(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer(){this.deviceInnerBack=!1},changeVisible(e){this.drawer=e}}},[["render",function(t,p,f,I,y,v){const _=i("el-dropdown-item"),W=i("el-dropdown-menu"),k=i("el-dropdown"),C=i("FullScreen"),x=i("el-icon"),S=i("Minus"),M=i("Close");return o(),s("div",u,[p[3]||(p[3]=a("div",{class:"header-logo"},[a("img",{src:e,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),p[4]||(p[4]=a("div",{id:"u-electron-drag"},null,-1)),a("ul",h,[a("li",g,[n(k,{id:"ui-headNav-header-div-account_info",placement:"bottom-start",onCommand:v.userMenuHandle,onVisibleChange:v.dropdownVisiHandle},{default:r((()=>[a("div",m,[p[0]||(p[0]=a("div",{class:"user-face"},[a("img",{src:c,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),a("span",w,d(y.username),1)]),n(W,{slot:"dropdown",class:"header-count-menu"},{default:r((()=>[n(_,{id:"ui-headNav-header-li-cancel_account",command:"lougOut"},{default:r((()=>p[1]||(p[1]=[a("i",{class:"iconfont icon-zhuxiao"},null,-1),l("注销 ")]))),_:1,__:[1]})])),_:1})])),_:1},8,["onCommand","onVisibleChange"])],512),p[2]||(p[2]=a("div",{class:"user-divider"},null,-1)),n(x,{class:"window-operate",onClick:v.maximizeWndOrNot},{default:r((()=>[n(C)])),_:1},8,["onClick"]),n(x,{class:"window-operate",onClick:v.minimizeWnd},{default:r((()=>[n(S)])),_:1},8,["onClick"]),n(x,{class:"window-operate",style:{"margin-right":"16px"},onClick:v.closeWnd},{default:r((()=>[n(M)])),_:1},8,["onClick"])])])}],["__scopeId","data-v-3a119553"]]);export{p as default};
