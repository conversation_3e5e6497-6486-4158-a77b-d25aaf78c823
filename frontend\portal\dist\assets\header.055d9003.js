/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as f}from"./ASD.492c8837.js";import{_ as w,h as n,o as y,d as v,e as o,j as s,w as i,t as I,k}from"./index.d0594432.js";const x=""+new URL("avator.bd83723a.png",import.meta.url).href;const C={name:"ClientHeader",data(){return{drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1}},computed:{isAccess(){return!1}},watch:{userId(e,t){console.log("\u7528\u6237id\u53D8\u52A8",e,t),console.debug("\u7528\u6237id\u53D8\u52A8")}},mounted(){},beforeDestroy(){},methods:{minimizeWnd(){agentApi.minimizeWnd()},maximizeWndOrNot(){this.isMaxWindow?(agentApi.normalnizeWnd(),this.isMaxWindow=!1):(agentApi.maximizeWnd(),this.isMaxWindow=!0)},dropdownVisiHandle(){},closeWnd(){if(!TestQtModule("UIPlatform_Window","HideWnd")){try{this.$ipcSend("UIPlatform_Window","TerminateWnd")}catch{this.$message.error("\u64CD\u4F5C\u5931\u8D25\uFF0C\u56E0\u5C0F\u52A9\u624B\u7248\u672C\u4F4E\u3002\u8BF7\u91CD\u542F\u5C0F\u52A9\u624B\u6216\u7535\u8111\u4EE5\u5347\u7EA7\u3002")}return}EventBus.$emit("closeAssui"),this.$nextTick(()=>{agentApi.hideWend()})},async setHandle(e){if(e==="changeLange"){const t=this.$i18n.locale;setLang(t==="zh"?"en":"zh")}else e==="changeMode"&&this.changeMode()},userMenuHandle(e,t={}){switch(this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="\u6CE8\u9500\u540E\u4F1A\u53D6\u6D88\u81EA\u52A8\u8EAB\u4EFD\u8BA4\u8BC1\u529F\u80FD\uFF0C\u60A8\u786E\u5B9A\u8981\u6CE8\u9500\u5417\uFF1F",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0;break}e!=="lougOut"&&e!=="switchNetwork"&&(this.drawer=!0)},async logoutHandle(){if(this.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),this.logoutType===1){try{let e;this.isSsoAuth()&&(e=await ssoLogout(_.get(this.clientInfo,"accessStatus.lastAuthType")));const t=await proxyApi.cutoffDevice({device_id:_.get(this.clientInfo,"detail.DeviceID",0),remark:"LogOut"});if(parseInt(_.get(t,"errcode"))!==0){_.get(t,"errmsg")||this.$message.error("\u6CE8\u9500\u5931\u8D25\uFF01\u53EF\u80FD\u662F\u56E0\u4E3A\u7F51\u7EDC\u4E0D\u53EF\u7528\uFF0C\u6216\u8005\u670D\u52A1\u5668\u7E41\u5FD9\u3002"),loading.destory();return}commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),await agentApi.logOut({IsCredibleDevice:_.get(this.clientInfo,"detail.IsTrustDev","0")}),this.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,this.isDot1xMode&&this.setClientInfo(_.merge({},this.clientInfo,{basic:{IsOnline:0}})),this.setAuthInfo({...this.authInfo,basic:{}}),this.setClientInfo({...this.clientInfo,accessStatus:{}});const l=new Date().getTime();this.$router.push({name:"message",params:{forceTo:!0},query:{t:l}}),_.isString(e)&&e!==""&&(console.log("logoutUrl:".logoutUrl),agentApi.windowOpenUrl(e))}catch(e){console.error("\u9000\u51FA\u767B\u5F55\u9519\u8BEF",e)}loading.destory()}},getCountMenuWidth(){const e=this.isZtpUser?44:0,t=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);this.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(t)+e})},hdEventHandle(e){switch(e.type){case"router":this.userMenuHandle(e.val);break}},closeDrawer(){this.deviceInnerBack=!1},changeVisible(e){this.drawer=e}}},W={class:"layout-header"},S={id:"u-header-menu",class:"right-wrapper"},b={id:"u-avator",ref:"countMenu"},M={class:"user-info"},T={class:"user-name"};function D(e,t,l,z,d,a){const c=n("el-dropdown-item"),u=n("el-dropdown-menu"),h=n("el-dropdown"),g=n("FullScreen"),r=n("el-icon"),m=n("Minus"),p=n("Close");return y(),v("div",W,[t[3]||(t[3]=o("div",{class:"header-logo"},[o("img",{src:f,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),t[4]||(t[4]=o("div",{id:"u-electron-drag"},null,-1)),o("ul",S,[o("li",b,[s(h,{id:"ui-headNav-header-div-account_info",placement:"bottom-start",onCommand:a.userMenuHandle,onVisibleChange:a.dropdownVisiHandle},{default:i(()=>[o("div",M,[t[0]||(t[0]=o("div",{class:"user-face"},[o("img",{src:x,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),o("span",T,I(d.username),1)]),s(u,{slot:"dropdown",class:"header-count-menu"},{default:i(()=>[s(c,{id:"ui-headNav-header-li-cancel_account",command:"lougOut"},{default:i(()=>t[1]||(t[1]=[o("i",{class:"iconfont icon-zhuxiao"},null,-1),k("\u6CE8\u9500 ")])),_:1,__:[1]})]),_:1})]),_:1},8,["onCommand","onVisibleChange"])],512),t[2]||(t[2]=o("div",{class:"user-divider"},null,-1)),s(r,{class:"window-operate",onClick:a.maximizeWndOrNot},{default:i(()=>[s(g)]),_:1},8,["onClick"]),s(r,{class:"window-operate",onClick:a.minimizeWnd},{default:i(()=>[s(m)]),_:1},8,["onClick"]),s(r,{class:"window-operate",style:{"margin-right":"16px"},onClick:a.closeWnd},{default:i(()=>[s(p)]),_:1},8,["onClick"])])])}const N=w(C,[["render",D],["__scopeId","data-v-3a119553"]]);export{N as default};
