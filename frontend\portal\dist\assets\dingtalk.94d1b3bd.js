/*! 
 Build based on gin-vue-admin 
 Time : 1749628938000 */
import{a as t,r as e,b as i,z as n,o as a,d as o,e as s,k as d,X as r}from"./index.c733b50d.js";const l={style:{"text-align":"center"}},c={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},p={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},u={name:"Dingtalk",mounted(){this.loadThirdPartyScript()},methods:{loadThirdPartyScript(){const t=document.createElement("script");t.src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js",t.onload=()=>{this.doSomethingWithThirdPartyLibrary()},document.body.appendChild(t)}}},h=Object.assign(u,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(u){const h=t(),g=e(0),y=u;i();const m=async()=>{await(async()=>{const t={type:"dingtalk",data:{idpId:y.auth_id}},e=await r(t);if(200===e.status)return e.data.uniqKey})();const t=y.auth_info.dingtalkAppKey,e=window.location.host,i=`${window.location.protocol}//${e}`;setTimeout((()=>{window.DTFrameLogin({id:"self_defined_element",width:300,height:300},{redirect_uri:encodeURIComponent(i),client_id:t,scope:"openid",response_type:"code",state:y.auth_id,prompt:"consent"},(t=>{const{redirectUrl:e,authCode:i,state:n}=t;h.push({name:"Status",query:{code:i,state:n,auth_type:"dingtalk"},replace:!0})}),(t=>{t&&console.error("钉钉登录错误:",t)}))}),100)};return m(),n(y,(async(t,e)=>{g.value++,await m()})),(t,e)=>(a(),o("div",{key:g.value},[s("div",l,[s("span",c,[(a(),o("svg",p,e[0]||(e[0]=[s("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),e[1]||(e[1]=d(" 钉钉认证 "))])]),e[2]||(e[2]=s("div",{id:"self_defined_element",class:"self-defined-classname"},null,-1))]))}});export{h as default};
