/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{r as u,u as b,h as m,o as n,d as r,e as i,k as d,t as p,j as y,w as g,f as C,g as N,s as V,n as F,M as v,v as I}from"./index.d0594432.js";const z={style:{width:"100%",height:"100%",background:"#FFFFFF"}},B={style:{width:"442px",height:"175px","padding-left":"38.5%","padding-top":"21%","text-align":"center"}},j={style:{"font-size":"24px",display:"flex","justify-content":"center","align-items":"center"}},D={class:"icon",style:{"margin-right":"10px","font-size":"14px",width:"24px",height:"24px"},"aria-hidden":"true"},P={key:0,style:{"margin-top":"20px"}},S={style:{float:"left","margin-left":"34px"}},K={key:1,style:{"margin-top":"20px"}},M={name:"Appverify"},R=Object.assign(M,{setup(A){const l=u(),t=u({}),o=b();(async()=>{const a={user_id:o.query.user_id,idp_id:o.query.idp_id},e=await V(a);e.status===200&&(t.value=e.data.data,t.value?.notPhone||await c())})();const s=u(60);let _;const h=()=>{s.value=60,_=setInterval(()=>{s.value--,s.value===0&&x()},1e3)},x=()=>{clearInterval(_)},c=async()=>{const a={uniq_key:t.value.uniqKey,idp_id:o.query.idp_id},e=await F(a);e.status===200&&e.data.code!==-1?h():(v({showClose:!0,message:e.data.msg,type:"error"}),s.value=0)},w=async()=>{const a={uniq_key:t.value.uniqKey,auth_code:l.value,user_name:t.value.userName,idp_id:o.query.idp_id,redirect_uri:"app_redirect",grant_type:"implicit",client_id:"client_portal"},e=await I(a);e.status===200&&e.data.code!==-1?location.href=o.query.redirect_url:v({showClose:!0,message:e.data.msg,type:"error"})};return(a,e)=>{const k=m("el-input"),f=m("el-button");return n(),r("div",z,[i("div",B,[i("div",j,[(n(),r("svg",D,e[1]||(e[1]=[d("--> "),i("use",{"xlink:href":"#icon-shuoming2"},null,-1)]))),e[2]||(e[2]=d("--> \u8BE5\u5E94\u7528\u9700\u901A\u8FC7\u5B89\u5168\u8BA4\u8BC1\u540E\u624D\u53EF\u7EE7\u7EED\u8BBF\u95EE "))]),t.value?.notPhone===!1?(n(),r("div",P,[i("span",S,"\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001\u81F3\u60A8\u7684\u8D26\u53F7("+p(t.value?.userName)+")\u5173\u8054\u7684\u624B\u673A\uFF0C\u8BF7\u6CE8\u610F\u67E5\u6536",1),y(k,{modelValue:l.value,"onUpdate:modelValue":e[0]||(e[0]=q=>l.value=q),placeholder:"\u8BF7\u8F93\u5165\u77ED\u4FE1\u9A8C\u8BC1\u7801",style:{float:"left","margin-left":"34px","font-size":"12px","margin-top":"12px",width:"258px",height:"32px"},class:"input-with-select"},null,8,["modelValue"]),y(f,{style:{"border-radius":"4px","font-size":"12px",float:"left","margin-top":"12px",position:"relative","margin-left":"10px",width:"92px",height:"32px"},disabled:s.value>0,onClick:c},{default:g(()=>[d("\u91CD\u65B0\u53D1\u9001 "+p(s.value>0?`(${s.value}\u79D2)`:""),1)]),_:1},8,["disabled"])])):(n(),r("div",K,[i("span",null,"\u60A8\u7684\u8D26\u53F7("+p(t.value?.userName)+")\u672A\u5173\u8054\u624B\u673A\u53F7\u7801\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458",1)])),t.value?.notPhone===!1?(n(),C(f,{key:2,type:"primary",size:"large",style:{float:"left","margin-left":"34px",height:"44px","margin-top":"14px",width:"365px"},disabled:!l.value,onClick:w},{default:g(()=>e[3]||(e[3]=[d("\u786E \u5B9A ")])),_:1,__:[3]},8,["disabled"])):N("",!0)])])}}});export{R as default};
