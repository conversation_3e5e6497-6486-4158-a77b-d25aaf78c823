/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{_ as e,r as t,u as a,z as n,o as i,d as o,f as r,e as s,Y as c}from"./index.2a422357.js";const d=e(Object.assign({name:"Wechat"},{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(e){const d=t(0),l=e,u=a(),f=async()=>{var e,t,a,n;const i=window.location.host;let o=`${window.location.protocol}//${i}/#/status`;if(null==(e=u.query)?void 0:e.redirect){const e=(null==(t=u.query)?void 0:t.redirect.indexOf("?"))>-1?null==(n=u.query)?void 0:n.redirect.substring((null==(a=u.query)?void 0:a.redirect.indexOf("?"))+1):"";o=o+"?"+e}else if(u.query){const e=new URLSearchParams;for(const[t,a]of Object.entries(u.query))e.append(t,a);o=o+"?"+e.toString()}await(async()=>{const e={type:"qiyewx",data:{idpId:l.auth_id}},t=await c(e);if(200===t.status)return t.data.uniqKey})(),setTimeout((()=>{window.getQRCode({id:"qr_login",appid:l.auth_info.wxCorpId,agentid:l.auth_info.wxAgentId,redirect_uri:encodeURIComponent(o+"&auth_type=qiyewx"),state:l.auth_id,href:"",lang:"zh"});const e=document.querySelector("iframe");e.contentWindow.location.href!==e.src?console.log("iframe已重新加载"):console.log("iframe未重新加载")}),100)};return f(),n(l,(async(e,t)=>{d.value++,await f()})),(e,t)=>(i(),o("div",{key:d.value},[r('    <div style="text-align: center">'),r('      <span class="title">企业微信认证</span>'),r("    </div>"),t[0]||(t[0]=s("div",{id:"qr_login",slot:"content",class:"wechat-class"},null,-1))]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wechat.vue"]]);export{d as default};
