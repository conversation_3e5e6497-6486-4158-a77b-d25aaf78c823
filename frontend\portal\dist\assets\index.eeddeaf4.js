/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
import{_ as a,h as e,o as s,d as t,f as o,j as r,e as i,g as l}from"./index.5ebe5a72.js";import n from"./header.a08fee70.js";import u from"./menu.4158a0e5.js";import"./ASD.492c8837.js";const m={class:"layout-page"},c={class:"layout-wrap"},d={id:"layoutMain",class:"layout-main"},f=a(Object.assign({name:"Client"},{setup:a=>(a,f)=>{const p=e("router-view");return s(),t("div",m,[o("公共顶部菜单-"),r(n),i("div",c,[o("公共侧边栏菜单"),r(u),i("div",d,[o("主流程路由渲染点"),(s(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{f as default};
