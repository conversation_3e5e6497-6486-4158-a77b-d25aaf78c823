/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
!function(){function e(a){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(a)}function a(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,n)}return t}function t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach((function(a){n(e,a,r[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(r,a))}))}return e}function n(a,t,n){return(t=function(a){var t=function(a,t){if("object"!=e(a)||!a)return a;var n=a[Symbol.toPrimitive];if(void 0!==n){var r=n.call(a,t||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(a)}(a,"string");return"symbol"==e(t)?t:t+""}(t))in a?Object.defineProperty(a,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[t]=n,a}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,a,t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",i=t.toStringTag||"@@toStringTag";function p(t,n,r,i){var p=n&&n.prototype instanceof l?n:l,s=Object.create(p.prototype);return o(s,"_invoke",function(t,n,r){var o,i,p,l=0,s=r||[],u=!1,f={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(a,t){return o=a,i=0,p=e,f.n=t,c}};function d(t,n){for(i=t,p=n,a=0;!u&&l&&!r&&a<s.length;a++){var r,o=s[a],d=f.p,b=o[2];t>3?(r=b===n)&&(p=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=d&&((r=t<2&&d<o[1])?(i=0,f.v=n,f.n=o[1]):d<b&&(r=t<3||o[0]>n||n>b)&&(o[4]=t,o[5]=n,f.n=b,i=0))}if(r||t>1)return c;throw u=!0,n}return function(r,s,b){if(l>1)throw TypeError("Generator is already running");for(u&&1===s&&d(s,b),i=s,p=b;(a=i<2?e:p)||!u;){o||(i?i<3?(i>1&&(f.n=-1),d(i,p)):f.n=p:f.v=p);try{if(l=2,o){if(i||(r="next"),a=o[r]){if(!(a=a.call(o,p)))throw TypeError("iterator result is not an object");if(!a.done)return a;p=a.value,i<2&&(i=0)}else 1===i&&(a=o.return)&&a.call(o),i<2&&(p=TypeError("The iterator does not provide a '"+r+"' method"),i=1);o=e}else if((a=(u=f.n<0)?p:t.call(n,f))!==c)break}catch(a){o=e,i=1,p=a}finally{l=1}}return{value:a,done:u}}}(t,r,i),!0),s}var c={};function l(){}function s(){}function u(){}a=Object.getPrototypeOf;var f=[][n]?a(a([][n]())):(o(a={},n,(function(){return this})),a),d=u.prototype=l.prototype=Object.create(f);function b(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,o(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=u,o(d,"constructor",u),o(u,"constructor",s),s.displayName="GeneratorFunction",o(u,i,"GeneratorFunction"),o(d),o(d,i,"Generator"),o(d,n,(function(){return this})),o(d,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:p,m:b}})()}function o(e,a,t,n){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}o=function(e,a,t,n){if(a)r?r(e,a,{value:t,enumerable:!n,configurable:!n,writable:!n}):e[a]=t;else{var i=function(a,t){o(e,a,(function(e){return this._invoke(a,t,e)}))};i("next",0),i("throw",1),i("return",2)}},o(e,a,t,n)}function i(e,a,t,n,r,o,i){try{var p=e[o](i),c=p.value}catch(e){return void t(e)}p.done?a(c):Promise.resolve(c).then(n,r)}function p(e){return function(){var a=this,t=arguments;return new Promise((function(n,r){var o=e.apply(a,t);function p(e){i(o,n,r,p,c,"next",e)}function c(e){i(o,n,r,p,c,"throw",e)}p(void 0)}))}}System.register(["./index-legacy.60f18f5a.js"],(function(e,a){"use strict";var n,o,i,c,l,s,u,f,d,b,g,m,h,v,x,y,w,k,_,S,O,P,j,F,C,W=document.createElement("style");return W.textContent='@charset "UTF-8";.person[data-v-bf594aeb]{background:#FFFFFF;border-radius:4px;height:100vh;max-height:calc(100vh - 68px)}.person .base-header[data-v-bf594aeb]{justify-content:space-between;font-weight:Medium;color:#282a33;font-size:16px;line-height:22px;padding:0 15px;display:flex;align-items:center}.person .base-header .el-title[data-v-bf594aeb]{padding-top:19px}.person .base-header .el-search[data-v-bf594aeb]{padding-top:16px;display:flex;justify-content:space-between}.person .base-header .el-search-input[data-v-bf594aeb]{width:200px;height:28px;border-radius:4px;font-size:14px}.person .base-header .el-search-input>.el-input__wrapper[data-v-bf594aeb]{background:#f5f5f7}.person .base-header .el-search-btn[data-v-bf594aeb]{font-size:14px!important;margin-left:10px;min-height:28px;padding:0;width:28px;height:28px;background:#f5f5f7;border-radius:4px}.person .base-header .el-search-select[data-v-bf594aeb]{margin-left:10px;padding:3px 0 0;width:80px;height:20px!important}.person .base-header .el-search-select[data-v-bf594aeb] .el-input__wrapper{background-color:transparent!important;box-shadow:none!important;border:none!important;padding:0!important;line-height:1!important;height:20px!important}.person .base-header .el-search-select[data-v-bf594aeb] .el-input__suffix{padding:0!important;height:20px!important}.person .base-header .el-search-select[data-v-bf594aeb] .el-input__suffix-inner{margin-left:-28px}.person .base-header .el-search-select[data-v-bf594aeb] .el-input__inner{height:20px!important;line-height:20px!important}.person .base-header .search-input[data-v-bf594aeb]{width:300px}.person .base-header .search-input[data-v-bf594aeb] .el-input__wrapper{border-radius:4px;background-color:#f5f7fa}.person .base-header .search-input[data-v-bf594aeb] .el-input__wrapper.is-focus{background-color:#fff}.person .base-header .search-input[data-v-bf594aeb] .el-input__inner{height:32px;line-height:32px;font-size:14px}.person .base-header .search-input[data-v-bf594aeb] .el-input__inner::placeholder{color:#909399}.person .category-aside .category-menu[data-v-bf594aeb]{border-right:none}.person .category-aside .category-menu .el-menu-item[data-v-bf594aeb]{height:28px;line-height:28px;font-size:14px;color:#606266;position:relative;transition:all .3s;cursor:pointer;border-radius:4px;margin:4px 0 4px 20px}.person .category-aside .category-menu .el-menu-item[data-v-bf594aeb]:not(.is-active){background-color:transparent;color:#606266}.person .category-aside .category-menu .el-menu-item[data-v-bf594aeb]:hover:not(.is-active){background-color:#f0f7ff;color:#409eff}.person .category-aside .category-menu .el-menu-item[data-v-bf594aeb]:focus{outline:none;background-color:#f0f7ff}.person .category-aside .category-menu .el-menu-item[data-v-bf594aeb]:focus:not(.is-active){color:#409eff}.person .category-aside .category-menu .el-menu-item.is-active[data-v-bf594aeb]{background-color:#536ce6;color:#fff;font-weight:500}.person .category-aside .category-menu .el-menu-item[data-v-bf594aeb]:active{background-color:#dcedff}.person .app-main[data-v-bf594aeb]{padding:12px 20px;overflow-y:auto}.person .app-main .category-section[data-v-bf594aeb]{margin-bottom:24px}.person .app-main .category-section .category-title[data-v-bf594aeb]{font-size:16px;font-weight:700;margin-bottom:12px;margin-top:0;padding-bottom:6px;width:fit-content;min-width:100px;max-width:200px;line-height:1.2}.person .app-main .category-section .apps-container[data-v-bf594aeb]{display:grid;grid-template-columns:repeat(auto-fill,265px);gap:16px;padding:16px 0}.person .app-main .category-section .apps-container .app-card[data-v-bf594aeb]{width:257px;height:64px;margin-right:8px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px;position:relative;display:flex;justify-content:center}.person .app-main .category-section .apps-container .app-card[data-v-bf594aeb] .el-link{display:flex;flex-direction:column;align-items:center;justify-content:space-between;width:100%;height:100%;padding:0}.person .app-main .category-section .apps-container .app-card[data-v-bf594aeb] .el-link__inner{width:100%!important;height:100%!important}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-bf594aeb]{width:48px;height:48px;margin-left:13px;display:flex;align-items:center}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-bf594aeb] .el-avatar{width:48px;height:48px;color:#fff;font-size:16px;display:flex;align-items:center;justify-content:center;text-align:center}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-bf594aeb] .el-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.person .app-main .category-section .apps-container .app-card .app-info[data-v-bf594aeb]{width:100%;display:flex;flex-direction:column;justify-content:center}.person .app-main .category-section .apps-container .app-card .app-info .app-name[data-v-bf594aeb]{height:20px;width:56px;font-size:14px;font-family:PingFang SC,PingFang SC-Medium;font-weight:700;text-align:left;color:#282a33;line-height:20px;margin-left:12px;margin-top:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.person .app-main .category-section .apps-container .app-card .app-info .app-remark[data-v-bf594aeb]{margin-left:12px;width:168px;height:17px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#686e84;line-height:17px}.person .app-main .category-section .apps-container .app-card .status-badge[data-v-bf594aeb]{position:absolute;top:-6px;right:-6px;padding:1px 6px;border-radius:8px;font-size:11px;background-color:#d3c5a5;color:#000;font-weight:500;z-index:2}.person .app-main .category-section .apps-container .app-card[data-v-bf594aeb] .el-link:hover .icon-wrapper{transform:scale(1.05)}.person .app-main .category-section .apps-container .app-card.disabled[data-v-bf594aeb] .el-link{cursor:not-allowed}.person .app-main .category-section .apps-container .app-card.disabled[data-v-bf594aeb] .el-link:hover .icon-wrapper{transform:none}.person .app-main .category-section .apps-container .app-card.disabled .icon-wrapper[data-v-bf594aeb]{opacity:.6}.person .app-main .category-section .apps-container .app-card.disabled[data-v-bf594aeb] .el-avatar{filter:grayscale(100%)}.person .app-main .category-section[data-v-bf594aeb]:first-child{margin-top:0}.person .app-main .el-recent-access[data-v-bf594aeb]{margin-left:4px;margin-top:3px;width:56px;height:20px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#282a33;line-height:20px}.person .app-main .el-recent-data[data-v-bf594aeb]{margin-left:15px;height:20px}.person .app-main .el-recent-item[data-v-bf594aeb]{margin-top:2px;margin-right:6px;height:25px;padding:4px 0 4px 6px;background:#f5f6fe;border-radius:4px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;color:#536ce6}.person .app-main .el-recent-icon[data-v-bf594aeb]{width:8px;height:8px;margin:8px 6px 8px 5px}.person .app-main .el-recent-clear[data-v-bf594aeb]{opacity:.6;margin-top:6px;position:absolute;width:14px;height:14px;right:6px}@media screen and (max-width: 1439px){.apps-container[data-v-bf594aeb]{grid-template-columns:repeat(auto-fill,180px)}.apps-container .app-card[data-v-bf594aeb]{width:180px;height:200px}}@media screen and (max-width: 1023px){.apps-container[data-v-bf594aeb]{grid-template-columns:repeat(auto-fill,200px)}.apps-container .app-card[data-v-bf594aeb]{width:200px;height:220px}}@media screen and (max-width: 767px){.apps-container[data-v-bf594aeb]{grid-template-columns:repeat(2,1fr)}.apps-container .app-card[data-v-bf594aeb]{width:100%;height:auto;aspect-ratio:1/1.125}}.tooltip-content[data-v-bf594aeb]{width:200px;text-align:center}.web-link[data-v-bf594aeb]{color:#409eff;text-decoration:none;word-break:break-all}.el-select__popper[data-v-bf594aeb]{background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1);margin-left:-10px!important;right:15px;top:110px;max-width:88px}.el-select__popper .el-select-dropdown__item[data-v-bf594aeb]{width:72px;height:28px;border-radius:4px;margin-left:7px;margin-bottom:4px;padding:0 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;line-height:20px;display:flex;align-items:center;background:#f5f5f7!important}.el-select__popper .el-select-dropdown__item.selected[data-v-bf594aeb]{color:#fff;background:#536ce6!important}.text-center[data-v-bf594aeb]{text-align:center}.web-link[data-v-bf594aeb]{color:#409eff;text-decoration:none}.web-link[data-v-bf594aeb]:hover{text-decoration:underline}.el-message{white-space:pre-line!important;line-height:1.5!important;padding:12px 20px!important}\n',document.head.appendChild(W),{setters:[function(e){n=e.x,o=e.r,i=e.I,c=e.J,l=e.K,s=e.b,u=e.u,f=e.C,d=e.h,b=e.o,g=e.d,m=e.e,h=e.j,v=e.w,x=e._,y=e.F,w=e.i,k=e.f,_=e.k,S=e.t,O=e.H,P=e.g,j=e.N,F=e.O,C=e.M}],execute:function(){var a={class:"person"},W={class:"el-search"},z={class:"category-title"},T={class:"apps-container"},D={key:0,class:"status-badge"},E={class:"icon-wrapper"},U={class:"tooltip-content text-center"},I={key:0},R={key:1},A={class:"app-info"},N={class:"app-name"},G=Object.assign({name:"AppPage"},{setup:function(e){var x=o(""),G=o(null),M=o([]),V=o([]),J=o("1"),B=o(!1),L=o("standard"),q=i([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),H=o(null),X=o(!1),K=function(e){C({message:e,type:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",duration:arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3})},Q=function(){var e=p(r().m((function e(a){return r().w((function(e){for(;;)if(0===e.n)return e.a(2,new Promise((function(e,t){var n,o=!1,i=function(){var i=p(r().m((function i(){var p,c,l;return r().w((function(r){for(;;)switch(r.n){case 0:return r.p=0,r.n=1,new Promise((function(e,a){if(H.value&&H.value.readyState===WebSocket.OPEN)e(H.value);else{var t=new WebSocket("ws://localhost:50001");X.value=!0,t.onopen=function(){console.log("WebSocket Connected"),H.value=t,X.value=!1,e(t)},t.onmessage=function(e){var a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&K(a,"error")},t.onclose=function(){console.log("WebSocket Disconnected"),H.value=null,X.value=!1},t.onerror=function(e){console.error("WebSocket Error:",e),X.value=!1,a(e)},setTimeout((function(){X.value&&(X.value=!1,t.close(),a(new Error("连接超时")))}),5e3)}}));case 1:p=r.v,c={action:3,msg:a},n=setTimeout((function(){o||(p.close(),t(new Error("启动超时：未收到响应")))}),3e3),p.onmessage=function(a){o=!0,clearTimeout(n);var r=a.data;r.startsWith("Ok")?e():t(new Error(r))},p.send(JSON.stringify(c)),console.log("发送消息:",c),r.n=3;break;case 2:r.p=2,l=r.v,clearTimeout(n),t(l);case 3:return r.a(2)}}),i,null,[[0,2]])})));return function(){return i.apply(this,arguments)}}();i()})))}),e)})));return function(a){return e.apply(this,arguments)}}(),Y=function(){var e=p(r().m((function e(a){var t;return r().w((function(e){for(;;)switch(e.n){case 0:if(a.WebUrl&&!a.maint){e.n=1;break}return e.a(2);case 1:if(!a.WebUrl.toLowerCase().startsWith("cs:")){e.n=6;break}return t=a.WebUrl.substring(3),e.p=2,K("正在启动爱尔企业浏览器...","info"),e.n=3,Q(t);case 3:K("启动成功","success"),e.n=5;break;case 4:e.p=4,e.v,K("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3);case 5:e.n=7;break;case 6:window.open(a.WebUrl,"_blank");case 7:return e.a(2)}}),e,null,[[2,4]])})));return function(a){return e.apply(this,arguments)}}();c((function(){H.value&&(H.value.close(),H.value=null)}));var Z=function(e){for(var a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,n=0;n<e.length;n++)t+=e.charCodeAt(n);return a[t%a.length]},$=function(){B.value=!0},ee=function(e){G.value=parseInt(e),V.value=e?M.value.filter((function(a){return a.id===parseInt(e)})):M.value},ae=function(){if(x.value){var e=x.value.toLowerCase().trim();V.value=M.value.map((function(a){return t(t({},a),{},{apps:a.apps.filter((function(a){return a.app_name.toLowerCase().includes(e)}))})})).filter((function(e){return e.apps.length>0}))}else V.value=M.value},te=function(){var e=p(r().m((function e(){var a,t,o,i;return r().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,n({url:"/console/v1/application/getuserapp",method:"get"});case 1:a=e.v,t=a.data,console.log("API返回数据:",t),0===t.code&&t.data&&(o=t.data.map((function(e,a){return{id:a+1,name:e.category,apps:e.apps.map((function(e){return{id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl}}))}})),console.log("格式化后的数据:",o),M.value=o,V.value=o,o.length>0&&(G.value=o[0].id,J.value=o[0].id.toString())),e.n=3;break;case 2:e.p=2,i=e.v,console.error("API调用出错:",i);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();l((function(){te()}));var ne=s(),re=u().query,oe=null;try{if(!f.isClient()){var ie=new XMLHttpRequest;ie.open("GET",document.location,!1),ie.send(null),oe=ie.getResponseHeader("X-Corp-ID")}}catch(be){console.warn("无法获取 X-Corp-ID header，使用默认值:",be)}var pe={action:0,msg:{token:ne.token.accessToken,refreshToken:ne.token.refreshToken,realm:oe||"default"},platform:document.location.hostname},ce=re.wp||50001,le=o({}),se=o("ws://127.0.0.1:".concat(ce)),ue=navigator.platform;0!==ue.indexOf("Mac")&&"MacIntel"!==ue||(se.value="wss://127.0.0.1:".concat(ce));var fe=function(e){console.log(e,"0"),le.value.send(e)},de=function(){console.log("socket断开链接"),le.value.close()};return console.log("asecagent://?web=".concat(JSON.stringify(pe))),le.value=new WebSocket(se.value),le.value.onopen=function(){console.log("socket连接成功"),fe(JSON.stringify(pe))},le.value.onmessage=function(e){console.log(e),de()},le.value.onerror=function(){console.log("socket连接错误:"+se.value),window.location.href="asecagent://?web=".concat(JSON.stringify(pe))},function(e,t){var n=d("base-input"),r=d("base-button"),o=d("base-option"),i=d("base-select"),p=d("base-header"),c=d("el-menu-item"),l=d("el-menu"),s=d("base-aside"),u=d("base-avatar"),f=d("el-tooltip"),C=d("el-link"),G=d("base-main"),H=d("base-container");return b(),g("div",null,[m("div",a,[h(p,null,{default:v((function(){return[t[3]||(t[3]=m("span",{class:"el-title"},"我的应用",-1)),m("span",W,[h(n,{class:"el-search-input",modelValue:x.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return x.value=e}),placeholder:"搜索应用","prefix-icon":"Search",onInput:ae,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),h(r,{class:"el-search-btn",icon:"Refresh",size:"small"}),h(i,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:L.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return L.value=e}),placeholder:"Select",size:"small"},{default:v((function(){return[(b(!0),g(y,null,w(q,(function(e){return b(),k(o,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])]})),_:1,__:[3]}),h(H,null,{default:v((function(){return[h(s,{width:"96px",class:"category-aside"},{default:v((function(){return[h(l,{class:"category-menu",onSelect:ee,"default-active":J.value},{default:v((function(){return[h(c,{index:"0",onClick:t[2]||(t[2]=function(e){return ee(null)})},{default:v((function(){return t[4]||(t[4]=[_(" 全部 ")])})),_:1,__:[4]}),(b(!0),g(y,null,w(M.value,(function(e){return b(),k(c,{key:e.id,index:e.id.toString()},{default:v((function(){return[_(S(e.name),1)]})),_:2},1032,["index"])})),128))]})),_:1},8,["default-active"])]})),_:1}),h(G,{class:"app-main"},{default:v((function(){return[(b(!0),g(y,null,w(V.value,(function(e){return b(),g("div",{key:e.id,class:"category-section"},[m("h3",z,S(e.name),1),m("div",T,[(b(!0),g(y,null,w(e.apps,(function(e){return b(),g("div",{key:e.id,class:O(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(b(),g("div",D," 维护中 ")):P("",!0),h(C,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:j((function(a){return Y(e)}),["prevent"])},{default:v((function(){return[m("div",E,[h(f,{effect:"light",placement:"bottom"},{content:v((function(){return[m("div",U,[e.WebUrl?(b(),g("span",I,S(e.WebUrl),1)):(b(),g("span",R,"暂无访问地址"))])]})),default:v((function(){return[h(u,{shape:"square",size:48,src:e.icon,onError:$,style:F(!e.icon||B.value?"background-color: ".concat(Z(e.app_name)," !important"):"")},{default:v((function(){return[_(S(!e.icon||B.value?e.app_name.slice(0,2):""),1)]})),_:2},1032,["src","style"])]})),_:2},1024)]),m("div",A,[m("div",N,S(e.app_name),1),t[5]||(t[5]=m("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])]})),_:2},1032,["disabled","onClick"])],2)})),128))])])})),128))]})),_:1})]})),_:1})])])}}});e("default",x(G,[["__scopeId","data-v-bf594aeb"]]))}}}))}();
