/*! 
 Build based on gin-vue-admin 
 Time : 1749623364000 */
import{u as e,a as r,b as a,r as t,G as s,p as i,L as c,M as u}from"./index.49a4551d.js";const l=Object.assign({name:"WxOAuthCallback"},{setup(l){const n=e(),o=r(),y=a(),{code:d,state:p,auth_type:h,redirect_url:m}=n.query,_=t(Array.isArray(p)?p[0]:p),b=t("");return s((async()=>{const e=c.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:_.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(e,"qiyewx_oauth",_.value)?await o.push({name:"verify",query:{redirect_url:m}}):u.error("登录失败，请重试")}catch(r){console.error("登录过程出错:",r),u.error("登录过程出错，请重试")}finally{e.close()}})),i("userName",b),(e,r)=>null}});export{l as default};
