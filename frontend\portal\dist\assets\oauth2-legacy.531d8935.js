/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
!function(){function t(t){return function(t){if(Array.isArray(t))return e(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return e(t,n);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(n,o,i,a){var c=o&&o.prototype instanceof s?o:s,f=Object.create(c.prototype);return r(f,"_invoke",function(n,r,o){var i,a,c,s=0,f=o||[],l=!1,d={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,n){return i=e,a=0,c=t,d.n=n,u}};function p(n,r){for(a=n,c=r,e=0;!l&&s&&!o&&e<f.length;e++){var o,i=f[e],p=d.p,h=i[2];n>3?(o=h===r)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=p&&((o=n<2&&p<i[1])?(a=0,d.v=r,d.n=i[1]):p<h&&(o=n<3||i[0]>r||r>h)&&(i[4]=n,i[5]=r,d.n=h,a=0))}if(o||n>1)return u;throw l=!0,r}return function(o,f,h){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&p(f,h),a=f,c=h;(e=a<2?t:c)||!l;){i||(a?a<3?(a>1&&(d.n=-1),p(a,c)):d.n=c:d.v=c);try{if(s=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(l=d.n<0)?c:n.call(r,d))!==u)break}catch(e){i=t,a=1,c=e}finally{s=1}}return{value:e,done:l}}}(n,i,a),!0),f}var u={};function s(){}function f(){}function l(){}e=Object.getPrototypeOf;var d=[][i]?e(e([][i]())):(r(e={},i,(function(){return this})),e),p=l.prototype=s.prototype=Object.create(d);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,r(t,a,"GeneratorFunction")),t.prototype=Object.create(p),t}return f.prototype=l,r(p,"constructor",l),r(l,"constructor",f),f.displayName="GeneratorFunction",r(l,a,"GeneratorFunction"),r(p),r(p,a,"Generator"),r(p,i,(function(){return this})),r(p,"toString",(function(){return"[object Generator]"})),(n=function(){return{w:c,m:h}})()}function r(t,e,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,e,n,o){if(e)i?i(t,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):t[e]=n;else{var a=function(e,n){r(t,e,(function(t){return this._invoke(e,n,t)}))};a("next",0),a("throw",1),a("return",2)}},r(t,e,n,o)}function o(t,e,n,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(r,o)}function i(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var a=t.apply(e,n);function c(t){o(a,r,i,c,u,"next",t)}function u(t){o(a,r,i,c,u,"throw",t)}c(void 0)}))}}System.register(["./index-legacy.8748bb61.js"],(function(e,r){"use strict";var o,a,c,u,s,f,l,d,p=document.createElement("style");return p.textContent=".sso-warpper{&[data-v-5a770c39]{padding:30px 60px 0;overflow:visible;position:relative}.sso-img[data-v-5a770c39] {width: 120px; height: 120px; display: block; margin: 0 auto;} .sso-iframe[data-v-5a770c39] {margin-left: -60px; height: 300px; transform: translateZ(0); backface-visibility: hidden;} .login_submit_button[data-v-5a770c39] {margin-bottom: 30px;}}.sso-iframe{height:300px}\n",document.head.appendChild(p),{setters:[function(t){o=t._,a=t.h,c=t.o,u=t.d,s=t.g,f=t.w,l=t.k,d=t.u}],execute:function(){var r={class:"sso-warpper"},p=["src"];function h(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:64,e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",n="",r=0;r<t;r++)n+=e.charAt(Math.floor(66*Math.random()));return n}function v(t){return m.apply(this,arguments)}function m(){return(m=i(n().m((function e(r){var o,i,a;return n().w((function(e){for(;;)switch(e.n){case 0:return o=new TextEncoder,i=o.encode(r),e.n=1,window.crypto.subtle.digest("SHA-256",i);case 1:return a=e.v,e.a(2,btoa(String.fromCharCode.apply(String,t(new Uint8Array(a)))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""))}}),e)})))).apply(this,arguments)}var y={props:{auth_id:{type:String,default:function(){return""}},auth_info:{type:Object,default:function(){return{}}}},data:function(){return{iframeSrc:"",isListen:!1,isThirdBack:!1,isAutoLogin:!1,isListenShowApp:!1,route:d(),loading:!1}},computed:{isForceBrowser:function(){return"cas"===this.auth_info.authType?1===parseInt(this.auth_info.casOpenType):1===parseInt(this.auth_info.oauth2OpenType)}},watch:{auth_id:{handler:function(t){this.init()},deep:!0,immediate:!0}},mounted:function(){},destroyed:function(){this.unListenGoBack(),this.clearLoading(),this.isListen&&this.removeEvent(window,"message",this.listenHandle)},methods:{init:function(){this.isForceBrowser||this.clickSubmit()},clickSubmit:function(){var t=this;return i(n().m((function e(){var r,o;return n().w((function(e){for(;;)switch(e.n){case 0:return r=h(),sessionStorage.setItem("oauth2_code_verifier",r),e.n=1,v(r);case 1:o=e.v,t.submit({code_challenge:encodeURIComponent(o),code_challenge_method:"S256"});case 2:return e.a(2)}}),e)})))()},submit:function(t){var e=this;return i(n().m((function r(){var o,i,a,c,u,s;return n().w((function(n){for(;;)switch(n.n){case 0:if(o="/auth/login/v1/callback/"+e.auth_id,t){for(c in a=[],t)a.push(c+"="+encodeURIComponent(t[c]));o+="?"+a.join("&"),null!==(i=e.route.query)&&void 0!==i&&i.redirect&&(s=null===(u=e.route.query)||void 0===u?void 0:u.redirect,o+="&redirect=/"+encodeURIComponent(s))}if(!e.isForceBrowser){n.n=1;break}window.location.href=o,n.n=3;break;case 1:if(!e.isListen){n.n=2;break}return o.includes("code=")||o.includes("token=")||o.includes("auth_success=true")?window.location.href=o:e.iframeSrc=o,n.a(2);case 2:e.iframeSrc=o,console.log("iframe初始地址",e.iframeSrc),e.isListen=!0,e.addEvent(window,"message",e.listenHandle);case 3:return n.a(2)}}),r)})))()},listenHandle:function(t){var e=this;return i(n().m((function r(){var o;return n().w((function(n){for(;;)switch(n.n){case 0:if(console.log("sso触发监听"),o=t.data.event,!e.isThirdAppWakeup(o)){n.n=1;break}return e.wakeupApp(t),n.a(2);case 1:t.data&&e.submit(t.data);case 2:return n.a(2)}}),r)})))()},addEvent:function(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent&&t.attachEvent("on"+e,(function(){n.call(t,window.event)}))},removeEvent:function(t,e,n){t.removeEventListener?t.removeEventListener(e,n):t.detachEvent&&t.detachEvent("on"+e,n)},isThirdAppWakeup:function(t){return"wakeup-app"===t},wakeupApp:function(t){var e=t.data.params.url;e&&(window.location.href=e)},clearLoading:function(){this.loading&&(this.loading.clear(),this.loading=!1)}}},b=Object.assign(y,{__name:"oauth2",setup:function(t){return function(t,e){var n=a("base-button");return c(),u("div",r,[t.isForceBrowser?(c(),s(n,{key:0,type:"primary",size:"large",class:"login_submit_button",onClick:t.clickSubmit},{default:f((function(){return e[0]||(e[0]=[l("授权登录")])})),_:1,__:[0]},8,["onClick"])):(c(),u("iframe",{key:1,src:t.iframeSrc,frameborder:"0",class:"sso-iframe"},null,8,p))])}}});e("default",o(b,[["__scopeId","data-v-5a770c39"],["__file","D:/asec-platform/frontend/portal/src/view/login/oauth2/oauth2.vue"]]))}}}))}();
