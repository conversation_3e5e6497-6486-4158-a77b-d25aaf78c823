/*! 
 Build based on gin-vue-admin 
 Time : 1749628938000 */
import a from"./header.9a6f2ced.js";import s from"./menu.a25e96af.js";import{h as e,o as t,d as o,j as r,e as i,f as l}from"./index.c733b50d.js";import"./ASD.492c8837.js";const u={class:"layout-page"},d={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},n=Object.assign({name:"Client"},{setup:n=>(n,c)=>{const f=e("router-view");return t(),o("div",u,[r(a),i("div",d,[r(s),i("div",m,[(t(),l(f,{key:n.$route.fullPath}))])])])}});export{n as default};
