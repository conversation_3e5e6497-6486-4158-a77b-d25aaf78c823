/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as F,J as B,r as n,z as C,h as a,o,d as _,e as h,t as v,j as s,w as c,f as l,C as x,g as p}from"./index.d0594432.js";const T={key:0,style:{height:"34px","margin-top":"6px","margin-bottom":"6px",border:"4px","line-height":"34px","margin-left":"14px",background:"#2F3C4B","padding-left":"35px","margin-right":"29px"}},j={style:{"font-size":"12px",color:"#FFFFFF",opacity:"1"}},z={key:1,class:"gva-menu-item"},V={class:"gva-menu-item-title"},w={name:"MenuItem",setup(){}},N=Object.assign(w,{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){B(y=>({"11d2448d":r.value,"4d464d2d":m.value,cbe5a220:d.value,"11f2e7cc":i.value}));const t=e,r=n(t.theme.activeBackground),i=n(t.theme.activeText),m=n(t.theme.normalText),g=n(t.theme.hoverBackground),d=n(t.theme.hoverText);return C(()=>t.theme,()=>{r.value=t.theme.activeBackground,i.value=t.theme.activeText,m.value=t.theme.normalText,g.value=t.theme.hoverBackground,d.value=t.theme.hoverText}),(y,O)=>{const k=a("Plus"),u=a("el-icon"),f=a("component"),I=a("el-tooltip"),b=a("el-menu-item");return e.routerInfo.meta.isDisabled?(o(),_("div",T,[h("span",j,v(e.routerInfo.meta.title),1),s(u,{color:"#FFFFFF",size:"12px",style:{"padding-left":"17px"}},{default:c(()=>[s(k)]),_:1})])):(o(),l(b,{key:1,index:e.routerInfo.name},{default:c(()=>[e.isCollapse?(o(),l(I,{key:0,class:"box-item",effect:"light",content:e.routerInfo.meta.title,placement:"right"},{default:c(()=>[e.routerInfo.meta.icon?(o(),l(u,{key:0},{default:c(()=>[s(f,{class:x(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])]),_:1})):p("",!0)]),_:1},8,["content"])):(o(),_("div",z,[e.routerInfo.meta.icon?(o(),l(u,{key:0},{default:c(()=>[s(f,{class:x(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])]),_:1})):p("",!0),h("span",V,v(e.routerInfo.meta.title),1)]))]),_:1},8,["index"]))}}}),M=F(N,[["__scopeId","data-v-b7c2f96b"]]);export{M as default};
