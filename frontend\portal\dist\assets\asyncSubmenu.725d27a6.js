/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
import{_ as e,O as t,r as a,z as n,h as o,o as s,g as u,w as r,d as l,I as f,f as i,e as c,t as m,F as d,Q as v}from"./index.df61e453.js";const p={key:0,class:"gva-subMenu"},x=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"1f4df5c8-normalText":b.value,"1f4df5c8-activeText":I.value})));const x=e,y=a(x.theme.activeBackground),I=a(x.theme.activeText),b=a(x.theme.normalText);return n((()=>x.theme),(()=>{y.value=x.theme.activeBackground,I.value=x.theme.activeText,b.value=x.theme.normalText})),(t,a)=>{const n=o("base-sub-menu");return s(),u(n,{ref:"subMenu",index:e.routerInfo.name},{title:r((()=>[e.isCollapse?(s(),l(d,{key:1},[e.routerInfo.meta.icon?(s(),l("i",{key:0,class:f(["iconfont",e.routerInfo.meta.icon])},null,2)):i("v-if",!0),c("span",null,m(e.routerInfo.meta.title),1)],64)):(s(),l("div",p,[e.routerInfo.meta.icon?(s(),l("i",{key:0,class:f(["iconfont",e.routerInfo.meta.icon])},null,2)):i("v-if",!0),c("span",null,m(e.routerInfo.meta.title),1)]))])),default:r((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-1f4df5c8"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/asyncSubmenu.vue"]]);export{x as default};
