<template>
  <div v-if="routerInfo.meta.isDisabled"
       style="
  height: 34px;
  margin-top: 6px;
  margin-bottom: 6px;
  border: 4px;
  line-height: 34px;
  margin-left: 14px;
  background: #2F3C4B;
  padding-left: 35px;
  margin-right: 29px;
"
  >
    <span style="font-size: 12px;color: #FFFFFF;opacity: 1">{{ routerInfo.meta.title }}</span>
    <base-icon color="#FFFFFF" size="12px" style="padding-left: 17px" name="plus" />
  </div>
  <base-menu-item v-else :index="routerInfo.name" :class="isCollapse ? 'base-sam-menu-item' : 'base-gva-menu-item'">
    <template v-if="isCollapse">
      <base-tooltip
        class="sam-menu-item"
        effect="light"
        :content="routerInfo.meta.title"
        placement="right"
      >
        <svg v-if="routerInfo.meta.icon" class="icon iconfont iconfont-active" aria-hidden="true"><use :xlink:href="`#${routerInfo.meta.icon}`" /></svg>
      </base-tooltip>
    </template>
    <template v-else>
      <div class="gva-menu-item">
        <svg v-if="routerInfo.meta.icon" class="icon iconfont" aria-hidden="true"><use :xlink:href="`#${routerInfo.meta.icon}`" /></svg>
        <span class="gva-menu-item-title">{{ routerInfo.meta.title }}</span>
      </div>
    </template>
  </base-menu-item>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'MenuItem',
  setup() {
  },
}
</script>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  routerInfo: {
    default: function() {
      return null
    },
    type: Object,
  },
  isCollapse: {
    default: function() {
      return false
    },
    type: Boolean,
  },
  theme: {
    default: function() {
      return {}
    },
    type: Object,
  },
})

const activeBackground = ref(props.theme.activeBackground)
const activeText = ref(props.theme.activeText)
const normalText = ref(props.theme.normalText)
const hoverBackground = ref(props.theme.hoverBackground)
const hoverText = ref(props.theme.hoverText)

watch(() => props.theme, () => {
  activeBackground.value = props.theme.activeBackground
  activeText.value = props.theme.activeText
  normalText.value = props.theme.normalText
  hoverBackground.value = props.theme.hoverBackground
  hoverText.value = props.theme.hoverText
})

</script>

<style lang="scss" scoped>

.gva-menu-item {
  width: 100%;
  flex: 1;
  height: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: rgba(255, 255, 255, 0.674509803921569);

  .gva-menu-item-title {
    flex: 1;
    font-size: 14px;
    height: 40px;
    //margin-left: 30px;
    //color: rgba(255, 255, 255, 0.674509803921569);
  }
}

.gva-menu-item .iconfont {
  height: 20px;
  width: 20px;
  padding-right: 9px;
  margin-left: 10px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.sam-menu-item .iconfont {
  height: 20px;
  width: 20px;
  margin-left: 10px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.base-sam-menu-item {
  background: none !important;
}

.base-menu-item--active {
  .iconfont-active {
    color: #526ce6;
  }
}

:deep(.base-menu--collapse) {
  :deep(.base-menu-item--active) {
    color: v-bind(activeBackground);
    opacity: 100%;
  }
}

:deep(.base-menu-item) {
  color: v-bind(normalText);
  font-size: 14px;
  height: 44px;
  line-height: 40px;
  margin: 6px 0px 6px 0px;
  color: v-bind(hoverText);
}

:deep(.base-menu-item--active) {
  border-radius: 4px;
  opacity: 100%;

  .gva-menu-item {
    background: #536ce6 !important;
    border-radius: 4px;
    height: 44px;

    i {
      opacity: 100%;
      color: v-bind(activeText);
    }

    span {
      opacity: 100% !important;
      color: v-bind(activeText);
    }
  }
}

:deep(.base-menu-item:hover) {
  background: #3C4857;
  border-radius: 4px;
  .gva-menu-item {
    height: 44px;
    background: none !important;
    //background: v-bind(hoverBackground);
    border-radius: 4px;
    //box-shadow: 0 0 2px 1px v-bind(hoverBackground);
    color: v-bind(hoverText);
  }
}
:deep(.base-menu-item--active:hover) {
  .gva-menu-item {
    background: #536ce6 !important;
  }
}
</style>
<style>
:deep(.base-sub-menu__title) {
  height: 40px;
  line-height: 40px;
}

:deep(.base-sub-menu__title .gva-subMenu) {
  font-size: 14px;
  height: 40px !important;
}

:deep(.base-sub-menu__title .gva-subMenu .iconfont) {
  margin-right: 13px;
  color: rgba(255, 255, 255, 0.674509803921569);
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
