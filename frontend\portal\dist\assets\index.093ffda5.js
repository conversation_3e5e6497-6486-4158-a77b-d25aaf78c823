/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as h,r as m,b as C,h as n,o as i,d as x,j as l,w as c,e as t,m as p,f as v,g}from"./index.d0594432.js";const S={class:"setting_body"},N={class:"setting_card"},V={class:"setting_content"},j={class:"theme-box"},B={class:"item-top"},M={class:"item-top"},z={name:"Setting"},D=Object.assign(z,{setup(q){const s=m(!1),f=m("rtl"),o=C(),k=()=>{s.value=!1},b=()=>{s.value=!0},r=a=>{if(a===null){o.changeSideMode("dark");return}o.changeSideMode(a)};return(a,e)=>{const y=n("base-button"),_=n("check"),u=n("el-icon"),w=n("el-drawer");return i(),x("div",null,[l(y,{type:"primary",class:"drawer-container",icon:"setting",onClick:b}),l(w,{modelValue:s.value,"onUpdate:modelValue":e[2]||(e[2]=d=>s.value=d),title:"\u7CFB\u7EDF\u914D\u7F6E",direction:f.value,"before-close":k},{default:c(()=>[t("div",S,[t("div",N,[t("div",V,[t("div",j,[t("div",{class:"item",onClick:e[0]||(e[0]=d=>r("light"))},[t("div",B,[p(o).mode==="light"?(i(),v(u,{key:0,class:"check"},{default:c(()=>[l(_)]),_:1})):g("",!0),e[3]||(e[3]=t("img",{src:"https://gw.alipayobjects.com/zos/antfincdn/NQ%24zoisaD2/jpRkZQMyYRryryPNtyIC.svg"},null,-1))]),e[4]||(e[4]=t("p",null," \u7B80\u7EA6\u767D ",-1))]),t("div",{class:"item",onClick:e[1]||(e[1]=d=>r("dark"))},[t("div",M,[p(o).mode==="dark"?(i(),v(u,{key:0,class:"check"},{default:c(()=>[l(_)]),_:1})):g("",!0),e[5]||(e[5]=t("img",{src:"https://gw.alipayobjects.com/zos/antfincdn/XwFOFbLkSM/LCkqqYNmvBEbokSDscrm.svg"},null,-1))]),e[6]||(e[6]=t("p",null," \u5546\u52A1\u9ED1 ",-1))])])])])])]),_:1},8,["modelValue","direction"])])}}}),F=h(D,[["__scopeId","data-v-f5d2d3d7"]]);export{F as default};
