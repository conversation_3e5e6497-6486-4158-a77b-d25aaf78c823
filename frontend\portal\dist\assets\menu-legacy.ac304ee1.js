/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}System.register(["./index-legacy.e795fa57.js"],(function(e,t){"use strict";var r,a,i,o,u,c,d,m,f=document.createElement("style");return f.textContent='@charset "UTF-8";.layout-aside[data-v-aa2fe5d8]{width:56px;height:100%;background:#F5F5F7;overflow:auto;z-index:10}.layout-aside .u-offlineTips[data-v-aa2fe5d8]{width:100%;padding:10px;background:#fceded;display:flex;justify-content:center}.layout-aside .u-offlineTips .off-tip-content[data-v-aa2fe5d8]{display:flex;line-height:20px;font-size:14px;color:#e65353}.layout-aside .u-offlineTips .off-tip-content i[data-v-aa2fe5d8]{padding-right:10px;font-size:14px}.layout-aside .menu-wrapper[data-v-aa2fe5d8]{padding-bottom:60px;margin:0}.layout-aside .menu-wrapper .menu-item[data-v-aa2fe5d8]{height:65px;font-size:13px;color:#b3b6c1;font-weight:400;display:flex;flex-direction:column;align-items:center;justify-content:center}.layout-aside .menu-wrapper .menu-item .menu-item-title[data-v-aa2fe5d8]{height:17px;font-size:12px;font-family:PingFang SC,PingFang SC-Medium;font-weight:Medium;color:#686e84}.layout-aside .menu-wrapper .menu-item .menu-item-icon[data-v-aa2fe5d8]{height:18px;width:18px;margin-bottom:6px;fill:currentColor}.layout-aside .menu-wrapper .menu-item[data-v-aa2fe5d8]:hover{background:#EBEBED;color:#536ce6;border-radius:4px;cursor:pointer}.layout-aside .menu-wrapper .menu-item:hover .iconfont[data-v-aa2fe5d8]{color:#536ce6}.layout-aside .menu-wrapper .active-menu-item[data-v-aa2fe5d8]{border-radius:4px;color:#fff}.layout-aside .menu-wrapper .active-menu-item .iconfont[data-v-aa2fe5d8]{color:#fff}.layout-aside .menu-wrapper .active-menu-item .menu-item-title[data-v-aa2fe5d8]{color:#536ce6}.layout-aside .menu-wrapper .active-menu-item[data-v-aa2fe5d8]:hover{color:#fff;border-radius:4px}.layout-aside .menu-wrapper .active-menu-item:hover .iconfont[data-v-aa2fe5d8]{color:#fff}.layout-aside .version-wrapper[data-v-aa2fe5d8]{position:fixed;bottom:1px;left:1px;width:200px;background:#F5F5F7;font-size:12px;line-height:33px;text-align:center;color:#b3b6c1;z-index:11}\n',document.head.appendChild(f),{setters:[function(e){r=e._,a=e.o,i=e.d,o=e.e,u=e.F,c=e.i,d=e.E,m=e.t}],execute:function(){var t=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],f={name:"ClientMenu",data:function(){return{currentRouteCode:"101"}},computed:{computedMenu:function(){return this.computedMenuFun()}},mounted:function(){this.$router.push({path:"/client/main",query:[]})},watch:{$route:{handler:function(e,t){if(logger.log("路由变化",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun:function(){var e=[];return t&&t.forEach((function(t){if(t.meta&&t.meta.menu){var n=t.meta.menu,r=n.name,a=n.icon,i=n.uiId,o={name:r,icon:a,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:i};e.push(o)}})),e},changeMenu:function(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=n(n({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),{menuClick:!0});logger.log(e,r),this.$router.push({path:e,query:r}),this.currentRouteCode=this.cutOut(t)},routerInterceptor:function(e){var t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:function(e){return e&&e.length?e.substr(0,3):e}}},l={class:"layout-aside"},p={class:"menu-wrapper"},s=["onClick"],h={class:"icon menu-item-icon","aria-hidden":"true"},y=["xlink:href"],v={class:"menu-item-title"};e("default",r(f,[["render",function(e,t,n,r,f,g){return a(),i("div",l,[o("ul",p,[(a(!0),i(u,null,c(g.computedMenu,(function(e){return a(),i("li",{key:e.code,class:d(["menu-item",g.cutOut(e.code)===f.currentRouteCode?"active-menu-item":""]),onClick:function(t){return g.changeMenu(e.url,e.params,e.code)}},[(a(),i("svg",h,[o("use",{"xlink:href":"#"+e.icon+(g.cutOut(e.code)===f.currentRouteCode?"-active":"")},null,8,y)])),o("div",v,m(e.name),1)],10,s)})),128))])])}],["__scopeId","data-v-aa2fe5d8"],["__file","D:/asec-platform/frontend/portal/src/view/client/menu.vue"]]))}}}))}();
