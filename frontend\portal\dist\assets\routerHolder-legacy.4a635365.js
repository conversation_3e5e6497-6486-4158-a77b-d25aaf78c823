/*! 
 Build based on gin-vue-admin 
 Time : 1749610601000 */
System.register(["./index-legacy.0e039e9c.js"],(function(e,n){"use strict";var t,u,r,i,o,c,l,a,f,d,s;return{setters:[function(e){t=e.S,u=e.h,r=e.o,i=e.d,o=e.j,c=e.w,l=e.T,a=e.f,f=e.W,d=e.m,s=e.A}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[o(v,null,{default:c((function(e){var t=e.Component;return[o(l,{mode:"out-in",name:"el-fade-in-linear"},{default:c((function(){return[(r(),a(f,{include:d(n).keepAliveRouters},[(r(),a(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
