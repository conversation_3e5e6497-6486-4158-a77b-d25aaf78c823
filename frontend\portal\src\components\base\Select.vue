<template>
  <div class="base-select" :class="{ 'is-disabled': disabled }">
    <div 
      class="base-select__input"
      :class="{ 'is-focus': visible }"
      @click="toggleDropdown"
    >
      <span v-if="selectedLabel" class="base-select__selected">{{ selectedLabel }}</span>
      <span v-else class="base-select__placeholder">{{ placeholder }}</span>
      <base-icon v-if="visible" name="shouqi" size="8px" />
      <base-icon v-else name="zhankai" size="8px" />
    </div>
    <div v-show="visible" class="base-select__dropdown">
      <div class="base-select__options">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseSelect',
  props: {
    modelValue: {
      type: [String, Number, Boolean],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['large', 'default', 'small'].includes(value)
    }
  },
  emits: ['update:modelValue', 'change'],
  data() {
    return {
      visible: false,
      selectedLabel: ''
    }
  },
  mounted() {
    this.updateSelectedLabel()
    document.addEventListener('click', this.handleDocumentClick)
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleDocumentClick)
  },
  watch: {
    modelValue() {
      this.updateSelectedLabel()
    }
  },
  methods: {
    toggleDropdown() {
      if (this.disabled) return
      this.visible = !this.visible
    },
    handleDocumentClick(e) {
      if (!this.$el.contains(e.target)) {
        this.visible = false
      }
    },
    handleOptionClick(value, label) {
      this.$emit('update:modelValue', value)
      this.$emit('change', value)
      this.selectedLabel = label
      this.visible = false
    },
    updateSelectedLabel() {
      // 通过子组件更新选中的标签
      this.$nextTick(() => {
        const options = this.$el?.querySelectorAll('.base-option')
        if (options) {
          options.forEach(option => {
            if (option.__vue__?.value === this.modelValue) {
              this.selectedLabel = option.__vue__?.label || option.textContent
            }
          })
        }
      })
    }
  },
  provide() {
    return {
      select: this
    }
  }
}
</script>

<style scoped>
.base-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.base-select__input {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: border-color 0.2s;
}

.base-select__input:hover {
  border-color: #c0c4cc;
}

.base-select__input.is-focus {
  border-color: #409eff;
}

.base-select.is-disabled .base-select__input {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.base-select__selected {
  color: #606266;
}

.base-select__placeholder {
  color: #c0c4cc;
}

.base-select__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 4px;
}

.base-select__options {
  max-height: 200px;
  overflow-y: auto;
}
</style>
