/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var r,i,n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function h(t,n,s,o){var h=n&&n.prototype instanceof u?n:u,c=Object.create(h.prototype);return e(c,"_invoke",function(t,e,n){var s,o,h,u=0,c=n||[],f=!1,l={p:0,n:0,v:r,a:p,f:p.bind(r,4),d:function(t,e){return s=t,o=0,h=r,l.n=e,a}};function p(t,e){for(o=t,h=e,i=0;!f&&u&&!n&&i<c.length;i++){var n,s=c[i],p=l.p,g=s[2];t>3?(n=g===e)&&(h=s[(o=s[4])?5:(o=3,3)],s[4]=s[5]=r):s[0]<=p&&((n=t<2&&p<s[1])?(o=0,l.v=e,l.n=s[1]):p<g&&(n=t<3||s[0]>e||e>g)&&(s[4]=t,s[5]=e,l.n=g,o=0))}if(n||t>1)return a;throw f=!0,e}return function(n,c,g){if(u>1)throw TypeError("Generator is already running");for(f&&1===c&&p(c,g),o=c,h=g;(i=o<2?r:h)||!f;){s||(o?o<3?(o>1&&(l.n=-1),p(o,h)):l.n=h:l.v=h);try{if(u=2,s){if(o||(n="next"),i=s[n]){if(!(i=i.call(s,h)))throw TypeError("iterator result is not an object");if(!i.done)return i;h=i.value,o<2&&(o=0)}else 1===o&&(i=s.return)&&i.call(s),o<2&&(h=TypeError("The iterator does not provide a '"+n+"' method"),o=1);s=r}else if((i=(f=l.n<0)?h:t.call(e,l))!==a)break}catch(i){s=r,o=1,h=i}finally{u=1}}return{value:i,done:f}}}(t,s,o),!0),c}var a={};function u(){}function c(){}function f(){}i=Object.getPrototypeOf;var l=[][s]?i(i([][s]())):(e(i={},s,(function(){return this})),i),p=f.prototype=u.prototype=Object.create(l);function g(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,e(t,o,"GeneratorFunction")),t.prototype=Object.create(p),t}return c.prototype=f,e(p,"constructor",f),e(f,"constructor",c),c.displayName="GeneratorFunction",e(f,o,"GeneratorFunction"),e(p),e(p,o,"Generator"),e(p,s,(function(){return this})),e(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:h,m:g}})()}function e(t,r,i,n){var s=Object.defineProperty;try{s({},"",{})}catch(t){s=0}e=function(t,r,i,n){if(r)s?s(t,r,{value:i,enumerable:!n,configurable:!n,writable:!n}):t[r]=i;else{var o=function(r,i){e(t,r,(function(t){return this._invoke(r,i,t)}))};o("next",0),o("throw",1),o("return",2)}},e(t,r,i,n)}function r(t,e,r,i,n,s,o){try{var h=t[s](o),a=h.value}catch(t){return void r(t)}h.done?e(a):Promise.resolve(a).then(i,n)}function i(t){return function(){var e=this,i=arguments;return new Promise((function(n,s){var o=t.apply(e,i);function h(t){r(o,n,s,h,a,"next",t)}function a(t){r(o,n,s,h,a,"throw",t)}h(void 0)}))}}System.register(["./index-legacy.8748bb61.js"],(function(e,r){"use strict";var n,s,o,h,a,u,c,f,l,p,g,d,m,v,y,b=document.createElement("style");return b.textContent=".login-page{width:100%;height:100%;background-image:url("+new URL("login_background.4576f25d.png",r.meta.url).href+");background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}\n",document.head.appendChild(b),{setters:[function(t){n=t._,s=t.r,o=t.J,h=t.b,a=t.l,u=t.h,c=t.o,f=t.g,l=t.w,p=t.j,g=t.e,d=t.k,m=t.Y,v=t.M,y=t.Z}],execute:function(){function r(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function b(t,e){return t&e}function T(t,e){return t|e}function S(t,e){return t^e}function w(t,e){return t&~e}function E(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function x(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var D,B="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function O(t){var e,r,i="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),i+=B.charAt(r>>6)+B.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),i+=B.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),i+=B.charAt(r>>2)+B.charAt((3&r)<<4));(3&i.length)>0;)i+="=";return i}function R(t){var e,i="",n=0,s=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var o=B.indexOf(t.charAt(e));o<0||(0==n?(i+=r(o>>2),s=3&o,n=1):1==n?(i+=r(s<<2|o>>4),s=15&o,n=2):2==n?(i+=r(s),i+=r(o>>2),s=3&o,n=3):(i+=r(s<<2|o>>4),i+=r(15&o),n=0))}return 1==n&&(i+=r(s<<2)),i}var A,V=function(t){var e;if(void 0===D){var r="0123456789ABCDEF",i=" \f\n\r\t \u2028\u2029";for(D={},e=0;e<16;++e)D[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)D[r.charAt(e)]=e;for(e=0;e<8;++e)D[i.charAt(e)]=-1}var n=[],s=0,o=0;for(e=0;e<t.length;++e){var h=t.charAt(e);if("="==h)break;if(-1!=(h=D[h])){if(void 0===h)throw new Error("Illegal character at offset "+e);s|=h,++o>=2?(n[n.length]=s,s=0,o=0):s<<=4}}if(o)throw new Error("Hex encoding incomplete: 4 bits missing");return n},I={decode:function(t){var e;if(void 0===A){var r="= \f\n\r\t \u2028\u2029";for(A=Object.create(null),e=0;e<64;++e)A["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(A["-"]=62,A._=63,e=0;e<9;++e)A[r.charAt(e)]=-1}var i=[],n=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=A[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);n|=o,++s>=4?(i[i.length]=n>>16,i[i.length]=n>>8&255,i[i.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=n>>10;break;case 3:i[i.length]=n>>16,i[i.length]=n>>8&255}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=I.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return I.decode(t)}},N=1e13,P=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,i,n=this.buf,s=n.length;for(r=0;r<s;++r)(i=n[r]*t+e)<N?e=0:i-=(e=0|i/N)*N,n[r]=i;e>0&&(n[r]=e)},t.prototype.sub=function(t){var e,r,i=this.buf,n=i.length;for(e=0;e<n;++e)(r=i[e]-t)<0?(r+=N,t=1):t=0,i[e]=r;for(;0===i[i.length-1];)i.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),i=e.length-2;i>=0;--i)r+=(N+e[i]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*N+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),M=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,_=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function j(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var q,L=function(){function t(e,r){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var i="",n=t;n<e;++n)if(i+=this.hexByte(this.get(n)),!0!==r)switch(15&n){case 7:i+="  ";break;case 15:i+="\n";break;default:i+=" "}return i},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var i=this.get(r);if(i<32||i>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r="",i=t;i<e;++i)r+=String.fromCharCode(this.get(i));return r},t.prototype.parseStringUTF=function(t,e){for(var r="",i=t;i<e;){var n=this.get(i++);r+=n<128?String.fromCharCode(n):n>191&&n<224?String.fromCharCode((31&n)<<6|63&this.get(i++)):String.fromCharCode((15&n)<<12|(63&this.get(i++))<<6|63&this.get(i++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,i,n="",s=t;s<e;)r=this.get(s++),i=this.get(s++),n+=String.fromCharCode(r<<8|i);return n},t.prototype.parseTime=function(t,e,r){var i=this.parseStringISO(t,e),n=(r?M:_).exec(i);return n?(r&&(n[1]=+n[1],n[1]+=+n[1]<70?2e3:1900),i=n[1]+"-"+n[2]+"-"+n[3]+" "+n[4],n[5]&&(i+=":"+n[5],n[6]&&(i+=":"+n[6],n[7]&&(i+="."+n[7]))),n[8]&&(i+=" UTC","Z"!=n[8]&&(i+=n[8],n[9]&&(i+=":"+n[9]))),i):"Unrecognized time: "+i},t.prototype.parseInteger=function(t,e){for(var r,i=this.get(t),n=i>127,s=n?255:0,o="";i==s&&++t<e;)i=this.get(t);if(0===(r=e-t))return n?-1:0;if(r>4){for(o=i,r<<=3;!(128&(+o^s));)o=+o<<1,--r;o="("+r+" bit)\n"}n&&(i-=256);for(var h=new P(i),a=t+1;a<e;++a)h.mulAdd(256,this.get(a));return o+h.toString()},t.prototype.parseBitString=function(t,e,r){for(var i=this.get(t),n="("+((e-t-1<<3)-i)+" bit)\n",s="",o=t+1;o<e;++o){for(var h=this.get(o),a=o==e-1?i:0,u=7;u>=a;--u)s+=h>>u&1?"1":"0";if(s.length>r)return n+j(s,r)}return n+s},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return j(this.parseStringISO(t,e),r);var i=e-t,n="("+i+" byte)\n";i>(r/=2)&&(e=t+r);for(var s=t;s<e;++s)n+=this.hexByte(this.get(s));return i>r&&(n+="…"),n},t.prototype.parseOID=function(t,e,r){for(var i="",n=new P,s=0,o=t;o<e;++o){var h=this.get(o);if(n.mulAdd(128,127&h),s+=7,!(128&h)){if(""===i)if((n=n.simplify())instanceof P)n.sub(80),i="2."+n.toString();else{var a=n<80?n<40?0:1:2;i=a+"."+(n-40*a)}else i+="."+n.toString();if(i.length>r)return j(i,r);n=new P,s=0}}return s>0&&(i+=".incomplete"),i},t}(),C=function(){function t(t,e,r,i,n){if(!(i instanceof H))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=i,this.sub=n}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return j(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return j(this.stream.parseStringISO(e,e+r),t);case 30:return j(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,i=this.sub.length;r<i;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var i=0;i<r;++i)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof L?e:new L(e,0);var i=new L(r),n=new H(r),s=t.decodeLength(r),o=r.pos,h=o-i.pos,a=null,u=function(){var e=[];if(null!==s){for(var i=o+s;r.pos<i;)e[e.length]=t.decode(r);if(r.pos!=i)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var n=t.decode(r);if(n.tag.isEOC())break;e[e.length]=n}s=o-r.pos}catch(h){throw new Error("Exception while decoding undefined length content: "+h)}return e};if(n.tagConstructed)a=u();else if(n.isUniversal()&&(3==n.tagNumber||4==n.tagNumber))try{if(3==n.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");a=u();for(var c=0;c<a.length;++c)if(a[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(f){a=null}if(null===a){if(null===s)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);r.pos=o+Math.abs(s)}return new t(i,h,s,n,a)},t}(),H=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=!!(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new P;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),k=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],F=(1<<26)/k[k.length-1],U=function(){function t(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var i,n=(1<<e)-1,s=!1,o="",h=this.t,a=this.DB-h*this.DB%e;if(h-- >0)for(a<this.DB&&(i=this[h]>>a)>0&&(s=!0,o=r(i));h>=0;)a<e?(i=(this[h]&(1<<a)-1)<<e-a,i|=this[--h]>>(a+=this.DB-e)):(i=this[h]>>(a-=e)&n,a<=0&&(a+=this.DB,--h)),i>0&&(s=!0),s&&(o+=r(i));return s?o:"0"},t.prototype.negate=function(){var e=Y();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+rt(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=Y();return this.abs().divRemTo(e,null,r),this.s<0&&r.compareTo(t.ZERO)>0&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new z(e):new G(e),this.exp(t,r)},t.prototype.clone=function(){var t=Y();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,i=this.DB-t*this.DB%8,n=0;if(t-- >0)for(i<this.DB&&(r=this[t]>>i)!=(this.s&this.DM)>>i&&(e[n++]=r|this.s<<this.DB-i);t>=0;)i<8?(r=(this[t]&(1<<i)-1)<<8-i,r|=this[--t]>>(i+=this.DB-8)):(r=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&r&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=Y();return this.bitwiseTo(t,b,e),e},t.prototype.or=function(t){var e=Y();return this.bitwiseTo(t,T,e),e},t.prototype.xor=function(t){var e=Y();return this.bitwiseTo(t,S,e),e},t.prototype.andNot=function(t){var e=Y();return this.bitwiseTo(t,w,e),e},t.prototype.not=function(){for(var t=Y(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=Y();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=Y();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+E(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=x(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,T)},t.prototype.clearBit=function(t){return this.changeBit(t,w)},t.prototype.flipBit=function(t){return this.changeBit(t,S)},t.prototype.add=function(t){var e=Y();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=Y();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=Y();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=Y();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=Y();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=Y(),r=Y();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,i,n=t.bitLength(),s=et(1);if(n<=0)return s;r=n<18?1:n<48?3:n<144?4:n<768?5:6,i=n<8?new z(e):e.isEven()?new Z(e):new G(e);var o=[],h=3,a=r-1,u=(1<<r)-1;if(o[1]=i.convert(this),r>1){var c=Y();for(i.sqrTo(o[1],c);h<=u;)o[h]=Y(),i.mulTo(c,o[h-2],o[h]),h+=2}var f,l,p=t.t-1,g=!0,d=Y();for(n=rt(t[p])-1;p>=0;){for(n>=a?f=t[p]>>n-a&u:(f=(t[p]&(1<<n+1)-1)<<a-n,p>0&&(f|=t[p-1]>>this.DB+n-a)),h=r;!(1&f);)f>>=1,--h;if((n-=h)<0&&(n+=this.DB,--p),g)o[f].copyTo(s),g=!1;else{for(;h>1;)i.sqrTo(s,d),i.sqrTo(d,s),h-=2;h>0?i.sqrTo(s,d):(l=s,s=d,d=l),i.mulTo(d,o[f],s)}for(;p>=0&&!(t[p]&1<<n);)i.sqrTo(s,d),l=s,s=d,d=l,--n<0&&(n=this.DB-1,--p)}return i.revert(s)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var i=e.clone(),n=this.clone(),s=et(1),o=et(0),h=et(0),a=et(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),r?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(e,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(e,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),r?(h.isEven()&&a.isEven()||(h.addTo(this,h),a.subTo(e,a)),h.rShiftTo(1,h)):a.isEven()||a.subTo(e,a),a.rShiftTo(1,a);i.compareTo(n)>=0?(i.subTo(n,i),r&&s.subTo(h,s),o.subTo(a,o)):(n.subTo(i,n),r&&h.subTo(s,h),a.subTo(o,a))}return 0!=n.compareTo(t.ONE)?t.ZERO:a.compareTo(e)>=0?a.subtract(e):a.signum()<0?(a.addTo(e,a),a.signum()<0?a.add(e):a):a},t.prototype.pow=function(t){return this.exp(t,new K)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var i=e;e=r,r=i}var n=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(n<s&&(s=n),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=k[k.length-1]){for(e=0;e<k.length;++e)if(r[0]==k[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<k.length;){for(var i=k[e],n=e+1;n<k.length&&i<F;)i*=k[n++];for(i=r.modInt(i);e<n;)if(i%k[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var i;if(16==r)i=4;else if(8==r)i=3;else if(256==r)i=8;else if(2==r)i=1;else if(32==r)i=5;else{if(4!=r)return void this.fromRadix(e,r);i=2}this.t=0,this.s=0;for(var n=e.length,s=!1,o=0;--n>=0;){var h=8==i?255&+e[n]:tt(e,n);h<0?"-"==e.charAt(n)&&(s=!0):(s=!1,0==o?this[this.t++]=h:o+i>this.DB?(this[this.t-1]|=(h&(1<<this.DB-o)-1)<<o,this[this.t++]=h>>this.DB-o):this[this.t-1]|=h<<o,(o+=i)>=this.DB&&(o-=this.DB))}8==i&&128&+e[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,i=this.DB-r,n=(1<<i)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,h=this.t-1;h>=0;--h)e[h+s+1]=this[h]>>i|o,o=(this[h]&n)<<r;for(h=s-1;h>=0;--h)e[h]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var i=t%this.DB,n=this.DB-i,s=(1<<i)-1;e[0]=this[r]>>i;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<n,e[o-r]=this[o]>>i;i>0&&(e[this.t-r-1]|=(this.s&s)<<n),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]-t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i-=t[r],e[r++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[r++]=this.DV+i:i>0&&(e[r++]=i),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var i=this.abs(),n=e.abs(),s=i.t;for(r.t=s+n.t;--s>=0;)r[s]=0;for(s=0;s<n.t;++s)r[s+i.t]=i.am(0,n[s],r,s,0,i.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var i=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,i,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,i){var n=e.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return null!=r&&r.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=Y());var o=Y(),h=this.s,a=e.s,u=this.DB-rt(n[n.t-1]);u>0?(n.lShiftTo(u,o),s.lShiftTo(u,i)):(n.copyTo(o),s.copyTo(i));var c=o.t,f=o[c-1];if(0!=f){var l=f*(1<<this.F1)+(c>1?o[c-2]>>this.F2:0),p=this.FV/l,g=(1<<this.F1)/l,d=1<<this.F2,m=i.t,v=m-c,y=null==r?Y():r;for(o.dlShiftTo(v,y),i.compareTo(y)>=0&&(i[i.t++]=1,i.subTo(y,i)),t.ONE.dlShiftTo(c,y),y.subTo(o,o);o.t<c;)o[o.t++]=0;for(;--v>=0;){var b=i[--m]==f?this.DM:Math.floor(i[m]*p+(i[m-1]+d)*g);if((i[m]+=o.am(0,b,i,v,0,c))<b)for(o.dlShiftTo(v,y),i.subTo(y,i);i[m]<--b;)i.subTo(y,i)}null!=r&&(i.drShiftTo(c,r),h!=a&&t.ZERO.subTo(r,r)),i.t=c,i.clamp(),u>0&&i.rShiftTo(u,i),h<0&&t.ZERO.subTo(i,i)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(e>4294967295||e<1)return t.ONE;var i=Y(),n=Y(),s=r.convert(this),o=rt(e)-1;for(s.copyTo(i);--o>=0;)if(r.sqrTo(i,n),(e&1<<o)>0)r.mulTo(n,s,i);else{var h=i;i=n,n=h}return r.revert(i)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=et(r),n=Y(),s=Y(),o="";for(this.divRemTo(i,n,s);n.signum()>0;)o=(r+s.intValue()).toString(t).substr(1)+o,n.divRemTo(i,n,s);return s.intValue().toString(t)+o},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var i=this.chunkSize(r),n=Math.pow(r,i),s=!1,o=0,h=0,a=0;a<e.length;++a){var u=tt(e,a);u<0?"-"==e.charAt(a)&&0==this.signum()&&(s=!0):(h=r*h+u,++o>=i&&(this.dMultiply(n),this.dAddOffset(h,0),o=0,h=0))}o>0&&(this.dMultiply(Math.pow(r,o)),this.dAddOffset(h,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,i){if("number"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),T,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var n=[],s=7&e;n.length=1+(e>>3),r.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},t.prototype.bitwiseTo=function(t,e,r){var i,n,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=e(this[i],t[i]);if(t.t<this.t){for(n=t.s&this.DM,i=s;i<this.t;++i)r[i]=e(this[i],n);r.t=this.t}else{for(n=this.s&this.DM,i=s;i<t.t;++i)r[i]=e(n,t[i]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var i=t.ONE.shiftLeft(e);return this.bitwiseTo(i,r,i),i},t.prototype.addTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]+t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i+=t[r],e[r++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[r++]=i:i<-1&&(e[r++]=this.DV+i),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(var n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var i=r.t=this.t+t.t-e;for(r.s=0;--i>=0;)r[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)r[this.t+i-e]=this.am(e-i,t[i],r,0,0,this.t+i-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(e*r+this[i])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),i=r.getLowestSetBit();if(i<=0)return!1;var n=r.shiftRight(i);(e=e+1>>1)>k.length&&(e=k.length);for(var s=Y(),o=0;o<e;++o){s.fromInt(k[Math.floor(Math.random()*k.length)]);var h=s.modPow(n,this);if(0!=h.compareTo(t.ONE)&&0!=h.compareTo(r)){for(var a=1;a++<i&&0!=h.compareTo(r);)if(0==(h=h.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=h.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=Y();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(r.compareTo(i)<0){var n=r;r=i,i=n}var s=r.getLowestSetBit(),o=i.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),o>0&&(r.rShiftTo(o,r),i.rShiftTo(o,i));var h=function(){(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),r.compareTo(i)>=0?(r.subTo(i,r),r.rShiftTo(1,r)):(i.subTo(r,i),i.rShiftTo(1,i)),r.signum()>0?setTimeout(h,0):(o>0&&i.lShiftTo(o,i),setTimeout((function(){e(i)}),0))};setTimeout(h,10)}},t.prototype.fromNumberAsync=function(e,r,i,n){if("number"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),T,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>e&&s.subTo(t.ONE.shiftLeft(e-1),s),s.isProbablePrime(r)?setTimeout((function(){n()}),0):setTimeout(o,0)};setTimeout(o,0)}else{var h=[],a=7&e;h.length=1+(e>>3),r.nextBytes(h),a>0?h[0]&=(1<<a)-1:h[0]=0,this.fromString(h,256)}},t}(),K=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),z=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),G=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=Y();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(U.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=Y();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],i=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),Z=function(){function t(t){this.m=t,this.r2=Y(),this.q3=Y(),U.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=Y();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function Y(){return new U(null)}function Q(t,e){return new U(t,e)}var $="undefined"!=typeof navigator;$&&"Microsoft Internet Explorer"==navigator.appName?(U.prototype.am=function(t,e,r,i,n,s){for(var o=32767&e,h=e>>15;--s>=0;){var a=32767&this[t],u=this[t++]>>15,c=h*a+u*o;n=((a=o*a+((32767&c)<<15)+r[i]+(1073741823&n))>>>30)+(c>>>15)+h*u+(n>>>30),r[i++]=1073741823&a}return n},q=30):$&&"Netscape"!=navigator.appName?(U.prototype.am=function(t,e,r,i,n,s){for(;--s>=0;){var o=e*this[t++]+r[i]+n;n=Math.floor(o/67108864),r[i++]=67108863&o}return n},q=26):(U.prototype.am=function(t,e,r,i,n,s){for(var o=16383&e,h=e>>14;--s>=0;){var a=16383&this[t],u=this[t++]>>14,c=h*a+u*o;n=((a=o*a+((16383&c)<<14)+r[i]+n)>>28)+(c>>14)+h*u,r[i++]=268435455&a}return n},q=28),U.prototype.DB=q,U.prototype.DM=(1<<q)-1,U.prototype.DV=1<<q;U.prototype.FV=Math.pow(2,52),U.prototype.F1=52-q,U.prototype.F2=2*q-52;var J,W,X=[];for(J="0".charCodeAt(0),W=0;W<=9;++W)X[J++]=W;for(J="a".charCodeAt(0),W=10;W<36;++W)X[J++]=W;for(J="A".charCodeAt(0),W=10;W<36;++W)X[J++]=W;function tt(t,e){var r=X[t.charCodeAt(e)];return null==r?-1:r}function et(t){var e=Y();return e.fromInt(t),e}function rt(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}U.ZERO=et(0),U.ONE=et(1);var it=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,i;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[r],this.S[r]=i;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}();var nt,st,ot=null;if(null==ot){ot=[],st=0;var ht=void 0;if("undefined"!=typeof window&&window.crypto&&window.crypto.getRandomValues){var at=new Uint32Array(256);for(window.crypto.getRandomValues(at),ht=0;ht<at.length;++ht)ot[st++]=255&at[ht]}var ut=0,ct=function(t){if((ut=ut||0)>=256||st>=256)window.removeEventListener?window.removeEventListener("mousemove",ct,!1):window.detachEvent&&window.detachEvent("onmousemove",ct);else try{var e=t.x+t.y;ot[st++]=255&e,ut+=1}catch(r){}};"undefined"!=typeof window&&(window.addEventListener?window.addEventListener("mousemove",ct,!1):window.attachEvent&&window.attachEvent("onmousemove",ct))}function ft(){if(null==nt){for(nt=new it;st<256;){var t=Math.floor(65536*Math.random());ot[st++]=255&t}for(nt.init(ot),st=0;st<ot.length;++st)ot[st]=0;st=0}return nt.next()}var lt=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=ft()},t}();var pt=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Q(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var r=[],i=t.length-1;i>=0&&e>0;){var n=t.charCodeAt(i--);n<128?r[--e]=n:n>127&&n<2048?(r[--e]=63&n|128,r[--e]=n>>6|192):(r[--e]=63&n|128,r[--e]=n>>6&63|128,r[--e]=n>>12|224)}r[--e]=0;for(var s=new lt,o=[];e>2;){for(o[0]=0;0==o[0];)s.nextBytes(o);r[--e]=o[0]}return r[--e]=2,r[--e]=0,new U(r)}(t,e);if(null==r)return null;var i=this.doPublic(r);if(null==i)return null;for(var n=i.toString(16),s=n.length,o=0;o<2*e-s;o++)n="0"+n;return n},t.prototype.setPrivate=function(t,e,r){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Q(t,16),this.e=parseInt(e,16),this.d=Q(r,16)):console.error("Invalid RSA private key")},t.prototype.setPrivateEx=function(t,e,r,i,n,s,o,h){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Q(t,16),this.e=parseInt(e,16),this.d=Q(r,16),this.p=Q(i,16),this.q=Q(n,16),this.dmp1=Q(s,16),this.dmq1=Q(o,16),this.coeff=Q(h,16)):console.error("Invalid RSA private key")},t.prototype.generate=function(t,e){var r=new lt,i=t>>1;this.e=parseInt(e,16);for(var n=new U(e,16);;){for(;this.p=new U(t-i,1,r),0!=this.p.subtract(U.ONE).gcd(n).compareTo(U.ONE)||!this.p.isProbablePrime(10););for(;this.q=new U(i,1,r),0!=this.q.subtract(U.ONE).gcd(n).compareTo(U.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(U.ONE),h=this.q.subtract(U.ONE),a=o.multiply(h);if(0==a.gcd(n).compareTo(U.ONE)){this.n=this.p.multiply(this.q),this.d=n.modInverse(a),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(h),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=Q(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){var r=t.toByteArray(),i=0;for(;i<r.length&&0==r[i];)++i;if(r.length-i!=e-1||2!=r[i])return null;++i;for(;0!=r[i];)if(++i>=r.length)return null;var n="";for(;++i<r.length;){var s=255&r[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&r[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&r[i+1])<<6|63&r[i+2]),i+=2)}return n}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var i=new lt,n=t>>1;this.e=parseInt(e,16);var s=new U(e,16),o=this,h=function(){var e=function(){if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var e=o.p.subtract(U.ONE),i=o.q.subtract(U.ONE),n=e.multiply(i);0==n.gcd(s).compareTo(U.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(n),o.dmp1=o.d.mod(e),o.dmq1=o.d.mod(i),o.coeff=o.q.modInverse(o.p),setTimeout((function(){r()}),0)):setTimeout(h,0)},a=function(){o.q=Y(),o.q.fromNumberAsync(n,1,i,(function(){o.q.subtract(U.ONE).gcda(s,(function(t){0==t.compareTo(U.ONE)&&o.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(a,0)}))}))},u=function(){o.p=Y(),o.p.fromNumberAsync(t-n,1,i,(function(){o.p.subtract(U.ONE).gcda(s,(function(t){0==t.compareTo(U.ONE)&&o.p.isProbablePrime(10)?setTimeout(a,0):setTimeout(u,0)}))}))};setTimeout(u,0)};setTimeout(h,0)},t.prototype.sign=function(t,e,r){var i=function(t,e){if(e<t.length+22)return console.error("Message too long for RSA"),null;for(var r=e-t.length-6,i="",n=0;n<r;n+=2)i+="ff";return Q("0001"+i+"00"+t,16)}((gt[r]||"")+e(t).toString(),this.n.bitLength()/4);if(null==i)return null;var n=this.doPrivate(i);if(null==n)return null;var s=n.toString(16);return 1&s.length?"0"+s:s},t.prototype.verify=function(t,e,r){var i=Q(e,16),n=this.doPublic(i);return null==n?null:function(t){for(var e in gt)if(gt.hasOwnProperty(e)){var r=gt[e],i=r.length;if(t.substr(0,i)==r)return t.substr(i)}return t}
/*!
            Copyright (c) 2011, Yahoo! Inc. All rights reserved.
            Code licensed under the BSD License:
            http://developer.yahoo.com/yui/license.html
            version: 2.9.0
            */(n.toString(16).replace(/^1f+00/,""))==r(t).toString()},t}();var gt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var dt={};dt.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var n;for(n in r)t.prototype[n]=r[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(t,e){for(n=0;n<o.length;n+=1){var r=o[n],i=e[r];"function"==typeof i&&i!=Object.prototype[r]&&(t[r]=i)}})}catch(h){}s(t.prototype,r)}}};
/**
             * @fileOverview
             * @name asn1-1.0.js
             * <AUTHOR>
             * @version asn1 1.0.13 (2017-Jun-02)
             * @since jsrsasign 2.1
             * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
             */
var mt={};void 0!==mt.asn1&&mt.asn1||(mt.asn1={}),mt.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var i="",n=0;n<r;n++)i+="f";e=new U(i,16).xor(t).add(U.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=mt.asn1,r=e.DERBoolean,i=e.DERInteger,n=e.DERBitString,s=e.DEROctetString,o=e.DERNull,h=e.DERObjectIdentifier,a=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,f=e.DERPrintableString,l=e.DERTeletexString,p=e.DERIA5String,g=e.DERUTCTime,d=e.DERGeneralizedTime,m=e.DERSequence,v=e.DERSet,y=e.DERTaggedObject,b=e.ASN1Util.newObject,T=Object.keys(t);if(1!=T.length)throw"key of param shall be only one.";var S=T[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+S+":"))throw"undefined key: "+S;if("bool"==S)return new r(t[S]);if("int"==S)return new i(t[S]);if("bitstr"==S)return new n(t[S]);if("octstr"==S)return new s(t[S]);if("null"==S)return new o(t[S]);if("oid"==S)return new h(t[S]);if("enum"==S)return new a(t[S]);if("utf8str"==S)return new u(t[S]);if("numstr"==S)return new c(t[S]);if("prnstr"==S)return new f(t[S]);if("telstr"==S)return new l(t[S]);if("ia5str"==S)return new p(t[S]);if("utctime"==S)return new g(t[S]);if("gentime"==S)return new d(t[S]);if("seq"==S){for(var w=t[S],E=[],x=0;x<w.length;x++){var D=b(w[x]);E.push(D)}return new m({array:E})}if("set"==S){for(w=t[S],E=[],x=0;x<w.length;x++){D=b(w[x]);E.push(D)}return new v({array:E})}if("tag"==S){var B=t[S];if("[object Array]"===Object.prototype.toString.call(B)&&3==B.length){var O=b(B[2]);return new y({tag:B[0],explicit:B[1],obj:O})}var R={};if(void 0!==B.explicit&&(R.explicit=B.explicit),void 0!==B.tag&&(R.tag=B.tag),void 0===B.obj)throw"obj shall be specified for 'tag'.";return R.obj=b(B.obj),new y(R)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},mt.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),i=(e=Math.floor(r/40)+"."+r%40,""),n=2;n<t.length;n+=2){var s=("00000000"+parseInt(t.substr(n,2),16).toString(2)).slice(-8);if(i+=s.substr(1,7),"0"==s.substr(0,1))e=e+"."+new U(i,2).toString(10),i=""}return e},mt.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new U(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var h=i.substr(o,7);o!=i.length-7&&(h="1"+h),r+=e(parseInt(h,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);return i},mt.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},mt.asn1.DERAbstractString=function(t){mt.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},dt.lang.extend(mt.asn1.DERAbstractString,mt.asn1.ASN1Object),mt.asn1.DERAbstractTime=function(t){mt.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var i=this.zeroPadding,n=this.localDateToUTC(t),s=String(n.getFullYear());"utc"==e&&(s=s.substr(2,2));var o=s+i(String(n.getMonth()+1),2)+i(String(n.getDate()),2)+i(String(n.getHours()),2)+i(String(n.getMinutes()),2)+i(String(n.getSeconds()),2);if(!0===r){var h=n.getMilliseconds();if(0!=h){var a=i(String(h),3);o=o+"."+(a=a.replace(/[0]+$/,""))}}return o+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,i,n,s){var o=new Date(Date.UTC(t,e-1,r,i,n,s,0));this.setByDate(o)},this.getFreshValueHex=function(){return this.hV}},dt.lang.extend(mt.asn1.DERAbstractTime,mt.asn1.ASN1Object),mt.asn1.DERAbstractStructured=function(t){mt.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},dt.lang.extend(mt.asn1.DERAbstractStructured,mt.asn1.ASN1Object),mt.asn1.DERBoolean=function(){mt.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},dt.lang.extend(mt.asn1.DERBoolean,mt.asn1.ASN1Object),mt.asn1.DERInteger=function(t){mt.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=mt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new U(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},dt.lang.extend(mt.asn1.DERInteger,mt.asn1.ASN1Object),mt.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=mt.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}mt.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var i="";for(r=0;r<t.length-1;r+=8){var n=t.substr(r,8),s=parseInt(n,2).toString(16);1==s.length&&(s="0"+s),i+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+i},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},dt.lang.extend(mt.asn1.DERBitString,mt.asn1.ASN1Object),mt.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=mt.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}mt.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},dt.lang.extend(mt.asn1.DEROctetString,mt.asn1.DERAbstractString),mt.asn1.DERNull=function(){mt.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},dt.lang.extend(mt.asn1.DERNull,mt.asn1.ASN1Object),mt.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new U(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var h=i.substr(o,7);o!=i.length-7&&(h="1"+h),r+=e(parseInt(h,2))}return r};mt.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(t){var e=mt.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},dt.lang.extend(mt.asn1.DERObjectIdentifier,mt.asn1.ASN1Object),mt.asn1.DEREnumerated=function(t){mt.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=mt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new U(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},dt.lang.extend(mt.asn1.DEREnumerated,mt.asn1.ASN1Object),mt.asn1.DERUTF8String=function(t){mt.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},dt.lang.extend(mt.asn1.DERUTF8String,mt.asn1.DERAbstractString),mt.asn1.DERNumericString=function(t){mt.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},dt.lang.extend(mt.asn1.DERNumericString,mt.asn1.DERAbstractString),mt.asn1.DERPrintableString=function(t){mt.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},dt.lang.extend(mt.asn1.DERPrintableString,mt.asn1.DERAbstractString),mt.asn1.DERTeletexString=function(t){mt.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},dt.lang.extend(mt.asn1.DERTeletexString,mt.asn1.DERAbstractString),mt.asn1.DERIA5String=function(t){mt.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},dt.lang.extend(mt.asn1.DERIA5String,mt.asn1.DERAbstractString),mt.asn1.DERUTCTime=function(t){mt.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},dt.lang.extend(mt.asn1.DERUTCTime,mt.asn1.DERAbstractTime),mt.asn1.DERGeneralizedTime=function(t){mt.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},dt.lang.extend(mt.asn1.DERGeneralizedTime,mt.asn1.DERAbstractTime),mt.asn1.DERSequence=function(t){mt.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},dt.lang.extend(mt.asn1.DERSequence,mt.asn1.DERAbstractStructured),mt.asn1.DERSet=function(t){mt.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},dt.lang.extend(mt.asn1.DERSet,mt.asn1.DERAbstractStructured),mt.asn1.DERTaggedObject=function(t){mt.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},dt.lang.extend(mt.asn1.DERTaggedObject,mt.asn1.ASN1Object);var vt,yt,bt=globalThis&&globalThis.__extends||(vt=function(t,e){return vt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},vt(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}vt(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),Tt=function(t){function e(r){var i=t.call(this)||this;return r&&("string"==typeof r?i.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&i.parsePropertiesFrom(r)),i}return bt(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,i=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?V(t):I.unarmor(t),n=C.decode(i);if(3===n.sub.length&&(n=n.sub[2].sub[0]),9===n.sub.length){e=n.sub[1].getHexStringValue(),this.n=Q(e,16),r=n.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=n.sub[3].getHexStringValue();this.d=Q(s,16);var o=n.sub[4].getHexStringValue();this.p=Q(o,16);var h=n.sub[5].getHexStringValue();this.q=Q(h,16);var a=n.sub[6].getHexStringValue();this.dmp1=Q(a,16);var u=n.sub[7].getHexStringValue();this.dmq1=Q(u,16);var c=n.sub[8].getHexStringValue();this.coeff=Q(c,16)}else{if(2!==n.sub.length)return!1;if(n.sub[0].sub){var f=n.sub[1].sub[0];e=f.sub[0].getHexStringValue(),this.n=Q(e,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}else e=n.sub[0].getHexStringValue(),this.n=Q(e,16),r=n.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(l){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new mt.asn1.DERInteger({int:0}),new mt.asn1.DERInteger({bigint:this.n}),new mt.asn1.DERInteger({int:this.e}),new mt.asn1.DERInteger({bigint:this.d}),new mt.asn1.DERInteger({bigint:this.p}),new mt.asn1.DERInteger({bigint:this.q}),new mt.asn1.DERInteger({bigint:this.dmp1}),new mt.asn1.DERInteger({bigint:this.dmq1}),new mt.asn1.DERInteger({bigint:this.coeff})]};return new mt.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return O(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new mt.asn1.DERSequence({array:[new mt.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new mt.asn1.DERNull]}),e=new mt.asn1.DERSequence({array:[new mt.asn1.DERInteger({bigint:this.n}),new mt.asn1.DERInteger({int:this.e})]}),r=new mt.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new mt.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return O(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(r,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(pt),St="undefined"!=typeof process?null===(yt={})||void 0===yt?void 0:yt.npm_package_version:void 0,wt=function(){function t(t){void 0===t&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Tt(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(R(t))}catch(e){return!1}},t.prototype.encrypt=function(t){try{return O(this.getKey().encrypt(t))}catch(e){return!1}},t.prototype.sign=function(t,e,r){try{return O(this.getKey().sign(t,e,r))}catch(i){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,R(e),r)}catch(i){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new Tt,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=St,t}(),Et=Object.assign({name:"LocalLogin"},{props:{auth_id:{type:String,default:function(){return""}},auth_info:{type:Object,default:function(){return[]}}},setup:function(e){var r=e,n=s(null),b=o({user_name:"",password:"",idp_id:r.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"}),T=o({user_name:[{required:!0,trigger:"change",message:"用户名不能为空"}],password:[{required:!0,trigger:"change",message:"密码不能为空"}]}),S=h(),w=a("secondary"),E=a("isSecondary"),x=a("uniqKey"),D=a("userName"),B=a("contactType"),O=a("hasContactInfo"),R=function(){var e=i(t().m((function e(){var i,n,s;return t().w((function(t){for(;;)switch(t.n){case 0:return console.log({idp_id:r.auth_id}),b.idp_id=r.auth_id,(i=new wt).setPublicKey("-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA52nU2J3CmT/UsKy2oKYp\ng7GyY/wn6T/cymNFrHFGjwpdzYQ0W+wZS75JNPOVvUPYu5zLFsr3FnfddXrBpxo7\nctNYaPAO9maCqo8WfmE5lA04av4trueA0Qd31OVVeBOfxvSkZxMevOneioxFqVh5\nyO9meOc01oKzpQ6m8qLYh3Ru4/GUus9XABkV1ue7Ll1Owxj4h0ovXTZN2rVpyrNU\nvr+OZeaKA+aMqv2t4woehMuj9hDU9t79mjmVCEJVTPjf051cBFpQawAPUzmMIDWU\nEz3OalPwD03+pHubn80+x+FN94wNK2VV5KtXxwx2g7ZfHGWfY3AwPaJ/uh7cDg/z\nWQIDAQAB\n-----END PUBLIC KEY-----"),(n=y.cloneDeep(b)).password=i.encrypt(b.password),n.user_name=i.encrypt(b.user_name),"msad"!==r.auth_info.authType&&"ldap"!==r.auth_info.authType||(n.ad_pwd=n.password,n.ad_username=n.user_name,delete n.password,delete n.user_name),t.n=1,S.LoginIn(n,r.auth_info.authType,r.auth_id);case 1:(s=t.v).isSecondary&&(E.value=s.isSecondary,w.value=s.secondary,x.value=s.uniqKey,D.value=b.user_name,B.value=s.contactType,O.value=s.hasContactInfo||!1);case 2:return t.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),A=function(){n.value.validate(function(){var e=i(t().m((function e(r){return t().w((function(t){for(;;)switch(t.n){case 0:if(!r){t.n=2;break}return t.n=1,R();case 1:t.n=3;break;case 2:return v({type:"error",message:"用户名密码不能为空",showClose:!0}),t.a(2,!1);case 3:return t.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())};return function(t,e){var r=u("base-input"),i=u("base-form-item"),s=u("base-button"),o=u("base-form");return c(),f(o,{ref_key:"loginForm",ref:n,model:b,rules:T,"validate-on-rule-change":!1,onKeyup:m(A,["enter"])},{default:l((function(){return[p(i,{prop:"user_name"},{default:l((function(){return[e[2]||(e[2]=g("span",null,"账号",-1)),p(r,{modelValue:b.user_name,"onUpdate:modelValue":e[0]||(e[0]=function(t){return b.user_name=t}),size:"large",placeholder:"请输入用户名","suffix-icon":"user"},null,8,["modelValue"])]})),_:1,__:[2]}),p(i,{prop:"password"},{default:l((function(){return[e[3]||(e[3]=g("span",null,"密码",-1)),p(r,{modelValue:b.password,"onUpdate:modelValue":e[1]||(e[1]=function(t){return b.password=t}),"show-password":"",size:"large",type:"password",placeholder:"请输入密码",autocomplete:"current-password"},null,8,["modelValue"])]})),_:1,__:[3]}),p(i,null,{default:l((function(){return[p(s,{type:"primary",size:"large",class:"login_submit_button",onClick:A},{default:l((function(){return e[4]||(e[4]=[d("登 录")])})),_:1,__:[4]})]})),_:1})]})),_:1},8,["model","rules"])}}});e("default",n(Et,[["__file","D:/asec-platform/frontend/portal/src/view/login/localLogin/localLogin.vue"]]))}}}))}();
