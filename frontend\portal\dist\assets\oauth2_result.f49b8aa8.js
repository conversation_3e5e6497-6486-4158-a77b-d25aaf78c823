/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{_ as e,u as a,a as s,b as t,r as n,p as r,c as o,N as i,M as l,h as c,o as u,d,j as p,e as m,t as v,f as y}from"./index.2a422357.js";import h from"./secondaryAuth.2a0113ef.js";import"./verifyCode.bf902937.js";const f={class:"oauth-result-container"},g={key:0,class:"loading-box"},_={class:"message"},w={key:1,class:"secondary-auth-container"},b=e(Object.assign({name:"OAuth2Result"},{setup(e){const b=a(),S=s(),q=t(),I=n("正在处理认证信息..."),T=n(!1),C=n(""),j=n(""),P=n(""),x=n(""),A=n(""),L=n("phone"),k=n(!0);r("userName",P),r("last_id",x),r("isSecondary",n(!0)),r("contactType",L),r("hasContactInfo",k);const N=o((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===L.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===L.value}])),O=async e=>{I.value="认证成功，正在跳转...";let a=decodeURIComponent(A.value||"/");if(e.clientParams){const s=new URLSearchParams;s.set("type",e.clientParams.type),e.clientParams.wp&&s.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+s.toString()}window.location.href=a},R=()=>{T.value=!1,I.value="已取消验证，正在返回登录页...";const e=new URLSearchParams;b.query.idp_id&&e.set("idp_id",b.query.idp_id),b.query.redirect_url&&e.set("redirect",b.query.redirect_url),"client"===b.query.type&&(e.set("type","client"),b.query.wp&&e.set("wp",b.query.wp));const a=e.toString()?`/login?${e.toString()}`:"/login";S.push(a)};return i((async()=>{var e;try{const{auth_token:e,idp_id:a,redirect_url:s,login_type:t,auth_error:n}=b.query;if("is_test"===t)return I.value="测试完成",l.success("测试完成，正在关闭窗口..."),void setTimeout((()=>window.close()),2e3);if(n)throw new Error(n);if(!e)throw new Error("缺少有效的认证令牌");localStorage.setItem("loginType",t);const r={clientId:"client_portal",grantType:"implicit",redirect_uri:s,idpId:a,authWeb:{authWebToken:e}};I.value="验证登录信息...";const o=await q.LoginIn(r,t,a);if("object"==typeof o&&null!==o&&o.isSecondary){I.value="需要进行二次认证...",L.value=o.contactType||"phone",k.value=o.hasContactInfo||!1;const e=o.secondary&&Array.isArray(o.secondary)&&o.secondary.length>0?o.secondary[0].id:a;C.value=o.uniqKey,j.value=o.user_id,P.value=o.userName,x.value=e,A.value=s||"/",T.value=!0}else{if(!0!==o)throw new Error("登录处理失败");I.value="认证成功，正在跳转...",A.value=s||"/"}}catch(a){console.error("处理错误:",a);let t="认证失败，请稍后再试";try{if(null==(e=a.response)?void 0:e.data)t=a.response.data.error_description||a.response.data.message||a.response.data.error||t;else if(a.message){let e=a.message;if(e.includes("msg=登录失败")){const a=e.split("msg=登录失败:")[1]||e;e=a.trim()}if(e.includes("{"))try{const a=e.indexOf("{"),s=e.substring(a),n=JSON.parse(s);n&&n.message&&(t=n.message)}catch(s){}if("认证失败，请稍后再试"===t&&e.includes("message =")){const a=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,s=e.match(a);s&&s[1]&&(t=s[1].trim())}if("认证失败，请稍后再试"===t&&e.includes("reason =")){const a=/reason\s*=\s*(\w+)/,s=e.match(a);if(s&&s[1]){t=`认证失败: ${s[1].replace(/_/g," ").toLowerCase()}`}}"认证失败，请稍后再试"===t&&(t=e.split("\n")[0],t.length>100&&(t=t.substring(0,97)+"..."))}}catch(s){console.error("处理错误消息时发生异常:",s)}I.value=t,l.error(t),setTimeout((()=>{S.push({name:"Login"})}),2e3)}})),(e,a)=>{const s=c("base-icon");return u(),d("div",f,[T.value?y("v-if",!0):(u(),d("div",g,[p(s,{class:"loading-icon",name:"loading",size:"40"}),m("div",_,v(I.value),1)])),y(" 使用SecondaryAuth组件代替Sms组件 "),T.value?(u(),d("div",w,[p(h,{"auth-info":{uniqKey:C.value,contactType:L.value,hasContactInfo:k.value},"auth-id":x.value,"user-name":P.value,"last-id":x.value,"auth-methods":N.value,onVerificationSuccess:O,onCancel:R},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])])):y("v-if",!0)])}}}),[["__scopeId","data-v-fe7893b9"],["__file","D:/asec-platform/frontend/portal/src/view/login/oauth2/oauth2_result.vue"]]);export{b as default};
