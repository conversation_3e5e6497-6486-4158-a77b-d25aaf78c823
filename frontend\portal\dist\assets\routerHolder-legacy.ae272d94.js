/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
System.register(["./index-legacy.e795fa57.js"],(function(e,n){"use strict";var t,r,u,i,o,a,l,c,f,d,s,v;return{setters:[function(e){t=e._,r=e.V,u=e.h,i=e.o,o=e.d,a=e.j,l=e.w,c=e.T,f=e.g,d=e.Y,s=e.m,v=e.A}],execute:function(){var n=Object.assign({name:"RouterHolder"},{setup:function(e){var n=r();return function(e,t){var r=u("router-view");return i(),o("div",null,[a(r,null,{default:l((function(e){var t=e.Component;return[a(c,{mode:"out-in",name:"el-fade-in-linear"},{default:l((function(){return[(i(),f(d,{include:s(n).keepAliveRouters},[(i(),f(v(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}});e("default",t(n,[["__file","D:/asec-platform/frontend/portal/src/view/routerHolder.vue"]]))}}}));
