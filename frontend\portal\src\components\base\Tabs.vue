<template>
  <div 
    :class="[
      'base-tabs',
      `base-tabs--${type}`,
      `base-tabs--${tabPosition}`
    ]"
  >
    <div class="base-tabs__header">
      <div 
        class="base-tabs__nav-wrap"
        :class="{ 'base-tabs__nav-wrap--scrollable': scrollable }"
      >
        <div class="base-tabs__nav-scroll">
          <div 
            ref="nav"
            class="base-tabs__nav"
            role="tablist"
          >
            <div 
              v-for="(pane, index) in panes"
              :key="pane.name || index"
              :class="[
                'base-tabs__item',
                { 'base-tabs__item--active': pane.name === activeTab },
                { 'base-tabs__item--disabled': pane.disabled },
                { 'base-tabs__item--closable': pane.closable }
              ]"
              :id="`tab-${pane.name || index}`"
              :aria-controls="`pane-${pane.name || index}`"
              role="tab"
              :aria-selected="pane.name === activeTab"
              :tabindex="pane.name === activeTab ? 0 : -1"
              @click="handleTabClick(pane, index)"
              @keydown="handleTabKeydown"
            >
              <span class="base-tabs__item-label">
                {{ pane.label }}
              </span>
              <span 
                v-if="pane.closable"
                class="base-tabs__item-close"
                @click.stop="handleTabRemove(pane, index)"
              >
                ×
              </span>
            </div>
            <div 
              ref="activeBar"
              class="base-tabs__active-bar"
              :style="activeBarStyle"
            />
          </div>
        </div>
      </div>
    </div>
    
    <div class="base-tabs__content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseTabs',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: 'line',
      validator: (value) => ['line', 'card', 'border-card'].includes(value)
    },
    tabPosition: {
      type: String,
      default: 'top',
      validator: (value) => ['top', 'right', 'bottom', 'left'].includes(value)
    },
    stretch: {
      type: Boolean,
      default: false
    },
    beforeLeave: {
      type: Function,
      default: null
    },
    activeName: {
      type: [String, Number],
      default: ''
    }
  },
  emits: ['update:modelValue', 'tab-click', 'tab-remove', 'tab-add'],
  data() {
    return {
      panes: [],
      activeTab: this.modelValue || this.activeName,
      activeBarStyle: {}
    }
  },
  computed: {
    scrollable() {
      return this.panes.length > 6
    }
  },
  provide() {
    return {
      tabs: this,
      addPane: this.addPane,
      removePane: this.removePane
    }
  },
  watch: {
    modelValue(val) {
      this.activeTab = val
      this.updateActiveBar()
    },
    activeName(val) {
      this.activeTab = val
      this.updateActiveBar()
    },
    activeTab() {
      this.$nextTick(() => {
        this.updateActiveBar()
      })
    }
  },
  mounted() {
    this.updateActiveBar()
  },
  methods: {
    addPane(pane) {
      this.panes.push(pane)
      if (!this.activeTab && this.panes.length === 1) {
        this.activeTab = pane.name
      }
    },
    removePane(pane) {
      const index = this.panes.indexOf(pane)
      if (index > -1) {
        this.panes.splice(index, 1)
      }
    },
    handleTabClick(pane, index) {
      if (pane.disabled) return
      
      if (this.beforeLeave) {
        const shouldLeave = this.beforeLeave(pane.name, this.activeTab)
        if (shouldLeave === false) return
      }
      
      this.activeTab = pane.name
      this.$emit('update:modelValue', pane.name)
      this.$emit('tab-click', pane, index)
    },
    handleTabRemove(pane, index) {
      this.$emit('tab-remove', pane.name, index)
    },
    handleTabKeydown(event) {
      const { code } = event
      const tabs = this.$refs.nav.querySelectorAll('.base-tabs__item:not(.base-tabs__item--disabled)')
      const currentIndex = Array.from(tabs).indexOf(event.target)
      
      let nextIndex
      if (code === 'ArrowLeft' || code === 'ArrowUp') {
        nextIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1
      } else if (code === 'ArrowRight' || code === 'ArrowDown') {
        nextIndex = currentIndex === tabs.length - 1 ? 0 : currentIndex + 1
      }
      
      if (nextIndex !== undefined) {
        tabs[nextIndex].focus()
        tabs[nextIndex].click()
        event.preventDefault()
      }
    },
    updateActiveBar() {
      this.$nextTick(() => {
        const activeTab = this.$refs.nav?.querySelector('.base-tabs__item--active')
        if (!activeTab) return
        
        const { offsetLeft, offsetWidth } = activeTab
        this.activeBarStyle = {
          transform: `translateX(${offsetLeft}px)`,
          width: `${offsetWidth}px`
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.base-tabs {
  &__header {
    padding: 0;
    position: relative;
    margin: 0 0 15px 0;
  }
  
  &__nav-wrap {
    overflow: hidden;
    position: relative;
    
    &--scrollable {
      padding: 0 20px;
    }
  }
  
  &__nav-scroll {
    overflow: hidden;
  }
  
  &__nav {
    white-space: nowrap;
    position: relative;
    transition: transform 0.3s;
    float: left;
    z-index: 2;
    display: flex;
  }
  
  &__item {
    padding: 0 20px;
    height: 40px;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      color: #536ce6;
    }
    
    &--active {
      color: #536ce6;
    }
    
    &--disabled {
      color: #c0c4cc;
      cursor: not-allowed;
      
      &:hover {
        color: #c0c4cc;
      }
    }
    
    &--closable {
      padding-right: 8px;
    }
    
    &-label {
      display: inline-block;
    }
    
    &-close {
      margin-left: 8px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s ease;
      font-size: 16px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      display: inline-block;
      
      &:hover {
        background-color: #c0c4cc;
        color: #fff;
      }
    }
  }
  
  &__active-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background-color: #536ce6;
    z-index: 1;
    transition: all 0.3s ease;
    list-style: none;
  }
  
  &__content {
    overflow: hidden;
    position: relative;
  }
  
  // 卡片类型
  &--card {
    .base-tabs__header {
      border-bottom: 1px solid #e4e7ed;
    }
    
    .base-tabs__item {
      border: 1px solid transparent;
      border-bottom: none;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      
      &--active {
        background-color: #fff;
        border-color: #e4e7ed;
        border-bottom-color: #fff;
      }
    }
    
    .base-tabs__active-bar {
      display: none;
    }
  }
  
  // 边框卡片类型
  &--border-card {
    background: #fff;
    border: 1px solid #dcdfe6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    
    .base-tabs__header {
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      margin: 0;
    }
    
    .base-tabs__item {
      transition: all 0.3s ease;
      border: 1px solid transparent;
      margin-top: -1px;
      color: #909399;
      
      &:first-child {
        margin-left: -1px;
      }
      
      &--active {
        color: #536ce6;
        background-color: #fff;
        border-right-color: #dcdfe6;
        border-left-color: #dcdfe6;
      }
    }
    
    .base-tabs__content {
      padding: 15px;
    }
    
    .base-tabs__active-bar {
      display: none;
    }
  }
}
</style>
