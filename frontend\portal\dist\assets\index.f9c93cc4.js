/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
import{_ as a,h as s,o as e,d as t,f as o,j as r,e as i,g as l}from"./index.4f1b43e7.js";import n from"./header.fab778df.js";import f from"./menu.05579477.js";import"./ASD.492c8837.js";const u={class:"layout-page"},d={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},c=a(Object.assign({name:"Client"},{setup:a=>(a,c)=>{const p=s("router-view");return e(),t("div",u,[o("公共顶部菜单-"),r(n),i("div",d,[o("公共侧边栏菜单"),r(f),i("div",m,[o("主流程路由渲染点"),(e(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{c as default};
