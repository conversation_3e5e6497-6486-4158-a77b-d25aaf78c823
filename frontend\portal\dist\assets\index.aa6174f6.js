/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{x as j,r as d,D as ee,E as se,G as ne,b as te,u as oe,h as c,o as r,d as _,e as v,j as i,w as u,_ as ae,F as x,i as W,f as B,k as T,t as k,C as le,g as ce,H as re,I as ie,M as ue}from"./index.d0594432.js";const de=()=>j({url:"/console/v1/application/getuserapp",method:"get"});const pe={class:"person"},_e={class:"el-search"},ve={class:"category-title"},fe={class:"apps-container"},me={key:0,class:"status-badge"},be={class:"icon-wrapper"},ge={class:"tooltip-content text-center"},he={key:0},ke={key:1},ye={class:"app-info"},we={class:"app-name"},Se={name:"AppPage"},Ce=Object.assign(Se,{setup(xe){const y=d(""),E=d(null),b=d([]),g=d([]),O=d("1"),D=d(!1),M=d("standard"),F=ee([{key:"standard",label:"\u6807\u51C6\u89C6\u56FE"},{key:"compact",label:"\u7D27\u51D1\u89C6\u56FE"}]),m=d(null),h=d(!1),w=(s,e="success",n=3e3)=>{ue({message:s,type:e,duration:n})},L=()=>new Promise((s,e)=>{if(m.value&&m.value.readyState===WebSocket.OPEN){s(m.value);return}const n=new WebSocket("ws://localhost:50001");h.value=!0,n.onopen=()=>{console.log("WebSocket Connected"),m.value=n,h.value=!1,s(n)},n.onmessage=o=>{const t=o.data;t.startsWith("Ok")||t.startsWith("Failed")&&w(t,"error")},n.onclose=()=>{console.log("WebSocket Disconnected"),m.value=null,h.value=!1},n.onerror=o=>{console.error("WebSocket Error:",o),h.value=!1,e(o)},setTimeout(()=>{h.value&&(h.value=!1,n.close(),e(new Error("\u8FDE\u63A5\u8D85\u65F6")))},5e3)}),P=async s=>new Promise((e,n)=>{let o=!1,t;(async()=>{try{const f=await L(),p={action:3,msg:s};t=setTimeout(()=>{o||(f.close(),n(new Error("\u542F\u52A8\u8D85\u65F6\uFF1A\u672A\u6536\u5230\u54CD\u5E94")))},3e3),f.onmessage=I=>{o=!0,clearTimeout(t);const C=I.data;C.startsWith("Ok")?e():n(new Error(C))},f.send(JSON.stringify(p)),console.log("\u53D1\u9001\u6D88\u606F:",p)}catch(f){clearTimeout(t),n(f)}})()}),R=async s=>{if(!(!s.WebUrl||s.maint))if(s.WebUrl.toLowerCase().startsWith("cs:")){const e=s.WebUrl.substring(3);try{w("\u6B63\u5728\u542F\u52A8\u7231\u5C14\u4F01\u4E1A\u6D4F\u89C8\u5668...","info"),await P(e),w("\u542F\u52A8\u6210\u529F","success")}catch{w(`\u542F\u52A8\u4F01\u4E1A\u6D4F\u89C8\u5668\u5931\u8D25\uFF1A
      \u68C0\u67E5\u662F\u5426\u5DF2\u5B89\u88C5\u4F01\u4E1A\u6D4F\u89C8\u5668\uFF0C
      \u5982\u4ECD\u7136\u65E0\u6CD5\u542F\u52A8\uFF0C\u8BF7\u624B\u52A8\u8FD0\u884C\u4F01\u4E1A\u6D4F\u89C8\u5668\u8BBF\u95EE\u8BE5\u5E94\u7528\uFF01`,"warning",8e3)}}else window.open(s.WebUrl,"_blank")};se(()=>{m.value&&(m.value.close(),m.value=null)});const $=s=>{const e=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let n=0;for(let o=0;o<s.length;o++)n+=s.charCodeAt(o);return e[n%e.length]},q=()=>{D.value=!0},V=s=>{E.value=parseInt(s),s?g.value=b.value.filter(e=>e.id===parseInt(s)):g.value=b.value},z=()=>{if(!y.value){g.value=b.value;return}const s=y.value.toLowerCase().trim();g.value=b.value.map(e=>({...e,apps:e.apps.filter(n=>n.app_name.toLowerCase().includes(s))})).filter(e=>e.apps.length>0)},J=async()=>{try{const{data:s}=await de();if(console.log("API\u8FD4\u56DE\u6570\u636E:",s),s.code===0&&s.data){const e=s.data.map((n,o)=>({id:o+1,name:n.category,apps:n.apps.map(t=>({id:t.id,app_name:t.app_name,app_desc:t.app_type,icon:t.icon,maint:t.maintenance===2,app_type:t.app_type,app_sites:t.app_sites,WebUrl:t.WebUrl}))}));console.log("\u683C\u5F0F\u5316\u540E\u7684\u6570\u636E:",e),b.value=e,g.value=e,e.length>0&&(E.value=e[0].id,O.value=e[0].id.toString())}}catch(s){console.error("API\u8C03\u7528\u51FA\u9519:",s)}};ne(()=>{J()});const N=te(),H=oe().query,U=new XMLHttpRequest;U.open("GET",document.location,!1),U.send(null);const G=U.getResponseHeader("X-Corp-ID"),A={action:0,msg:{token:N.token.accessToken,refreshToken:N.token.refreshToken,realm:G||"default"},platform:document.location.hostname};{const s=H.wp||50001,e=d({}),n=d(`ws://127.0.0.1:${s}`),o=navigator.platform;(o.indexOf("Mac")===0||o==="MacIntel")&&(n.value=`wss://127.0.0.1:${s}`);const t=()=>{e.value=new WebSocket(n.value),e.value.onopen=()=>{console.log("socket\u8FDE\u63A5\u6210\u529F"),S(JSON.stringify(A))},e.value.onmessage=p=>{console.log(p),f()},e.value.onerror=()=>{console.log("socket\u8FDE\u63A5\u9519\u8BEF:"+n.value),window.location.href=`asecagent://?web=${JSON.stringify(A)}`}},S=p=>{console.log(p,"0"),e.value.send(p)},f=()=>{console.log("socket\u65AD\u5F00\u94FE\u63A5"),e.value.close()};console.log(`asecagent://?web=${JSON.stringify(A)}`),t()}return(s,e)=>{const n=c("base-input"),o=c("base-button"),t=c("base-option"),S=c("base-select"),f=c("el-header"),p=c("el-menu-item"),I=c("el-menu"),C=c("base-aside"),X=c("base-avatar"),Q=c("el-tooltip"),K=c("el-link"),Y=c("base-main"),Z=c("base-container");return r(),_("div",null,[v("div",pe,[i(f,null,{default:u(()=>[e[3]||(e[3]=v("span",{class:"el-title"},"\u6211\u7684\u5E94\u7528",-1)),v("span",_e,[i(n,{class:"el-search-input",modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>y.value=a),placeholder:"\u641C\u7D22\u5E94\u7528","prefix-icon":"Search",onInput:z,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),i(o,{class:"el-search-btn",icon:"Refresh",size:"small"}),i(S,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:M.value,"onUpdate:modelValue":e[1]||(e[1]=a=>M.value=a),placeholder:"Select",size:"small"},{default:u(()=>[(r(!0),_(x,null,W(F,a=>(r(),B(t,{key:a.key,label:a.label,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1,__:[3]}),i(Z,null,{default:u(()=>[i(C,{width:"96px",class:"category-aside"},{default:u(()=>[i(I,{class:"category-menu",onSelect:V,"default-active":O.value},{default:u(()=>[i(p,{index:"0",onClick:e[2]||(e[2]=a=>V(null))},{default:u(()=>e[4]||(e[4]=[T(" \u5168\u90E8 ")])),_:1,__:[4]}),(r(!0),_(x,null,W(b.value,a=>(r(),B(p,{key:a.id,index:a.id.toString()},{default:u(()=>[T(k(a.name),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])]),_:1}),i(Y,{class:"app-main"},{default:u(()=>[(r(!0),_(x,null,W(g.value,a=>(r(),_("div",{key:a.id,class:"category-section"},[v("h3",ve,k(a.name),1),v("div",fe,[(r(!0),_(x,null,W(a.apps,l=>(r(),_("div",{key:l.id,class:le(["app-card",{disabled:!l.WebUrl||l.maint}])},[l.maint?(r(),_("div",me," \u7EF4\u62A4\u4E2D ")):ce("",!0),i(K,{class:"app_list",underline:!1,disabled:!l.WebUrl||l.maint,onClick:re(De=>R(l),["prevent"])},{default:u(()=>[v("div",be,[i(Q,{effect:"light",placement:"bottom"},{content:u(()=>[v("div",ge,[l.WebUrl?(r(),_("span",he,k(l.WebUrl),1)):(r(),_("span",ke,"\u6682\u65E0\u8BBF\u95EE\u5730\u5740"))])]),default:u(()=>[i(X,{shape:"square",size:48,src:l.icon,onError:q,style:ie(!l.icon||D.value?`background-color: ${$(l.app_name)} !important`:"")},{default:u(()=>[T(k(!l.icon||D.value?l.app_name.slice(0,2):""),1)]),_:2},1032,["src","style"])]),_:2},1024)]),v("div",ye,[v("div",we,k(l.app_name),1),e[5]||(e[5]=v("div",{class:"app-remark"}," \u8FD9\u662F\u4E00\u6BB5\u5E94\u7528\u7A0B\u5E8F\u7684\u63CF\u8FF0\u4FE1\u606F\u3002 ",-1))])]),_:2},1032,["disabled","onClick"])],2))),128))])]))),128))]),_:1})]),_:1})])])}}}),Ae=ae(Ce,[["__scopeId","data-v-1225b8c9"]]);export{Ae as default};
