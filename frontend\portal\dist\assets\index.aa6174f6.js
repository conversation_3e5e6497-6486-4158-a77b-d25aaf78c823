/*! 
 Build based on gin-vue-admin 
 Time : 1749610601000 */
import{x as e,r as a,D as l,E as s,G as t,b as n,u as o,h as c,o as r,d as i,e as u,j as p,w as d,_ as v,F as m,i as g,f as b,k as f,t as k,C as y,g as h,H as _,I as w,M as W}from"./index.d0594432.js";const S={class:"person"},x={class:"el-search"},C={class:"category-title"},D={class:"apps-container"},U={key:0,class:"status-badge"},I={class:"icon-wrapper"},O={class:"tooltip-content text-center"},T={key:0},A={key:1},E={class:"app-info"},F={class:"app-name"},P=v(Object.assign({name:"AppPage"},{setup(v){const P=a(""),V=a(null),N=a([]),$=a([]),B=a("1"),J=a(!1),L=a("standard"),M=l([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),j=a(null),q=a(!1),z=(e,a="success",l=3e3)=>{W({message:e,type:a,duration:l})},H=async e=>new Promise(((a,l)=>{let s,t=!1;(async()=>{try{const n=await new Promise(((e,a)=>{if(j.value&&j.value.readyState===WebSocket.OPEN)return void e(j.value);const l=new WebSocket("ws://localhost:50001");q.value=!0,l.onopen=()=>{console.log("WebSocket Connected"),j.value=l,q.value=!1,e(l)},l.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&z(a,"error")},l.onclose=()=>{console.log("WebSocket Disconnected"),j.value=null,q.value=!1},l.onerror=e=>{console.error("WebSocket Error:",e),q.value=!1,a(e)},setTimeout((()=>{q.value&&(q.value=!1,l.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};s=setTimeout((()=>{t||(n.close(),l(new Error("启动超时：未收到响应")))}),3e3),n.onmessage=e=>{t=!0,clearTimeout(s);const n=e.data;n.startsWith("Ok")?a():l(new Error(n))},n.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(n){clearTimeout(s),l(n)}})()}));s((()=>{j.value&&(j.value.close(),j.value=null)}));const R=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let l=0;for(let s=0;s<e.length;s++)l+=e.charCodeAt(s);return a[l%a.length]},G=()=>{J.value=!0},X=e=>{V.value=parseInt(e),$.value=e?N.value.filter((a=>a.id===parseInt(e))):N.value},K=()=>{if(!P.value)return void($.value=N.value);const e=P.value.toLowerCase().trim();$.value=N.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))},Q=async()=>{try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(console.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",e),N.value=e,$.value=e,e.length>0&&(V.value=e[0].id,B.value=e[0].id.toString())}}catch(a){console.error("API调用出错:",a)}};t((()=>{Q()}));const Y=n(),Z=o().query,ee=new XMLHttpRequest;ee.open("GET",document.location,!1),ee.send(null);const ae=ee.getResponseHeader("X-Corp-ID"),le={action:0,msg:{token:Y.token.accessToken,refreshToken:Y.token.refreshToken,realm:ae||"default"},platform:document.location.hostname};{const e=Z.wp||50001,l=a({}),s=a(`ws://127.0.0.1:${e}`),t=navigator.platform;0!==t.indexOf("Mac")&&"MacIntel"!==t||(s.value=`wss://127.0.0.1:${e}`);const n=()=>{l.value=new WebSocket(s.value),l.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(le))},l.value.onmessage=e=>{console.log(e),c()},l.value.onerror=()=>{console.log("socket连接错误:"+s.value),window.location.href=`asecagent://?web=${JSON.stringify(le)}`}},o=e=>{console.log(e,"0"),l.value.send(e)},c=()=>{console.log("socket断开链接"),l.value.close()};console.log(`asecagent://?web=${JSON.stringify(le)}`),n()}return(e,a)=>{const l=c("base-input"),s=c("base-button"),t=c("base-option"),n=c("base-select"),o=c("el-header"),v=c("el-menu-item"),W=c("el-menu"),V=c("base-aside"),j=c("base-avatar"),q=c("el-tooltip"),Q=c("el-link"),Y=c("base-main"),Z=c("base-container");return r(),i("div",null,[u("div",S,[p(o,null,{default:d((()=>[a[3]||(a[3]=u("span",{class:"el-title"},"我的应用",-1)),u("span",x,[p(l,{class:"el-search-input",modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:K,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),p(s,{class:"el-search-btn",icon:"Refresh",size:"small"}),p(n,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:L.value,"onUpdate:modelValue":a[1]||(a[1]=e=>L.value=e),placeholder:"Select",size:"small"},{default:d((()=>[(r(!0),i(m,null,g(M,(e=>(r(),b(t,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])])),_:1,__:[3]}),p(Z,null,{default:d((()=>[p(V,{width:"96px",class:"category-aside"},{default:d((()=>[p(W,{class:"category-menu",onSelect:X,"default-active":B.value},{default:d((()=>[p(v,{index:"0",onClick:a[2]||(a[2]=e=>X(null))},{default:d((()=>a[4]||(a[4]=[f(" 全部 ")]))),_:1,__:[4]}),(r(!0),i(m,null,g(N.value,(e=>(r(),b(v,{key:e.id,index:e.id.toString()},{default:d((()=>[f(k(e.name),1)])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),p(Y,{class:"app-main"},{default:d((()=>[(r(!0),i(m,null,g($.value,(e=>(r(),i("div",{key:e.id,class:"category-section"},[u("h3",C,k(e.name),1),u("div",D,[(r(!0),i(m,null,g(e.apps,(e=>(r(),i("div",{key:e.id,class:y(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(r(),i("div",U," 维护中 ")):h("",!0),p(Q,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:_((a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const l=e.WebUrl.substring(3);try{z("正在启动爱尔企业浏览器...","info"),await H(l),z("启动成功","success")}catch(a){z("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)),["prevent"])},{default:d((()=>[u("div",I,[p(q,{effect:"light",placement:"bottom"},{content:d((()=>[u("div",O,[e.WebUrl?(r(),i("span",T,k(e.WebUrl),1)):(r(),i("span",A,"暂无访问地址"))])])),default:d((()=>[p(j,{shape:"square",size:48,src:e.icon,onError:G,style:w(!e.icon||J.value?`background-color: ${R(e.app_name)} !important`:"")},{default:d((()=>[f(k(!e.icon||J.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["src","style"])])),_:2},1024)]),u("div",E,[u("div",F,k(e.app_name),1),a[5]||(a[5]=u("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])])),_:2},1032,["disabled","onClick"])],2)))),128))])])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-1225b8c9"]]);export{P as default};
