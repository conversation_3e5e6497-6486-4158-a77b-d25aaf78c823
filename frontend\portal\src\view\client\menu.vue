<template>
  <div class="layout-aside">
    <ul class="menu-wrapper">
      <li 
        v-for="item in computedMenu" 
        :key="item.code" 
        :class="['menu-item', cutOut(item.code) === currentRouteCode ? 'active-menu-item' : '']"
        @click="changeMenu(item.url, item.params, item.code)"
      >
        <svg class="icon menu-item-icon" aria-hidden="true">
          <use :xlink:href="'#'+ item.icon" />
        </svg>
        <div class="menu-item-title">
          {{ item.name }}
        </div>
      </li>
    </ul>
  </div>
</template>
<script>

import menuList from '@/router/menu'
// 使用轻量级 SVG 图标，已在 main.js 中全局加载

export default {
  name: 'ClientMenu',
  data() {
    return {
      currentRouteCode: '101'
    }
  },
  computed: {
    computedMenu() {
      return this.computedMenuFun()
    },
  },
  mounted() {
    this.$router.push({ path: '/client/main', query: [] })
  },
  watch: {
    '$route': {
      handler(to, from) {
        logger.log('路由变化', to, from)
        if (to.meta && to.meta.code) {
          if (!_.get(to.meta, 'code')) {
            return
          }
          if (to.meta.code === this.currentRouteCode) {
            return
          }
          this.currentRouteCode = this.cutOut(to.meta.code)
        }
      },
      immediate: true
    }
  },

  methods: {
    // 计算菜单
    computedMenuFun() {
      const list = []
      if (menuList) {
        menuList.forEach(item => {
          if (item.meta && item.meta.menu) {
            const { name, icon, uiId } = item.meta.menu
            const sublistItem = { name, icon, code: item.meta.code, requiresAuth: item.meta.requiresAuth, url: item.path, params: item.params || [], uiId }
            list.push(sublistItem)
          }
        })
      }
      return list
    },

    // 切换菜单前做逻辑判断
    changeMenu(path, query = {}, code = 0) {
      logger.log("切换菜单:", path, query)
      this.$router.push({ path, query: query })
      this.currentRouteCode = this.cutOut(code)
    },
    routerInterceptor(path) {
      // 入网状态页点击进入认证、来宾认证提示
      const state = {
        next: false,
        stateMsg: '您好，系统正在检测您的网络环境，请稍候......'
      }
      state['next'] = true
      return state
    },
    cutOut(str) {
      if (str && str.length) {
        return str.substr(0, 3)
      }
      return str
    }
  }
}
</script>
<style lang="scss" scoped>
.layout-aside {
  width: 56px;
  height:100%;
  background: $menu-bg;
  overflow: auto;
  z-index: 10;

  .u-offlineTips {
    width: 100%;
    padding: 10px;
    background: #fceded;
    display: flex;
    justify-content: center;

    .off-tip-content {
      display: flex;
      line-height: 20px;
      font-size: 14px;
      color: rgba(230, 83, 83, 1);

      i {
        padding-right: 10px;
        font-size: 14px;
      }
    }
  }

  .menu-wrapper {
    padding-bottom: 60px;
    padding-top: 24px;
    margin: 0px;

    .menu-item {
      height: 65px;
      font-size: 13px;
      color: #686e84;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .menu-item-title {
        height: 17px;
        width: 24px;
        font-size: 12px;
        font-family: PingFang SC, PingFang SC-Medium;
        font-weight: Medium;
      }

      .menu-item-icon {
        transform: scaleY(-1);
        height: 18px;
        width: 18px;
        margin-bottom: 6px;
        fill: currentColor;
      }
    }

    .menu-item:hover {
      background: $default-bg;
      color: #536ce6;
      border-radius: 4px;
      cursor: pointer;

      .iconfont {
        color: $--color-primary;
      }
    }

    .active-menu-item {
      border-radius: 4px;
      color: #536ce6;
    }

    .active-menu-item:hover {
      border-radius: 4px;
    }
  }

  .version-wrapper {
    position: fixed;
    bottom: 1px;
    left: 1px;
    width: 200px;
    background: $menu-bg;
    font-size: 12px;
    line-height: 33px;
    text-align: center;
    color: #B3B6C1;
    z-index: 11;
  }
}</style>
