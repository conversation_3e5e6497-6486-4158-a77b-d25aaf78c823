/*! 
 Build based on gin-vue-admin 
 Time : 1749610601000 */
import{S as e,h as a,o as s,d as n,j as t,w as o,T as u,f as l,W as r,m as d,A as i}from"./index.d0594432.js";const m=Object.assign({name:"RouterHolder"},{setup(m){const c=e();return(e,m)=>{const f=a("router-view");return s(),n("div",null,[t(f,null,{default:o((({Component:e})=>[t(u,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(s(),l(r,{include:d(c).keepAliveRouters},[(s(),l(i(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{m as default};
