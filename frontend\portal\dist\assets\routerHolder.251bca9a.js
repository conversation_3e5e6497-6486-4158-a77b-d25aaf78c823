/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{S as l,h as u,o as e,d as c,j as o,w as t,T as i,f as n,W as _,m as d,A as m}from"./index.d0594432.js";const p={name:"RouterHolder"},h=Object.assign(p,{setup(f){const a=l();return(v,k)=>{const r=u("router-view");return e(),c("div",null,[o(r,null,{default:t(({Component:s})=>[o(i,{mode:"out-in",name:"el-fade-in-linear"},{default:t(()=>[(e(),n(_,{include:d(a).keepAliveRouters},[(e(),n(m(s)))],1032,["include"]))]),_:2},1024)]),_:1})])}}});export{h as default};
