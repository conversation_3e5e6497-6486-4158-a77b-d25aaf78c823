/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
import{_ as a,h as e,o as s,d as t,f as o,j as r,e as i,g as l}from"./index.342352cf.js";import n from"./header.3f118f7c.js";import c from"./menu.eb28e775.js";import"./ASD.492c8837.js";const f={class:"layout-page"},u={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},d=a(Object.assign({name:"Client"},{setup:a=>(a,d)=>{const p=e("router-view");return s(),t("div",f,[o("公共顶部菜单-"),r(n),i("div",u,[o("公共侧边栏菜单"),r(c),i("div",m,[o("主流程路由渲染点"),(s(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{d as default};
