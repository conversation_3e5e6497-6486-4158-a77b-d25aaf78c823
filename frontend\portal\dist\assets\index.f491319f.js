/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
import{x as e,_ as a,y as l,u as t,r as n,c as i,b as s,z as u,p as o,o as r,d as v,e as c,f as d,j as p,m,F as h,k as y,t as g,g as f,A as _,B as x,i as w,C as k,L as C,D as b,E as L}from"./index.e2f48a61.js";const T={class:"login-page"},P={class:"content"},E={class:"right-panel"},O={key:0},I={key:0,class:"title"},j={key:1,class:"title"},q={style:{"text-align":"center"}},R={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},S={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},A=["xlink:href"],D={key:2,class:"login_panel_form"},V={key:3,class:"auth-switcher"},U={class:"auth-switcher-container"},$=["disabled"],K={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},N={class:"auth-methods-wrapper"},z=["onClick"],H=["data-auth-type"],J={class:"icon","aria-hidden":"true",style:{height:"18px",width:"18px"}},M=["xlink:href"],W={class:"auth-method-name"},B=["disabled"],F={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},X={class:"auth-waiting"},G={class:"waiting-icon"},Q={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},Y=["xlink:href"],Z={class:"waiting-title"},ee={class:"security-tips"},ae={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px",color:"#67c23a"}},le=a(Object.assign({name:"Login"},{setup(a){const le=l({loader:()=>k((()=>import("./localLogin.cc63235b.js")),["./localLogin.cc63235b.js","./index.e2f48a61.js","./index.97481913.css","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=l({loader:()=>k((()=>import("./wechat.9465dcdd.js")),["./wechat.9465dcdd.js","./index.e2f48a61.js","./index.97481913.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=l({loader:()=>k((()=>import("./feishu.d3e601d4.js")),["./feishu.d3e601d4.js","./index.e2f48a61.js","./index.97481913.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ie=l({loader:()=>k((()=>import("./dingtalk.319bbae2.js")),["./dingtalk.319bbae2.js","./index.e2f48a61.js","./index.97481913.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=l({loader:()=>k((()=>import("./oauth2.d08b991d.js")),["./oauth2.d08b991d.js","./index.e2f48a61.js","./index.97481913.css","./oauth2.03d0b5c4.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=l({loader:()=>k((()=>import("./sms.94b711fe.js")),["./sms.94b711fe.js","./index.e2f48a61.js","./index.97481913.css","./sms.844b2c56.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),oe=l({loader:()=>k((()=>import("./secondaryAuth.5e5b2ac1.js")),["./secondaryAuth.5e5b2ac1.js","./index.e2f48a61.js","./index.97481913.css","./verifyCode.731f1e88.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),re=l({loader:()=>k((()=>import("./serverConfig.799c6b81.js")),["./serverConfig.799c6b81.js","./index.e2f48a61.js","./index.97481913.css","./serverConfig.7b10f103.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ve=t(),ce=n(0),de=n([]),pe=n("local"),me=n(""),he=n(""),ye=n(""),ge=n([]),fe=n([]),_e=n(!1),xe=n(!1),we=n(),ke=n(""),Ce=n(!1),be=n(""),Le=n(!1),Te=n(""),Pe=n(""),Ee=n(""),Oe=n({}),Ie=n(0),je=n(80),qe=n(3),Re=n(null),Se=i((()=>{const e=_e.value?Te.value:he.value;return de.value.filter((a=>a.id!==e))})),Ae=i((()=>Math.max(0,Se.value.length-qe.value))),De=s();i((()=>fe.value.filter((e=>e.id!==he.value))));const Ve=e=>{logger.log("服务器配置完成:",e),xe.value=!1,Ue()},Ue=async()=>{var a,l,t,n,i,s,u,o,r,v,c,d;try{if((()=>{if(b.isClient()){let a=urlHashParams?urlHashParams.get("WebUrl"):"";try{const e=new URL(a);a=`${e.protocol}//${e.host}`}catch(e){a="",console.warn("解析 WebUrl 参数失败:",e)}if(a)return!1;if(!localStorage.getItem("server_host"))return!0}return!1})())return void(xe.value=!0);const p=(()=>{const e={};if(ve.query.type&&(e.type=ve.query.type),ve.query.wp&&(e.wp=ve.query.wp),ve.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(ve.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){de.value=m.data.idpList;const e=ve.query.idp_id||De.loginType;if(e&&"undefined"!==e){let u=!1;for(const a of m.data.idpList)e===a.id&&(u=!0,he.value=a.id,pe.value=a.type,me.value=a.templateType,ge.value=a.attrs,ge.value.name=a.name,ge.value.authType=a.type);u||(ye.value=null==(a=de.value[0])?void 0:a.id,he.value=null==(l=de.value[0])?void 0:l.id,pe.value=null==(t=de.value[0])?void 0:t.type,me.value=null==(n=de.value[0])?void 0:n.templateType,ge.value=null==(i=de.value[0])?void 0:i.attrs,ge.value.name=de.value[0].name,ge.value.authType=null==(s=de.value[0])?void 0:s.type)}else ye.value=null==(u=de.value[0])?void 0:u.id,he.value=null==(o=de.value[0])?void 0:o.id,pe.value=null==(r=de.value[0])?void 0:r.type,me.value=null==(v=de.value[0])?void 0:v.templateType,ge.value=null==(c=de.value[0])?void 0:c.attrs,ge.value.name=de.value[0].name,ge.value.authType=null==(d=de.value[0])?void 0:d.type;++ce.value}}catch(p){console.error("获取认证列表失败:",p),b.isClient()&&(xe.value=!0)}};Ue();const $e=i((()=>{switch(pe.value){case"local":case"msad":case"ldap":case"web":case"email":return le;case"qiyewx":return te;case"feishu":return ne;case"dingtalk":return ie;case"oauth2":case"cas":return se;case"sms":return ue;default:return"oauth2"===me.value?se:"local"}})),Ke=i((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===be.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===be.value}])),Ne=()=>{_e.value=!1,fe.value=[],we.value="",ke.value="",be.value="",Le.value=!1,Te.value&&(he.value=Te.value,pe.value=Pe.value,me.value=Ee.value,ge.value={...Oe.value},Te.value="",Pe.value="",Ee.value="",Oe.value={}),++ce.value,console.log("取消后恢复的状态:",{isSecondary:_e.value,auth_id:he.value,auth_type:pe.value})},ze=async e=>{const a=C.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=ve.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},He=i((()=>!["dingtalk","feishu","qiyewx"].includes(pe.value)&&("oauth2"!==me.value&&"cas"!==pe.value||("cas"===pe.value?1===parseInt(ge.value.casOpenType):"oauth2"===me.value&&1===parseInt(ge.value.oauth2OpenType))))),Je=()=>{Ie.value>0&&Ie.value--},Me=()=>{Ie.value<Ae.value&&Ie.value++},We=e=>L[e]||"user",Be=e=>{ye.value=e.id,ge.value=e.attrs||{},ge.value.name=e.name,ge.value.authType=e.type,_e.value&&(ge.value.uniqKey=we.value,ge.value.notPhone=Ce.value),he.value=e.id,pe.value=e.type,me.value=e.templateType,++ce.value};return u(_e,(async()=>{_e.value&&(Te.value=he.value,Pe.value=pe.value,Ee.value=me.value,Oe.value={...ge.value},console.log("二次认证数据:",{secondary:fe.value,secondaryLength:fe.value.length}),fe.value.length>0&&Be(fe.value[0]))})),o("secondary",fe),o("isSecondary",_e),o("uniqKey",we),o("userName",ke),o("notPhone",Ce),o("last_id",ye),o("contactType",be),o("hasContactInfo",Le),(e,a)=>(r(),v("div",T,[c("div",P,[a[6]||(a[6]=c("div",{class:"left-panel"},[d(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),d('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),d(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),c("div",E,[d(" 服务器配置状态 "),xe.value?(r(),v("div",O,[p(m(re),{onServerConfigured:Ve})])):_e.value?(r(),v(h,{key:2},[d(" 二次认证等待状态 "),c("div",X,[c("div",G,[(r(),v("svg",Q,[c("use",{"xlink:href":`#icon-${We(Pe.value||pe.value)}`},null,8,Y)]))]),c("h4",Z,g(Oe.value.name||ge.value.name)+" 登录成功",1),a[5]||(a[5]=c("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),c("div",ee,[(r(),v("svg",ae,a[3]||(a[3]=[c("use",{"xlink:href":"#icon-shield"},null,-1)]))),a[4]||(a[4]=c("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(r(),v(h,{key:1},[d(" 正常登录状态 "),c("div",null,["local"===pe.value?(r(),v("span",I,"本地账号登录")):He.value?(r(),v("span",j,[c("div",q,[c("span",R,[(r(),v("svg",S,[c("use",{"xlink:href":"#icon-auth-"+pe.value},null,8,A)])),y(" "+g(ge.value.name),1)])])])):d("v-if",!0),he.value?(r(),v("div",D,[d(' <component :is="getLoginType"></component> '),(r(),f(_($e.value),{auth_id:he.value,auth_info:ge.value},null,8,["auth_id","auth_info"])),d(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):d("v-if",!0),Se.value.length>0?(r(),v("div",V,[a[2]||(a[2]=c("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),c("div",U,[Ie.value>0?(r(),v("button",{key:0,class:"auth-nav-btn auth-nav-prev",onClick:Je,disabled:0===Ie.value},[(r(),v("svg",K,a[0]||(a[0]=[c("use",{"xlink:href":"#icon-chevron-left"},null,-1)])))],8,$)):d("v-if",!0),c("div",N,[c("div",{class:"auth-methods-container",ref_key:"authMethodsContainer",ref:Re,style:x({transform:`translateX(-${Ie.value*je.value}px)`})},[(r(!0),v(h,null,w(Se.value,(e=>(r(),v("div",{key:e.id,class:"auth-method-item",onClick:a=>Be(e)},[c("div",{class:"auth-method-icon","data-auth-type":e.type},[(r(),v("svg",J,[c("use",{"xlink:href":`#icon-${We(e.type)}`},null,8,M)]))],8,H),c("div",W,g(e.name),1)],8,z)))),128))],4)]),Ie.value<Ae.value?(r(),v("button",{key:1,class:"auth-nav-btn auth-nav-next",onClick:Me,disabled:Ie.value>=Ae.value},[(r(),v("svg",F,a[1]||(a[1]=[c("use",{"xlink:href":"#icon-chevron-right"},null,-1)])))],8,B)):d("v-if",!0)])])):d("v-if",!0)])],2112))])]),d(" 二次认证弹窗 "),_e.value?(r(),f(m(oe),{key:0,"auth-info":{uniqKey:we.value,contactType:be.value,hasContactInfo:Le.value},"auth-id":he.value,"user-name":ke.value,"last-id":ye.value,"auth-methods":Ke.value,onVerificationSuccess:ze,onCancel:Ne},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):d("v-if",!0)]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]);export{le as default};
