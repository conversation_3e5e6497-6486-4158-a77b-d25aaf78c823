/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
import{_ as e,u as a,a as r,b as t,r as s,O as i,p as c,f as l,L as o,M as n}from"./index.e2f48a61.js";const u=e(Object.assign({name:"WxOAuthCallback"},{setup(e){const u=a(),p=r(),y=t(),{code:d,state:_,auth_type:f,redirect_url:h}=u.query,m=s(Array.isArray(_)?_[0]:_),x=s("");return i((async()=>{const e=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(e,"qiyewx_oauth",m.value)?await p.push({name:"verify",query:{redirect_url:h}}):n.error("登录失败，请重试")}catch(a){console.error("登录过程出错:",a),n.error("登录过程出错，请重试")}finally{e.close()}})),c("userName",x),(e,a)=>l(" 空模板，因为所有逻辑都在 script 中处理 ")}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wx_oauth_callback.vue"]]);export{u as default};
