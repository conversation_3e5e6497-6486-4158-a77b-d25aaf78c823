import { useUserStore } from '@/pinia/modules/user'
import { useRouterStore } from '@/pinia/modules/router'
import getPageTitle from '@/utils/page'
import router from '@/router'
import Nprogress from 'nprogress'
import startRefreshToken from '@/utils/timer'
import stopRefreshToken from '@/utils/timer'
import { ref } from 'vue'
import { refreshToken } from '@/api/token'
import agentApi from '@/api/agentApi'
import { routerClientBefore } from '@/clientPermission'

let asyncRouterFlag = 0

const whiteList = ['Login', 'Init', 'ClientLogin', 'Status', 'downloadWin', 'WxOAuthCallback', 'OAuth2Result', 'OAuth2Premises']

const getRouter = async(userStore) => {
  logger.log('----getRouter---')
  const routerStore = useRouterStore()
  await routerStore.SetAsyncRouter()
  await userStore.GetUserInfo()
  //   start(userStore.LoginOut, userStore.setToken)
  const asyncRouters = routerStore.asyncRouters
  asyncRouters.forEach(asyncRouter => {
    router.addRoute(asyncRouter)
  })
}

async function handleKeepAlive(to) {
  if (to.matched.some(item => item.meta.keepAlive)) {
    if (to.matched && to.matched.length > 2) {
      for (let i = 1; i < to.matched.length; i++) {
        const element = to.matched[i - 1]
        if (element.name === 'layout') {
          to.matched.splice(i, 1)
          await handleKeepAlive(to)
        }
        // 如果没有按需加载完成则等待加载
        if (typeof element.components.default === 'function') {
          await element.components.default()
          await handleKeepAlive(to)
        }
      }
    }
  }
}

const scoketToken = (userStore) => {
  logger.log('socket连接开始')
  return new Promise((resolve, reject) => {
    const clineData = {
      action: 2,
      msg: '',
      platform: document.location.hostname
    }
    const websocket = ref({})
    const wsUrl = ref('ws://127.0.0.1:50001')
    const platform = navigator.platform
    if (platform.indexOf('Mac') === 0 || platform === 'MacIntel') {
      wsUrl.value = 'wss://127.0.0.1:50001'
    }
    const initWebSocket = async() => {
      websocket.value = new WebSocket(wsUrl.value)
      let timeoutId // 超时计时器ID

      const startTimeout = () => {
        timeoutId = setTimeout(() => {
          console.log('WebSocket连接超时')
          closeWebSocket() // 关闭WebSocket连接
          resolve()
        }, 2000) // 超时时间为2秒
      }
      websocket.value.onopen = () => {
        logger.log('socket连接成功')
        startTimeout()
        sendMessage(JSON.stringify(clineData))
      }
      websocket.value.onmessage = async(e) => {
        logger.log('-------e--------')
        logger.log(JSON.parse(e.data))
        clearTimeout(timeoutId)
        if (e?.data) {
          try {
            const tokenInfo = JSON.parse(e.data)
            if (!tokenInfo.msg.token) {
              resolve()
              return
            }
            const info = {
              accessToken: tokenInfo.msg.token,
              expireIn: 3600,
              refreshToken: tokenInfo.msg.refreshToken,
              refreshExpireIn: 604800,
              tokenType: 'Bearer'
            }
            await userStore.setToken(info)
            const reftoken = await refreshToken()
            if (reftoken.status === 200) {
              if (reftoken?.data?.code || reftoken?.data?.code !== -1) {
                await userStore.setToken(reftoken.data)
                await userStore.GetUserInfo()
                resolve()
              }
              resolve()
            }
            resolve()
          } catch (e) {
            await closeWebSocket()
            resolve()
          }
        }
        await closeWebSocket()
        resolve()
      }
      websocket.value.onerror = () => {
        console.log('socket连接错误')
        clearTimeout(timeoutId)
        resolve()
      }
    }
    // 发送消息
    const sendMessage = (msg) => {
      websocket.value.send(msg)
    }
    // 关闭链接（在页面销毁时可销毁链接）
    const closeWebSocket = () => {
      logger.log('socket断开链接')
      websocket.value.close()
    }
    logger.log(`asecagent://?web=${JSON.stringify(clineData)}`)
    initWebSocket()
  })
}

router.beforeEach(async(to, from) => {
  Nprogress.start()
  // 如果是客户端则直接返回
  if (agentApi.isClient()) {
    return routerClientBefore(to, from)
  }
  const userStore = useUserStore()
  to.meta.matched = [...to.matched]
  await handleKeepAlive(to)
  let token = userStore.token
  // 在白名单中的判断情况
  document.title = getPageTitle(to.meta.title, to)

  if (to.name == 'WxOAuthCallback' || to.name == 'verify') {
    document.title = ''
  } else {
    document.title = getPageTitle(to.meta.title, to)
  }
  // logger.log('to.name')
  logger.log('路由参数：', { whiteList: whiteList, to: to, from: from })
  // logger.log(to.name)
  const refresh_times = window.localStorage.getItem('refresh_times') || 0
  if ((!token || token === '""') && Number(refresh_times) < 5 && to.name !== 'Login') {
    await scoketToken(userStore)
    token = userStore.token
  }
//    if (whiteList.includes(to.name)) {
//     if (token && to.name != "downloadWin") {
  if (whiteList.includes(to.name)) {
    if (token && !['downloadWin', 'Login', 'WxOAuthCallback','OAuth2Callback'].includes(to.name)) {        
      if (!asyncRouterFlag && whiteList.indexOf(from.name) < 0) {
        asyncRouterFlag++
        await getRouter(userStore)
        logger.log('getRouter')
      }
      // token 可以解析但是却是不存在的用户 id 或角色 id 会导致无限调用
      if (userStore.userInfo) {
        logger.log('dashboard')
        return { name: 'dashboard' }
      } else {
        stopRefreshToken()
        // 强制退出账号
        await userStore.ClearStorage()
        logger.log('强制退出账号')
        return {
          name: 'Login',
          query: {
            redirect: document.location.hash,
          },
        }
      }
    } else {
      logger.log('直接返回')
      return true
    }
  } else {
    // 不在白名单中并且已经登录的时候
    // logger.log('permission token')
    logger.log('不在白名单中:', token)
    if (token) {
      // logger.log('permission token')
      // logger.log(token)
      // 添加flag防止多次获取动态路由和栈溢出
      // logger.log(asyncRouterFlag)
      // logger.log(whiteList)
      // logger.log(from.name)
      if (!asyncRouterFlag && whiteList.indexOf(from.name) < 0) {
        asyncRouterFlag++
        await getRouter(userStore)
        // logger.log('userStore.token')
        logger.log("初始化动态路由:", userStore.token)
        // logger.log('to')
        // logger.log(to)
        if (userStore.token) {
          logger.log('返回to')
          return { ...to, replace: false }
        } else {
          logger.log('返回login')
          return {
            name: 'Login',
            query: { redirect: to.href },
          }
        }
      } else {
        if (to.matched.length) {
          startRefreshToken(userStore.LoginOut, userStore.setToken)
          logger.log('返回refresh')
          return true
        } else {
          // logger.log(404)
          console.log('404:', to.matched)
          return { path: '/layout/404' }
        }
      }
    } else {
      logger.log('不在白名单中并且未登录的时候')
      return {
        name: 'Login',
        query: {
          redirect: document.location.hash,
        },
      }
    }
  }
  logger.log('正常返回')
})

router.afterEach(() => {
  // 路由加载完成后关闭进度条
  Nprogress.done()
})

router.onError(() => {
  // 路由发生错误后销毁进度条
  Nprogress.remove()
})
