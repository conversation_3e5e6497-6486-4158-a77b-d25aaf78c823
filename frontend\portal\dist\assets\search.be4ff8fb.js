/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as C,a as I,S,r as t,h as _,o as e,d as a,j as i,w as h,O as B,e as n,F as V,i as R,m as T,f as N,R as j,T as q,C as F,g as c,U as L,K as O}from"./index.d0594432.js";import U from"./index.5c2ee242.js";const z={class:"search-component"},D={class:"transition-box",style:{display:"inline-block"}},E={key:0,class:"user-box"},K={key:1,class:"user-box"},A={key:2,class:"user-box"},G={key:3,class:"user-box"},H={name:"BtnBox"},J=Object.assign(H,{setup(M){const p=I(),f=S(),l=t(""),m=()=>{p.push({name:l.value}),l.value=""},r=t(!1),s=t(!0),b=()=>{r.value=!1,setTimeout(()=>{s.value=!0},500)},v=t(null),k=async()=>{s.value=!1,r.value=!0,await L(),v.value.focus()},u=t(!1),g=()=>{u.value=!0,O.emit("reload"),setTimeout(()=>{u.value=!1},500)},x=()=>{window.open("https://support.qq.com/product/371961")};return(P,d)=>{const y=_("base-option"),w=_("base-select");return e(),a("div",z,[i(q,{name:"el-fade-in-linear"},{default:h(()=>[B(n("div",D,[i(w,{ref_key:"searchInput",ref:v,modelValue:l.value,"onUpdate:modelValue":d[0]||(d[0]=o=>l.value=o),filterable:"",placeholder:"\u8BF7\u9009\u62E9",onBlur:b,onChange:m},{default:h(()=>[(e(!0),a(V,null,R(T(f).routerList,o=>(e(),N(y,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])],512),[[j,r.value]])]),_:1}),s.value?(e(),a("div",E,[n("div",{class:F(["gvaIcon gvaIcon-refresh",[u.value?"reloading":""]]),onClick:g},null,2)])):c("",!0),s.value?(e(),a("div",K,[n("div",{class:"gvaIcon gvaIcon-search",onClick:k})])):c("",!0),s.value?(e(),a("div",A,[i(U,{class:"search-icon",style:{cursor:"pointer"}})])):c("",!0),s.value?(e(),a("div",G,[n("div",{class:"gvaIcon gvaIcon-customer-service",onClick:x})])):c("",!0)])}}}),X=C(J,[["__scopeId","data-v-97ccbcef"]]);export{X as default};
