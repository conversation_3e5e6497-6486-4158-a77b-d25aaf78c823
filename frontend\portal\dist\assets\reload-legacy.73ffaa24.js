/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
System.register(["./index-legacy.8748bb61.js"],(function(e,t){"use strict";var r,n,o,u;return{setters:[function(e){r=e._,n=e.a,o=e.o,u=e.d}],execute:function(){var t=Object.assign({name:"Reload"},{setup:function(e){return n().go(-1),function(e,t){return o(),u("div")}}});e("default",r(t,[["__file","D:/asec-platform/frontend/portal/src/view/error/reload.vue"]]))}}}));
