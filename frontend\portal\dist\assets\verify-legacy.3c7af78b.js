/*! 
 Build based on gin-vue-admin 
 Time : 1749604390000 */
System.register(["./index-legacy.0e039e9c.js"],(function(t,e){"use strict";var n,r,c,o;return{setters:[function(t){n=t.b,r=t.q,c=t.o,o=t.d}],execute:function(){t("default",Object.assign({name:"Verify"},{setup:function(t){var e=location.href.split("?")[1],i=new URLSearchParams(e),a=Object.fromEntries(i.entries()),s=n(),u=document.location.protocol+"//"+document.location.host,l=new URLSearchParams;"client"===a.type&&(l.set("type","client"),a.wp&&l.set("wp",a.wp));var f={method:"GET",url:"".concat(u,"/auth/user/v1/redirect_verify?redirect_url=").concat(a.redirect_url),headers:{Accept:"application/json, text/plain, */*",Authorization:"".concat(s.token.tokenType," ").concat(s.token.accessToken)}};return r.request(f).then((function(t){if(200===t.status){var e=t.data.url;if(l.toString()){var n=e.includes("?")?"&":"?";e+=n+l.toString()}window.location.href=e}})).catch((function(t){console.error(t)})),function(t,e){return c(),o("div")}}}))}}}));
