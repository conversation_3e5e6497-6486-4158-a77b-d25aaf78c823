<template>
  <base-sub-menu ref="subMenu" :index="routerInfo.name">

    <template #title>
      <div v-if="!isCollapse" class="gva-subMenu">
        <i v-if="routerInfo.meta.icon" class="iconfont" :class="routerInfo.meta.icon"></i>
        <span>{{ routerInfo.meta.title }}</span>
      </div>
      <template v-else>
        <i v-if="routerInfo.meta.icon" class="iconfont" :class="routerInfo.meta.icon"></i>
        <span>{{ routerInfo.meta.title }}</span>
      </template>
    </template>
    <slot/>
  </base-sub-menu>
</template>

<script>
export default {
  name: 'AsyncSubmenu',
}
</script>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  routerInfo: {
    default: function() {
      return null
    },
    type: Object,
  },
  isCollapse: {
    default: function() {
      return false
    },
    type: <PERSON><PERSON><PERSON>,
  },
  theme: {
    default: function() {
      return {}
    },
    type: Object,
  },
})

const activeBackground = ref(props.theme.activeBackground)
const activeText = ref(props.theme.activeText)
const normalText = ref(props.theme.normalText)
// const hoverBackground = ref(props.theme.hoverBackground)
// const hoverText = ref(props.theme.hoverText)

watch(() => props.theme, () => {
  activeBackground.value = props.theme.activeBackground
  activeText.value = props.theme.activeText
  normalText.value = props.theme.normalText
  // hoverBackground.value = props.theme.hoverBackground
  // hoverText.value = props.theme.hoverText
})

</script>

<style lang="scss" scoped>
:deep(.base-sub-menu) {
  :deep(.base-sub-menu__title) {
    padding: 6px;
    color: v-bind(normalText);
    color: rgba(255, 255, 255, 0.674509803921569);
    .base-sub-menu__icon {
      transform: rotate(0deg);
    }
  }
}

:deep(.base-sub-menu:not(.base-sub-menu--opened)) {
  :deep(.base-sub-menu__title) {
    .base-sub-menu__icon {
      transform: rotate(-90deg);
    }
  }
}

:deep(.base-sub-menu--active:not(.base-sub-menu--opened)) {
  :deep(.base-sub-menu__title) {
    padding-left: 18px !important;
    flex: 1;
    opacity: 100%;
    height: 40px;
    line-height: 40px;
    border-left: 4px #71BDDF solid;
    background: #465566 !important;
    border-radius: 4px;
    .base-sub-menu__icon {
      transform: rotate(-90deg);
    }
    i {
      color: v-bind(activeText);
    }

    span {
      opacity: 100%;
      color: v-bind(activeText);
    }
  }

  & {
    background: #465566 !important;
  }
}

</style>
