{"name": "@babel/standalone", "version": "7.27.6", "description": "Standalone build of Babel for use in non-Node.js environments.", "main": "./babel.js", "files": ["./babel.js", "./babel.js.map", "./babel.min.js", "./babel.min.js.map"], "devDependencies": {"@babel/core": "^7.27.4", "@babel/generator": "^7.27.5", "@babel/parser": "^7.27.5", "@babel/plugin-external-helpers": "^7.27.1", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-proposal-destructuring-private": "^7.27.1", "@babel/plugin-proposal-do-expressions": "^7.27.1", "@babel/plugin-proposal-export-default-from": "^7.27.1", "@babel/plugin-proposal-function-bind": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-proposal-import-defer": "^7.27.1", "@babel/plugin-proposal-optional-chaining-assign": "^7.27.1", "@babel/plugin-proposal-pipeline-operator": "^7.27.1", "@babel/plugin-proposal-record-and-tuple": "^7.27.1", "@babel/plugin-proposal-throw-expressions": "^7.27.1", "@babel/plugin-syntax-decimal": "^7.24.7", "@babel/plugin-syntax-decorators": "^7.27.1", "@babel/plugin-syntax-destructuring-private": "^7.27.1", "@babel/plugin-syntax-do-expressions": "^7.27.1", "@babel/plugin-syntax-explicit-resource-management": "^7.27.1", "@babel/plugin-syntax-export-default-from": "^7.27.1", "@babel/plugin-syntax-flow": "^7.27.1", "@babel/plugin-syntax-function-bind": "^7.27.1", "@babel/plugin-syntax-function-sent": "^7.27.1", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/plugin-syntax-import-reflection": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-syntax-module-blocks": "^7.27.1", "@babel/plugin-syntax-optional-chaining-assign": "^7.27.1", "@babel/plugin-syntax-pipeline-operator": "^7.27.1", "@babel/plugin-syntax-record-and-tuple": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-generator-functions": "^7.27.1", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-block-scoped-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.5", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-computed-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.3", "@babel/plugin-transform-dotall-regex": "^7.27.1", "@babel/plugin-transform-duplicate-keys": "^7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-dynamic-import": "^7.27.1", "@babel/plugin-transform-explicit-resource-management": "^7.27.5", "@babel/plugin-transform-exponentiation-operator": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-flow-comments": "^7.27.3", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-function-name": "^7.27.1", "@babel/plugin-transform-instanceof": "^7.27.1", "@babel/plugin-transform-jscript": "^7.27.1", "@babel/plugin-transform-json-modules": "^7.27.1", "@babel/plugin-transform-json-strings": "^7.27.1", "@babel/plugin-transform-literals": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-member-expression-literals": "^7.27.1", "@babel/plugin-transform-modules-amd": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-modules-systemjs": "^7.27.1", "@babel/plugin-transform-modules-umd": "^7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-new-target": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-assign": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.3", "@babel/plugin-transform-object-set-prototype-of-to-assign": "^7.27.1", "@babel/plugin-transform-object-super": "^7.27.1", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-property-literals": "^7.27.1", "@babel/plugin-transform-property-mutators": "^7.27.1", "@babel/plugin-transform-proto-to-assign": "^7.27.1", "@babel/plugin-transform-react-constant-elements": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-inline-elements": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx-compat": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@babel/plugin-transform-regenerator": "^7.27.5", "@babel/plugin-transform-regexp-modifiers": "^7.27.1", "@babel/plugin-transform-reserved-words": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-spread": "^7.27.1", "@babel/plugin-transform-sticky-regex": "^7.27.1", "@babel/plugin-transform-strict-mode": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/plugin-transform-typeof-symbol": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1", "@babel/plugin-transform-unicode-escapes": "^7.27.1", "@babel/plugin-transform-unicode-property-regex": "^7.27.1", "@babel/plugin-transform-unicode-regex": "^7.27.1", "@babel/plugin-transform-unicode-sets-regex": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-flow": "^7.27.1", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.4", "@babel/types": "^7.27.6", "acorn": "^8.11.3", "jsdom": "^22.1.0"}, "keywords": ["babel", "babe<PERSON><PERSON><PERSON>", "6to5", "transpile", "transpiler"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20standalone%22+is%3Aopen"}, "homepage": "https://babel.dev/docs/en/next/babel-standalone", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-standalone"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}