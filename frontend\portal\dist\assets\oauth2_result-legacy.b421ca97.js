/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function u(t,a,o,i){var u=a&&a.prototype instanceof s?a:s,l=Object.create(u.prototype);return n(l,"_invoke",function(t,n,a){var o,i,u,s=0,l=a||[],f=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return o=t,i=0,u=e,d.n=n,c}};function p(t,n){for(i=t,u=n,r=0;!f&&s&&!a&&r<l.length;r++){var a,o=l[r],p=d.p,y=o[2];t>3?(a=y===n)&&(u=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=p&&((a=t<2&&p<o[1])?(i=0,d.v=n,d.n=o[1]):p<y&&(a=t<3||o[0]>n||n>y)&&(o[4]=t,o[5]=n,d.n=y,i=0))}if(a||t>1)return c;throw f=!0,n}return function(a,l,y){if(s>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,y),i=l,u=y;(r=i<2?e:u)||!f;){o||(i?i<3?(i>1&&(d.n=-1),p(i,u)):d.n=u:d.v=u);try{if(s=2,o){if(i||(a="next"),r=o[a]){if(!(r=r.call(o,u)))throw TypeError("iterator result is not an object");if(!r.done)return r;u=r.value,i<2&&(i=0)}else 1===i&&(r=o.return)&&r.call(o),i<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=e}else if((r=(f=d.n<0)?u:t.call(n,d))!==c)break}catch(r){o=e,i=1,u=r}finally{s=1}}return{value:r,done:f}}}(t,o,i),!0),l}var c={};function s(){}function l(){}function f(){}r=Object.getPrototypeOf;var d=[][o]?r(r([][o]())):(n(r={},o,(function(){return this})),r),p=f.prototype=s.prototype=Object.create(d);function y(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,n(e,i,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,n(p,"constructor",f),n(f,"constructor",l),l.displayName="GeneratorFunction",n(f,i,"GeneratorFunction"),n(p),n(p,i,"Generator"),n(p,o,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:u,m:y}})()}function n(e,t,r,a){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}n=function(e,t,r,a){if(t)o?o(e,t,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[t]=r;else{var i=function(t,r){n(e,t,(function(e){return this._invoke(t,r,e)}))};i("next",0),i("throw",1),i("return",2)}},n(e,t,r,a)}function r(e,t,n,r,a,o,i){try{var u=e[o](i),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,a)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(a,o){var i=e.apply(t,n);function u(e){r(i,a,o,u,c,"next",e)}function c(e){r(i,a,o,u,c,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.60f18f5a.js","./secondaryAuth-legacy.2e8406d4.js","./verifyCode-legacy.6b4f04f2.js"],(function(n,r){"use strict";var o,i,u,c,s,l,f,d,p,y,v,m,h,g,b,w,x,_=document.createElement("style");return _.textContent=".oauth-result-container[data-v-d93f1869]{height:100vh;display:flex;justify-content:center;align-items:center;background-color:#f5f7fa}.loading-box[data-v-d93f1869]{background:#fff;padding:40px;border-radius:8px;text-align:center;box-shadow:0 2px 12px rgba(0,0,0,.1);min-width:320px}.secondary-auth-container[data-v-d93f1869]{background:transparent;padding:30px;border-radius:8px;box-shadow:none;min-width:340px;display:flex;justify-content:center;align-items:center;position:relative;z-index:10}.loading-icon[data-v-d93f1869]{animation:rotate-d93f1869 2s linear infinite;color:#409eff}.message[data-v-d93f1869]{margin:20px 0;font-size:16px;color:#606266}@keyframes rotate-d93f1869{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n",document.head.appendChild(_),{setters:[function(e){o=e._,i=e.u,u=e.a,c=e.b,s=e.r,l=e.p,f=e.c,d=e.K,p=e.M,y=e.h,v=e.o,m=e.d,h=e.j,g=e.e,b=e.t,w=e.g},function(e){x=e.default},function(){}],execute:function(){var r={class:"oauth-result-container"},_={key:0,class:"loading-box"},S={class:"message"},k={key:1,class:"secondary-auth-container"},j=Object.assign({name:"OAuth2Result"},{setup:function(n){var o=i(),j=u(),O=c(),T=s("正在处理认证信息..."),P=s(!1),q=s(""),C=s(""),I=s(""),E=s(""),G=s(""),L=s("phone"),A=s(!0);l("userName",I),l("last_id",E),l("isSecondary",s(!0)),l("contactType",L),l("hasContactInfo",A);var N=f((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===L.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===L.value}]})),R=function(){var e=a(t().m((function e(n){var r,a;return t().w((function(e){for(;;)switch(e.n){case 0:T.value="认证成功，正在跳转...",r=decodeURIComponent(G.value||"/"),n.clientParams&&((a=new URLSearchParams).set("type",n.clientParams.type),n.clientParams.wp&&a.set("wp",n.clientParams.wp),r+=(r.includes("?")?"&":"?")+a.toString()),window.location.href=r;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),z=function(){P.value=!1,T.value="已取消验证，正在返回登录页...";var e=new URLSearchParams;o.query.idp_id&&e.set("idp_id",o.query.idp_id),o.query.redirect_url&&e.set("redirect",o.query.redirect_url),"client"===o.query.type&&(e.set("type","client"),o.query.wp&&e.set("wp",o.query.wp));var t=e.toString()?"/login?".concat(e.toString()):"/login";j.push(t)};return d(a(t().m((function n(){var r,a,i,u,c,s,l,f,d,y,v,m,h,g,b,w,x,_,S,k,N,R;return t().w((function(t){for(;;)switch(t.n){case 0:if(t.p=0,r=o.query,a=r.auth_token,i=r.idp_id,u=r.redirect_url,c=r.login_type,s=r.auth_error,"is_test"!==c){t.n=1;break}return T.value="测试完成",p.success("测试完成，正在关闭窗口..."),setTimeout((function(){return window.close()}),2e3),t.a(2);case 1:if(!s){t.n=2;break}throw new Error(s);case 2:if(a){t.n=3;break}throw new Error("缺少有效的认证令牌");case 3:return localStorage.setItem("loginType",c),l={clientId:"client_portal",grantType:"implicit",redirect_uri:u,idpId:i,authWeb:{authWebToken:a}},T.value="验证登录信息...",t.n=4,O.LoginIn(l,c,i);case 4:if("object"!==e(f=t.v)||null===f||!f.isSecondary){t.n=5;break}T.value="需要进行二次认证...",L.value=f.contactType||"phone",A.value=f.hasContactInfo||!1,d=f.secondary&&Array.isArray(f.secondary)&&f.secondary.length>0?f.secondary[0].id:i,q.value=f.uniqKey,C.value=f.user_id,I.value=f.userName,E.value=d,G.value=u||"/",P.value=!0,t.n=7;break;case 5:if(!0!==f){t.n=6;break}T.value="认证成功，正在跳转...",G.value=u||"/",t.n=7;break;case 6:throw new Error("登录处理失败");case 7:t.n=9;break;case 8:t.p=8,R=t.v,console.error("处理错误:",R),y="认证失败，请稍后再试";try{if(null!==(v=R.response)&&void 0!==v&&v.data)y=R.response.data.error_description||R.response.data.message||R.response.data.error||y;else if(R.message){if((m=R.message).includes("msg=登录失败")&&(h=m.split("msg=登录失败:")[1]||m,m=h.trim()),m.includes("{"))try{g=m.indexOf("{"),b=m.substring(g),(w=JSON.parse(b))&&w.message&&(y=w.message)}catch(n){}"认证失败，请稍后再试"===y&&m.includes("message =")&&(x=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,(_=m.match(x))&&_[1]&&(y=_[1].trim())),"认证失败，请稍后再试"===y&&m.includes("reason =")&&(S=/reason\s*=\s*(\w+)/,(k=m.match(S))&&k[1]&&(N=k[1].replace(/_/g," ").toLowerCase(),y="认证失败: ".concat(N))),"认证失败，请稍后再试"===y&&(y=m.split("\n")[0]).length>100&&(y=y.substring(0,97)+"...")}}catch(n){console.error("处理错误消息时发生异常:",n)}T.value=y,p.error(y),setTimeout((function(){j.push({name:"Login"})}),2e3);case 9:return t.a(2)}}),n,null,[[0,8]])})))),function(e,t){var n=y("base-icon");return v(),m("div",r,[P.value?w("",!0):(v(),m("div",_,[h(n,{class:"loading-icon",name:"loading",size:"40"}),g("div",S,b(T.value),1)])),P.value?(v(),m("div",k,[h(x,{"auth-info":{uniqKey:q.value,contactType:L.value,hasContactInfo:A.value},"auth-id":E.value,"user-name":I.value,"last-id":E.value,"auth-methods":N.value,onVerificationSuccess:R,onCancel:z},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])])):w("",!0)])}}});n("default",o(j,[["__scopeId","data-v-d93f1869"]]))}}}))}();
