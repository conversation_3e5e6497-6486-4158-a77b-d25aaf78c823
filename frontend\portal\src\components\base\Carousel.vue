<template>
  <div class="carousel" :style="{ height: height }">
    <div class="carousel-container" :style="containerStyle">
      <slot></slot>
    </div>
    <div v-if="indicatorPosition !== 'none'" :class="indicatorClass">
      <button
        v-for="(item, index) in itemCount"
        :key="index"
        :class="['carousel-indicator', { active: index === currentIndex }]"
        @click="setCurrentIndex(index)"
      ></button>
    </div>
    <button v-if="arrow !== 'never'" class="carousel-arrow carousel-arrow-left" @click="prev">
      ‹
    </button>
    <button v-if="arrow !== 'never'" class="carousel-arrow carousel-arrow-right" @click="next">
      ›
    </button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, provide } from 'vue'

const props = defineProps({
  height: {
    type: String,
    default: '300px'
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  interval: {
    type: Number,
    default: 3000
  },
  indicatorPosition: {
    type: String,
    default: 'bottom',
    validator: (value) => ['bottom', 'top', 'none'].includes(value)
  },
  arrow: {
    type: String,
    default: 'hover',
    validator: (value) => ['always', 'hover', 'never'].includes(value)
  }
})

const emit = defineEmits(['change'])

const currentIndex = ref(0)
const itemCount = ref(0)
let autoplayTimer = null

const containerStyle = computed(() => ({
  transform: `translateX(-${currentIndex.value * 100}%)`
}))

const indicatorClass = computed(() => {
  const classes = ['carousel-indicators']
  classes.push(`carousel-indicators-${props.indicatorPosition}`)
  return classes.join(' ')
})

const setCurrentIndex = (index) => {
  if (index !== currentIndex.value) {
    currentIndex.value = index
    emit('change', index)
  }
}

const next = () => {
  const nextIndex = (currentIndex.value + 1) % itemCount.value
  setCurrentIndex(nextIndex)
}

const prev = () => {
  const prevIndex = (currentIndex.value - 1 + itemCount.value) % itemCount.value
  setCurrentIndex(prevIndex)
}

const startAutoplay = () => {
  if (props.autoplay && itemCount.value > 1) {
    autoplayTimer = setInterval(next, props.interval)
  }
}

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer)
    autoplayTimer = null
  }
}

const addItem = () => {
  itemCount.value++
}

const removeItem = () => {
  itemCount.value--
}

// 提供给子组件的方法
provide('carousel', {
  addItem,
  removeItem
})

onMounted(() => {
  startAutoplay()
})

onUnmounted(() => {
  stopAutoplay()
})

// 暴露方法
defineExpose({
  next,
  prev,
  setCurrentIndex
})
</script>

<style scoped>
.carousel {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}

.carousel-container {
  display: flex;
  transition: transform 0.3s ease;
  height: 100%;
}

.carousel-indicators {
  position: absolute;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.carousel-indicators-bottom {
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-indicators-top {
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.carousel-indicator.active {
  background-color: #536ce6;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
  z-index: 10;
}

.carousel-arrow:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.carousel-arrow-left {
  left: 10px;
}

.carousel-arrow-right {
  right: 10px;
}

.carousel[data-arrow="hover"] .carousel-arrow {
  opacity: 0;
  transition: opacity 0.3s;
}

.carousel[data-arrow="hover"]:hover .carousel-arrow {
  opacity: 1;
}
</style>
