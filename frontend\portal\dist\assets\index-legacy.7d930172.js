/*! 
 Build based on gin-vue-admin 
 Time : 1749618054000 */
System.register(["./header-legacy.306ae93b.js","./menu-legacy.c2e930e8.js","./index-legacy.7e8ba759.js","./ASD-legacy.b6ffb1bc.js"],(function(e,t){"use strict";var a,n,i,u,o,l,r,c,d=document.createElement("style");return d.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(d),{setters:[function(e){a=e.default},function(e){n=e.default},function(e){i=e.h,u=e.o,o=e.d,l=e.j,r=e.e,c=e.f},function(){}],execute:function(){var t={class:"layout-page"},d={class:"layout-wrap"},f={id:"layoutMain",class:"layout-main"};e("default",Object.assign({name:"Client"},{setup:function(e){return function(e,s){var h=i("router-view");return u(),o("div",t,[l(a),r("div",d,[l(n),r("div",f,[(u(),c(h,{key:e.$route.fullPath}))])])])}}}))}}}));
