/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
import{_ as a,u as e,a as r,b as t,r as s,N as c,p as i,f as l,L as o,M as n}from"./index.a5cb1178.js";const u=a(Object.assign({name:"WxOAuthCallback"},{setup(a){const u=e(),p=r(),y=t(),{code:d,state:_,auth_type:f,redirect_url:h}=u.query,b=s(Array.isArray(_)?_[0]:_),m=s("");return c((async()=>{const a=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const a={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:b.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(a,"qiyewx_oauth",b.value)?await p.push({name:"verify",query:{redirect_url:h}}):n.error("登录失败，请重试")}catch(e){console.error("登录过程出错:",e),n.error("登录过程出错，请重试")}finally{a.close()}})),i("userName",m),(a,e)=>l(" 空模板，因为所有逻辑都在 script 中处理 ")}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wx_oauth_callback.vue"]]);export{u as default};
