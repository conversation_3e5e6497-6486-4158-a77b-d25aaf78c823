/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
import{x as e,y as a,u as l,r as t,c as n,b as u,z as i,p as s,h as o,o as r,d as v,e as c,k as d,t as p,g as m,f as y,A as h,j as g,w as f,m as _,B as x,L as w,F as k,i as C}from"./index.8abc592d.js";const T={class:"login-page"},b={class:"content"},E={class:"right-panel"},O={key:0},L={key:0,class:"title"},P={key:1,class:"title"},I={style:{"text-align":"center"}},j={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},q={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},A=["xlink:href"],R={key:2,class:"login_panel_form"},S={key:3},V=["onClick"],D={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},B=["xlink:href"],z={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},K={key:1,class:"auth-waiting"},N={class:"waiting-icon"},U={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},J=["xlink:href"],F={class:"waiting-title"},M=Object.assign({name:"Login"},{setup(M){const $=a({loader:()=>x((()=>import("./localLogin.e10a4542.js")),["./localLogin.e10a4542.js","./index.8abc592d.js","./index.d9de825b.css","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),G=a({loader:()=>x((()=>import("./wechat.744a09bb.js")),["./wechat.744a09bb.js","./index.8abc592d.js","./index.d9de825b.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),H=a({loader:()=>x((()=>import("./feishu.6aeaec44.js")),["./feishu.6aeaec44.js","./index.8abc592d.js","./index.d9de825b.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Q=a({loader:()=>x((()=>import("./dingtalk.c3276507.js")),["./dingtalk.c3276507.js","./index.8abc592d.js","./index.d9de825b.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),W=a({loader:()=>x((()=>import("./oauth2.c632e34f.js")),["./oauth2.c632e34f.js","./index.8abc592d.js","./index.d9de825b.css","./oauth2.79676400.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),X=a({loader:()=>x((()=>import("./sms.93760d6a.js")),["./sms.93760d6a.js","./index.8abc592d.js","./index.d9de825b.css","./sms.ef70f8fb.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Y=a({loader:()=>x((()=>import("./secondaryAuth.ee74906c.js")),["./secondaryAuth.ee74906c.js","./verifyCode.4e207107.js","./index.8abc592d.js","./index.d9de825b.css","./verifyCode.978f9466.css","./secondaryAuth.170537bd.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Z=l(),ee=t(0),ae=t([]),le=t("local"),te=t(""),ne=t(""),ue=t(""),ie=t([]),se=t([]),oe=t(!1),re=t(),ve=t(""),ce=t(!1),de=t(""),pe=t(!1),me=t(""),ye=t(""),he=t(""),ge=t({}),fe=n((()=>{const e=oe.value?me.value:ne.value;return ae.value.filter((a=>a.id!==e))})),_e=u();n((()=>se.value.filter((e=>e.id!==ne.value))));(async()=>{var a,l,t,n,u,i,s,o,r,v,c,d;try{const p=(()=>{const e={};if(Z.query.type&&(e.type=Z.query.type),Z.query.wp&&(e.wp=Z.query.wp),Z.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(Z.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){ae.value=m.data.idpList;const e=Z.query.idp_id||_e.loginType;if(e&&"undefined"!==e){let s=!1;for(const a of m.data.idpList)e===a.id&&(s=!0,ne.value=a.id,le.value=a.type,te.value=a.templateType,ie.value=a.attrs,ie.value.name=a.name,ie.value.authType=a.type);s||(ue.value=null==(a=ae.value[0])?void 0:a.id,ne.value=null==(l=ae.value[0])?void 0:l.id,le.value=null==(t=ae.value[0])?void 0:t.type,te.value=null==(n=ae.value[0])?void 0:n.templateType,ie.value=null==(u=ae.value[0])?void 0:u.attrs,ie.value.name=ae.value[0].name,ie.value.authType=null==(i=ae.value[0])?void 0:i.type)}else ue.value=null==(s=ae.value[0])?void 0:s.id,ne.value=null==(o=ae.value[0])?void 0:o.id,le.value=null==(r=ae.value[0])?void 0:r.type,te.value=null==(v=ae.value[0])?void 0:v.templateType,ie.value=null==(c=ae.value[0])?void 0:c.attrs,ie.value.name=ae.value[0].name,ie.value.authType=null==(d=ae.value[0])?void 0:d.type;++ee.value}}catch(p){console.error(p)}})();const xe=n((()=>{switch(le.value){case"local":case"msad":case"ldap":case"web":case"email":return $;case"qiyewx":return G;case"feishu":return H;case"dingtalk":return Q;case"oauth2":case"cas":return W;case"sms":return X;default:return"oauth2"===te.value?W:"local"}})),we=n((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===de.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===de.value}])),ke=()=>{oe.value=!1,se.value=[],re.value="",ve.value="",de.value="",pe.value=!1,me.value&&(ne.value=me.value,le.value=ye.value,te.value=he.value,ie.value={...ge.value},me.value="",ye.value="",he.value="",ge.value={}),++ee.value,console.log("取消后恢复的状态:",{isSecondary:oe.value,auth_id:ne.value,auth_type:le.value})},Ce=async e=>{const a=w.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=Z.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},Te=n((()=>!["dingtalk","feishu","qiyewx"].includes(le.value)&&("oauth2"!==te.value&&"cas"!==le.value||("cas"===le.value?1===parseInt(ie.value.casOpenType):"oauth2"===te.value&&1===parseInt(ie.value.oauth2OpenType))))),be=e=>{ue.value=e.id,ie.value=e.attrs||{},ie.value.name=e.name,ie.value.authType=e.type,oe.value&&(ie.value.uniqKey=re.value,ie.value.notPhone=ce.value),ne.value=e.id,le.value=e.type,te.value=e.templateType,++ee.value};return i(oe,(async(e,a)=>{oe.value&&(me.value=ne.value,ye.value=le.value,he.value=te.value,ge.value={...ie.value},console.log("二次认证数据:",{secondary:se.value,secondaryLength:se.value.length}),se.value.length>0&&be(se.value[0]))})),s("secondary",se),s("isSecondary",oe),s("uniqKey",re),s("userName",ve),s("notPhone",ce),s("last_id",ue),s("contactType",de),s("hasContactInfo",pe),(e,a)=>{const l=o("base-divider"),t=o("base-avatar"),n=o("base-carousel-item"),u=o("base-carousel");return r(),v("div",T,[c("div",b,[a[3]||(a[3]=c("div",{class:"left-panel"},null,-1)),c("div",E,[oe.value?(r(),v("div",K,[c("div",N,[(r(),v("svg",U,[c("use",{"xlink:href":`#icon-auth-${ye.value||le.value}`},null,8,J)]))]),c("h4",F,p(ge.value.name||ie.value.name)+" 登录成功",1),a[1]||(a[1]=c("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),a[2]||(a[2]=c("div",{class:"security-tips"},[c("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),c("span",null,"为了您的账户安全，请完成二次身份验证")],-1))])):(r(),v("div",O,["local"===le.value?(r(),v("span",L,"本地账号登录")):Te.value?(r(),v("span",P,[c("div",I,[c("span",j,[(r(),v("svg",q,[c("use",{"xlink:href":"#icon-auth-"+le.value},null,8,A)])),d(" "+p(ie.value.name),1)])])])):m("",!0),ne.value?(r(),v("div",R,[(r(),y(h(xe.value),{auth_id:ne.value,auth_info:ie.value},null,8,["auth_id","auth_info"]))])):m("",!0),fe.value.length>0?(r(),v("div",S,[g(l,null,{default:f((()=>a[0]||(a[0]=[c("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)]))),_:1,__:[0]}),(r(),y(u,{key:ee.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:f((()=>[(r(!0),v(k,null,C(Math.ceil(fe.value.length/2),(e=>(r(),y(n,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:f((()=>[(r(!0),v(k,null,C(fe.value.slice(2*(e-1),2*(e-1)+2),(e=>(r(),v("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:a=>be(e)},[c("div",null,[g(t,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:f((()=>[(r(),v("svg",D,[c("use",{"xlink:href":"#icon-auth-"+e.type},null,8,B)]))])),_:2},1024)]),c("div",z,p(e.name),1)],8,V)))),128))])),_:2},1024)))),128))])),_:1}))])):m("",!0)]))])]),oe.value?(r(),y(_(Y),{key:0,"auth-info":{uniqKey:re.value,contactType:de.value,hasContactInfo:pe.value},"auth-id":ne.value,"user-name":ve.value,"last-id":ue.value,"auth-methods":we.value,onVerificationSuccess:Ce,onCancel:ke},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):m("",!0)])}}});export{M as default};
