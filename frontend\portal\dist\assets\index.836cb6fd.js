/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import e from"./menuItem.37f8bc48.js";import t from"./asyncSubmenu.79aa3c88.js";import{c as o,h as n,o as r,f as s,w as a,d as u,F as l,i,g as f,A as c}from"./index.5a1fa56a.js";const m=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(m){const d=m,h=o((()=>d.routerInfo.children&&d.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return m.routerInfo.hidden?f("",!0):(r(),s(c(h.value),{key:0,"is-collapse":m.isCollapse,theme:m.theme,"router-info":m.routerInfo},{default:a((()=>[m.routerInfo.children&&m.routerInfo.children.length?(r(!0),u(l,{key:0},i(m.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:m.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{m as default};
