/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{r as f,u as p,z as y,o as _,d as w,e as h,X as m}from"./index.d0594432.js";const q={name:"Wechat"},x=Object.assign(q,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(c){const s=f(0),a=c,u=async()=>{const n={type:"qiyewx",data:{idpId:a.auth_id}},e=await m(n);if(e.status===200)return e.data.uniqKey},o=p(),i=async()=>{const n=window.location.host;let r=`${window.location.protocol}//${n}/#/status`;if(o.query?.redirect){const t=o.query?.redirect.indexOf("?")>-1?o.query?.redirect.substring(o.query?.redirect.indexOf("?")+1):"";r=r+"?"+t}else if(o.query){const t=new URLSearchParams;for(const[d,l]of Object.entries(o.query))t.append(d,l);r=r+"?"+t.toString()}await u(),setTimeout(()=>{window.getQRCode({id:"qr_login",appid:a.auth_info.wxCorpId,agentid:a.auth_info.wxAgentId,redirect_uri:encodeURIComponent(r+"&auth_type=qiyewx"),state:a.auth_id,href:"",lang:"zh"});const t=document.querySelector("iframe");t.contentWindow.location.href!==t.src?console.log("iframe\u5DF2\u91CD\u65B0\u52A0\u8F7D"):console.log("iframe\u672A\u91CD\u65B0\u52A0\u8F7D")},100)};return i(),y(a,async(n,e)=>{s.value++,await i()}),(n,e)=>(_(),w("div",{key:s.value},e[0]||(e[0]=[h("div",{id:"qr_login",slot:"content",class:"wechat-class"},null,-1)])))}});export{x as default};
