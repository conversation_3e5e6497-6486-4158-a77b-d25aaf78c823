/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{_ as a,o,d as t,e,f as n}from"./index.2a422357.js";const r={class:"bottom-info"};const s=a({name:"BottomInfo"},[["render",function(a,s,p,i,f,d){return o(),t("div",r,s[0]||(s[0]=[e("div",null,[n("      <span>Powered by</span>"),n("      <span>"),n('        <a href="https://github.com/flipped-aurora/gin-vue-admin">{{ $GIN_VUE_ADMIN.appName }}</a>'),n("      </span>"),n('      <base-divider direction="vertical" />'),n("      <span>Copyright</span>"),n("      <span>"),n('        <a href="https://github.com/flipped-aurora">flipped-aurora团队</a>'),n("      </span>")],-1)]))}],["__file","D:/asec-platform/frontend/portal/src/view/layout/bottomInfo/bottomInfo.vue"]]);export{s as default};
