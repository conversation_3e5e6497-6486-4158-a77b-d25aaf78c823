/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,a,o="function"==typeof Symbol?Symbol:{},r=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function l(e,o,r,i){var l=o&&o.prototype instanceof d?o:d,u=Object.create(l.prototype);return t(u,"_invoke",function(e,t,o){var r,i,l,d=0,u=o||[],s=!1,f={p:0,n:0,v:n,a:v,f:v.bind(n,4),d:function(e,t){return r=e,i=0,l=n,f.n=t,c}};function v(e,t){for(i=e,l=t,a=0;!s&&d&&!o&&a<u.length;a++){var o,r=u[a],v=f.p,p=r[2];e>3?(o=p===t)&&(l=r[(i=r[4])?5:(i=3,3)],r[4]=r[5]=n):r[0]<=v&&((o=e<2&&v<r[1])?(i=0,f.v=t,f.n=r[1]):v<p&&(o=e<3||r[0]>t||t>p)&&(r[4]=e,r[5]=t,f.n=p,i=0))}if(o||e>1)return c;throw s=!0,t}return function(o,u,p){if(d>1)throw TypeError("Generator is already running");for(s&&1===u&&v(u,p),i=u,l=p;(a=i<2?n:l)||!s;){r||(i?i<3?(i>1&&(f.n=-1),v(i,l)):f.n=l:f.v=l);try{if(d=2,r){if(i||(o="next"),a=r[o]){if(!(a=a.call(r,l)))throw TypeError("iterator result is not an object");if(!a.done)return a;l=a.value,i<2&&(i=0)}else 1===i&&(a=r.return)&&a.call(r),i<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),i=1);r=n}else if((a=(s=f.n<0)?l:e.call(t,f))!==c)break}catch(a){r=n,i=1,l=a}finally{d=1}}return{value:a,done:s}}}(e,r,i),!0),u}var c={};function d(){}function u(){}function s(){}a=Object.getPrototypeOf;var f=[][r]?a(a([][r]())):(t(a={},r,(function(){return this})),a),v=s.prototype=d.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,t(e,i,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=s,t(v,"constructor",s),t(s,"constructor",u),u.displayName="GeneratorFunction",t(s,i,"GeneratorFunction"),t(v),t(v,i,"Generator"),t(v,r,(function(){return this})),t(v,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:p}})()}function t(e,n,a,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}t=function(e,n,a,o){if(n)r?r(e,n,{value:a,enumerable:!o,configurable:!o,writable:!o}):e[n]=a;else{var i=function(n,a){t(e,n,(function(e){return this._invoke(n,a,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,a,o)}function n(e,t,n,a,o,r,i){try{var l=e[r](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(a,o)}function a(e){return function(){var t=this,a=arguments;return new Promise((function(o,r){var i=e.apply(t,a);function l(e){n(i,o,r,l,c,"next",e)}function c(e){n(i,o,r,l,c,"throw",e)}l(void 0)}))}}System.register(["./index-legacy.60f18f5a.js","./ASD-legacy.b6ffb1bc.js","./browser-legacy.aecf21ac.js"],(function(t,n){"use strict";var o,r,i,l,c,d,u,s,f,v,p,m,g,h,w,b,y,x,k,_,F=document.createElement("style");return F.textContent='@charset "UTF-8";.dark[data-v-1cad6437]{background-color:#273444!important;color:#fff!important}.light[data-v-1cad6437]{background-color:#fff!important;color:#000!important}.icon-rizhi1 span[data-v-1cad6437]{margin-left:5px}.day-select[data-v-1cad6437]{height:23px;width:88px;margin-left:15px}.day-select div[data-v-1cad6437]{height:23px;width:88px}.day-select div input[data-v-1cad6437]{height:23px;width:50px;font-size:12px;color:#2972c8}.right-box[data-v-1cad6437]{margin-top:9px}.hidelogoimg[data-v-1cad6437]{overflow:hidden!important;width:54px!important;padding-left:9px!important}.hidelogoimg .logoimg[data-v-1cad6437]{margin-left:7px}[data-v-1cad6437],[data-v-1cad6437]:before,[data-v-1cad6437]:after{box-sizing:border-box}.el-menu--collapse .el-menu-item.is-active[data-v-1cad6437]{color:var(--9c12943e);opacity:100%}.el-menu-item[data-v-1cad6437],.el-sub-menu[data-v-1cad6437]{color:var(--389f4d81);font-size:14px;height:44px;line-height:40px;color:#fff;border-left:4px #273444 solid;width:100%;justify-content:flex-start}.hideside .el-menu-item[data-v-1cad6437]{padding:0 12px}.hideside .el-menu-item.is-active .base-icon[data-v-1cad6437]{color:#4d70ff}.el-menu-item.is-active[data-v-1cad6437],.el-sub-menu.is-active[data-v-1cad6437]{background:#465566!important;border-left:4px #71BDDF solid;border-bottom:none;opacity:100%}.el-menu-item[data-v-1cad6437]:hover{background:#303E4E!important}.menu-info .menu-contorl[data-v-1cad6437]{line-height:52px;font-size:20px;display:table-cell;vertical-align:middle}.layout-cont .main-cont .menu-total[data-v-1cad6437]{margin-left:20px}.client[data-v-1cad6437]{text-align:center;background:#FFFFFF;max-height:100vh;padding:20% 25%}.client .downloadWin[data-v-1cad6437]{height:100%;display:flex;justify-content:center;align-items:center}.client .downloadWin .icon[data-v-1cad6437]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.client .downloadWin div:hover .window-show[data-v-1cad6437]{display:none}.client .downloadWin div:hover .window-hidden[data-v-1cad6437]{display:initial!important}.client .downloadWin div:hover .window-hidden span[data-v-1cad6437]{margin-top:42px!important}.download-complete[data-v-1cad6437]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background-color:rgba(255,255,255,.9);padding:5px 10px;border-radius:4px;font-weight:700;color:#4d70ff}\n',document.head.appendChild(F),{setters:[function(e){o=e._,r=e.P,i=e.r,l=e.K,c=e.Q,d=e.h,u=e.o,s=e.f,f=e.w,v=e.j,p=e.H,m=e.e,g=e.O,h=e.T,w=e.d,b=e.k,y=e.g,x=e.M},function(e){k=e._},function(e){_=e.g}],execute:function(){var n={style:{background:"'#273444'"}},F={class:"downloadWin"},j={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},T={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},O={key:1,class:"download-complete"},z=Object.assign({name:"downloadWin"},{setup:function(t){r((function(e){return{"9c12943e":e.activeBackground,"389f4d81":e.normalText}}));var o=i(!1),z=i(!0),E=i(!1),S=i("1"),W=i({});W.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};var C=i(!1),P=i(0),L=i(!1),B=0,G=function(){var e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(E.value=!1,z.value=!1,o.value=!0):(E.value=!1,z.value=!0,o.value=!1)};G();var D=i(!1);l((function(){c.emit("collapse",o.value),c.emit("mobile",E.value),c.on("showLoading",(function(){D.value=!0})),c.on("closeLoading",(function(){D.value=!1})),window.onresize=function(){return G(),c.emit("collapse",o.value),void c.emit("mobile",E.value)}}));var R=i("#1f2a36"),U=i(!1),M=function(){o.value=!o.value,z.value=!o.value,U.value=!o.value,c.emit("collapse",o.value)},q=function(e){return 100===e?"下载完成":"".concat(e,"%")},H=function(){var t=a(e().m((function t(n){var a,o,r,i,l,c;return e().w((function(e){for(;;)switch(e.n){case 0:if("windows"===n){e.n=1;break}return e.a(2);case 1:return C.value=!0,P.value=0,L.value=!1,B=0,e.p=2,e.n=3,_({platform:n});case 3:if(0!==(a=e.v).data.code){e.n=5;break}return o=window.location.port,r=new URL(a.data.data.download_url),o?r.toString().includes("asec-deploy")?i=a.data.data.download_url:(r.port=o,i=r.toString()):(r.port="",i=r.toString()),l=o?a.data.data.latest_filename.replace(/@(\d+)/,"@".concat(o)):a.data.data.latest_filename,e.n=4,A(i,l);case 4:L.value=!0,x({type:"success",message:"下载完成"}),e.n=6;break;case 5:throw new Error(a.data.msg);case 6:e.n=8;break;case 7:e.p=7,c=e.v,x({type:"error",message:c.message||"下载失败，请联系管理员"});case 8:return e.p=8,C.value=!1,setTimeout((function(){L.value=!1}),3e3),e.f(8);case 9:return e.a(2)}}),t,null,[[2,7,8,9]])})));return function(e){return t.apply(this,arguments)}}(),A=function(){var t=a(e().m((function t(n,a){var o,r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,I(n);case 1:o=e.v,K(o,a),e.n=4;break;case 2:if(e.p=2,r=e.v,!(B<3&&"网络连接超时"===r.message)){e.n=3;break}return B++,e.a(2,A(n,a));case 3:throw new Error("安装包下载失败，请检查网络连接或联系管理员。错误: ".concat(r.message));case 4:return e.a(2)}}),t,null,[[0,2]])})));return function(e,n){return t.apply(this,arguments)}}(),I=function(e){return new Promise((function(t,n){var a=new XMLHttpRequest;a.open("GET",e,!0),a.responseType="blob",a.timeout=3e5;var o=Date.now();a.onprogress=function(e){if(e.lengthComputable){var t=e.loaded/e.total*100;P.value=Math.round(t)}else{var n=(Date.now()-o)/1e3,a=60*(e.loaded/n),r=e.loaded/a*100;P.value=Math.min(99,Math.round(r))}},a.onload=function(){200===a.status?t(a.response):n(new Error("HTTP 错误: ".concat(a.status)))},a.onerror=function(){n(new Error("网络错误"))},a.ontimeout=function(){n(new Error("网络连接超时"))},a.send()}))},K=function(e,t){if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,t);else{var n=document.createElement("a"),a=document.querySelector("body");n.href=window.URL.createObjectURL(e),n.download=t,n.style.display="none",a.appendChild(n),n.click(),a.removeChild(n),window.URL.revokeObjectURL(n.href)}};return function(e,t){var a=d("base-row"),r=d("base-icon"),i=d("el-menu-item"),l=d("el-menu"),c=d("el-scrollbar"),x=d("base-aside"),_=d("el-link"),B=d("el-progress"),G=d("base-main"),D=d("base-container");return u(),s(D,{class:"layout-cont"},{default:f((function(){return[v(D,{class:p([z.value?"openside":"hideside",E.value?"mobile":""])},{default:f((function(){return[v(a,{class:p([U.value?"shadowBg":""]),onClick:t[0]||(t[0]=function(e){return U.value=!U.value,z.value=!!o.value,void M()})},null,8,["class"]),v(x,{class:"main-cont main-left gva-aside"},{default:f((function(){return[m("div",{class:p(["tilte",[z.value?"openlogoimg":"hidelogoimg"]]),style:g({background:R.value})},t[2]||(t[2]=[m("img",{alt:"",class:"logoimg",src:k},null,-1)]),6),m("div",n,[v(c,{style:{height:"calc(100vh - 110px)"}},{default:f((function(){return[v(h,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((function(){return[v(l,{collapse:o.value,"collapse-transition":!1,"default-active":S.value,"background-color":W.value.background,"active-text-color":W.value.activeText,class:"el-menu-vertical","unique-opened":""},{default:f((function(){return[v(i,{index:"1"},{default:f((function(){return[v(r,{name:"xiazai",size:"16px"}),t[3]||(t[3]=m("span",null,"客户端下载",-1))]})),_:1,__:[3]})]})),_:1},8,["collapse","default-active","background-color","active-text-color"])]})),_:1})]})),_:1})]),m("div",{class:"footer",style:g({background:R.value})},[m("div",{class:"menu-total",onClick:M},[o.value?(u(),s(r,{key:0,color:"#FFFFFF",size:"14px",name:"expand"})):(u(),s(r,{key:1,color:"#FFFFFF",size:"14px",name:"fold"}))])],4)]})),_:1}),v(G,{class:"main-cont main-right client"},{default:f((function(){return[m("div",F,[m("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:t[1]||(t[1]=function(e){return H("windows")})},[(u(),w("svg",j,t[4]||(t[4]=[m("use",{"xlink:href":"#icon-windows"},null,-1)]))),(u(),w("svg",T,t[5]||(t[5]=[m("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),t[8]||(t[8]=m("br",null,null,-1)),v(_,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:f((function(){return t[6]||(t[6]=[b(" Windows客户端 ")])})),_:1,__:[6]}),v(_,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:f((function(){return t[7]||(t[7]=[b(" 点击下载Windows客户端 ")])})),_:1,__:[7]}),C.value?(u(),s(B,{key:0,percentage:P.value,format:q,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):y("",!0),L.value?(u(),w("div",O,"下载完成")):y("",!0)])])]})),_:1})]})),_:1},8,["class"])]})),_:1})}}});t("default",o(z,[["__scopeId","data-v-1cad6437"]]))}}}))}();
