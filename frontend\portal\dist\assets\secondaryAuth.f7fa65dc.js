/*! 
 Build based on gin-vue-admin 
 Time : 1749623364000 */
import a from"./verifyCode.5aacc449.js";import{r as e,c as t,h as s,o as l,d as n,e as u,F as i,i as c,j as o,w as r,g as d,f as h,_ as v,t as y,k as f}from"./index.49a4551d.js";const m={class:"secondary-auth-overlay"},p={class:"secondary-auth-container"},_={key:0,class:"auth-selector"},b={class:"auth-methods"},k={class:"auth-method-content"},g={class:"icon","aria-hidden":"true"},I=["xlink:href"],j={class:"auth-method-name"},x={class:"selector-footer"},C=v(Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup(v,{emit:C}){const S=v,q=e(!0),w=e(null),A=t((()=>S.authMethods.filter((a=>a.available)))),M=a=>{w.value=a,q.value=!1};1===A.value.length&&M(A.value[0]);const N=C,O=()=>{N("cancel")},B=a=>{"client"===route.query.type&&(a.clientParams={type:"client",wp:route.query.wp||"50001"}),N("verification-success",a)};return(e,t)=>{const C=s("base-avatar"),S=s("base-card"),N=s("base-button");return l(),n("div",m,[u("div",p,[q.value?(l(),n("div",_,[t[3]||(t[3]=u("h2",{class:"title"},"请选择二次认证方式",-1)),u("div",b,[(l(!0),n(i,null,c(A.value,(a=>(l(),h(S,{key:a.type,class:"auth-method-card",onClick:e=>M(a)},{default:r((()=>[u("div",k,[o(C,null,{default:r((()=>[(l(),n("svg",g,[u("use",{"xlink:href":"#icon-auth-"+a.icon},null,8,I)]))])),_:2},1024),u("div",j,y(a.name),1)])])),_:2},1032,["onClick"])))),128))]),u("div",x,[o(N,{type:"info",onClick:t[0]||(t[0]=()=>O())},{default:r((()=>t[2]||(t[2]=[f("取消")]))),_:1,__:[2]})])])):d("",!0),!q.value&&w.value?(l(),h(a,{key:1,auth_info:v.authInfo,auth_id:v.authId,"user-name":v.userName,last_id:v.lastId,"secondary-type":w.value.type,onVerificationSuccess:B,onBack:t[1]||(t[1]=a=>q.value=!0),onCancel:O},null,8,["auth_info","auth_id","user-name","last_id","secondary-type"])):d("",!0)])])}}}),[["__scopeId","data-v-3e719deb"]]);export{C as default};
