<template>
  <teleport to="body">
    <transition name="drawer-fade">
      <div 
        v-show="visible"
        class="base-drawer__wrapper"
        @click="handleWrapperClick"
      >
        <div 
          :class="[
            'base-drawer',
            `base-drawer--${direction}`,
            { 'base-drawer--with-header': showHeader }
          ]"
          :style="drawerStyle"
          @click.stop
        >
          <!-- 头部 -->
          <header 
            v-if="showHeader"
            class="base-drawer__header"
          >
            <slot name="title">
              <span class="base-drawer__title">{{ title }}</span>
            </slot>
            <button 
              v-if="showClose"
              class="base-drawer__close-btn"
              type="button"
              @click="handleClose"
            >
              <svg class="base-drawer__close-icon" viewBox="0 0 1024 1024">
                <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3.1-3.6-7.6-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3.1 3.6 7.6 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
              </svg>
            </button>
          </header>
          
          <!-- 内容 -->
          <main class="base-drawer__body">
            <slot />
          </main>
          
          <!-- 底部 -->
          <footer 
            v-if="$slots.footer"
            class="base-drawer__footer"
          >
            <slot name="footer" />
          </footer>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script>
export default {
  name: 'BaseDrawer',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    size: {
      type: [String, Number],
      default: '30%'
    },
    direction: {
      type: String,
      default: 'rtl',
      validator: (value) => ['ltr', 'rtl', 'ttb', 'btt'].includes(value)
    },
    modal: {
      type: Boolean,
      default: true
    },
    modalClass: {
      type: String,
      default: ''
    },
    showClose: {
      type: Boolean,
      default: true
    },
    closeOnClickModal: {
      type: Boolean,
      default: true
    },
    closeOnPressEscape: {
      type: Boolean,
      default: true
    },
    openDelay: {
      type: Number,
      default: 0
    },
    closeDelay: {
      type: Number,
      default: 0
    },
    customClass: {
      type: String,
      default: ''
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    beforeClose: {
      type: Function,
      default: null
    },
    lockScroll: {
      type: Boolean,
      default: true
    },
    withHeader: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'open', 'opened', 'close', 'closed'],
  data() {
    return {
      visible: this.modelValue,
      opening: false,
      closing: false
    }
  },
  computed: {
    showHeader() {
      return this.withHeader && (this.title || this.$slots.title)
    },
    drawerStyle() {
      const style = {}
      
      if (this.direction === 'ltr' || this.direction === 'rtl') {
        style.width = typeof this.size === 'number' ? `${this.size}px` : this.size
      } else {
        style.height = typeof this.size === 'number' ? `${this.size}px` : this.size
      }
      
      return style
    }
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.open()
      } else {
        this.close()
      }
    },
    visible(val) {
      if (val) {
        this.handleLockScroll()
      } else {
        this.handleUnlockScroll()
      }
    }
  },
  mounted() {
    if (this.modelValue) {
      this.open()
    }
  },
  beforeUnmount() {
    this.handleUnlockScroll()
  },
  methods: {
    open() {
      if (this.opening) return
      
      this.opening = true
      this.visible = true
      this.$emit('update:modelValue', true)
      this.$emit('open')
      
      this.$nextTick(() => {
        this.opening = false
        this.$emit('opened')
      })
    },
    close() {
      if (this.closing) return
      
      const shouldClose = () => {
        this.closing = true
        this.visible = false
        this.$emit('update:modelValue', false)
        this.$emit('close')
        
        this.$nextTick(() => {
          this.closing = false
          this.$emit('closed')
        })
      }
      
      if (this.beforeClose) {
        this.beforeClose(shouldClose)
      } else {
        shouldClose()
      }
    },
    handleClose() {
      this.close()
    },
    handleWrapperClick() {
      if (this.closeOnClickModal) {
        this.close()
      }
    },
    handleLockScroll() {
      if (this.lockScroll) {
        document.body.style.overflow = 'hidden'
      }
    },
    handleUnlockScroll() {
      if (this.lockScroll) {
        document.body.style.overflow = ''
      }
    },
    handleEscapeKeydown(event) {
      if (this.closeOnPressEscape && event.code === 'Escape' && this.visible) {
        this.close()
      }
    }
  },
  created() {
    if (this.closeOnPressEscape) {
      document.addEventListener('keydown', this.handleEscapeKeydown)
    }
  },
  beforeUnmount() {
    if (this.closeOnPressEscape) {
      document.removeEventListener('keydown', this.handleEscapeKeydown)
    }
  }
}
</script>

<style lang="scss" scoped>
.base-drawer__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  margin: 0;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.5);
}

.base-drawer {
  position: absolute;
  background-color: #fff;
  box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2), 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  
  &--rtl {
    right: 0;
    top: 0;
    height: 100%;
  }
  
  &--ltr {
    left: 0;
    top: 0;
    height: 100%;
  }
  
  &--ttb {
    top: 0;
    left: 0;
    width: 100%;
  }
  
  &--btt {
    bottom: 0;
    left: 0;
    width: 100%;
  }
  
  &__header {
    align-items: center;
    color: #72767b;
    display: flex;
    margin-bottom: 32px;
    padding: 20px 20px 0;
    justify-content: space-between;
  }
  
  &__title {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #303133;
  }
  
  &__close-btn {
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: inherit;
    background-color: transparent;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      color: #536ce6;
    }
  }
  
  &__close-icon {
    width: 14px;
    height: 14px;
    fill: currentColor;
  }
  
  &__body {
    flex: 1;
    overflow: auto;
    padding: 0 20px;
  }
  
  &__footer {
    padding: 20px;
    border-top: 1px solid #e4e7ed;
  }
}

// 动画
.drawer-fade-enter-active,
.drawer-fade-leave-active {
  transition: opacity 0.3s ease;
  
  .base-drawer {
    transition: transform 0.3s ease;
  }
}

.drawer-fade-enter-from,
.drawer-fade-leave-to {
  opacity: 0;
  
  .base-drawer--rtl {
    transform: translateX(100%);
  }
  
  .base-drawer--ltr {
    transform: translateX(-100%);
  }
  
  .base-drawer--ttb {
    transform: translateY(-100%);
  }
  
  .base-drawer--btt {
    transform: translateY(100%);
  }
}
</style>
