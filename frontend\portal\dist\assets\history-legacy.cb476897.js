/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}System.register(["./index-legacy.00b16b45.js","./index-browser-esm-legacy.6966c248.js"],(function(e,r){"use strict";var a,u,o,i,l,s,c,f,v,m,p,y,d,b,g,h,S,O,x,w,j,I,k,q,C,P,N,J,E=document.createElement("style");return E.textContent='@charset "UTF-8";.contextmenu{width:100px;margin:0;border:1px solid #ccc;background:#fff;z-index:3000;position:absolute;list-style-type:none;padding:5px 0;border-radius:4px;font-size:14px;color:#333;box-shadow:2px 2px 3px rgba(0,0,0,.2)}.el-tabs__item .el-icon-close{color:initial!important}.el-tabs__item .dot{content:"";width:9px;height:9px;margin-right:8px;display:inline-block;border-radius:50%;transition:background-color .2s}.contextmenu li{margin:0;padding:7px 16px}.contextmenu li:hover{background:#f2f2f2;cursor:pointer}\n',document.head.appendChild(E),{setters:[function(e){a=e._,u=e.u,o=e.a,i=e.r,l=e.b,s=e.c,c=e.z,f=e.K,v=e.Q,m=e.h,p=e.o,y=e.d,d=e.j,b=e.w,g=e.F,h=e.i,S=e.g,O=e.e,x=e.B,w=e.m,j=e.k,I=e.t,k=e.S,q=e.O,C=e.f,P=e.G,N=e.H},function(e){J=e.J}],execute:function(){var r={class:"router-history"},E=["tab"],_=Object.assign({name:"HistoryComponent"},{setup:function(e){var a=u(),_=o(),V=function(e){return e.name+JSON.stringify(e.query)+JSON.stringify(e.params)},T=i([]),D=i(""),A=i(!1),z=l(),L=function(e){return e.name+JSON.stringify(e.query)+JSON.stringify(e.params)},R=i(0),F=i(0),H=i(!1),U=i(!1),$=i(""),B=s((function(){return J("$..defaultRouter[0]",z.userInfo)[0]||"dashboard"})),G=function(){T.value=[{name:B.value,meta:{title:"总览"},query:{},params:{}}],_.push({name:B.value}),A.value=!1,sessionStorage.setItem("historys",JSON.stringify(T.value))},K=function(){var e,t=T.value.findIndex((function(t){return V(t)===$.value&&(e=t),V(t)===$.value})),n=T.value.findIndex((function(e){return V(e)===D.value}));T.value.splice(0,t),t>n&&_.push(e),sessionStorage.setItem("historys",JSON.stringify(T.value))},Q=function(){var e,t=T.value.findIndex((function(t){return V(t)===$.value&&(e=t),V(t)===$.value})),n=T.value.findIndex((function(e){return V(e)===D.value}));T.value.splice(t+1,T.value.length),t<n&&_.push(e),sessionStorage.setItem("historys",JSON.stringify(T.value))},X=function(){var e;T.value=T.value.filter((function(t){return V(t)===$.value&&(e=t),V(t)===$.value})),_.push(e),sessionStorage.setItem("historys",JSON.stringify(T.value))},Y=function(e){if(!T.value.some((function(t){return function(e,t){if(e.name!==t.name)return!1;if(Object.keys(e.query).length!==Object.keys(t.query).length||Object.keys(e.params).length!==Object.keys(t.params).length)return!1;for(var n in e.query)if(e.query[n]!==t.query[n])return!1;for(var r in e.params)if(e.params[r]!==t.params[r])return!1;return!0}(t,e)}))){var r={};r.name=e.name,r.meta=function(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?t(Object(a),!0).forEach((function(t){n(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}({},e.meta),delete r.meta.matched,r.query=e.query,r.params=e.params,T.value.push(r)}window.sessionStorage.setItem("activeValue",V(e))},M=i({});c((function(){return T.value}),(function(){M.value={},T.value.forEach((function(e){M.value[V(e)]=e}))}));var W=function(e){var t=M.value[e];_.push({name:t.name,query:t.query,params:t.params})},Z=function(e){var t=T.value.findIndex((function(t){return V(t)===e}));V(a)===e&&(1===T.value.length?_.push({name:B.value}):t<T.value.length-1?_.push({name:T.value[t+1].name,query:T.value[t+1].query,params:T.value[t+1].params}):_.push({name:T.value[t-1].name,query:T.value[t-1].query,params:T.value[t-1].params})),T.value.splice(t,1)};c((function(){return A.value}),(function(){A.value?document.body.addEventListener("click",(function(){A.value=!1})):document.body.removeEventListener("click",(function(){A.value=!1}))})),c((function(){return a}),(function(e,t){"Login"!==e.name&&"Reload"!==e.name&&(T.value=T.value.filter((function(e){return!e.meta.closeTab})),Y(e),sessionStorage.setItem("historys",JSON.stringify(T.value)),D.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),c((function(){return T.value}),(function(){sessionStorage.setItem("historys",JSON.stringify(T.value))}),{deep:!0});return function(){v.on("closeThisPage",(function(){Z(L(a))})),v.on("closeAllPage",(function(){G()})),v.on("mobile",(function(e){U.value=e})),v.on("collapse",(function(e){H.value=e}));var e=[{name:B.value,meta:{title:"总览"},query:{},params:{}}];T.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?D.value=window.sessionStorage.getItem("activeValue"):D.value=V(a),Y(a),"true"===window.sessionStorage.getItem("needCloseAll")&&(G(),window.sessionStorage.removeItem("needCloseAll"))}(),f((function(){v.off("collapse"),v.off("mobile")})),function(e,t){var n=m("el-tab-pane"),u=m("el-tabs");return p(),y("div",r,[d(u,{modelValue:D.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return D.value=e}),closable:!(1===T.value.length&&e.$route.name===B.value),type:"card",onContextmenu:t[1]||(t[1]=q((function(e){return function(e){if(1===T.value.length&&a.name===B.value)return!1;var t,n="";(n="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id)&&(A.value=!0,t=H.value?54:220,U.value&&(t=0),R.value=e.clientX-t,F.value=e.clientY+10,$.value=n.substring(4))}(e)}),["prevent"])),onTabChange:W,onTabRemove:Z},{default:b((function(){return[(p(!0),y(g,null,h(T.value,(function(e){return p(),S(n,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:b((function(){return[O("span",{tab:e,style:x({color:D.value===L(e)?w(z).activeColor:"#333"})},[O("i",{class:"dot",style:x({backgroundColor:D.value===L(e)?w(z).activeColor:"#ddd"})},null,4),j(" "+I(w(k)(e.meta.title,e)),1)],12,E)]})),_:2},1032,["label","name","tab"])})),128))]})),_:1},8,["modelValue","closable"]),C("自定义右键菜单html代码"),P(O("ul",{style:x({left:R.value+"px",top:F.value+"px"}),class:"contextmenu"},[O("li",{onClick:G},"关闭所有"),O("li",{onClick:K},"关闭左侧"),O("li",{onClick:Q},"关闭右侧"),O("li",{onClick:X},"关闭其他")],4),[[N,A.value]])])}}});e("default",a(_,[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/historyComponent/history.vue"]]))}}}))}();
