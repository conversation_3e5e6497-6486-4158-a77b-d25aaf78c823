/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(t,n,a){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a,t}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function l(n,a,r,o){var l=a&&a.prototype instanceof c?a:c,s=Object.create(l.prototype);return i(s,"_invoke",function(n,a,r){var i,o,l,c=0,s=r||[],p=!1,d={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,o=0,l=e,d.n=n,u}};function h(n,a){for(o=n,l=a,t=0;!p&&c&&!r&&t<s.length;t++){var r,i=s[t],h=d.p,v=i[2];n>3?(r=v===a)&&(l=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=h&&((r=n<2&&h<i[1])?(o=0,d.v=a,d.n=i[1]):h<v&&(r=n<3||i[0]>a||a>v)&&(i[4]=n,i[5]=a,d.n=v,o=0))}if(r||n>1)return u;throw p=!0,a}return function(r,s,v){if(c>1)throw TypeError("Generator is already running");for(p&&1===s&&h(s,v),o=s,l=v;(t=o<2?e:l)||!p;){i||(o?o<3?(o>1&&(d.n=-1),h(o,l)):d.n=l:d.v=l);try{if(c=2,i){if(o||(r="next"),t=i[r]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),o=1);i=e}else if((t=(p=d.n<0)?l:n.call(a,d))!==u)break}catch(t){i=e,o=1,l=t}finally{c=1}}return{value:t,done:p}}}(n,r,o),!0),s}var u={};function c(){}function s(){}function p(){}t=Object.getPrototypeOf;var d=[][a]?t(t([][a]())):(i(t={},a,(function(){return this})),t),h=p.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,i(e,o,"GeneratorFunction")),e.prototype=Object.create(h),e}return s.prototype=p,i(h,"constructor",p),i(p,"constructor",s),s.displayName="GeneratorFunction",i(p,o,"GeneratorFunction"),i(h),i(h,o,"Generator"),i(h,a,(function(){return this})),i(h,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:l,m:v}})()}function i(e,t,n,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,a){if(t)r?r(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n;else{var o=function(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},i(e,t,n,a)}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function u(e,t,n,a,r,i,o){try{var l=e[i](o),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(a,r)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function o(e){u(i,a,r,o,l,"next",e)}function l(e){u(i,a,r,o,l,"throw",e)}o(void 0)}))}}System.register(["./index-legacy.f4c7ba6b.js"],(function(e,t){"use strict";var a,i,l,u,s,p,d,h,v,f,m,g,y,x,b,w,k,j,O,C,_,S,P,T,z,L=document.createElement("style");return L.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+');background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}.error-component{text-align:center;padding:20px;background-color:#fef2f2;border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;margin:10px 0}.error-component:before{content:"\\26a0\\fe0f";display:block;font-size:24px;margin-bottom:8px}.auth-switcher{margin-top:24px}.auth-switcher .auth-switcher-title{text-align:center;color:#929298;font-size:14px;margin-bottom:20px;position:relative}.auth-switcher .auth-switcher-title:before,.auth-switcher .auth-switcher-title:after{content:"";position:absolute;top:50%;width:80px;height:1px;background:#e8e8e8}.auth-switcher .auth-switcher-title:before{left:0}.auth-switcher .auth-switcher-title:after{right:0}.auth-switcher .auth-switcher-container{display:flex;align-items:center;justify-content:center;position:relative}.auth-switcher .auth-switcher-container .auth-nav-btn{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border:1px solid #e8e8e8;border-radius:50%;background:#ffffff;color:#666;cursor:pointer;transition:all .3s ease;z-index:2}.auth-switcher .auth-switcher-container .auth-nav-btn:hover:not(:disabled){border-color:#1890ff;color:#1890ff;box-shadow:0 2px 8px rgba(24,144,255,.2)}.auth-switcher .auth-switcher-container .auth-nav-btn:disabled{opacity:.3;cursor:not-allowed}.auth-switcher .auth-switcher-container .auth-nav-btn.auth-nav-next{margin-left:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper{flex:1;max-width:320px;overflow:hidden;position:relative}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container{display:flex;transition:transform .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item{flex:0 0 80px;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:12px 8px;cursor:pointer;transition:all .3s ease;border-radius:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover{background:#f8f9fa;transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,.1)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon{width:40px;height:40px;border-radius:50%;background:#f5f5f7;display:flex;align-items:center;justify-content:center;margin-bottom:8px;color:#fff;font-size:18px;transition:all .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-name{font-size:12px;color:#666;text-align:center;line-height:1.2;max-width:64px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover .auth-method-icon{transform:scale(1.1);box-shadow:0 4px 12px rgba(0,0,0,.2)}\n',document.head.appendChild(L),{setters:[function(e){a=e.x,i=e._,l=e.y,u=e.u,s=e.r,p=e.c,d=e.b,h=e.z,v=e.p,f=e.o,m=e.d,g=e.e,y=e.f,x=e.j,b=e.m,w=e.F,k=e.k,j=e.t,O=e.g,C=e.A,_=e.B,S=e.i,P=e.C,T=e.L,z=e.D}],execute:function(){var L={class:"login-page"},q={class:"content"},I={class:"right-panel"},A={key:0},E={key:0,class:"title"},U={key:1,class:"title"},D={style:{"text-align":"center"}},G={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},F={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},N=["xlink:href"],R={key:2,class:"login_panel_form"},K={key:3,class:"auth-switcher"},M={class:"auth-switcher-container"},H=["disabled"],J={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},W={class:"auth-methods-wrapper"},B=["onClick"],V=["data-auth-type"],X={class:"icon","aria-hidden":"true",style:{height:"18px",width:"18px"}},Y=["xlink:href"],$={class:"auth-method-name"},Q=["disabled"],Z={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},ee={class:"auth-waiting"},te={class:"waiting-icon"},ne={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},ae=["xlink:href"],re={class:"waiting-title"},ie={class:"security-tips"},oe={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px",color:"#67c23a"}},le=Object.assign({name:"Login"},{setup:function(e){var i=l({loader:function(){return P((function(){return t.import("./localLogin-legacy.38aaec1d.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=l({loader:function(){return P((function(){return t.import("./wechat-legacy.ae63fea6.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=l({loader:function(){return P((function(){return t.import("./feishu-legacy.e9c71457.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ce=l({loader:function(){return P((function(){return t.import("./dingtalk-legacy.3f6b0299.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=l({loader:function(){return P((function(){return t.import("./oauth2-legacy.0f3ce309.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),pe=l({loader:function(){return P((function(){return t.import("./sms-legacy.ed9b2c2f.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),de=l({loader:function(){return P((function(){return t.import("./secondaryAuth-legacy.261548df.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),he=l({loader:function(){return P((function(){return t.import("./serverConfig-legacy.0b37bdad.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ve=u(),fe=s(0),me=s([]),ge=s("local"),ye=s(""),xe=s(""),be=s(""),we=s([]),ke=s([]),je=s(!1),Oe=s(!1),Ce=s(),_e=s(""),Se=s(!1),Pe=s(""),Te=s(!1),ze=s(""),Le=s(""),qe=s(""),Ie=s({}),Ae=s(0),Ee=s(80),Ue=s(3),De=s(null),Ge=p((function(){var e=je.value?ze.value:xe.value;return me.value.filter((function(t){return t.id!==e}))})),Fe=p((function(){return Math.max(0,Ge.value.length-Ue.value)})),Ne=d();p((function(){return ke.value.filter((function(e){return e.id!==xe.value}))}));var Re=function(){var e={};if(ve.query.type&&(e.type=ve.query.type),ve.query.wp&&(e.wp=ve.query.wp),ve.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(ve.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),a=new URLSearchParams(n);a.get("type")&&(e.type=a.get("type")),a.get("wp")&&(e.wp=a.get("wp"))}}catch(r){console.warn("解析redirect参数失败:",r)}return e},Ke=function(){if(z.isClient()){var e=urlHashParams?urlHashParams.get("WebUrl"):"";try{var t=new URL(e);e="".concat(t.protocol,"//").concat(t.host)}catch(n){e="",console.warn("解析 WebUrl 参数失败:",n)}if(e)return!1;if(!localStorage.getItem("server_host"))return!0}return!1},Me=function(e){logger.log("服务器配置完成:",e),Oe.value=!1,He()},He=function(){var e=c(r().m((function e(){var t,n,i,l,u,c,s,p,d,h,v,f,m,g,y,x,b,w,k,j;return r().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,!Ke()){e.n=1;break}return Oe.value=!0,e.a(2);case 1:return t=Re(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=2,a({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 2:if(200===(n=e.v).status){if(me.value=n.data.idpList,(i=ve.query.idp_id||Ne.loginType)&&"undefined"!==i){l=!1,u=o(n.data.idpList);try{for(u.s();!(c=u.n()).done;)s=c.value,i===s.id&&(l=!0,xe.value=s.id,ge.value=s.type,ye.value=s.templateType,we.value=s.attrs,we.value.name=s.name,we.value.authType=s.type)}catch(r){u.e(r)}finally{u.f()}l||(be.value=null===(p=me.value[0])||void 0===p?void 0:p.id,xe.value=null===(d=me.value[0])||void 0===d?void 0:d.id,ge.value=null===(h=me.value[0])||void 0===h?void 0:h.type,ye.value=null===(v=me.value[0])||void 0===v?void 0:v.templateType,we.value=null===(f=me.value[0])||void 0===f?void 0:f.attrs,we.value.name=me.value[0].name,we.value.authType=null===(m=me.value[0])||void 0===m?void 0:m.type)}else be.value=null===(g=me.value[0])||void 0===g?void 0:g.id,xe.value=null===(y=me.value[0])||void 0===y?void 0:y.id,ge.value=null===(x=me.value[0])||void 0===x?void 0:x.type,ye.value=null===(b=me.value[0])||void 0===b?void 0:b.templateType,we.value=null===(w=me.value[0])||void 0===w?void 0:w.attrs,we.value.name=me.value[0].name,we.value.authType=null===(k=me.value[0])||void 0===k?void 0:k.type;++fe.value}e.n=4;break;case 3:e.p=3,j=e.v,console.error("获取认证列表失败:",j),z.isClient()&&(Oe.value=!0);case 4:return e.a(2)}}),e,null,[[0,3]])})));return function(){return e.apply(this,arguments)}}();He();var Je=p((function(){switch(ge.value){case"local":case"msad":case"ldap":case"web":case"email":return i;case"qiyewx":return le;case"feishu":return ue;case"dingtalk":return ce;case"oauth2":case"cas":return se;case"sms":return pe;default:return"oauth2"===ye.value?se:"local"}})),We=p((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===Pe.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===Pe.value}]})),Be=function(){je.value=!1,ke.value=[],Ce.value="",_e.value="",Pe.value="",Te.value=!1,ze.value&&(xe.value=ze.value,ge.value=Le.value,ye.value=qe.value,we.value=n({},Ie.value),ze.value="",Le.value="",qe.value="",Ie.value={}),++fe.value,console.log("取消后恢复的状态:",{isSecondary:je.value,auth_id:xe.value,auth_type:ge.value})},Ve=function(){var e=c(r().m((function e(t){var n,a,i;return r().w((function(e){for(;;)switch(e.n){case 0:n=T.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{a=ve.query.redirect_url||"/",t.clientParams&&((i=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&i.set("wp",t.clientParams.wp),a+=(a.includes("?")?"&":"?")+i.toString()),window.location.href=a}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),Xe=p((function(){return!["dingtalk","feishu","qiyewx"].includes(ge.value)&&("oauth2"!==ye.value&&"cas"!==ge.value||("cas"===ge.value?1===parseInt(we.value.casOpenType):"oauth2"===ye.value&&1===parseInt(we.value.oauth2OpenType)))})),Ye=function(){Ae.value>0&&Ae.value--},$e=function(){Ae.value<Fe.value&&Ae.value++},Qe=function(e){be.value=e.id,we.value=e.attrs||{},we.value.name=e.name,we.value.authType=e.type,je.value&&(we.value.uniqKey=Ce.value,we.value.notPhone=Se.value),xe.value=e.id,ge.value=e.type,ye.value=e.templateType,++fe.value};return h(je,c(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:je.value&&(ze.value=xe.value,Le.value=ge.value,qe.value=ye.value,Ie.value=n({},we.value),console.log("二次认证数据:",{secondary:ke.value,secondaryLength:ke.value.length}),ke.value.length>0&&Qe(ke.value[0]));case 1:return e.a(2)}}),e)})))),v("secondary",ke),v("isSecondary",je),v("uniqKey",Ce),v("userName",_e),v("notPhone",Se),v("last_id",be),v("contactType",Pe),v("hasContactInfo",Te),function(e,t){return f(),m("div",L,[g("div",q,[t[6]||(t[6]=g("div",{class:"left-panel"},[y(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),y('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),y(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),g("div",I,[y(" 服务器配置状态 "),Oe.value?(f(),m("div",A,[x(b(he),{onServerConfigured:Me})])):je.value?(f(),m(w,{key:2},[y(" 二次认证等待状态 "),g("div",ee,[g("div",te,[(f(),m("svg",ne,[g("use",{"xlink:href":"#icon-auth-".concat(Le.value||ge.value)},null,8,ae)]))]),g("h4",re,j(Ie.value.name||we.value.name)+" 登录成功",1),t[5]||(t[5]=g("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),g("div",ie,[(f(),m("svg",oe,t[3]||(t[3]=[g("use",{"xlink:href":"#icon-shield"},null,-1)]))),t[4]||(t[4]=g("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(f(),m(w,{key:1},[y(" 正常登录状态 "),g("div",null,["local"===ge.value?(f(),m("span",E,"本地账号登录")):Xe.value?(f(),m("span",U,[g("div",D,[g("span",G,[(f(),m("svg",F,[g("use",{"xlink:href":"#icon-auth-"+ge.value},null,8,N)])),k(" "+j(we.value.name),1)])])])):y("v-if",!0),xe.value?(f(),m("div",R,[y(' <component :is="getLoginType"></component> '),(f(),O(C(Je.value),{auth_id:xe.value,auth_info:we.value},null,8,["auth_id","auth_info"])),y(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):y("v-if",!0),Ge.value.length>0?(f(),m("div",K,[t[2]||(t[2]=g("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),g("div",M,[Ae.value>0?(f(),m("button",{key:0,class:"auth-nav-btn auth-nav-prev",onClick:Ye,disabled:0===Ae.value},[(f(),m("svg",J,t[0]||(t[0]=[g("use",{"xlink:href":"#icon-chevron-left"},null,-1)])))],8,H)):y("v-if",!0),g("div",W,[g("div",{class:"auth-methods-container",ref_key:"authMethodsContainer",ref:De,style:_({transform:"translateX(-".concat(Ae.value*Ee.value,"px)")})},[(f(!0),m(w,null,S(Ge.value,(function(e){return f(),m("div",{key:e.id,class:"auth-method-item",onClick:function(t){return Qe(e)}},[g("div",{class:"auth-method-icon","data-auth-type":e.type},[(f(),m("svg",X,[g("use",{"xlink:href":"#icon-auth-".concat(e.type)},null,8,Y)]))],8,V),g("div",$,j(e.name),1)],8,B)})),128))],4)]),Ae.value<Fe.value?(f(),m("button",{key:1,class:"auth-nav-btn auth-nav-next",onClick:$e,disabled:Ae.value>=Fe.value},[(f(),m("svg",Z,t[1]||(t[1]=[g("use",{"xlink:href":"#icon-chevron-right"},null,-1)])))],8,Q)):y("v-if",!0)])])):y("v-if",!0)])],2112))])]),y(" 二次认证弹窗 "),je.value?(f(),O(b(de),{key:0,"auth-info":{uniqKey:Ce.value,contactType:Pe.value,hasContactInfo:Te.value},"auth-id":xe.value,"user-name":_e.value,"last-id":be.value,"auth-methods":We.value,onVerificationSuccess:Ve,onCancel:Be},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):y("v-if",!0)])}}});e("default",i(le,[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]))}}}))}();
