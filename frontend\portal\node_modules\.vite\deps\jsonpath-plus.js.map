{"version": 3, "sources": ["../../jsonpath-plus/dist/index-browser-esm.js"], "sourcesContent": ["function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}\n\nfunction _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\n\nfunction _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !_isNativeFunction(Class)) return Class;\n\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n\n      _cache.set(Class, Wrapper);\n    }\n\n    function Wrapper() {\n      return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n    }\n\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return _setPrototypeOf(Wrapper, Class);\n  };\n\n  return _wrapNativeSuper(Class);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n\n  if (!it) {\n    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n      if (it) o = it;\n      var i = 0;\n\n      var F = function () {};\n\n      return {\n        s: F,\n        n: function () {\n          if (i >= o.length) return {\n            done: true\n          };\n          return {\n            done: false,\n            value: o[i++]\n          };\n        },\n        e: function (e) {\n          throw e;\n        },\n        f: F\n      };\n    }\n\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  var normalCompletion = true,\n      didErr = false,\n      err;\n  return {\n    s: function () {\n      it = it.call(o);\n    },\n    n: function () {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function (e) {\n      didErr = true;\n      err = e;\n    },\n    f: function () {\n      try {\n        if (!normalCompletion && it.return != null) it.return();\n      } finally {\n        if (didErr) throw err;\n      }\n    }\n  };\n}\n\nvar hasOwnProp = Object.prototype.hasOwnProperty;\n/**\n * @typedef {null|boolean|number|string|PlainObject|GenericArray} JSONObject\n */\n\n/**\n * @typedef {any} AnyItem\n */\n\n/**\n * @typedef {any} AnyResult\n */\n\n/**\n * Copies array and then pushes item into it.\n * @param {GenericArray} arr Array to copy and into which to push\n * @param {AnyItem} item Array item to add (to end)\n * @returns {GenericArray} Copy of the original array\n */\n\nfunction push(arr, item) {\n  arr = arr.slice();\n  arr.push(item);\n  return arr;\n}\n/**\n * Copies array and then unshifts item into it.\n * @param {AnyItem} item Array item to add (to beginning)\n * @param {GenericArray} arr Array to copy and into which to unshift\n * @returns {GenericArray} Copy of the original array\n */\n\n\nfunction unshift(item, arr) {\n  arr = arr.slice();\n  arr.unshift(item);\n  return arr;\n}\n/**\n * Caught when JSONPath is used without `new` but rethrown if with `new`\n * @extends Error\n */\n\n\nvar NewError = /*#__PURE__*/function (_Error) {\n  _inherits(NewError, _Error);\n\n  var _super = _createSuper(NewError);\n\n  /**\n   * @param {AnyResult} value The evaluated scalar value\n   */\n  function NewError(value) {\n    var _this;\n\n    _classCallCheck(this, NewError);\n\n    _this = _super.call(this, 'JSONPath should not be called with \"new\" (it prevents return ' + 'of (unwrapped) scalar values)');\n    _this.avoidNew = true;\n    _this.value = value;\n    _this.name = 'NewError';\n    return _this;\n  }\n\n  return _createClass(NewError);\n}( /*#__PURE__*/_wrapNativeSuper(Error));\n/**\n* @typedef {PlainObject} ReturnObject\n* @property {string} path\n* @property {JSONObject} value\n* @property {PlainObject|GenericArray} parent\n* @property {string} parentProperty\n*/\n\n/**\n* @callback JSONPathCallback\n* @param {string|PlainObject} preferredOutput\n* @param {\"value\"|\"property\"} type\n* @param {ReturnObject} fullRetObj\n* @returns {void}\n*/\n\n/**\n* @callback OtherTypeCallback\n* @param {JSONObject} val\n* @param {string} path\n* @param {PlainObject|GenericArray} parent\n* @param {string} parentPropName\n* @returns {boolean}\n*/\n\n/* eslint-disable max-len -- Can make multiline type after https://github.com/syavorsky/comment-parser/issues/109 */\n\n/**\n * @typedef {PlainObject} JSONPathOptions\n * @property {JSON} json\n * @property {string|string[]} path\n * @property {\"value\"|\"path\"|\"pointer\"|\"parent\"|\"parentProperty\"|\"all\"} [resultType=\"value\"]\n * @property {boolean} [flatten=false]\n * @property {boolean} [wrap=true]\n * @property {PlainObject} [sandbox={}]\n * @property {boolean} [preventEval=false]\n * @property {PlainObject|GenericArray|null} [parent=null]\n * @property {string|null} [parentProperty=null]\n * @property {JSONPathCallback} [callback]\n * @property {OtherTypeCallback} [otherTypeCallback] Defaults to\n *   function which throws on encountering `@other`\n * @property {boolean} [autostart=true]\n */\n\n/* eslint-enable max-len -- Can make multiline type after https://github.com/syavorsky/comment-parser/issues/109 */\n\n/**\n * @param {string|JSONPathOptions} opts If a string, will be treated as `expr`\n * @param {string} [expr] JSON path to evaluate\n * @param {JSON} [obj] JSON object to evaluate against\n * @param {JSONPathCallback} [callback] Passed 3 arguments: 1) desired payload\n *     per `resultType`, 2) `\"value\"|\"property\"`, 3) Full returned object with\n *     all payloads\n * @param {OtherTypeCallback} [otherTypeCallback] If `@other()` is at the end\n *   of one's query, this will be invoked with the value of the item, its\n *   path, its parent, and its parent's property name, and it should return\n *   a boolean indicating whether the supplied value belongs to the \"other\"\n *   type or not (or it may handle transformations and return `false`).\n * @returns {JSONPath}\n * @class\n */\n\n\nfunction JSONPath(opts, expr, obj, callback, otherTypeCallback) {\n  // eslint-disable-next-line no-restricted-syntax\n  if (!(this instanceof JSONPath)) {\n    try {\n      return new JSONPath(opts, expr, obj, callback, otherTypeCallback);\n    } catch (e) {\n      if (!e.avoidNew) {\n        throw e;\n      }\n\n      return e.value;\n    }\n  }\n\n  if (typeof opts === 'string') {\n    otherTypeCallback = callback;\n    callback = obj;\n    obj = expr;\n    expr = opts;\n    opts = null;\n  }\n\n  var optObj = opts && _typeof(opts) === 'object';\n  opts = opts || {};\n  this.json = opts.json || obj;\n  this.path = opts.path || expr;\n  this.resultType = opts.resultType || 'value';\n  this.flatten = opts.flatten || false;\n  this.wrap = hasOwnProp.call(opts, 'wrap') ? opts.wrap : true;\n  this.sandbox = opts.sandbox || {};\n  this.preventEval = opts.preventEval || false;\n  this.parent = opts.parent || null;\n  this.parentProperty = opts.parentProperty || null;\n  this.callback = opts.callback || callback || null;\n\n  this.otherTypeCallback = opts.otherTypeCallback || otherTypeCallback || function () {\n    throw new TypeError('You must supply an otherTypeCallback callback option ' + 'with the @other() operator.');\n  };\n\n  if (opts.autostart !== false) {\n    var args = {\n      path: optObj ? opts.path : expr\n    };\n\n    if (!optObj) {\n      args.json = obj;\n    } else if ('json' in opts) {\n      args.json = opts.json;\n    }\n\n    var ret = this.evaluate(args);\n\n    if (!ret || _typeof(ret) !== 'object') {\n      throw new NewError(ret);\n    }\n\n    return ret;\n  }\n} // PUBLIC METHODS\n\n\nJSONPath.prototype.evaluate = function (expr, json, callback, otherTypeCallback) {\n  var _this2 = this;\n\n  var currParent = this.parent,\n      currParentProperty = this.parentProperty;\n  var flatten = this.flatten,\n      wrap = this.wrap;\n  this.currResultType = this.resultType;\n  this.currPreventEval = this.preventEval;\n  this.currSandbox = this.sandbox;\n  callback = callback || this.callback;\n  this.currOtherTypeCallback = otherTypeCallback || this.otherTypeCallback;\n  json = json || this.json;\n  expr = expr || this.path;\n\n  if (expr && _typeof(expr) === 'object' && !Array.isArray(expr)) {\n    if (!expr.path && expr.path !== '') {\n      throw new TypeError('You must supply a \"path\" property when providing an object ' + 'argument to JSONPath.evaluate().');\n    }\n\n    if (!hasOwnProp.call(expr, 'json')) {\n      throw new TypeError('You must supply a \"json\" property when providing an object ' + 'argument to JSONPath.evaluate().');\n    }\n\n    var _expr = expr;\n    json = _expr.json;\n    flatten = hasOwnProp.call(expr, 'flatten') ? expr.flatten : flatten;\n    this.currResultType = hasOwnProp.call(expr, 'resultType') ? expr.resultType : this.currResultType;\n    this.currSandbox = hasOwnProp.call(expr, 'sandbox') ? expr.sandbox : this.currSandbox;\n    wrap = hasOwnProp.call(expr, 'wrap') ? expr.wrap : wrap;\n    this.currPreventEval = hasOwnProp.call(expr, 'preventEval') ? expr.preventEval : this.currPreventEval;\n    callback = hasOwnProp.call(expr, 'callback') ? expr.callback : callback;\n    this.currOtherTypeCallback = hasOwnProp.call(expr, 'otherTypeCallback') ? expr.otherTypeCallback : this.currOtherTypeCallback;\n    currParent = hasOwnProp.call(expr, 'parent') ? expr.parent : currParent;\n    currParentProperty = hasOwnProp.call(expr, 'parentProperty') ? expr.parentProperty : currParentProperty;\n    expr = expr.path;\n  }\n\n  currParent = currParent || null;\n  currParentProperty = currParentProperty || null;\n\n  if (Array.isArray(expr)) {\n    expr = JSONPath.toPathString(expr);\n  }\n\n  if (!expr && expr !== '' || !json) {\n    return undefined;\n  }\n\n  var exprList = JSONPath.toPathArray(expr);\n\n  if (exprList[0] === '$' && exprList.length > 1) {\n    exprList.shift();\n  }\n\n  this._hasParentSelector = null;\n\n  var result = this._trace(exprList, json, ['$'], currParent, currParentProperty, callback).filter(function (ea) {\n    return ea && !ea.isParentSelector;\n  });\n\n  if (!result.length) {\n    return wrap ? [] : undefined;\n  }\n\n  if (!wrap && result.length === 1 && !result[0].hasArrExpr) {\n    return this._getPreferredOutput(result[0]);\n  }\n\n  return result.reduce(function (rslt, ea) {\n    var valOrPath = _this2._getPreferredOutput(ea);\n\n    if (flatten && Array.isArray(valOrPath)) {\n      rslt = rslt.concat(valOrPath);\n    } else {\n      rslt.push(valOrPath);\n    }\n\n    return rslt;\n  }, []);\n}; // PRIVATE METHODS\n\n\nJSONPath.prototype._getPreferredOutput = function (ea) {\n  var resultType = this.currResultType;\n\n  switch (resultType) {\n    case 'all':\n      {\n        var path = Array.isArray(ea.path) ? ea.path : JSONPath.toPathArray(ea.path);\n        ea.pointer = JSONPath.toPointer(path);\n        ea.path = typeof ea.path === 'string' ? ea.path : JSONPath.toPathString(ea.path);\n        return ea;\n      }\n\n    case 'value':\n    case 'parent':\n    case 'parentProperty':\n      return ea[resultType];\n\n    case 'path':\n      return JSONPath.toPathString(ea[resultType]);\n\n    case 'pointer':\n      return JSONPath.toPointer(ea.path);\n\n    default:\n      throw new TypeError('Unknown result type');\n  }\n};\n\nJSONPath.prototype._handleCallback = function (fullRetObj, callback, type) {\n  if (callback) {\n    var preferredOutput = this._getPreferredOutput(fullRetObj);\n\n    fullRetObj.path = typeof fullRetObj.path === 'string' ? fullRetObj.path : JSONPath.toPathString(fullRetObj.path); // eslint-disable-next-line n/callback-return\n\n    callback(preferredOutput, type, fullRetObj);\n  }\n};\n/**\n *\n * @param {string} expr\n * @param {JSONObject} val\n * @param {string} path\n * @param {PlainObject|GenericArray} parent\n * @param {string} parentPropName\n * @param {JSONPathCallback} callback\n * @param {boolean} hasArrExpr\n * @param {boolean} literalPriority\n * @returns {ReturnObject|ReturnObject[]}\n */\n\n\nJSONPath.prototype._trace = function (expr, val, path, parent, parentPropName, callback, hasArrExpr, literalPriority) {\n  var _this3 = this;\n\n  // No expr to follow? return path and value as the result of\n  //  this trace branch\n  var retObj;\n\n  if (!expr.length) {\n    retObj = {\n      path: path,\n      value: val,\n      parent: parent,\n      parentProperty: parentPropName,\n      hasArrExpr: hasArrExpr\n    };\n\n    this._handleCallback(retObj, callback, 'value');\n\n    return retObj;\n  }\n\n  var loc = expr[0],\n      x = expr.slice(1); // We need to gather the return value of recursive trace calls in order to\n  // do the parent sel computation.\n\n  var ret = [];\n  /**\n   *\n   * @param {ReturnObject|ReturnObject[]} elems\n   * @returns {void}\n   */\n\n  function addRet(elems) {\n    if (Array.isArray(elems)) {\n      // This was causing excessive stack size in Node (with or\n      //  without Babel) against our performance test:\n      //  `ret.push(...elems);`\n      elems.forEach(function (t) {\n        ret.push(t);\n      });\n    } else {\n      ret.push(elems);\n    }\n  }\n\n  if ((typeof loc !== 'string' || literalPriority) && val && hasOwnProp.call(val, loc)) {\n    // simple case--directly follow property\n    addRet(this._trace(x, val[loc], push(path, loc), val, loc, callback, hasArrExpr)); // eslint-disable-next-line unicorn/prefer-switch -- Part of larger `if`\n  } else if (loc === '*') {\n    // all child properties\n    this._walk(val, function (m) {\n      addRet(_this3._trace(x, val[m], push(path, m), val, m, callback, true, true));\n    });\n  } else if (loc === '..') {\n    // all descendent parent properties\n    // Check remaining expression with val's immediate children\n    addRet(this._trace(x, val, path, parent, parentPropName, callback, hasArrExpr));\n\n    this._walk(val, function (m) {\n      // We don't join m and x here because we only want parents,\n      //   not scalar values\n      if (_typeof(val[m]) === 'object') {\n        // Keep going with recursive descent on val's\n        //   object children\n        addRet(_this3._trace(expr.slice(), val[m], push(path, m), val, m, callback, true));\n      }\n    }); // The parent sel computation is handled in the frame above using the\n    // ancestor object of val\n\n  } else if (loc === '^') {\n    // This is not a final endpoint, so we do not invoke the callback here\n    this._hasParentSelector = true;\n    return {\n      path: path.slice(0, -1),\n      expr: x,\n      isParentSelector: true\n    };\n  } else if (loc === '~') {\n    // property name\n    retObj = {\n      path: push(path, loc),\n      value: parentPropName,\n      parent: parent,\n      parentProperty: null\n    };\n\n    this._handleCallback(retObj, callback, 'property');\n\n    return retObj;\n  } else if (loc === '$') {\n    // root only\n    addRet(this._trace(x, val, path, null, null, callback, hasArrExpr));\n  } else if (/^(\\x2D?[0-9]*):(\\x2D?[0-9]*):?([0-9]*)$/.test(loc)) {\n    // [start:end:step]  Python slice syntax\n    addRet(this._slice(loc, x, val, path, parent, parentPropName, callback));\n  } else if (loc.indexOf('?(') === 0) {\n    // [?(expr)] (filtering)\n    if (this.currPreventEval) {\n      throw new Error('Eval [?(expr)] prevented in JSONPath expression.');\n    }\n\n    var safeLoc = loc.replace(/^\\?\\(((?:[\\0-\\t\\x0B\\f\\x0E-\\u2027\\u202A-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])*?)\\)$/, '$1');\n\n    this._walk(val, function (m) {\n      if (_this3._eval(safeLoc, val[m], m, path, parent, parentPropName)) {\n        addRet(_this3._trace(x, val[m], push(path, m), val, m, callback, true));\n      }\n    });\n  } else if (loc[0] === '(') {\n    // [(expr)] (dynamic property/index)\n    if (this.currPreventEval) {\n      throw new Error('Eval [(expr)] prevented in JSONPath expression.');\n    } // As this will resolve to a property name (but we don't know it\n    //  yet), property and parent information is relative to the\n    //  parent of the property to which this expression will resolve\n\n\n    addRet(this._trace(unshift(this._eval(loc, val, path[path.length - 1], path.slice(0, -1), parent, parentPropName), x), val, path, parent, parentPropName, callback, hasArrExpr));\n  } else if (loc[0] === '@') {\n    // value type: @boolean(), etc.\n    var addType = false;\n    var valueType = loc.slice(1, -2);\n\n    switch (valueType) {\n      case 'scalar':\n        if (!val || !['object', 'function'].includes(_typeof(val))) {\n          addType = true;\n        }\n\n        break;\n\n      case 'boolean':\n      case 'string':\n      case 'undefined':\n      case 'function':\n        // eslint-disable-next-line valid-typeof\n        if (_typeof(val) === valueType) {\n          addType = true;\n        }\n\n        break;\n\n      case 'integer':\n        if (Number.isFinite(val) && !(val % 1)) {\n          addType = true;\n        }\n\n        break;\n\n      case 'number':\n        if (Number.isFinite(val)) {\n          addType = true;\n        }\n\n        break;\n\n      case 'nonFinite':\n        if (typeof val === 'number' && !Number.isFinite(val)) {\n          addType = true;\n        }\n\n        break;\n\n      case 'object':\n        // eslint-disable-next-line valid-typeof\n        if (val && _typeof(val) === valueType) {\n          addType = true;\n        }\n\n        break;\n\n      case 'array':\n        if (Array.isArray(val)) {\n          addType = true;\n        }\n\n        break;\n\n      case 'other':\n        addType = this.currOtherTypeCallback(val, path, parent, parentPropName);\n        break;\n\n      case 'null':\n        if (val === null) {\n          addType = true;\n        }\n\n        break;\n\n      /* c8 ignore next 2 */\n\n      default:\n        throw new TypeError('Unknown value type ' + valueType);\n    }\n\n    if (addType) {\n      retObj = {\n        path: path,\n        value: val,\n        parent: parent,\n        parentProperty: parentPropName\n      };\n\n      this._handleCallback(retObj, callback, 'value');\n\n      return retObj;\n    } // `-escaped property\n\n  } else if (loc[0] === '`' && val && hasOwnProp.call(val, loc.slice(1))) {\n    var locProp = loc.slice(1);\n    addRet(this._trace(x, val[locProp], push(path, locProp), val, locProp, callback, hasArrExpr, true));\n  } else if (loc.includes(',')) {\n    // [name1,name2,...]\n    var parts = loc.split(',');\n\n    var _iterator = _createForOfIteratorHelper(parts),\n        _step;\n\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var part = _step.value;\n        addRet(this._trace(unshift(part, x), val, path, parent, parentPropName, callback, true));\n      } // simple case--directly follow property\n\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  } else if (!literalPriority && val && hasOwnProp.call(val, loc)) {\n    addRet(this._trace(x, val[loc], push(path, loc), val, loc, callback, hasArrExpr, true));\n  } // We check the resulting values for parent selections. For parent\n  // selections we discard the value object and continue the trace with the\n  // current val object\n\n\n  if (this._hasParentSelector) {\n    for (var t = 0; t < ret.length; t++) {\n      var rett = ret[t];\n\n      if (rett && rett.isParentSelector) {\n        var tmp = this._trace(rett.expr, val, rett.path, parent, parentPropName, callback, hasArrExpr);\n\n        if (Array.isArray(tmp)) {\n          ret[t] = tmp[0];\n          var tl = tmp.length;\n\n          for (var tt = 1; tt < tl; tt++) {\n            t++;\n            ret.splice(t, 0, tmp[tt]);\n          }\n        } else {\n          ret[t] = tmp;\n        }\n      }\n    }\n  }\n\n  return ret;\n};\n\nJSONPath.prototype._walk = function (val, f) {\n  if (Array.isArray(val)) {\n    var n = val.length;\n\n    for (var i = 0; i < n; i++) {\n      f(i);\n    }\n  } else if (val && _typeof(val) === 'object') {\n    Object.keys(val).forEach(function (m) {\n      f(m);\n    });\n  }\n};\n\nJSONPath.prototype._slice = function (loc, expr, val, path, parent, parentPropName, callback) {\n  if (!Array.isArray(val)) {\n    return undefined;\n  }\n\n  var len = val.length,\n      parts = loc.split(':'),\n      step = parts[2] && Number.parseInt(parts[2]) || 1;\n  var start = parts[0] && Number.parseInt(parts[0]) || 0,\n      end = parts[1] && Number.parseInt(parts[1]) || len;\n  start = start < 0 ? Math.max(0, start + len) : Math.min(len, start);\n  end = end < 0 ? Math.max(0, end + len) : Math.min(len, end);\n  var ret = [];\n\n  for (var i = start; i < end; i += step) {\n    var tmp = this._trace(unshift(i, expr), val, path, parent, parentPropName, callback, true); // Should only be possible to be an array here since first part of\n    //   ``unshift(i, expr)` passed in above would not be empty, nor `~`,\n    //     nor begin with `@` (as could return objects)\n    // This was causing excessive stack size in Node (with or\n    //  without Babel) against our performance test: `ret.push(...tmp);`\n\n\n    tmp.forEach(function (t) {\n      ret.push(t);\n    });\n  }\n\n  return ret;\n};\n\nJSONPath.prototype._eval = function (code, _v, _vname, path, parent, parentPropName) {\n  this.currSandbox._$_parentProperty = parentPropName;\n  this.currSandbox._$_parent = parent;\n  this.currSandbox._$_property = _vname;\n  this.currSandbox._$_root = this.json;\n  this.currSandbox._$_v = _v;\n  var containsPath = code.includes('@path');\n\n  if (containsPath) {\n    this.currSandbox._$_path = JSONPath.toPathString(path.concat([_vname]));\n  }\n\n  var scriptCacheKey = 'script:' + code;\n\n  if (!JSONPath.cache[scriptCacheKey]) {\n    var script = code.replace(/@parentProperty/g, '_$_parentProperty').replace(/@parent/g, '_$_parent').replace(/@property/g, '_$_property').replace(/@root/g, '_$_root').replace(/@([\\t-\\r \\)\\.\\[\\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF])/g, '_$_v$1');\n\n    if (containsPath) {\n      script = script.replace(/@path/g, '_$_path');\n    }\n\n    JSONPath.cache[scriptCacheKey] = new this.vm.Script(script);\n  }\n\n  try {\n    return JSONPath.cache[scriptCacheKey].runInNewContext(this.currSandbox);\n  } catch (e) {\n    throw new Error('jsonPath: ' + e.message + ': ' + code);\n  }\n}; // PUBLIC CLASS PROPERTIES AND METHODS\n// Could store the cache object itself\n\n\nJSONPath.cache = {};\n/**\n * @param {string[]} pathArr Array to convert\n * @returns {string} The path string\n */\n\nJSONPath.toPathString = function (pathArr) {\n  var x = pathArr,\n      n = x.length;\n  var p = '$';\n\n  for (var i = 1; i < n; i++) {\n    if (!/^(~|\\^|@(?:[\\0-\\t\\x0B\\f\\x0E-\\u2027\\u202A-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])*?\\(\\))$/.test(x[i])) {\n      p += /^[\\*0-9]+$/.test(x[i]) ? '[' + x[i] + ']' : \"['\" + x[i] + \"']\";\n    }\n  }\n\n  return p;\n};\n/**\n * @param {string} pointer JSON Path\n * @returns {string} JSON Pointer\n */\n\n\nJSONPath.toPointer = function (pointer) {\n  var x = pointer,\n      n = x.length;\n  var p = '';\n\n  for (var i = 1; i < n; i++) {\n    if (!/^(~|\\^|@(?:[\\0-\\t\\x0B\\f\\x0E-\\u2027\\u202A-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])*?\\(\\))$/.test(x[i])) {\n      p += '/' + x[i].toString().replace(/~/g, '~0').replace(/\\//g, '~1');\n    }\n  }\n\n  return p;\n};\n/**\n * @param {string} expr Expression to convert\n * @returns {string[]}\n */\n\n\nJSONPath.toPathArray = function (expr) {\n  var cache = JSONPath.cache;\n\n  if (cache[expr]) {\n    return cache[expr].concat();\n  }\n\n  var subx = [];\n  var normalized = expr // Properties\n  .replace(/@(?:null|boolean|number|string|integer|undefined|nonFinite|scalar|array|object|function|other)\\(\\)/g, ';$&;') // Parenthetical evaluations (filtering and otherwise), directly\n  //   within brackets or single quotes\n  .replace(/['\\[](\\??\\((?:[\\0-\\t\\x0B\\f\\x0E-\\u2027\\u202A-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])*?\\))['\\]]/g, function ($0, $1) {\n    return '[#' + (subx.push($1) - 1) + ']';\n  }) // Escape periods and tildes within properties\n  .replace(/\\[[\"']((?:(?!['\\]])[\\s\\S])*)[\"']\\]/g, function ($0, prop) {\n    return \"['\" + prop.replace(/\\./g, '%@%').replace(/~/g, '%%@@%%') + \"']\";\n  }) // Properties operator\n  .replace(/~/g, ';~;') // Split by property boundaries\n  .replace(/[\"']?\\.[\"']?(?!(?:(?!\\[)[\\s\\S])*\\])|\\[[\"']?/g, ';') // Reinsert periods within properties\n  .replace(/%@%/g, '.') // Reinsert tildes within properties\n  .replace(/%%@@%%/g, '~') // Parent\n  .replace(/(?:;)?(\\^+)(?:;)?/g, function ($0, ups) {\n    return ';' + ups.split('').join(';') + ';';\n  }) // Descendents\n  .replace(/;;;|;;/g, ';..;') // Remove trailing\n  .replace(/;$|'?\\]|'$/g, '');\n  var exprList = normalized.split(';').map(function (exp) {\n    var match = exp.match(/#([0-9]+)/);\n    return !match || !match[1] ? exp : subx[match[1]];\n  });\n  cache[expr] = exprList;\n  return cache[expr].concat();\n};\n\n/**\n * @typedef {any} ContextItem\n */\n\n/**\n * @typedef {any} EvaluatedResult\n */\n\n/**\n * @callback ConditionCallback\n * @param {ContextItem} item\n * @returns {boolean}\n */\n\n/**\n * Copy items out of one array into another.\n * @param {GenericArray} source Array with items to copy\n * @param {GenericArray} target Array to which to copy\n * @param {ConditionCallback} conditionCb Callback passed the current item;\n *     will move item if evaluates to `true`\n * @returns {void}\n */\n\nvar moveToAnotherArray = function moveToAnotherArray(source, target, conditionCb) {\n  var il = source.length;\n\n  for (var i = 0; i < il; i++) {\n    var item = source[i];\n\n    if (conditionCb(item)) {\n      target.push(source.splice(i--, 1)[0]);\n    }\n  }\n};\n/**\n * In-browser replacement for NodeJS' VM.Script.\n */\n\n\nvar Script = /*#__PURE__*/function () {\n  /**\n   * @param {string} expr Expression to evaluate\n   */\n  function Script(expr) {\n    _classCallCheck(this, Script);\n\n    this.code = expr;\n  }\n  /**\n   * @param {PlainObject} context Object whose items will be added\n   *   to evaluation\n   * @returns {EvaluatedResult} Result of evaluated code\n   */\n\n\n  _createClass(Script, [{\n    key: \"runInNewContext\",\n    value: function runInNewContext(context) {\n      var expr = this.code;\n      var keys = Object.keys(context);\n      var funcs = [];\n      moveToAnotherArray(keys, funcs, function (key) {\n        return typeof context[key] === 'function';\n      });\n      var values = keys.map(function (vr, i) {\n        return context[vr];\n      });\n      var funcString = funcs.reduce(function (s, func) {\n        var fString = context[func].toString();\n\n        if (!/function/.test(fString)) {\n          fString = 'function ' + fString;\n        }\n\n        return 'var ' + func + '=' + fString + ';' + s;\n      }, '');\n      expr = funcString + expr; // Mitigate http://perfectionkills.com/global-eval-what-are-the-options/#new_function\n\n      if (!/([\"'])use strict\\1/.test(expr) && !keys.includes('arguments')) {\n        expr = 'var arguments = undefined;' + expr;\n      } // Remove last semi so `return` will be inserted before\n      //  the previous one instead, allowing for the return\n      //  of a bare ending expression\n\n\n      expr = expr.replace(/;[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*$/, ''); // Insert `return`\n\n      var lastStatementEnd = expr.lastIndexOf(';');\n      var code = lastStatementEnd > -1 ? expr.slice(0, lastStatementEnd + 1) + ' return ' + expr.slice(lastStatementEnd + 1) : ' return ' + expr; // eslint-disable-next-line no-new-func\n\n      return _construct(Function, keys.concat([code])).apply(void 0, _toConsumableArray(values));\n    }\n  }]);\n\n  return Script;\n}();\n\nJSONPath.prototype.vm = {\n  Script: Script\n};\n\nexport { JSONPath };\n"], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK;AACpB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,MAAK;AAClG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,MAAK;AACjB,WAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,EAC1H,GAAG,QAAQ,GAAG;AAChB;AAEA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM;AACvB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW;AAAY,iBAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI;AAAY,sBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI;AAAa,sBAAkB,aAAa,WAAW;AAC3D,SAAO,eAAe,aAAa,aAAa;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,eAAe,UAAU,aAAa;AAAA,IAC3C,UAAU;AAAA,EACZ,CAAC;AACD,MAAI;AAAY,oBAAgB,UAAU,UAAU;AACtD;AAEA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBC,IAAG;AACnG,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBD,IAAGE,IAAG;AACtG,IAAAF,GAAE,YAAYE;AACd,WAAOF;AAAA,EACT;AACA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AAEA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ;AAAW,WAAO;AACjE,MAAI,QAAQ,UAAU;AAAM,WAAO;AACnC,MAAI,OAAO,UAAU;AAAY,WAAO;AAExC,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,GAAP;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,WAAW,QAAQ,MAAM,OAAO;AACvC,MAAI,0BAA0B,GAAG;AAC/B,iBAAa,QAAQ,UAAU,KAAK;AAAA,EACtC,OAAO;AACL,iBAAa,SAASG,YAAWC,SAAQC,OAAMC,QAAO;AACpD,UAAI,IAAI,CAAC,IAAI;AACb,QAAE,KAAK,MAAM,GAAGD,KAAI;AACpB,UAAI,cAAc,SAAS,KAAK,MAAMD,SAAQ,CAAC;AAC/C,UAAI,WAAW,IAAI,YAAY;AAC/B,UAAIE;AAAO,wBAAgB,UAAUA,OAAM,SAAS;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,SAAS,SAAS,KAAK,EAAE,EAAE,QAAQ,eAAe,MAAM;AACjE;AAEA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,SAAS,OAAO,QAAQ,aAAa,oBAAI,IAAI,IAAI;AAErD,qBAAmB,SAASC,kBAAiBD,QAAO;AAClD,QAAIA,WAAU,QAAQ,CAAC,kBAAkBA,MAAK;AAAG,aAAOA;AAExD,QAAI,OAAOA,WAAU,YAAY;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC1E;AAEA,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,OAAO,IAAIA,MAAK;AAAG,eAAO,OAAO,IAAIA,MAAK;AAE9C,aAAO,IAAIA,QAAO,OAAO;AAAA,IAC3B;AAEA,aAAS,UAAU;AACjB,aAAO,WAAWA,QAAO,WAAW,gBAAgB,IAAI,EAAE,WAAW;AAAA,IACvE;AAEA,YAAQ,YAAY,OAAO,OAAOA,OAAM,WAAW;AAAA,MACjD,aAAa;AAAA,QACX,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,gBAAgB,SAASA,MAAK;AAAA,EACvC;AAEA,SAAO,iBAAiB,KAAK;AAC/B;AAEA,SAAS,uBAAuB,MAAM;AACpC,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAO;AACT;AAEA,SAAS,2BAA2B,MAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAChF;AAEA,SAAO,uBAAuB,IAAI;AACpC;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAE1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GAC/B;AAEJ,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AAEtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AAEA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,aAAa,QAAQ,KAAK,iBAAiB;AAAM,WAAO,MAAM,KAAK,IAAI;AAC1H;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC;AAAG;AACR,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,SAAK,KAAK,IAAI;AAEnE,SAAO;AACT;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,SAAS,2BAA2B,GAAG,gBAAgB;AACrD,MAAI,KAAK,OAAO,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE;AAElE,MAAI,CAAC,IAAI;AACP,QAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,4BAA4B,CAAC,MAAM,kBAAkB,KAAK,OAAO,EAAE,WAAW,UAAU;AACpH,UAAI;AAAI,YAAI;AACZ,UAAI,IAAI;AAER,UAAI,IAAI,WAAY;AAAA,MAAC;AAErB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG,WAAY;AACb,cAAI,KAAK,EAAE;AAAQ,mBAAO;AAAA,cACxB,MAAM;AAAA,YACR;AACA,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,EAAE;AAAA,UACX;AAAA,QACF;AAAA,QACA,GAAG,SAAU,GAAG;AACd,gBAAM;AAAA,QACR;AAAA,QACA,GAAG;AAAA,MACL;AAAA,IACF;AAEA,UAAM,IAAI,UAAU,uIAAuI;AAAA,EAC7J;AAEA,MAAI,mBAAmB,MACnB,SAAS,OACT;AACJ,SAAO;AAAA,IACL,GAAG,WAAY;AACb,WAAK,GAAG,KAAK,CAAC;AAAA,IAChB;AAAA,IACA,GAAG,WAAY;AACb,UAAI,OAAO,GAAG,KAAK;AACnB,yBAAmB,KAAK;AACxB,aAAO;AAAA,IACT;AAAA,IACA,GAAG,SAAU,GAAG;AACd,eAAS;AACT,YAAM;AAAA,IACR;AAAA,IACA,GAAG,WAAY;AACb,UAAI;AACF,YAAI,CAAC,oBAAoB,GAAG,UAAU;AAAM,aAAG,OAAO;AAAA,MACxD,UAAE;AACA,YAAI;AAAQ,gBAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa,OAAO,UAAU;AAoBlC,SAAS,KAAK,KAAK,MAAM;AACvB,QAAM,IAAI,MAAM;AAChB,MAAI,KAAK,IAAI;AACb,SAAO;AACT;AASA,SAAS,QAAQ,MAAM,KAAK;AAC1B,QAAM,IAAI,MAAM;AAChB,MAAI,QAAQ,IAAI;AAChB,SAAO;AACT;AAOA,IAAI,WAAwB,SAAU,QAAQ;AAC5C,YAAUE,WAAU,MAAM;AAE1B,MAAI,SAAS,aAAaA,SAAQ;AAKlC,WAASA,UAAS,OAAO;AACvB,QAAI;AAEJ,oBAAgB,MAAMA,SAAQ;AAE9B,YAAQ,OAAO,KAAK,MAAM,4FAAiG;AAC3H,UAAM,WAAW;AACjB,UAAM,QAAQ;AACd,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AAEA,SAAO,aAAaA,SAAQ;AAC9B,EAAgB,iBAAiB,KAAK,CAAC;AAgEvC,SAAS,SAAS,MAAM,MAAM,KAAK,UAAU,mBAAmB;AAE9D,MAAI,EAAE,gBAAgB,WAAW;AAC/B,QAAI;AACF,aAAO,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,iBAAiB;AAAA,IAClE,SAAS,GAAP;AACA,UAAI,CAAC,EAAE,UAAU;AACf,cAAM;AAAA,MACR;AAEA,aAAO,EAAE;AAAA,IACX;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,wBAAoB;AACpB,eAAW;AACX,UAAM;AACN,WAAO;AACP,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,QAAQ,QAAQ,IAAI,MAAM;AACvC,SAAO,QAAQ,CAAC;AAChB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,aAAa,KAAK,cAAc;AACrC,OAAK,UAAU,KAAK,WAAW;AAC/B,OAAK,OAAO,WAAW,KAAK,MAAM,MAAM,IAAI,KAAK,OAAO;AACxD,OAAK,UAAU,KAAK,WAAW,CAAC;AAChC,OAAK,cAAc,KAAK,eAAe;AACvC,OAAK,SAAS,KAAK,UAAU;AAC7B,OAAK,iBAAiB,KAAK,kBAAkB;AAC7C,OAAK,WAAW,KAAK,YAAY,YAAY;AAE7C,OAAK,oBAAoB,KAAK,qBAAqB,qBAAqB,WAAY;AAClF,UAAM,IAAI,UAAU,kFAAuF;AAAA,EAC7G;AAEA,MAAI,KAAK,cAAc,OAAO;AAC5B,QAAI,OAAO;AAAA,MACT,MAAM,SAAS,KAAK,OAAO;AAAA,IAC7B;AAEA,QAAI,CAAC,QAAQ;AACX,WAAK,OAAO;AAAA,IACd,WAAW,UAAU,MAAM;AACzB,WAAK,OAAO,KAAK;AAAA,IACnB;AAEA,QAAI,MAAM,KAAK,SAAS,IAAI;AAE5B,QAAI,CAAC,OAAO,QAAQ,GAAG,MAAM,UAAU;AACrC,YAAM,IAAI,SAAS,GAAG;AAAA,IACxB;AAEA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,UAAU,WAAW,SAAU,MAAM,MAAM,UAAU,mBAAmB;AAC/E,MAAI,SAAS;AAEb,MAAI,aAAa,KAAK,QAClB,qBAAqB,KAAK;AAC9B,MAAI,UAAU,KAAK,SACf,OAAO,KAAK;AAChB,OAAK,iBAAiB,KAAK;AAC3B,OAAK,kBAAkB,KAAK;AAC5B,OAAK,cAAc,KAAK;AACxB,aAAW,YAAY,KAAK;AAC5B,OAAK,wBAAwB,qBAAqB,KAAK;AACvD,SAAO,QAAQ,KAAK;AACpB,SAAO,QAAQ,KAAK;AAEpB,MAAI,QAAQ,QAAQ,IAAI,MAAM,YAAY,CAAC,MAAM,QAAQ,IAAI,GAAG;AAC9D,QAAI,CAAC,KAAK,QAAQ,KAAK,SAAS,IAAI;AAClC,YAAM,IAAI,UAAU,6FAAkG;AAAA,IACxH;AAEA,QAAI,CAAC,WAAW,KAAK,MAAM,MAAM,GAAG;AAClC,YAAM,IAAI,UAAU,6FAAkG;AAAA,IACxH;AAEA,QAAI,QAAQ;AACZ,WAAO,MAAM;AACb,cAAU,WAAW,KAAK,MAAM,SAAS,IAAI,KAAK,UAAU;AAC5D,SAAK,iBAAiB,WAAW,KAAK,MAAM,YAAY,IAAI,KAAK,aAAa,KAAK;AACnF,SAAK,cAAc,WAAW,KAAK,MAAM,SAAS,IAAI,KAAK,UAAU,KAAK;AAC1E,WAAO,WAAW,KAAK,MAAM,MAAM,IAAI,KAAK,OAAO;AACnD,SAAK,kBAAkB,WAAW,KAAK,MAAM,aAAa,IAAI,KAAK,cAAc,KAAK;AACtF,eAAW,WAAW,KAAK,MAAM,UAAU,IAAI,KAAK,WAAW;AAC/D,SAAK,wBAAwB,WAAW,KAAK,MAAM,mBAAmB,IAAI,KAAK,oBAAoB,KAAK;AACxG,iBAAa,WAAW,KAAK,MAAM,QAAQ,IAAI,KAAK,SAAS;AAC7D,yBAAqB,WAAW,KAAK,MAAM,gBAAgB,IAAI,KAAK,iBAAiB;AACrF,WAAO,KAAK;AAAA,EACd;AAEA,eAAa,cAAc;AAC3B,uBAAqB,sBAAsB;AAE3C,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,SAAS,aAAa,IAAI;AAAA,EACnC;AAEA,MAAI,CAAC,QAAQ,SAAS,MAAM,CAAC,MAAM;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,SAAS,YAAY,IAAI;AAExC,MAAI,SAAS,OAAO,OAAO,SAAS,SAAS,GAAG;AAC9C,aAAS,MAAM;AAAA,EACjB;AAEA,OAAK,qBAAqB;AAE1B,MAAI,SAAS,KAAK,OAAO,UAAU,MAAM,CAAC,GAAG,GAAG,YAAY,oBAAoB,QAAQ,EAAE,OAAO,SAAU,IAAI;AAC7G,WAAO,MAAM,CAAC,GAAG;AAAA,EACnB,CAAC;AAED,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO,OAAO,CAAC,IAAI;AAAA,EACrB;AAEA,MAAI,CAAC,QAAQ,OAAO,WAAW,KAAK,CAAC,OAAO,GAAG,YAAY;AACzD,WAAO,KAAK,oBAAoB,OAAO,EAAE;AAAA,EAC3C;AAEA,SAAO,OAAO,OAAO,SAAU,MAAM,IAAI;AACvC,QAAI,YAAY,OAAO,oBAAoB,EAAE;AAE7C,QAAI,WAAW,MAAM,QAAQ,SAAS,GAAG;AACvC,aAAO,KAAK,OAAO,SAAS;AAAA,IAC9B,OAAO;AACL,WAAK,KAAK,SAAS;AAAA,IACrB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAGA,SAAS,UAAU,sBAAsB,SAAU,IAAI;AACrD,MAAI,aAAa,KAAK;AAEtB,UAAQ,YAAY;AAAA,IAClB,KAAK,OACH;AACE,UAAI,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,GAAG,OAAO,SAAS,YAAY,GAAG,IAAI;AAC1E,SAAG,UAAU,SAAS,UAAU,IAAI;AACpC,SAAG,OAAO,OAAO,GAAG,SAAS,WAAW,GAAG,OAAO,SAAS,aAAa,GAAG,IAAI;AAC/E,aAAO;AAAA,IACT;AAAA,IAEF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,GAAG;AAAA,IAEZ,KAAK;AACH,aAAO,SAAS,aAAa,GAAG,WAAW;AAAA,IAE7C,KAAK;AACH,aAAO,SAAS,UAAU,GAAG,IAAI;AAAA,IAEnC;AACE,YAAM,IAAI,UAAU,qBAAqB;AAAA,EAC7C;AACF;AAEA,SAAS,UAAU,kBAAkB,SAAU,YAAY,UAAU,MAAM;AACzE,MAAI,UAAU;AACZ,QAAI,kBAAkB,KAAK,oBAAoB,UAAU;AAEzD,eAAW,OAAO,OAAO,WAAW,SAAS,WAAW,WAAW,OAAO,SAAS,aAAa,WAAW,IAAI;AAE/G,aAAS,iBAAiB,MAAM,UAAU;AAAA,EAC5C;AACF;AAeA,SAAS,UAAU,SAAS,SAAU,MAAM,KAAK,MAAM,QAAQ,gBAAgB,UAAU,YAAY,iBAAiB;AACpH,MAAI,SAAS;AAIb,MAAI;AAEJ,MAAI,CAAC,KAAK,QAAQ;AAChB,aAAS;AAAA,MACP;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,IACF;AAEA,SAAK,gBAAgB,QAAQ,UAAU,OAAO;AAE9C,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK,IACX,IAAI,KAAK,MAAM,CAAC;AAGpB,MAAI,MAAM,CAAC;AAOX,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,QAAQ,KAAK,GAAG;AAIxB,YAAM,QAAQ,SAAUC,IAAG;AACzB,YAAI,KAAKA,EAAC;AAAA,MACZ,CAAC;AAAA,IACH,OAAO;AACL,UAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AAEA,OAAK,OAAO,QAAQ,YAAY,oBAAoB,OAAO,WAAW,KAAK,KAAK,GAAG,GAAG;AAEpF,WAAO,KAAK,OAAO,GAAG,IAAI,MAAM,KAAK,MAAM,GAAG,GAAG,KAAK,KAAK,UAAU,UAAU,CAAC;AAAA,EAClF,WAAW,QAAQ,KAAK;AAEtB,SAAK,MAAM,KAAK,SAAU,GAAG;AAC3B,aAAO,OAAO,OAAO,GAAG,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,KAAK,GAAG,UAAU,MAAM,IAAI,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH,WAAW,QAAQ,MAAM;AAGvB,WAAO,KAAK,OAAO,GAAG,KAAK,MAAM,QAAQ,gBAAgB,UAAU,UAAU,CAAC;AAE9E,SAAK,MAAM,KAAK,SAAU,GAAG;AAG3B,UAAI,QAAQ,IAAI,EAAE,MAAM,UAAU;AAGhC,eAAO,OAAO,OAAO,KAAK,MAAM,GAAG,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,KAAK,GAAG,UAAU,IAAI,CAAC;AAAA,MACnF;AAAA,IACF,CAAC;AAAA,EAGH,WAAW,QAAQ,KAAK;AAEtB,SAAK,qBAAqB;AAC1B,WAAO;AAAA,MACL,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA,MACtB,MAAM;AAAA,MACN,kBAAkB;AAAA,IACpB;AAAA,EACF,WAAW,QAAQ,KAAK;AAEtB,aAAS;AAAA,MACP,MAAM,KAAK,MAAM,GAAG;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,MACA,gBAAgB;AAAA,IAClB;AAEA,SAAK,gBAAgB,QAAQ,UAAU,UAAU;AAEjD,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK;AAEtB,WAAO,KAAK,OAAO,GAAG,KAAK,MAAM,MAAM,MAAM,UAAU,UAAU,CAAC;AAAA,EACpE,WAAW,0CAA0C,KAAK,GAAG,GAAG;AAE9D,WAAO,KAAK,OAAO,KAAK,GAAG,KAAK,MAAM,QAAQ,gBAAgB,QAAQ,CAAC;AAAA,EACzE,WAAW,IAAI,QAAQ,IAAI,MAAM,GAAG;AAElC,QAAI,KAAK,iBAAiB;AACxB,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACpE;AAEA,QAAI,UAAU,IAAI,QAAQ,8KAA8K,IAAI;AAE5M,SAAK,MAAM,KAAK,SAAU,GAAG;AAC3B,UAAI,OAAO,MAAM,SAAS,IAAI,IAAI,GAAG,MAAM,QAAQ,cAAc,GAAG;AAClE,eAAO,OAAO,OAAO,GAAG,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,KAAK,GAAG,UAAU,IAAI,CAAC;AAAA,MACxE;AAAA,IACF,CAAC;AAAA,EACH,WAAW,IAAI,OAAO,KAAK;AAEzB,QAAI,KAAK,iBAAiB;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AAKA,WAAO,KAAK,OAAO,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,MAAM,GAAG,EAAE,GAAG,QAAQ,cAAc,GAAG,CAAC,GAAG,KAAK,MAAM,QAAQ,gBAAgB,UAAU,UAAU,CAAC;AAAA,EACjL,WAAW,IAAI,OAAO,KAAK;AAEzB,QAAI,UAAU;AACd,QAAI,YAAY,IAAI,MAAM,GAAG,EAAE;AAE/B,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,YAAI,CAAC,OAAO,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,QAAQ,GAAG,CAAC,GAAG;AAC1D,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH,YAAI,QAAQ,GAAG,MAAM,WAAW;AAC9B,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AACH,YAAI,OAAO,SAAS,GAAG,KAAK,EAAE,MAAM,IAAI;AACtC,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AACH,YAAI,OAAO,SAAS,GAAG,GAAG;AACxB,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AACH,YAAI,OAAO,QAAQ,YAAY,CAAC,OAAO,SAAS,GAAG,GAAG;AACpD,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AAEH,YAAI,OAAO,QAAQ,GAAG,MAAM,WAAW;AACrC,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AACH,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,oBAAU;AAAA,QACZ;AAEA;AAAA,MAEF,KAAK;AACH,kBAAU,KAAK,sBAAsB,KAAK,MAAM,QAAQ,cAAc;AACtE;AAAA,MAEF,KAAK;AACH,YAAI,QAAQ,MAAM;AAChB,oBAAU;AAAA,QACZ;AAEA;AAAA,MAIF;AACE,cAAM,IAAI,UAAU,wBAAwB,SAAS;AAAA,IACzD;AAEA,QAAI,SAAS;AACX,eAAS;AAAA,QACP;AAAA,QACA,OAAO;AAAA,QACP;AAAA,QACA,gBAAgB;AAAA,MAClB;AAEA,WAAK,gBAAgB,QAAQ,UAAU,OAAO;AAE9C,aAAO;AAAA,IACT;AAAA,EAEF,WAAW,IAAI,OAAO,OAAO,OAAO,WAAW,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,GAAG;AACtE,QAAI,UAAU,IAAI,MAAM,CAAC;AACzB,WAAO,KAAK,OAAO,GAAG,IAAI,UAAU,KAAK,MAAM,OAAO,GAAG,KAAK,SAAS,UAAU,YAAY,IAAI,CAAC;AAAA,EACpG,WAAW,IAAI,SAAS,GAAG,GAAG;AAE5B,QAAI,QAAQ,IAAI,MAAM,GAAG;AAEzB,QAAI,YAAY,2BAA2B,KAAK,GAC5C;AAEJ,QAAI;AACF,WAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,YAAI,OAAO,MAAM;AACjB,eAAO,KAAK,OAAO,QAAQ,MAAM,CAAC,GAAG,KAAK,MAAM,QAAQ,gBAAgB,UAAU,IAAI,CAAC;AAAA,MACzF;AAAA,IAEF,SAAS,KAAP;AACA,gBAAU,EAAE,GAAG;AAAA,IACjB,UAAE;AACA,gBAAU,EAAE;AAAA,IACd;AAAA,EACF,WAAW,CAAC,mBAAmB,OAAO,WAAW,KAAK,KAAK,GAAG,GAAG;AAC/D,WAAO,KAAK,OAAO,GAAG,IAAI,MAAM,KAAK,MAAM,GAAG,GAAG,KAAK,KAAK,UAAU,YAAY,IAAI,CAAC;AAAA,EACxF;AAKA,MAAI,KAAK,oBAAoB;AAC3B,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAI,OAAO,IAAI;AAEf,UAAI,QAAQ,KAAK,kBAAkB;AACjC,YAAI,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK,MAAM,QAAQ,gBAAgB,UAAU,UAAU;AAE7F,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,cAAI,KAAK,IAAI;AACb,cAAI,KAAK,IAAI;AAEb,mBAAS,KAAK,GAAG,KAAK,IAAI,MAAM;AAC9B;AACA,gBAAI,OAAO,GAAG,GAAG,IAAI,GAAG;AAAA,UAC1B;AAAA,QACF,OAAO;AACL,cAAI,KAAK;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,SAAU,KAAK,GAAG;AAC3C,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,QAAI,IAAI,IAAI;AAEZ,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAE,CAAC;AAAA,IACL;AAAA,EACF,WAAW,OAAO,QAAQ,GAAG,MAAM,UAAU;AAC3C,WAAO,KAAK,GAAG,EAAE,QAAQ,SAAU,GAAG;AACpC,QAAE,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAEA,SAAS,UAAU,SAAS,SAAU,KAAK,MAAM,KAAK,MAAM,QAAQ,gBAAgB,UAAU;AAC5F,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,IAAI,QACV,QAAQ,IAAI,MAAM,GAAG,GACrB,OAAO,MAAM,MAAM,OAAO,SAAS,MAAM,EAAE,KAAK;AACpD,MAAI,QAAQ,MAAM,MAAM,OAAO,SAAS,MAAM,EAAE,KAAK,GACjD,MAAM,MAAM,MAAM,OAAO,SAAS,MAAM,EAAE,KAAK;AACnD,UAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG,QAAQ,GAAG,IAAI,KAAK,IAAI,KAAK,KAAK;AAClE,QAAM,MAAM,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG;AAC1D,MAAI,MAAM,CAAC;AAEX,WAAS,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM;AACtC,QAAI,MAAM,KAAK,OAAO,QAAQ,GAAG,IAAI,GAAG,KAAK,MAAM,QAAQ,gBAAgB,UAAU,IAAI;AAOzF,QAAI,QAAQ,SAAU,GAAG;AACvB,UAAI,KAAK,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,SAAU,MAAM,IAAI,QAAQ,MAAM,QAAQ,gBAAgB;AACnF,OAAK,YAAY,oBAAoB;AACrC,OAAK,YAAY,YAAY;AAC7B,OAAK,YAAY,cAAc;AAC/B,OAAK,YAAY,UAAU,KAAK;AAChC,OAAK,YAAY,OAAO;AACxB,MAAI,eAAe,KAAK,SAAS,OAAO;AAExC,MAAI,cAAc;AAChB,SAAK,YAAY,UAAU,SAAS,aAAa,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC;AAAA,EACxE;AAEA,MAAI,iBAAiB,YAAY;AAEjC,MAAI,CAAC,SAAS,MAAM,iBAAiB;AACnC,QAAI,SAAS,KAAK,QAAQ,oBAAoB,mBAAmB,EAAE,QAAQ,YAAY,WAAW,EAAE,QAAQ,cAAc,aAAa,EAAE,QAAQ,UAAU,SAAS,EAAE,QAAQ,iFAAiF,QAAQ;AAEvQ,QAAI,cAAc;AAChB,eAAS,OAAO,QAAQ,UAAU,SAAS;AAAA,IAC7C;AAEA,aAAS,MAAM,kBAAkB,IAAI,KAAK,GAAG,OAAO,MAAM;AAAA,EAC5D;AAEA,MAAI;AACF,WAAO,SAAS,MAAM,gBAAgB,gBAAgB,KAAK,WAAW;AAAA,EACxE,SAAS,GAAP;AACA,UAAM,IAAI,MAAM,eAAe,EAAE,UAAU,OAAO,IAAI;AAAA,EACxD;AACF;AAIA,SAAS,QAAQ,CAAC;AAMlB,SAAS,eAAe,SAAU,SAAS;AACzC,MAAI,IAAI,SACJ,IAAI,EAAE;AACV,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,CAAC,iLAAiL,KAAK,EAAE,EAAE,GAAG;AAChM,WAAK,aAAa,KAAK,EAAE,EAAE,IAAI,MAAM,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK;AAAA,IAClE;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,YAAY,SAAU,SAAS;AACtC,MAAI,IAAI,SACJ,IAAI,EAAE;AACV,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,CAAC,iLAAiL,KAAK,EAAE,EAAE,GAAG;AAChM,WAAK,MAAM,EAAE,GAAG,SAAS,EAAE,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI;AAAA,IACpE;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,cAAc,SAAU,MAAM;AACrC,MAAI,QAAQ,SAAS;AAErB,MAAI,MAAM,OAAO;AACf,WAAO,MAAM,MAAM,OAAO;AAAA,EAC5B;AAEA,MAAI,OAAO,CAAC;AACZ,MAAI,aAAa,KAChB,QAAQ,uGAAuG,MAAM,EAErH,QAAQ,wLAAwL,SAAU,IAAI,IAAI;AACjN,WAAO,QAAQ,KAAK,KAAK,EAAE,IAAI,KAAK;AAAA,EACtC,CAAC,EACA,QAAQ,uCAAuC,SAAU,IAAI,MAAM;AAClE,WAAO,OAAO,KAAK,QAAQ,OAAO,KAAK,EAAE,QAAQ,MAAM,QAAQ,IAAI;AAAA,EACrE,CAAC,EACA,QAAQ,MAAM,KAAK,EACnB,QAAQ,gDAAgD,GAAG,EAC3D,QAAQ,QAAQ,GAAG,EACnB,QAAQ,WAAW,GAAG,EACtB,QAAQ,sBAAsB,SAAU,IAAI,KAAK;AAChD,WAAO,MAAM,IAAI,MAAM,EAAE,EAAE,KAAK,GAAG,IAAI;AAAA,EACzC,CAAC,EACA,QAAQ,WAAW,MAAM,EACzB,QAAQ,eAAe,EAAE;AAC1B,MAAI,WAAW,WAAW,MAAM,GAAG,EAAE,IAAI,SAAU,KAAK;AACtD,QAAI,QAAQ,IAAI,MAAM,WAAW;AACjC,WAAO,CAAC,SAAS,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM;AAAA,EAChD,CAAC;AACD,QAAM,QAAQ;AACd,SAAO,MAAM,MAAM,OAAO;AAC5B;AAyBA,IAAI,qBAAqB,SAASC,oBAAmB,QAAQ,QAAQ,aAAa;AAChF,MAAI,KAAK,OAAO;AAEhB,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,QAAI,OAAO,OAAO;AAElB,QAAI,YAAY,IAAI,GAAG;AACrB,aAAO,KAAK,OAAO,OAAO,KAAK,CAAC,EAAE,EAAE;AAAA,IACtC;AAAA,EACF;AACF;AAMA,IAAI,SAAsB,WAAY;AAIpC,WAASC,QAAO,MAAM;AACpB,oBAAgB,MAAMA,OAAM;AAE5B,SAAK,OAAO;AAAA,EACd;AAQA,eAAaA,SAAQ,CAAC;AAAA,IACpB,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,SAAS;AACvC,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,UAAI,QAAQ,CAAC;AACb,yBAAmB,MAAM,OAAO,SAAU,KAAK;AAC7C,eAAO,OAAO,QAAQ,SAAS;AAAA,MACjC,CAAC;AACD,UAAI,SAAS,KAAK,IAAI,SAAU,IAAI,GAAG;AACrC,eAAO,QAAQ;AAAA,MACjB,CAAC;AACD,UAAI,aAAa,MAAM,OAAO,SAAU,GAAG,MAAM;AAC/C,YAAI,UAAU,QAAQ,MAAM,SAAS;AAErC,YAAI,CAAC,WAAW,KAAK,OAAO,GAAG;AAC7B,oBAAU,cAAc;AAAA,QAC1B;AAEA,eAAO,SAAS,OAAO,MAAM,UAAU,MAAM;AAAA,MAC/C,GAAG,EAAE;AACL,aAAO,aAAa;AAEpB,UAAI,CAAC,qBAAqB,KAAK,IAAI,KAAK,CAAC,KAAK,SAAS,WAAW,GAAG;AACnE,eAAO,+BAA+B;AAAA,MACxC;AAKA,aAAO,KAAK,QAAQ,0EAA0E,EAAE;AAEhG,UAAI,mBAAmB,KAAK,YAAY,GAAG;AAC3C,UAAI,OAAO,mBAAmB,KAAK,KAAK,MAAM,GAAG,mBAAmB,CAAC,IAAI,aAAa,KAAK,MAAM,mBAAmB,CAAC,IAAI,aAAa;AAEtI,aAAO,WAAW,UAAU,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,QAAQ,mBAAmB,MAAM,CAAC;AAAA,IAC3F;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,SAAS,UAAU,KAAK;AAAA,EACtB;AACF;", "names": ["obj", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "_construct", "Parent", "args", "Class", "_wrapNativeSuper", "NewError", "t", "moveToAnotherArray", "<PERSON><PERSON><PERSON>"]}