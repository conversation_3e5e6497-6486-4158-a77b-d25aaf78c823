<template>
  <div 
    ref="trigger"
    class="base-tooltip"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="handleClick"
  >
    <slot />
    
    <teleport to="body">
      <transition name="tooltip-fade">
        <div
          v-show="visible"
          ref="tooltip"
          :class="[
            'base-tooltip__popper',
            `base-tooltip__popper--${placement}`,
            `base-tooltip__popper--${effect}`
          ]"
          :style="tooltipStyle"
        >
          <div class="base-tooltip__content">
            <slot name="content">
              {{ content }}
            </slot>
          </div>
          <div class="base-tooltip__arrow" />
        </div>
      </transition>
    </teleport>
  </div>
</template>

<script>
export default {
  name: 'BaseTooltip',
  props: {
    content: {
      type: String,
      default: ''
    },
    placement: {
      type: String,
      default: 'top',
      validator: (value) => [
        'top', 'top-start', 'top-end',
        'bottom', 'bottom-start', 'bottom-end',
        'left', 'left-start', 'left-end',
        'right', 'right-start', 'right-end'
      ].includes(value)
    },
    effect: {
      type: String,
      default: 'dark',
      validator: (value) => ['dark', 'light'].includes(value)
    },
    trigger: {
      type: String,
      default: 'hover',
      validator: (value) => ['hover', 'click', 'focus', 'manual'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    offset: {
      type: Number,
      default: 8
    },
    showAfter: {
      type: Number,
      default: 0
    },
    hideAfter: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      visible: false,
      tooltipStyle: {},
      showTimer: null,
      hideTimer: null
    }
  },
  methods: {
    show() {
      if (this.disabled) return
      
      this.clearTimers()
      
      if (this.showAfter > 0) {
        this.showTimer = setTimeout(() => {
          this.visible = true
          this.$nextTick(this.updatePosition)
        }, this.showAfter)
      } else {
        this.visible = true
        this.$nextTick(this.updatePosition)
      }
    },
    hide() {
      this.clearTimers()
      
      if (this.hideAfter > 0) {
        this.hideTimer = setTimeout(() => {
          this.visible = false
        }, this.hideAfter)
      } else {
        this.visible = false
      }
    },
    handleMouseEnter() {
      if (this.trigger === 'hover') {
        this.show()
      }
    },
    handleMouseLeave() {
      if (this.trigger === 'hover') {
        this.hide()
      }
    },
    handleClick() {
      if (this.trigger === 'click') {
        this.visible ? this.hide() : this.show()
      }
    },
    clearTimers() {
      if (this.showTimer) {
        clearTimeout(this.showTimer)
        this.showTimer = null
      }
      if (this.hideTimer) {
        clearTimeout(this.hideTimer)
        this.hideTimer = null
      }
    },
    updatePosition() {
      const trigger = this.$refs.trigger
      const tooltip = this.$refs.tooltip
      
      if (!trigger || !tooltip) return
      
      const triggerRect = trigger.getBoundingClientRect()
      const tooltipRect = tooltip.getBoundingClientRect()
      const { placement, offset } = this
      
      let top = 0
      let left = 0
      
      // 计算基础位置
      switch (placement.split('-')[0]) {
        case 'top':
          top = triggerRect.top - tooltipRect.height - offset
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
          break
        case 'bottom':
          top = triggerRect.bottom + offset
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
          break
        case 'left':
          top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
          left = triggerRect.left - tooltipRect.width - offset
          break
        case 'right':
          top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
          left = triggerRect.right + offset
          break
      }
      
      // 处理对齐方式
      if (placement.includes('-start')) {
        if (['top', 'bottom'].includes(placement.split('-')[0])) {
          left = triggerRect.left
        } else {
          top = triggerRect.top
        }
      } else if (placement.includes('-end')) {
        if (['top', 'bottom'].includes(placement.split('-')[0])) {
          left = triggerRect.right - tooltipRect.width
        } else {
          top = triggerRect.bottom - tooltipRect.height
        }
      }
      
      // 边界检测和调整
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      
      if (left < 0) left = 8
      if (left + tooltipRect.width > viewportWidth) left = viewportWidth - tooltipRect.width - 8
      if (top < 0) top = 8
      if (top + tooltipRect.height > viewportHeight) top = viewportHeight - tooltipRect.height - 8
      
      this.tooltipStyle = {
        position: 'fixed',
        top: `${top}px`,
        left: `${left}px`,
        zIndex: 9999
      }
    }
  },
  beforeUnmount() {
    this.clearTimers()
  }
}
</script>

<style lang="scss" scoped>
.base-tooltip {
  display: inline-block;
}

.base-tooltip__popper {
  position: fixed;
  z-index: 9999;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.2;
  min-width: 10px;
  word-wrap: break-word;
  
  &--dark {
    background-color: #303133;
    color: #ffffff;
    border: 1px solid #303133;
  }
  
  &--light {
    background-color: #ffffff;
    color: #606266;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.base-tooltip__content {
  padding: 8px 12px;
  max-width: 300px;
}

.base-tooltip__arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  
  .base-tooltip__popper--top & {
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: inherit;
    border-bottom: none;
  }
  
  .base-tooltip__popper--bottom & {
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: inherit;
    border-top: none;
  }
  
  .base-tooltip__popper--left & {
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: inherit;
    border-right: none;
  }
  
  .base-tooltip__popper--right & {
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: inherit;
    border-left: none;
  }
  
  .base-tooltip__popper--dark & {
    border-color: #303133;
  }
  
  .base-tooltip__popper--light & {
    border-color: #e4e7ed;
  }
}

.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: opacity 0.3s ease;
}

.tooltip-fade-enter-from,
.tooltip-fade-leave-to {
  opacity: 0;
}
</style>
