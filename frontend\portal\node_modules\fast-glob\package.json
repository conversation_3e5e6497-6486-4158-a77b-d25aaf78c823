{"name": "fast-glob", "version": "2.2.7", "description": "Is a faster `node-glob` alternative", "license": "MIT", "repository": "mrmlnc/fast-glob", "author": {"name": "<PERSON>", "url": "canonium.com"}, "engines": {"node": ">=4.0.0"}, "main": "index.js", "typings": "index.d.ts", "keywords": ["glob", "patterns", "fast", "implementation"], "devDependencies": {"@types/bash-glob": "^2.0.0", "@types/compute-stdev": "^1.0.0", "@types/easy-table": "^0.0.32", "@types/execa": "^0.9.0", "@types/glob": "^7.1.1", "@types/glob-parent": "^3.1.0", "@types/glob-stream": "^6.1.0", "@types/globby": "^8.0.0", "@types/is-glob": "^4.0.0", "@types/merge2": "^1.1.4", "@types/micromatch": "^3.1.0", "@types/minimist": "^1.2.0", "@types/mocha": "^5.2.5", "@types/node": "^11.13.5", "@types/rimraf": "^2.0.2", "bash-glob": "^2.0.0", "compute-stdev": "^1.0.0", "easy-table": "^1.1.1", "execa": "^0.9.0", "fast-glob": "^2.2.0", "glob": "^7.1.2", "glob-stream": "^6.1.0", "globby": "^8.0.1", "minimist": "^1.2.0", "mocha": "^5.2.0", "rimraf": "^2.6.2", "tiny-glob": "^0.2.3", "tslint": "^5.12.0", "tslint-config-mrmlnc": "^2.0.1", "typescript": "^3.1.3"}, "dependencies": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> out", "lint": "tslint \"src/**/*.ts\" -p . -t stylish", "compile": "tsc", "test": "mocha \"out/**/*.spec.js\" -s 0", "smoke": "mocha \"out/**/*.smoke.js\" -s 0", "build": "npm run clean && npm run compile && npm run lint && npm test", "watch": "npm run clean && npm run compile -- --sourceMap --watch", "bench-async-1": "node ./out/benchmark --depth 1", "bench-async-5": "node ./out/benchmark --depth 5", "bench-async-10": "node ./out/benchmark --depth 10", "bench-async-50": "node ./out/benchmark --depth 50", "bench-async-100": "node ./out/benchmark --depth 100", "bench-async": "npm run bench-async-1 && npm run bench-async-5 && npm run bench-async-10 && npm run bench-async-50 && npm run bench-async-100", "bench-sync-1": "node ./out/benchmark --depth 1 --type sync", "bench-sync-5": "node ./out/benchmark --depth 5 --type sync", "bench-sync-10": "node ./out/benchmark --depth 10 --type sync", "bench-sync-50": "node ./out/benchmark --depth 50 --type sync", "bench-sync-100": "node ./out/benchmark --depth 100 --type sync", "bench-sync": "npm run bench-sync-1 && npm run bench-sync-5 && npm run bench-sync-10 && npm run bench-sync-50 && npm run bench-sync-100", "bench": "npm run build && npm run bench-async && npm run bench-sync"}}