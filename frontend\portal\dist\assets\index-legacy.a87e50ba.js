/*! 
 Build based on gin-vue-admin 
 Time : 1749618054000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function l(n,r,a,i){var l=r&&r.prototype instanceof c?r:c,s=Object.create(l.prototype);return o(s,"_invoke",function(n,r,a){var o,i,l,c=0,s=a||[],p=!1,d={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,n){return o=t,i=0,l=e,d.n=n,u}};function f(n,r){for(i=n,l=r,t=0;!p&&c&&!a&&t<s.length;t++){var a,o=s[t],f=d.p,v=o[2];n>3?(a=v===r)&&(l=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=f&&((a=n<2&&f<o[1])?(i=0,d.v=r,d.n=o[1]):f<v&&(a=n<3||o[0]>r||r>v)&&(o[4]=n,o[5]=r,d.n=v,i=0))}if(a||n>1)return u;throw p=!0,r}return function(a,s,v){if(c>1)throw TypeError("Generator is already running");for(p&&1===s&&f(s,v),i=s,l=v;(t=i<2?e:l)||!p;){o||(i?i<3?(i>1&&(d.n=-1),f(i,l)):d.n=l:d.v=l);try{if(c=2,o){if(i||(a="next"),t=o[a]){if(!(t=t.call(o,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,i<2&&(i=0)}else 1===i&&(t=o.return)&&t.call(o),i<2&&(l=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=e}else if((t=(p=d.n<0)?l:n.call(r,d))!==u)break}catch(t){o=e,i=1,l=t}finally{c=1}}return{value:t,done:p}}}(n,a,i),!0),s}var u={};function c(){}function s(){}function p(){}t=Object.getPrototypeOf;var d=[][r]?t(t([][r]())):(o(t={},r,(function(){return this})),t),f=p.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,o(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=p,o(f,"constructor",p),o(p,"constructor",s),s.displayName="GeneratorFunction",o(p,i,"GeneratorFunction"),o(f),o(f,i,"Generator"),o(f,r,(function(){return this})),o(f,"toString",(function(){return"[object Generator]"})),(a=function(){return{w:l,m:v}})()}function o(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}o=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){o(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},o(e,t,n,r)}function i(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){u=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(u)throw o}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t,n,r,a,o,i){try{var l=e[o](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,a)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function i(e){u(o,r,a,i,l,"next",e)}function l(e){u(o,r,a,i,l,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.7e8ba759.js"],(function(e,t){"use strict";var r,o,l,u,s,p,d,f,v,m,y,g,h,b,x,w,k,j,O,S,_,P,C,T,z=document.createElement("style");return z.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+');background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}.error-component{text-align:center;padding:20px;background-color:#fef2f2;border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;margin:10px 0}.error-component:before{content:"\\26a0\\fe0f";display:block;font-size:24px;margin-bottom:8px}\n',document.head.appendChild(z),{setters:[function(e){r=e.x,o=e.y,l=e.u,u=e.r,s=e.c,p=e.b,d=e.z,f=e.p,v=e.h,m=e.o,y=e.d,g=e.e,h=e.k,b=e.t,x=e.g,w=e.f,k=e.A,j=e.j,O=e.w,S=e.m,_=e.B,P=e.L,C=e.F,T=e.i}],execute:function(){var z={class:"login-page"},q={class:"content"},E={class:"right-panel"},I={key:0},A={key:0,class:"title"},L={key:1,class:"title"},G={style:{"text-align":"center"}},U={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},D={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},F=["xlink:href"],N={key:2,class:"login_panel_form"},B={key:3},R=["onClick"],K={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},J=["xlink:href"],M={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},V={key:1,class:"auth-waiting"},$={class:"waiting-icon"},H={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},Q=["xlink:href"],W={class:"waiting-title"};e("default",Object.assign({name:"Login"},{setup:function(e){var X=o({loader:function(){return _((function(){return t.import("./localLogin-legacy.32a3220e.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Y=o({loader:function(){return _((function(){return t.import("./wechat-legacy.6a493c96.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Z=o({loader:function(){return _((function(){return t.import("./feishu-legacy.e088a96b.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ee=o({loader:function(){return _((function(){return t.import("./dingtalk-legacy.f9340dd6.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=o({loader:function(){return _((function(){return t.import("./oauth2-legacy.ef052ac1.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=o({loader:function(){return _((function(){return t.import("./sms-legacy.10f365b2.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),re=o({loader:function(){return _((function(){return t.import("./secondaryAuth-legacy.2066388a.js")}),void 0,t.meta.url)},loadingComponent:P,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ae=l(),oe=u(0),ie=u([]),le=u("local"),ue=u(""),ce=u(""),se=u(""),pe=u([]),de=u([]),fe=u(!1),ve=u(),me=u(""),ye=u(!1),ge=u(""),he=u(!1),be=u(""),xe=u(""),we=u(""),ke=u({}),je=s((function(){var e=fe.value?be.value:ce.value;return ie.value.filter((function(t){return t.id!==e}))})),Oe=p();s((function(){return de.value.filter((function(e){return e.id!==ce.value}))}));var Se=function(){var e={};if(ae.query.type&&(e.type=ae.query.type),ae.query.wp&&(e.wp=ae.query.wp),ae.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(ae.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),r=new URLSearchParams(n);r.get("type")&&(e.type=r.get("type")),r.get("wp")&&(e.wp=r.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e},_e=function(){var e=c(a().m((function e(){var t,n,o,l,u,c,s,p,d,f,v,m,y,g,h,b,x,w,k,j;return a().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,t=Se(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=1,r({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 1:if(200===(n=e.v).status){if(ie.value=n.data.idpList,(o=ae.query.idp_id||Oe.loginType)&&"undefined"!==o){l=!1,u=i(n.data.idpList);try{for(u.s();!(c=u.n()).done;)s=c.value,o===s.id&&(l=!0,ce.value=s.id,le.value=s.type,ue.value=s.templateType,pe.value=s.attrs,pe.value.name=s.name,pe.value.authType=s.type)}catch(a){u.e(a)}finally{u.f()}l||(se.value=null===(p=ie.value[0])||void 0===p?void 0:p.id,ce.value=null===(d=ie.value[0])||void 0===d?void 0:d.id,le.value=null===(f=ie.value[0])||void 0===f?void 0:f.type,ue.value=null===(v=ie.value[0])||void 0===v?void 0:v.templateType,pe.value=null===(m=ie.value[0])||void 0===m?void 0:m.attrs,pe.value.name=ie.value[0].name,pe.value.authType=null===(y=ie.value[0])||void 0===y?void 0:y.type)}else se.value=null===(g=ie.value[0])||void 0===g?void 0:g.id,ce.value=null===(h=ie.value[0])||void 0===h?void 0:h.id,le.value=null===(b=ie.value[0])||void 0===b?void 0:b.type,ue.value=null===(x=ie.value[0])||void 0===x?void 0:x.templateType,pe.value=null===(w=ie.value[0])||void 0===w?void 0:w.attrs,pe.value.name=ie.value[0].name,pe.value.authType=null===(k=ie.value[0])||void 0===k?void 0:k.type;++oe.value}e.n=3;break;case 2:e.p=2,j=e.v,console.error(j);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();_e();var Pe=s((function(){switch(le.value){case"local":case"msad":case"ldap":case"web":case"email":return X;case"qiyewx":return Y;case"feishu":return Z;case"dingtalk":return ee;case"oauth2":case"cas":return te;case"sms":return ne;default:return"oauth2"===ue.value?te:"local"}})),Ce=s((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===ge.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===ge.value}]})),Te=function(){fe.value=!1,de.value=[],ve.value="",me.value="",ge.value="",he.value=!1,be.value&&(ce.value=be.value,le.value=xe.value,ue.value=we.value,pe.value=n({},ke.value),be.value="",xe.value="",we.value="",ke.value={}),++oe.value,console.log("取消后恢复的状态:",{isSecondary:fe.value,auth_id:ce.value,auth_type:le.value})},ze=function(){var e=c(a().m((function e(t){var n,r,o;return a().w((function(e){for(;;)switch(e.n){case 0:n=P.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{r=ae.query.redirect_url||"/",t.clientParams&&((o=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&o.set("wp",t.clientParams.wp),r+=(r.includes("?")?"&":"?")+o.toString()),window.location.href=r}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),qe=s((function(){return!["dingtalk","feishu","qiyewx"].includes(le.value)&&("oauth2"!==ue.value&&"cas"!==le.value||("cas"===le.value?1===parseInt(pe.value.casOpenType):"oauth2"===ue.value&&1===parseInt(pe.value.oauth2OpenType)))})),Ee=function(e){se.value=e.id,pe.value=e.attrs||{},pe.value.name=e.name,pe.value.authType=e.type,fe.value&&(pe.value.uniqKey=ve.value,pe.value.notPhone=ye.value),ce.value=e.id,le.value=e.type,ue.value=e.templateType,++oe.value};return d(fe,function(){var e=c(a().m((function e(t,r){return a().w((function(e){for(;;)switch(e.n){case 0:fe.value&&(be.value=ce.value,xe.value=le.value,we.value=ue.value,ke.value=n({},pe.value),console.log("二次认证数据:",{secondary:de.value,secondaryLength:de.value.length}),de.value.length>0&&Ee(de.value[0]));case 1:return e.a(2)}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),f("secondary",de),f("isSecondary",fe),f("uniqKey",ve),f("userName",me),f("notPhone",ye),f("last_id",se),f("contactType",ge),f("hasContactInfo",he),function(e,t){var n=v("base-divider"),r=v("base-avatar"),a=v("base-carousel-item"),o=v("base-carousel");return m(),y("div",z,[g("div",q,[t[3]||(t[3]=g("div",{class:"left-panel"},null,-1)),g("div",E,[fe.value?(m(),y("div",V,[g("div",$,[(m(),y("svg",H,[g("use",{"xlink:href":"#icon-auth-".concat(xe.value||le.value)},null,8,Q)]))]),g("h4",W,b(ke.value.name||pe.value.name)+" 登录成功",1),t[1]||(t[1]=g("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),t[2]||(t[2]=g("div",{class:"security-tips"},[g("i",{class:"el-icon-shield",style:{color:"#67c23a"}}),g("span",null,"为了您的账户安全，请完成二次身份验证")],-1))])):(m(),y("div",I,["local"===le.value?(m(),y("span",A,"本地账号登录")):qe.value?(m(),y("span",L,[g("div",G,[g("span",U,[(m(),y("svg",D,[g("use",{"xlink:href":"#icon-auth-"+le.value},null,8,F)])),h(" "+b(pe.value.name),1)])])])):x("",!0),ce.value?(m(),y("div",N,[(m(),w(k(Pe.value),{auth_id:ce.value,auth_info:pe.value},null,8,["auth_id","auth_info"]))])):x("",!0),je.value.length>0?(m(),y("div",B,[j(n,null,{default:O((function(){return t[0]||(t[0]=[g("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)])})),_:1,__:[0]}),(m(),w(o,{key:oe.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:O((function(){return[(m(!0),y(C,null,T(Math.ceil(je.value.length/2),(function(e){return m(),w(a,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:O((function(){return[(m(!0),y(C,null,T(je.value.slice(2*(e-1),2*(e-1)+2),(function(e){return m(),y("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:function(t){return Ee(e)}},[g("div",null,[j(r,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:O((function(){return[(m(),y("svg",K,[g("use",{"xlink:href":"#icon-auth-"+e.type},null,8,J)]))]})),_:2},1024)]),g("div",M,b(e.name),1)],8,R)})),128))]})),_:2},1024)})),128))]})),_:1}))])):x("",!0)]))])]),fe.value?(m(),w(S(re),{key:0,"auth-info":{uniqKey:ve.value,contactType:ge.value,hasContactInfo:he.value},"auth-id":ce.value,"user-name":me.value,"last-id":se.value,"auth-methods":Ce.value,onVerificationSuccess:ze,onCancel:Te},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):x("",!0)])}}}))}}}))}();
