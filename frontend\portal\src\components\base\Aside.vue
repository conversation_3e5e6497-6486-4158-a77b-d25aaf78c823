<template>
  <div :class="asideClass" :style="asideStyle">
    <slot></slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  width: {
    type: String,
    default: '220px'
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  collapsedWidth: {
    type: String,
    default: '54px'
  }
})

const asideClass = computed(() => {
  const classes = ['aside']
  
  if (props.collapsed) {
    classes.push('collapsed')
  }
  
  return classes.join(' ')
})

const asideStyle = computed(() => {
  return {
    width: props.collapsed ? props.collapsedWidth : props.width
  }
})
</script>

<style scoped>
.aside {
  transition: width 0.3s;
  overflow: hidden;
  flex-shrink: 0;
}
</style>
