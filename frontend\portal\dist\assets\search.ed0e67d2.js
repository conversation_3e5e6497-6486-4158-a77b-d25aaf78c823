/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
import{_ as e,a,S as s,r as l,h as o,o as u,d as c,j as n,w as t,O as r,e as v,F as i,i as d,m as p,f as m,R as b,T as f,C as g,g as h,U as k,K as y}from"./index.29054947.js";import x from"./index.79ec2cb3.js";const I={class:"search-component"},_={class:"transition-box",style:{display:"inline-block"}},C={key:0,class:"user-box"},j={key:1,class:"user-box"},w={key:2,class:"user-box"},B={key:3,class:"user-box"},T=e(Object.assign({name:"BtnBox"},{setup(e){const T=a(),V=s(),q=l(""),O=()=>{T.push({name:q.value}),q.value=""},U=l(!1),F=l(!0),K=()=>{U.value=!1,setTimeout((()=>{F.value=!0}),500)},L=l(null),R=async()=>{F.value=!1,U.value=!0,await k(),L.value.focus()},S=l(!1),z=()=>{S.value=!0,y.emit("reload"),setTimeout((()=>{S.value=!1}),500)},A=()=>{window.open("https://support.qq.com/product/371961")};return(e,a)=>{const s=o("base-option"),l=o("base-select");return u(),c("div",I,[n(f,{name:"el-fade-in-linear"},{default:t((()=>[r(v("div",_,[n(l,{ref_key:"searchInput",ref:L,modelValue:q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>q.value=e),filterable:"",placeholder:"请选择",onBlur:K,onChange:O},{default:t((()=>[(u(!0),c(i,null,d(p(V).routerList,(e=>(u(),m(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],512),[[b,U.value]])])),_:1}),F.value?(u(),c("div",C,[v("div",{class:g(["gvaIcon gvaIcon-refresh",[S.value?"reloading":""]]),onClick:z},null,2)])):h("",!0),F.value?(u(),c("div",j,[v("div",{class:"gvaIcon gvaIcon-search",onClick:R})])):h("",!0),F.value?(u(),c("div",w,[n(x,{class:"search-icon",style:{cursor:"pointer"}})])):h("",!0),F.value?(u(),c("div",B,[v("div",{class:"gvaIcon gvaIcon-customer-service",onClick:A})])):h("",!0)])}}}),[["__scopeId","data-v-97ccbcef"]]);export{T as default};
