/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
import s from"./index.37367920.js";import{_ as a,h as c,o as e,d as t,e as n,j as p,w as o,k as l}from"./index.342352cf.js";const r={class:"access-main"},u={class:"content-wrapper"},i={class:"access-proxy-status"},m={class:"access-proxy-status-span"},d={class:"access-app"};const f=a({name:"Access",components:{AppPage:s}},[["render",function(s,a,f,x,y,_){const b=c("base-button"),v=c("AppPage");return e(),t("div",r,[n("ul",u,[n("li",i,[a[2]||(a[2]=n("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),n("span",m,[a[1]||(a[1]=n("span",{class:"access-proxy-status-tips"}," 点击连接，即可安全便捷地访问应用 ",-1)),p(b,{class:"access-proxy-status-btn",color:"#626aef",type:"primary"},{default:o((()=>a[0]||(a[0]=[l(" 一键连接 ")]))),_:1,__:[0]})])]),a[3]||(a[3]=n("li",{class:"access-common-status"},[n("span",{class:"access-common-status-span"},[n("span",null,"准入状态（企业网络下使用）："),n("span",{style:{color:"red"}},"未入网"),n("span",null,"（请重新建立连接）")]),n("span",{class:"access-common-status-detail"},[n("span",null,"查看详情")])],-1)),n("li",d,[p(v,{class:"access-app-page"})])])])}],["__scopeId","data-v-199e7ebc"],["__file","D:/asec-platform/frontend/portal/src/view/client/main.vue"]]);export{f as default};
