import service from '@/utils/request'

// 获取 corpID，在本地文件环境下使用默认值
let corpID = 'default'
try {
  // 检查是否为本地文件协议
  if (document.location.protocol !== 'file:') {
    const req = new XMLHttpRequest()
    req.open('GET', document.location, false)
    req.send(null)
    corpID = req.getResponseHeader('X-Corp-ID') || 'default'
  }
} catch (error) {
  console.warn('无法获取 X-Corp-ID header，使用默认值:', error)
  corpID = 'default'
}
// console.log(corpID)

// @Summary 用户登录
// @Produce  application/json
// @Param data body {username:"string",password:"string"}
// @Router /base/login [post]
export const login = (data) => {
  return service({
    url: `/auth/login/v1/user`,
    method: 'post',
    data: JSON.stringify(data),
  })
}

// @Summary 获取验证码
// @Produce  application/json
// @Param data body {username:"string",password:"string"}
// @Router /base/captcha [post]
export const captcha = (data) => {
  // return service({
  //   url: '/base/captcha',
  //   method: 'post',
  //   data: data
  // })
  return new Promise(function(resolve, reject) {
    resolve({
      code: 0,
      data: {
        captchaId: 'oz2hJeHmEyDzjGoxdoJG',
        captchaLength: '6',
        picPath: 'data:image/png;base64,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',
      },
      msg: '验证码获取成功',
    })
  })
}

// @Summary 用户注册
// @Produce  application/json
// @Param data body {username:"string",password:"string"}
// @Router /base/resige [post]
export const register = (data) => {
  return service({
    url: '/user/auth/admin_register',
    method: 'post',
    data: data,
  })
}

// @Summary 修改密码
// @Produce  application/json
// @Param data body {username:"string",password:"string",newPassword:"string"}
// @Router /user/changePassword [post]
export const changePassword = (data) => {
  return service({
    url: '/auth/user/v1/password',
    method: 'put',
    data: data,
  })
}

// @Tags User
// @Summary 分页获取用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body modelInterface.PageInfo true "分页获取用户列表"
// @Success 200 {string} json "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getUserList [post]
export const getUserList = (params) => {
  return service({
    url: `/auth/admin/realms/${corpID}/users`,
    method: 'get',
    params,
  })
}

export const getUserListCount = (params) => {
  return service({
    url: `/auth/admin/realms/${corpID}/users/count`,
    method: 'get',
    params,
  })
}

// @Tags User
// @Summary 设置用户权限
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body api.SetUserAuth true "设置用户权限"
// @Success 200 {string} json "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /user/setUserAuthority [post]
export const setUserAuthority = (data) => {
  return service({
    url: '/user/setUserAuthority',
    method: 'post',
    data: data,
  })
}

// @Tags SysUser
// @Summary 删除用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SetUserAuth true "删除用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /user/deleteUser [delete]
export const deleteUser = (id) => {
  return service({
    url: `/auth/admin/realms/${corpID}/users/${id}`,
    method: 'delete',
  })
}

// @Tags SysUser
// @Summary 设置用户信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysUser true "设置用户信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /user/setUserInfo [put]
export const setUserInfo = (data) => {
  return service({
    url: '/user/setUserInfo',
    method: 'put',
    data: data,
  })
}

// @Tags SysUser
// @Summary 设置用户信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysUser true "设置用户信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /user/setSelfInfo [put]
export const setSelfInfo = (data) => {
  return service({
    url: '/user/setSelfInfo',
    method: 'put',
    data: data,
  })
}

// @Tags User
// @Summary 设置用户权限
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body api.setUserAuthorities true "设置用户权限"
// @Success 200 {string} json "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /user/setUserAuthorities [post]
export const setUserAuthorities = (data) => {
  return service({
    url: '/user/setUserAuthorities',
    method: 'post',
    data: data,
  })
}

// @Tags User
// @Summary 获取用户信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} json "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getUserInfo [get]
export const getUserInfo = (id) => {
  return service({
    url: `/auth/user/v1/login_user`,
    method: 'get',
  })
}

export const updateUser = (user) => {
  const id = user.id
  delete user.id
  return service({
    url: `/auth/admin/realms/${corpID}/users/${id}`,
    method: 'put',
    data: user,
  })
}

export const resetPassword = (data) => {
  return service({
    url: '/user/resetPassword',
    method: 'post',
    data: data,
  })
}

export const getRoles = (data) => {
  return service({
    url: `/auth/admin/realms/${corpID}/roles`,
    method: 'get',
    data: data,
  })
}

export const getUserGroups = (id) => {
  return service({
    url: `/auth/admin/realms/${corpID}/users/${id}/groups`,
    method: 'get',
  })
}

export const getUserRole = (id) => {
  return service({
    url: `/auth/admin/realms/${corpID}/users/${id}/role-mappings`,
    method: 'get',
  })
}

export const getOrganize = (data) => {
  return service({
    url: `/auth/admin/realms/${corpID}/groups`,
    method: 'get',
    params: data,
  })
}

export const getUserOrigin = (data) => {
  return service({
    url: `/console/v1/user/director_types`,
    method: 'get',
    params: data,
  })
}

export const getOrganizeCount = (data) => {
  return service({
    url: `/auth/admin/realms/${corpID}/groups/count`,
    method: 'get',
    params: data,
  })
}

export const getGroupMembers = (id, params) => {
  return service({
    url: `/auth/admin/realms/${corpID}/groups/${id}/members`,
    method: 'get',
    params,
  })
}

export const createOrganize = (data) => {
  delete data.id
  return service({
    url: `/auth/admin/realms/${corpID}/groups`,
    method: 'post',
    data: data,
  })
}

export const updateOrganize = (data) => {
  const id = data.id
  delete data.id
  return service({
    url: `/auth/admin/realms/${corpID}/groups/${id}`,
    method: 'put',
    data: data,
  })
}

export const addSubgroup = (data) => {
  const id = data.id
  delete data.id
  return service({
    url: `/auth/admin/realms/${corpID}/groups/${id}/children`,
    method: 'post',
    data: data,
  })
}

export const delOrganize = (organizeId) => {
  return service({
    url: `/auth/admin/realms/${corpID}/groups/${organizeId}`,
    method: 'delete',
  })
}

export const getOrganizeDetails = (organizeId) => {
  return service({
    url: `/auth/admin/realms/${corpID}/groups/${organizeId}`,
    method: 'get',
  })
}

export const createUser = (user) => {
  return service({
    url: `/auth/admin/realms/${corpID}/users`,
    method: 'post',
    data: user,
  })
}
