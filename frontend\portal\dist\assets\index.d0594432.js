/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerpolicy&&(o.referrerPolicy=s.referrerpolicy),s.crossorigin==="use-credentials"?o.credentials="include":s.crossorigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function io(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const he={},cn=[],rt=()=>{},Ac=()=>!1,Pr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),lo=e=>e.startsWith("onUpdate:"),Te=Object.assign,ao=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ic=Object.prototype.hasOwnProperty,ue=(e,t)=>Ic.call(e,t),j=Array.isArray,un=e=>xn(e)==="[object Map]",Sn=e=>xn(e)==="[object Set]",Fo=e=>xn(e)==="[object Date]",Oc=e=>xn(e)==="[object RegExp]",Y=e=>typeof e=="function",Ce=e=>typeof e=="string",ut=e=>typeof e=="symbol",me=e=>e!==null&&typeof e=="object",bl=e=>(me(e)||Y(e))&&Y(e.then)&&Y(e.catch),wl=Object.prototype.toString,xn=e=>wl.call(e),$c=e=>xn(e).slice(8,-1),Cl=e=>xn(e)==="[object Object]",co=e=>Ce(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,kn=io(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Lr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pc=/-(\w)/g,ot=Lr(e=>e.replace(Pc,(t,n)=>n?n.toUpperCase():"")),Lc=/\B([A-Z])/g,zt=Lr(e=>e.replace(Lc,"-$1").toLowerCase()),kr=Lr(e=>e.charAt(0).toUpperCase()+e.slice(1)),dr=Lr(e=>e?`on${kr(e)}`:""),qt=(e,t)=>!Object.is(e,t),fn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Sl=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},xl=e=>{const t=parseFloat(e);return isNaN(t)?e:t},kc=e=>{const t=Ce(e)?Number(e):NaN;return isNaN(t)?e:t};let jo;const Xn=()=>jo||(jo=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ze(e){if(j(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=Ce(r)?Dc(r):Ze(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(Ce(e)||me(e))return e}const Mc=/;(?![^(]*\))/g,Nc=/:([^]+)/,Vc=/\/\*[^]*?\*\//g;function Dc(e){const t={};return e.replace(Vc,"").split(Mc).forEach(n=>{if(n){const r=n.split(Nc);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function _e(e){let t="";if(Ce(e))t=e;else if(j(e))for(let n=0;n<e.length;n++){const r=_e(e[n]);r&&(t+=r+" ")}else if(me(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Bc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Hc=io(Bc);function El(e){return!!e||e===""}function Uc(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=en(e[r],t[r]);return n}function en(e,t){if(e===t)return!0;let n=Fo(e),r=Fo(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=ut(e),r=ut(t),n||r)return e===t;if(n=j(e),r=j(t),n||r)return n&&r?Uc(e,t):!1;if(n=me(e),r=me(t),n||r){if(!n||!r)return!1;const s=Object.keys(e).length,o=Object.keys(t).length;if(s!==o)return!1;for(const i in e){const a=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(a&&!l||!a&&l||!en(e[i],t[i]))return!1}}return String(e)===String(t)}function uo(e,t){return e.findIndex(n=>en(n,t))}const Rl=e=>!!(e&&e.__v_isRef===!0),yt=e=>Ce(e)?e:e==null?"":j(e)||me(e)&&(e.toString===wl||!Y(e.toString))?Rl(e)?yt(e.value):JSON.stringify(e,Tl,2):String(e),Tl=(e,t)=>Rl(t)?Tl(e,t.value):un(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Xr(r,o)+" =>"]=s,n),{})}:Sn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Xr(n))}:ut(t)?Xr(t):me(t)&&!j(t)&&!Cl(t)?String(t):t,Xr=(e,t="")=>{var n;return ut(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ne;class Al{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ne,!t&&Ne&&(this.index=(Ne.scopes||(Ne.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ne;try{return Ne=this,t()}finally{Ne=n}}}on(){++this._on===1&&(this.prevScope=Ne,Ne=this)}off(){this._on>0&&--this._on===0&&(Ne=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Il(e){return new Al(e)}function Ol(){return Ne}function qc(e,t=!1){Ne&&Ne.cleanups.push(e)}let ve;const Yr=new WeakSet;class $l{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ne&&Ne.active&&Ne.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yr.has(this)&&(Yr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ll(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,zo(this),kl(this);const t=ve,n=at;ve=this,at=!0;try{return this.fn()}finally{Ml(this),ve=t,at=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ho(t);this.deps=this.depsTail=void 0,zo(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Os(this)&&this.run()}get dirty(){return Os(this)}}let Pl=0,Mn,Nn;function Ll(e,t=!1){if(e.flags|=8,t){e.next=Nn,Nn=e;return}e.next=Mn,Mn=e}function fo(){Pl++}function po(){if(--Pl>0)return;if(Nn){let t=Nn;for(Nn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Mn;){let t=Mn;for(Mn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function kl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ml(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),ho(r),Fc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function Os(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Nl(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Nl(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Fn)||(e.globalVersion=Fn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Os(e))))return;e.flags|=2;const t=e.dep,n=ve,r=at;ve=e,at=!0;try{kl(e);const s=e.fn(e._value);(t.version===0||qt(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ve=n,at=r,Ml(e),e.flags&=-3}}function ho(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)ho(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Fc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let at=!0;const Vl=[];function It(){Vl.push(at),at=!1}function Ot(){const e=Vl.pop();at=e===void 0?!0:e}function zo(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ve;ve=void 0;try{t()}finally{ve=n}}}let Fn=0;class jc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class mo{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ve||!at||ve===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ve)n=this.activeLink=new jc(ve,this),ve.deps?(n.prevDep=ve.depsTail,ve.depsTail.nextDep=n,ve.depsTail=n):ve.deps=ve.depsTail=n,Dl(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ve.depsTail,n.nextDep=void 0,ve.depsTail.nextDep=n,ve.depsTail=n,ve.deps===n&&(ve.deps=r)}return n}trigger(t){this.version++,Fn++,this.notify(t)}notify(t){fo();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{po()}}}function Dl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Dl(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const _r=new WeakMap,Yt=Symbol(""),$s=Symbol(""),jn=Symbol("");function Ve(e,t,n){if(at&&ve){let r=_r.get(e);r||_r.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new mo),s.map=r,s.key=n),s.track()}}function Rt(e,t,n,r,s,o){const i=_r.get(e);if(!i){Fn++;return}const a=l=>{l&&l.trigger()};if(fo(),t==="clear")i.forEach(a);else{const l=j(e),f=l&&co(n);if(l&&n==="length"){const c=Number(r);i.forEach((u,m)=>{(m==="length"||m===jn||!ut(m)&&m>=c)&&a(u)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),f&&a(i.get(jn)),t){case"add":l?f&&a(i.get("length")):(a(i.get(Yt)),un(e)&&a(i.get($s)));break;case"delete":l||(a(i.get(Yt)),un(e)&&a(i.get($s)));break;case"set":un(e)&&a(i.get(Yt));break}}po()}function zc(e,t){const n=_r.get(e);return n&&n.get(t)}function rn(e){const t=se(e);return t===e?t:(Ve(t,"iterate",jn),st(e)?t:t.map(Le))}function Mr(e){return Ve(e=se(e),"iterate",jn),e}const Kc={__proto__:null,[Symbol.iterator](){return Qr(this,Symbol.iterator,Le)},concat(...e){return rn(this).concat(...e.map(t=>j(t)?rn(t):t))},entries(){return Qr(this,"entries",e=>(e[1]=Le(e[1]),e))},every(e,t){return Ct(this,"every",e,t,void 0,arguments)},filter(e,t){return Ct(this,"filter",e,t,n=>n.map(Le),arguments)},find(e,t){return Ct(this,"find",e,t,Le,arguments)},findIndex(e,t){return Ct(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ct(this,"findLast",e,t,Le,arguments)},findLastIndex(e,t){return Ct(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ct(this,"forEach",e,t,void 0,arguments)},includes(...e){return Zr(this,"includes",e)},indexOf(...e){return Zr(this,"indexOf",e)},join(e){return rn(this).join(e)},lastIndexOf(...e){return Zr(this,"lastIndexOf",e)},map(e,t){return Ct(this,"map",e,t,void 0,arguments)},pop(){return An(this,"pop")},push(...e){return An(this,"push",e)},reduce(e,...t){return Ko(this,"reduce",e,t)},reduceRight(e,...t){return Ko(this,"reduceRight",e,t)},shift(){return An(this,"shift")},some(e,t){return Ct(this,"some",e,t,void 0,arguments)},splice(...e){return An(this,"splice",e)},toReversed(){return rn(this).toReversed()},toSorted(e){return rn(this).toSorted(e)},toSpliced(...e){return rn(this).toSpliced(...e)},unshift(...e){return An(this,"unshift",e)},values(){return Qr(this,"values",Le)}};function Qr(e,t,n){const r=Mr(e),s=r[t]();return r!==e&&!st(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Wc=Array.prototype;function Ct(e,t,n,r,s,o){const i=Mr(e),a=i!==e&&!st(e),l=i[t];if(l!==Wc[t]){const u=l.apply(e,o);return a?Le(u):u}let f=n;i!==e&&(a?f=function(u,m){return n.call(this,Le(u),m,e)}:n.length>2&&(f=function(u,m){return n.call(this,u,m,e)}));const c=l.call(i,f,r);return a&&s?s(c):c}function Ko(e,t,n,r){const s=Mr(e);let o=n;return s!==e&&(st(e)?n.length>3&&(o=function(i,a,l){return n.call(this,i,a,l,e)}):o=function(i,a,l){return n.call(this,i,Le(a),l,e)}),s[t](o,...r)}function Zr(e,t,n){const r=se(e);Ve(r,"iterate",jn);const s=r[t](...n);return(s===-1||s===!1)&&_o(n[0])?(n[0]=se(n[0]),r[t](...n)):s}function An(e,t,n=[]){It(),fo();const r=se(e)[t].apply(e,n);return po(),Ot(),r}const Gc=io("__proto__,__v_isRef,__isVue"),Bl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ut));function Jc(e){ut(e)||(e=String(e));const t=se(this);return Ve(t,"has",e),t.hasOwnProperty(e)}class Hl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?ou:jl:o?Fl:ql).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=j(t);if(!s){let l;if(i&&(l=Kc[n]))return l;if(n==="hasOwnProperty")return Jc}const a=Reflect.get(t,n,Ee(t)?t:r);return(ut(n)?Bl.has(n):Gc(n))||(s||Ve(t,"get",n),o)?a:Ee(a)?i&&co(n)?a:a.value:me(a)?s?Kl(a):Yn(a):a}}class Ul extends Hl{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const l=jt(o);if(!st(r)&&!jt(r)&&(o=se(o),r=se(r)),!j(t)&&Ee(o)&&!Ee(r))return l?!1:(o.value=r,!0)}const i=j(t)&&co(n)?Number(n)<t.length:ue(t,n),a=Reflect.set(t,n,r,Ee(t)?t:s);return t===se(s)&&(i?qt(r,o)&&Rt(t,"set",n,r):Rt(t,"add",n,r)),a}deleteProperty(t,n){const r=ue(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&Rt(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!ut(n)||!Bl.has(n))&&Ve(t,"has",n),r}ownKeys(t){return Ve(t,"iterate",j(t)?"length":Yt),Reflect.ownKeys(t)}}class Xc extends Hl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Yc=new Ul,Qc=new Xc,Zc=new Ul(!0);const Ps=e=>e,or=e=>Reflect.getPrototypeOf(e);function eu(e,t,n){return function(...r){const s=this.__v_raw,o=se(s),i=un(o),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,f=s[e](...r),c=n?Ps:t?yr:Le;return!t&&Ve(o,"iterate",l?$s:Yt),{next(){const{value:u,done:m}=f.next();return m?{value:u,done:m}:{value:a?[c(u[0]),c(u[1])]:c(u),done:m}},[Symbol.iterator](){return this}}}}function ir(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function tu(e,t){const n={get(s){const o=this.__v_raw,i=se(o),a=se(s);e||(qt(s,a)&&Ve(i,"get",s),Ve(i,"get",a));const{has:l}=or(i),f=t?Ps:e?yr:Le;if(l.call(i,s))return f(o.get(s));if(l.call(i,a))return f(o.get(a));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&Ve(se(s),"iterate",Yt),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=se(o),a=se(s);return e||(qt(s,a)&&Ve(i,"has",s),Ve(i,"has",a)),s===a?o.has(s):o.has(s)||o.has(a)},forEach(s,o){const i=this,a=i.__v_raw,l=se(a),f=t?Ps:e?yr:Le;return!e&&Ve(l,"iterate",Yt),a.forEach((c,u)=>s.call(o,f(c),f(u),i))}};return Te(n,e?{add:ir("add"),set:ir("set"),delete:ir("delete"),clear:ir("clear")}:{add(s){!t&&!st(s)&&!jt(s)&&(s=se(s));const o=se(this);return or(o).has.call(o,s)||(o.add(s),Rt(o,"add",s,s)),this},set(s,o){!t&&!st(o)&&!jt(o)&&(o=se(o));const i=se(this),{has:a,get:l}=or(i);let f=a.call(i,s);f||(s=se(s),f=a.call(i,s));const c=l.call(i,s);return i.set(s,o),f?qt(o,c)&&Rt(i,"set",s,o):Rt(i,"add",s,o),this},delete(s){const o=se(this),{has:i,get:a}=or(o);let l=i.call(o,s);l||(s=se(s),l=i.call(o,s)),a&&a.call(o,s);const f=o.delete(s);return l&&Rt(o,"delete",s,void 0),f},clear(){const s=se(this),o=s.size!==0,i=s.clear();return o&&Rt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=eu(s,e,t)}),n}function go(e,t){const n=tu(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(ue(n,s)&&s in r?n:r,s,o)}const nu={get:go(!1,!1)},ru={get:go(!1,!0)},su={get:go(!0,!1)};const ql=new WeakMap,Fl=new WeakMap,jl=new WeakMap,ou=new WeakMap;function iu(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function lu(e){return e.__v_skip||!Object.isExtensible(e)?0:iu($c(e))}function Yn(e){return jt(e)?e:vo(e,!1,Yc,nu,ql)}function zl(e){return vo(e,!1,Zc,ru,Fl)}function Kl(e){return vo(e,!0,Qc,su,jl)}function vo(e,t,n,r,s){if(!me(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=lu(e);if(o===0)return e;const i=s.get(e);if(i)return i;const a=new Proxy(e,o===2?r:n);return s.set(e,a),a}function Ft(e){return jt(e)?Ft(e.__v_raw):!!(e&&e.__v_isReactive)}function jt(e){return!!(e&&e.__v_isReadonly)}function st(e){return!!(e&&e.__v_isShallow)}function _o(e){return e?!!e.__v_raw:!1}function se(e){const t=e&&e.__v_raw;return t?se(t):e}function yo(e){return!ue(e,"__v_skip")&&Object.isExtensible(e)&&Sl(e,"__v_skip",!0),e}const Le=e=>me(e)?Yn(e):e,yr=e=>me(e)?Kl(e):e;function Ee(e){return e?e.__v_isRef===!0:!1}function we(e){return Wl(e,!1)}function au(e){return Wl(e,!0)}function Wl(e,t){return Ee(e)?e:new cu(e,t)}class cu{constructor(t,n){this.dep=new mo,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:se(t),this._value=n?t:Le(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||st(t)||jt(t);t=r?t:se(t),qt(t,n)&&(this._rawValue=t,this._value=r?t:Le(t),this.dep.trigger())}}function dn(e){return Ee(e)?e.value:e}const uu={get:(e,t,n)=>t==="__v_raw"?e:dn(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Ee(s)&&!Ee(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function Gl(e){return Ft(e)?e:new Proxy(e,uu)}function fu(e){const t=j(e)?new Array(e.length):{};for(const n in e)t[n]=pu(e,n);return t}class du{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return zc(se(this._object),this._key)}}function pu(e,t,n){const r=e[t];return Ee(r)?r:new du(e,t,n)}class hu{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new mo(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Fn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ve!==this)return Ll(this,!0),!0}get value(){const t=this.dep.track();return Nl(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function mu(e,t,n=!1){let r,s;return Y(e)?r=e:(r=e.get,s=e.set),new hu(r,s,n)}const lr={},br=new WeakMap;let Xt;function gu(e,t=!1,n=Xt){if(n){let r=br.get(n);r||br.set(n,r=[]),r.push(e)}}function vu(e,t,n=he){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:a,call:l}=n,f=_=>s?_:st(_)||s===!1||s===0?Tt(_,1):Tt(_);let c,u,m,v,d=!1,g=!1;if(Ee(e)?(u=()=>e.value,d=st(e)):Ft(e)?(u=()=>f(e),d=!0):j(e)?(g=!0,d=e.some(_=>Ft(_)||st(_)),u=()=>e.map(_=>{if(Ee(_))return _.value;if(Ft(_))return f(_);if(Y(_))return l?l(_,2):_()})):Y(e)?t?u=l?()=>l(e,2):e:u=()=>{if(m){It();try{m()}finally{Ot()}}const _=Xt;Xt=c;try{return l?l(e,3,[v]):e(v)}finally{Xt=_}}:u=rt,t&&s){const _=u,O=s===!0?1/0:s;u=()=>Tt(_(),O)}const C=Ol(),x=()=>{c.stop(),C&&C.active&&ao(C.effects,c)};if(o&&t){const _=t;t=(...O)=>{_(...O),x()}}let y=g?new Array(e.length).fill(lr):lr;const w=_=>{if(!(!(c.flags&1)||!c.dirty&&!_))if(t){const O=c.run();if(s||d||(g?O.some((q,z)=>qt(q,y[z])):qt(O,y))){m&&m();const q=Xt;Xt=c;try{const z=[O,y===lr?void 0:g&&y[0]===lr?[]:y,v];y=O,l?l(t,3,z):t(...z)}finally{Xt=q}}}else c.run()};return a&&a(w),c=new $l(u),c.scheduler=i?()=>i(w,!1):w,v=_=>gu(_,!1,c),m=c.onStop=()=>{const _=br.get(c);if(_){if(l)l(_,4);else for(const O of _)O();br.delete(c)}},t?r?w(!0):y=c.run():i?i(w.bind(null,!0),!0):c.run(),x.pause=c.pause.bind(c),x.resume=c.resume.bind(c),x.stop=x,x}function Tt(e,t=1/0,n){if(t<=0||!me(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Ee(e))Tt(e.value,t,n);else if(j(e))for(let r=0;r<e.length;r++)Tt(e[r],t,n);else if(Sn(e)||un(e))e.forEach(r=>{Tt(r,t,n)});else if(Cl(e)){for(const r in e)Tt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Tt(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Qn(e,t,n,r){try{return r?e(...r):e()}catch(s){Zn(s,t,n)}}function ft(e,t,n,r){if(Y(e)){const s=Qn(e,t,n,r);return s&&bl(s)&&s.catch(o=>{Zn(o,t,n)}),s}if(j(e)){const s=[];for(let o=0;o<e.length;o++)s.push(ft(e[o],t,n,r));return s}}function Zn(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||he;if(t){let a=t.parent;const l=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,l,f)===!1)return}a=a.parent}if(o){It(),Qn(o,null,10,[e,l,f]),Ot();return}}_u(e,n,s,r,i)}function _u(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const qe=[];let mt=-1;const pn=[];let Vt=null,ln=0;const Jl=Promise.resolve();let wr=null;function Nr(e){const t=wr||Jl;return e?t.then(this?e.bind(this):e):t}function yu(e){let t=mt+1,n=qe.length;for(;t<n;){const r=t+n>>>1,s=qe[r],o=zn(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function bo(e){if(!(e.flags&1)){const t=zn(e),n=qe[qe.length-1];!n||!(e.flags&2)&&t>=zn(n)?qe.push(e):qe.splice(yu(t),0,e),e.flags|=1,Xl()}}function Xl(){wr||(wr=Jl.then(Zl))}function Yl(e){j(e)?pn.push(...e):Vt&&e.id===-1?Vt.splice(ln+1,0,e):e.flags&1||(pn.push(e),e.flags|=1),Xl()}function Wo(e,t,n=mt+1){for(;n<qe.length;n++){const r=qe[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;qe.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Ql(e){if(pn.length){const t=[...new Set(pn)].sort((n,r)=>zn(n)-zn(r));if(pn.length=0,Vt){Vt.push(...t);return}for(Vt=t,ln=0;ln<Vt.length;ln++){const n=Vt[ln];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Vt=null,ln=0}}const zn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Zl(e){const t=rt;try{for(mt=0;mt<qe.length;mt++){const n=qe[mt];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),Qn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;mt<qe.length;mt++){const n=qe[mt];n&&(n.flags&=-2)}mt=-1,qe.length=0,Ql(),wr=null,(qe.length||pn.length)&&Zl()}}let Ie=null,ea=null;function Cr(e){const t=Ie;return Ie=e,ea=e&&e.type.__scopeId||null,t}function bu(e,t=Ie,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&ri(-1);const o=Cr(t);let i;try{i=e(...s)}finally{Cr(o),r._d&&ri(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function wo(e,t){if(Ie===null)return e;const n=Hr(Ie),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,a,l=he]=t[s];o&&(Y(o)&&(o={mounted:o,updated:o}),o.deep&&Tt(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function Kt(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const a=s[i];o&&(a.oldValue=o[i].value);let l=a.dir[r];l&&(It(),ft(l,n,8,[e.el,a,e,t]),Ot())}}const wu=Symbol("_vte"),ta=e=>e.__isTeleport,Dt=Symbol("_leaveCb"),ar=Symbol("_enterCb");function Cu(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return nn(()=>{e.isMounted=!0}),xo(()=>{e.isUnmounting=!0}),e}const tt=[Function,Array],na={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:tt,onEnter:tt,onAfterEnter:tt,onEnterCancelled:tt,onBeforeLeave:tt,onLeave:tt,onAfterLeave:tt,onLeaveCancelled:tt,onBeforeAppear:tt,onAppear:tt,onAfterAppear:tt,onAppearCancelled:tt},ra=e=>{const t=e.subTree;return t.component?ra(t.component):t},Su={name:"BaseTransition",props:na,setup(e,{slots:t}){const n=$o(),r=Cu();return()=>{const s=t.default&&ia(t.default(),!0);if(!s||!s.length)return;const o=sa(s),i=se(e),{mode:a}=i;if(r.isLeaving)return es(o);const l=Go(o);if(!l)return es(o);let f=Ls(l,i,r,n,u=>f=u);l.type!==ke&&gn(l,f);let c=n.subTree&&Go(n.subTree);if(c&&c.type!==ke&&!Ut(l,c)&&ra(n).type!==ke){let u=Ls(c,i,r,n);if(gn(c,u),a==="out-in"&&l.type!==ke)return r.isLeaving=!0,u.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete u.afterLeave,c=void 0},es(o);a==="in-out"&&l.type!==ke?u.delayLeave=(m,v,d)=>{const g=oa(r,c);g[String(c.key)]=c,m[Dt]=()=>{v(),m[Dt]=void 0,delete f.delayedLeave,c=void 0},f.delayedLeave=()=>{d(),delete f.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function sa(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==ke){t=n;break}}return t}const xu=Su;function oa(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Ls(e,t,n,r,s){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:f,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:m,onLeave:v,onAfterLeave:d,onLeaveCancelled:g,onBeforeAppear:C,onAppear:x,onAfterAppear:y,onAppearCancelled:w}=t,_=String(e.key),O=oa(n,e),q=(L,K)=>{L&&ft(L,r,9,K)},z=(L,K)=>{const ee=K[1];q(L,K),j(L)?L.every(D=>D.length<=1)&&ee():L.length<=1&&ee()},U={mode:i,persisted:a,beforeEnter(L){let K=l;if(!n.isMounted)if(o)K=C||l;else return;L[Dt]&&L[Dt](!0);const ee=O[_];ee&&Ut(e,ee)&&ee.el[Dt]&&ee.el[Dt](),q(K,[L])},enter(L){let K=f,ee=c,D=u;if(!n.isMounted)if(o)K=x||f,ee=y||c,D=w||u;else return;let te=!1;const xe=L[ar]=$e=>{te||(te=!0,$e?q(D,[L]):q(ee,[L]),U.delayedLeave&&U.delayedLeave(),L[ar]=void 0)};K?z(K,[L,xe]):xe()},leave(L,K){const ee=String(e.key);if(L[ar]&&L[ar](!0),n.isUnmounting)return K();q(m,[L]);let D=!1;const te=L[Dt]=xe=>{D||(D=!0,K(),xe?q(g,[L]):q(d,[L]),L[Dt]=void 0,O[ee]===e&&delete O[ee])};O[ee]=e,v?z(v,[L,te]):te()},clone(L){const K=Ls(L,t,n,r,s);return s&&s(K),K}};return U}function es(e){if(er(e))return e=$t(e),e.children=null,e}function Go(e){if(!er(e))return ta(e.type)&&e.children?sa(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Y(n.default))return n.default()}}function gn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,gn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ia(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Fe?(i.patchFlag&128&&s++,r=r.concat(ia(i.children,t,a))):(t||i.type!==ke)&&r.push(a!=null?$t(i,{key:a}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Co(e,t){return Y(e)?(()=>Te({name:e.name},t,{setup:e}))():e}function So(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Sr(e,t,n,r,s=!1){if(j(e)){e.forEach((d,g)=>Sr(d,t&&(j(t)?t[g]:t),n,r,s));return}if(Qt(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Sr(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?Hr(r.component):r.el,i=s?null:o,{i:a,r:l}=e,f=t&&t.r,c=a.refs===he?a.refs={}:a.refs,u=a.setupState,m=se(u),v=u===he?()=>!1:d=>ue(m,d);if(f!=null&&f!==l&&(Ce(f)?(c[f]=null,v(f)&&(u[f]=null)):Ee(f)&&(f.value=null)),Y(l))Qn(l,a,12,[i,c]);else{const d=Ce(l),g=Ee(l);if(d||g){const C=()=>{if(e.f){const x=d?v(l)?u[l]:c[l]:l.value;s?j(x)&&ao(x,o):j(x)?x.includes(o)||x.push(o):d?(c[l]=[o],v(l)&&(u[l]=c[l])):(l.value=[o],e.k&&(c[e.k]=l.value))}else d?(c[l]=i,v(l)&&(u[l]=i)):g&&(l.value=i,e.k&&(c[e.k]=i))};i?(C.id=-1,Pe(C,n)):C()}}}const Jo=e=>e.nodeType===8;Xn().requestIdleCallback;Xn().cancelIdleCallback;function Eu(e,t){if(Jo(e)&&e.data==="["){let n=1,r=e.nextSibling;for(;r;){if(r.nodeType===1){if(t(r)===!1)break}else if(Jo(r))if(r.data==="]"){if(--n===0)break}else r.data==="["&&n++;r=r.nextSibling}}else t(e)}const Qt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function r1(e){Y(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:s=200,hydrate:o,timeout:i,suspensible:a=!0,onError:l}=e;let f=null,c,u=0;const m=()=>(u++,f=null,v()),v=()=>{let d;return f||(d=f=t().catch(g=>{if(g=g instanceof Error?g:new Error(String(g)),l)return new Promise((C,x)=>{l(g,()=>C(m()),()=>x(g),u+1)});throw g}).then(g=>d!==f&&f?f:(g&&(g.__esModule||g[Symbol.toStringTag]==="Module")&&(g=g.default),c=g,g)))};return Co({name:"AsyncComponentWrapper",__asyncLoader:v,__asyncHydrate(d,g,C){const x=o?()=>{const w=o(()=>{C()},_=>Eu(d,_));w&&(g.bum||(g.bum=[])).push(w),(g.u||(g.u=[])).push(()=>!0)}:C;c?x():v().then(()=>!g.isUnmounted&&x())},get __asyncResolved(){return c},setup(){const d=Ae;if(So(d),c)return()=>ts(c,d);const g=w=>{f=null,Zn(w,d,13,!r)};if(a&&d.suspense||_n)return v().then(w=>()=>ts(w,d)).catch(w=>(g(w),()=>r?Oe(r,{error:w}):null));const C=we(!1),x=we(),y=we(!!s);return s&&setTimeout(()=>{y.value=!1},s),i!=null&&setTimeout(()=>{if(!C.value&&!x.value){const w=new Error(`Async component timed out after ${i}ms.`);g(w),x.value=w}},i),v().then(()=>{C.value=!0,d.parent&&er(d.parent.vnode)&&d.parent.update()}).catch(w=>{g(w),x.value=w}),()=>{if(C.value&&c)return ts(c,d);if(x.value&&r)return Oe(r,{error:x.value});if(n&&!y.value)return Oe(n)}}})}function ts(e,t){const{ref:n,props:r,children:s,ce:o}=t.vnode,i=Oe(e,r,s);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const er=e=>e.type.__isKeepAlive,Ru={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=$o(),r=n.ctx;if(!r.renderer)return()=>{const y=t.default&&t.default();return y&&y.length===1?y[0]:y};const s=new Map,o=new Set;let i=null;const a=n.suspense,{renderer:{p:l,m:f,um:c,o:{createElement:u}}}=r,m=u("div");r.activate=(y,w,_,O,q)=>{const z=y.component;f(y,w,_,0,a),l(z.vnode,y,w,_,z,a,O,y.slotScopeIds,q),Pe(()=>{z.isDeactivated=!1,z.a&&fn(z.a);const U=y.props&&y.props.onVnodeMounted;U&&nt(U,z.parent,y)},a)},r.deactivate=y=>{const w=y.component;Er(w.m),Er(w.a),f(y,m,null,1,a),Pe(()=>{w.da&&fn(w.da);const _=y.props&&y.props.onVnodeUnmounted;_&&nt(_,w.parent,y),w.isDeactivated=!0},a)};function v(y){ns(y),c(y,n,a,!0)}function d(y){s.forEach((w,_)=>{const O=Hs(w.type);O&&!y(O)&&g(_)})}function g(y){const w=s.get(y);w&&(!i||!Ut(w,i))?v(w):i&&ns(i),s.delete(y),o.delete(y)}vt(()=>[e.include,e.exclude],([y,w])=>{y&&d(_=>Pn(y,_)),w&&d(_=>!Pn(w,_))},{flush:"post",deep:!0});let C=null;const x=()=>{C!=null&&(Rr(n.subTree.type)?Pe(()=>{s.set(C,cr(n.subTree))},n.subTree.suspense):s.set(C,cr(n.subTree)))};return nn(x),ca(x),xo(()=>{s.forEach(y=>{const{subTree:w,suspense:_}=n,O=cr(w);if(y.type===O.type&&y.key===O.key){ns(O);const q=O.component.da;q&&Pe(q,_);return}v(y)})}),()=>{if(C=null,!t.default)return i=null;const y=t.default(),w=y[0];if(y.length>1)return i=null,y;if(!vn(w)||!(w.shapeFlag&4)&&!(w.shapeFlag&128))return i=null,w;let _=cr(w);if(_.type===ke)return i=null,_;const O=_.type,q=Hs(Qt(_)?_.type.__asyncResolved||{}:O),{include:z,exclude:U,max:L}=e;if(z&&(!q||!Pn(z,q))||U&&q&&Pn(U,q))return _.shapeFlag&=-257,i=_,w;const K=_.key==null?O:_.key,ee=s.get(K);return _.el&&(_=$t(_),w.shapeFlag&128&&(w.ssContent=_)),C=K,ee?(_.el=ee.el,_.component=ee.component,_.transition&&gn(_,_.transition),_.shapeFlag|=512,o.delete(K),o.add(K)):(o.add(K),L&&o.size>parseInt(L,10)&&g(o.values().next().value)),_.shapeFlag|=256,i=_,Rr(w.type)?w:_}}},s1=Ru;function Pn(e,t){return j(e)?e.some(n=>Pn(n,t)):Ce(e)?e.split(",").includes(t):Oc(e)?(e.lastIndex=0,e.test(t)):!1}function Tu(e,t){la(e,"a",t)}function Au(e,t){la(e,"da",t)}function la(e,t,n=Ae){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Vr(t,r,n),n){let s=n.parent;for(;s&&s.parent;)er(s.parent.vnode)&&Iu(r,t,n,s),s=s.parent}}function Iu(e,t,n,r){const s=Vr(t,e,r,!0);En(()=>{ao(r[t],s)},n)}function ns(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function cr(e){return e.shapeFlag&128?e.ssContent:e}function Vr(e,t,n=Ae,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{It();const a=nr(n),l=ft(t,n,e,i);return a(),Ot(),l});return r?s.unshift(o):s.push(o),o}}const Pt=e=>(t,n=Ae)=>{(!_n||e==="sp")&&Vr(e,(...r)=>t(...r),n)},Ou=Pt("bm"),nn=Pt("m"),aa=Pt("bu"),ca=Pt("u"),xo=Pt("bum"),En=Pt("um"),$u=Pt("sp"),Pu=Pt("rtg"),Lu=Pt("rtc");function ku(e,t=Ae){Vr("ec",e,t)}const Eo="components",Mu="directives";function Nu(e,t){return Ro(Eo,e,!0,t)||e}const ua=Symbol.for("v-ndc");function o1(e){return Ce(e)?Ro(Eo,e,!1)||e:e||ua}function i1(e){return Ro(Mu,e)}function Ro(e,t,n=!0,r=!1){const s=Ie||Ae;if(s){const o=s.type;if(e===Eo){const a=Hs(o,!1);if(a&&(a===t||a===ot(t)||a===kr(ot(t))))return o}const i=Xo(s[e]||o[e],t)||Xo(s.appContext[e],t);return!i&&r?o:i}}function Xo(e,t){return e&&(e[t]||e[ot(t)]||e[kr(ot(t))])}function Vu(e,t,n,r){let s;const o=n&&n[r],i=j(e);if(i||Ce(e)){const a=i&&Ft(e);let l=!1,f=!1;a&&(l=!st(e),f=jt(e),e=Mr(e)),s=new Array(e.length);for(let c=0,u=e.length;c<u;c++)s[c]=t(l?f?yr(Le(e[c])):Le(e[c]):e[c],c,void 0,o&&o[c])}else if(typeof e=="number"){s=new Array(e);for(let a=0;a<e;a++)s[a]=t(a+1,a,void 0,o&&o[a])}else if(me(e))if(e[Symbol.iterator])s=Array.from(e,(a,l)=>t(a,l,void 0,o&&o[l]));else{const a=Object.keys(e);s=new Array(a.length);for(let l=0,f=a.length;l<f;l++){const c=a[l];s[l]=t(e[c],c,l,o&&o[l])}}else s=[];return n&&(n[r]=s),s}function Se(e,t,n={},r,s){if(Ie.ce||Ie.parent&&Qt(Ie.parent)&&Ie.parent.ce)return t!=="default"&&(n.name=t),J(),Ds(Fe,null,[Oe("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),J();const i=o&&fa(o(n)),a=n.key||i&&i.key,l=Ds(Fe,{key:(a&&!ut(a)?a:`_${t}`)+(!i&&r?"_fb":"")},i||(r?r():[]),i&&e._===1?64:-2);return!s&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function fa(e){return e.some(t=>vn(t)?!(t.type===ke||t.type===Fe&&!fa(t.children)):!0)?e:null}function Du(e,t){const n={};for(const r in e)n[t&&/[A-Z]/.test(r)?`on:${r}`:dr(r)]=e[r];return n}const ks=e=>e?Oa(e)?Hr(e):ks(e.parent):null,Vn=Te(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ks(e.parent),$root:e=>ks(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>To(e),$forceUpdate:e=>e.f||(e.f=()=>{bo(e.update)}),$nextTick:e=>e.n||(e.n=Nr.bind(e.proxy)),$watch:e=>af.bind(e)}),rs=(e,t)=>e!==he&&!e.__isScriptSetup&&ue(e,t),Bu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:a,appContext:l}=e;let f;if(t[0]!=="$"){const v=i[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(rs(r,t))return i[t]=1,r[t];if(s!==he&&ue(s,t))return i[t]=2,s[t];if((f=e.propsOptions[0])&&ue(f,t))return i[t]=3,o[t];if(n!==he&&ue(n,t))return i[t]=4,n[t];Ms&&(i[t]=0)}}const c=Vn[t];let u,m;if(c)return t==="$attrs"&&Ve(e.attrs,"get",""),c(e);if((u=a.__cssModules)&&(u=u[t]))return u;if(n!==he&&ue(n,t))return i[t]=4,n[t];if(m=l.config.globalProperties,ue(m,t))return m[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return rs(s,t)?(s[t]=n,!0):r!==he&&ue(r,t)?(r[t]=n,!0):ue(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let a;return!!n[i]||e!==he&&ue(e,i)||rs(t,i)||(a=o[0])&&ue(a,i)||ue(r,i)||ue(Vn,i)||ue(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ue(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Yo(e){return j(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ms=!0;function Hu(e){const t=To(e),n=e.proxy,r=e.ctx;Ms=!1,t.beforeCreate&&Qo(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:a,provide:l,inject:f,created:c,beforeMount:u,mounted:m,beforeUpdate:v,updated:d,activated:g,deactivated:C,beforeDestroy:x,beforeUnmount:y,destroyed:w,unmounted:_,render:O,renderTracked:q,renderTriggered:z,errorCaptured:U,serverPrefetch:L,expose:K,inheritAttrs:ee,components:D,directives:te,filters:xe}=t;if(f&&Uu(f,r,null),i)for(const Q in i){const oe=i[Q];Y(oe)&&(r[Q]=oe.bind(n))}if(s){const Q=s.call(n,n);me(Q)&&(e.data=Yn(Q))}if(Ms=!0,o)for(const Q in o){const oe=o[Q],it=Y(oe)?oe.bind(n,n):Y(oe.get)?oe.get.bind(n,n):rt,pt=!Y(oe)&&Y(oe.set)?oe.set.bind(n):rt,k=le({get:it,set:pt});Object.defineProperty(r,Q,{enumerable:!0,configurable:!0,get:()=>k.value,set:V=>k.value=V})}if(a)for(const Q in a)da(a[Q],r,n,Q);if(l){const Q=Y(l)?l.call(n):l;Reflect.ownKeys(Q).forEach(oe=>{hn(oe,Q[oe])})}c&&Qo(c,e,"c");function fe(Q,oe){j(oe)?oe.forEach(it=>Q(it.bind(n))):oe&&Q(oe.bind(n))}if(fe(Ou,u),fe(nn,m),fe(aa,v),fe(ca,d),fe(Tu,g),fe(Au,C),fe(ku,U),fe(Lu,q),fe(Pu,z),fe(xo,y),fe(En,_),fe($u,L),j(K))if(K.length){const Q=e.exposed||(e.exposed={});K.forEach(oe=>{Object.defineProperty(Q,oe,{get:()=>n[oe],set:it=>n[oe]=it})})}else e.exposed||(e.exposed={});O&&e.render===rt&&(e.render=O),ee!=null&&(e.inheritAttrs=ee),D&&(e.components=D),te&&(e.directives=te),L&&So(e)}function Uu(e,t,n=rt){j(e)&&(e=Ns(e));for(const r in e){const s=e[r];let o;me(s)?"default"in s?o=De(s.from||r,s.default,!0):o=De(s.from||r):o=De(s),Ee(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function Qo(e,t,n){ft(j(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function da(e,t,n,r){let s=r.includes(".")?Ea(n,r):()=>n[r];if(Ce(e)){const o=t[e];Y(o)&&vt(s,o)}else if(Y(e))vt(s,e.bind(n));else if(me(e))if(j(e))e.forEach(o=>da(o,t,n,r));else{const o=Y(e.handler)?e.handler.bind(n):t[e.handler];Y(o)&&vt(s,o,e)}}function To(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:!s.length&&!n&&!r?l=t:(l={},s.length&&s.forEach(f=>xr(l,f,i,!0)),xr(l,t,i)),me(t)&&o.set(t,l),l}function xr(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&xr(e,o,n,!0),s&&s.forEach(i=>xr(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=qu[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const qu={data:Zo,props:ei,emits:ei,methods:Ln,computed:Ln,beforeCreate:Ue,created:Ue,beforeMount:Ue,mounted:Ue,beforeUpdate:Ue,updated:Ue,beforeDestroy:Ue,beforeUnmount:Ue,destroyed:Ue,unmounted:Ue,activated:Ue,deactivated:Ue,errorCaptured:Ue,serverPrefetch:Ue,components:Ln,directives:Ln,watch:ju,provide:Zo,inject:Fu};function Zo(e,t){return t?e?function(){return Te(Y(e)?e.call(this,this):e,Y(t)?t.call(this,this):t)}:t:e}function Fu(e,t){return Ln(Ns(e),Ns(t))}function Ns(e){if(j(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ue(e,t){return e?[...new Set([].concat(e,t))]:t}function Ln(e,t){return e?Te(Object.create(null),e,t):t}function ei(e,t){return e?j(e)&&j(t)?[...new Set([...e,...t])]:Te(Object.create(null),Yo(e),Yo(t??{})):t}function ju(e,t){if(!e)return t;if(!t)return e;const n=Te(Object.create(null),e);for(const r in t)n[r]=Ue(e[r],t[r]);return n}function pa(){return{app:null,config:{isNativeTag:Ac,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let zu=0;function Ku(e,t){return function(r,s=null){Y(r)||(r=Te({},r)),s!=null&&!me(s)&&(s=null);const o=pa(),i=new WeakSet,a=[];let l=!1;const f=o.app={_uid:zu++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:Tf,get config(){return o.config},set config(c){},use(c,...u){return i.has(c)||(c&&Y(c.install)?(i.add(c),c.install(f,...u)):Y(c)&&(i.add(c),c(f,...u))),f},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),f},component(c,u){return u?(o.components[c]=u,f):o.components[c]},directive(c,u){return u?(o.directives[c]=u,f):o.directives[c]},mount(c,u,m){if(!l){const v=f._ceVNode||Oe(r,s);return v.appContext=o,m===!0?m="svg":m===!1&&(m=void 0),u&&t?t(v,c):e(v,c,m),l=!0,f._container=c,c.__vue_app__=f,Hr(v.component)}},onUnmount(c){a.push(c)},unmount(){l&&(ft(a,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,u){return o.provides[c]=u,f},runWithContext(c){const u=Zt;Zt=f;try{return c()}finally{Zt=u}}};return f}}let Zt=null;function hn(e,t){if(Ae){let n=Ae.provides;const r=Ae.parent&&Ae.parent.provides;r===n&&(n=Ae.provides=Object.create(r)),n[e]=t}}function De(e,t,n=!1){const r=Ae||Ie;if(r||Zt){let s=Zt?Zt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&Y(t)?t.call(r&&r.proxy):t}}function Wu(){return!!(Ae||Ie||Zt)}const ha={},ma=()=>Object.create(ha),ga=e=>Object.getPrototypeOf(e)===ha;function Gu(e,t,n,r=!1){const s={},o=ma();e.propsDefaults=Object.create(null),va(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:zl(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function Ju(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,a=se(s),[l]=e.propsOptions;let f=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let u=0;u<c.length;u++){let m=c[u];if(Dr(e.emitsOptions,m))continue;const v=t[m];if(l)if(ue(o,m))v!==o[m]&&(o[m]=v,f=!0);else{const d=ot(m);s[d]=Vs(l,a,d,v,e,!1)}else v!==o[m]&&(o[m]=v,f=!0)}}}else{va(e,t,s,o)&&(f=!0);let c;for(const u in a)(!t||!ue(t,u)&&((c=zt(u))===u||!ue(t,c)))&&(l?n&&(n[u]!==void 0||n[c]!==void 0)&&(s[u]=Vs(l,a,u,void 0,e,!0)):delete s[u]);if(o!==a)for(const u in o)(!t||!ue(t,u)&&!0)&&(delete o[u],f=!0)}f&&Rt(e.attrs,"set","")}function va(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(kn(l))continue;const f=t[l];let c;s&&ue(s,c=ot(l))?!o||!o.includes(c)?n[c]=f:(a||(a={}))[c]=f:Dr(e.emitsOptions,l)||(!(l in r)||f!==r[l])&&(r[l]=f,i=!0)}if(o){const l=se(n),f=a||he;for(let c=0;c<o.length;c++){const u=o[c];n[u]=Vs(s,l,u,f[u],e,!ue(f,u))}}return i}function Vs(e,t,n,r,s,o){const i=e[n];if(i!=null){const a=ue(i,"default");if(a&&r===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&Y(l)){const{propsDefaults:f}=s;if(n in f)r=f[n];else{const c=nr(s);r=f[n]=l.call(null,t),c()}}else r=l;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!a?r=!1:i[1]&&(r===""||r===zt(n))&&(r=!0))}return r}const Xu=new WeakMap;function _a(e,t,n=!1){const r=n?Xu:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},a=[];let l=!1;if(!Y(e)){const c=u=>{l=!0;const[m,v]=_a(u,t,!0);Te(i,m),v&&a.push(...v)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!l)return me(e)&&r.set(e,cn),cn;if(j(o))for(let c=0;c<o.length;c++){const u=ot(o[c]);ti(u)&&(i[u]=he)}else if(o)for(const c in o){const u=ot(c);if(ti(u)){const m=o[c],v=i[u]=j(m)||Y(m)?{type:m}:Te({},m),d=v.type;let g=!1,C=!0;if(j(d))for(let x=0;x<d.length;++x){const y=d[x],w=Y(y)&&y.name;if(w==="Boolean"){g=!0;break}else w==="String"&&(C=!1)}else g=Y(d)&&d.name==="Boolean";v[0]=g,v[1]=C,(g||ue(v,"default"))&&a.push(u)}}const f=[i,a];return me(e)&&r.set(e,f),f}function ti(e){return e[0]!=="$"&&!kn(e)}const Ao=e=>e[0]==="_"||e==="$stable",Io=e=>j(e)?e.map(gt):[gt(e)],Yu=(e,t,n)=>{if(t._n)return t;const r=bu((...s)=>Io(t(...s)),n);return r._c=!1,r},ya=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Ao(s))continue;const o=e[s];if(Y(o))t[s]=Yu(s,o,r);else if(o!=null){const i=Io(o);t[s]=()=>i}}},ba=(e,t)=>{const n=Io(t);e.slots.default=()=>n},wa=(e,t,n)=>{for(const r in t)(n||!Ao(r))&&(e[r]=t[r])},Qu=(e,t,n)=>{const r=e.slots=ma();if(e.vnode.shapeFlag&32){const s=t._;s?(wa(r,t,n),n&&Sl(r,"_",s,!0)):ya(t,r)}else t&&ba(e,t)},Zu=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=he;if(r.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:wa(s,t,n):(o=!t.$stable,ya(t,s)),i=t}else t&&(ba(e,t),i={default:1});if(o)for(const a in s)!Ao(a)&&i[a]==null&&delete s[a]};function ef(){typeof __VUE_PROD_HYDRATION_MISMATCH_DETAILS__!="boolean"&&(Xn().__VUE_PROD_HYDRATION_MISMATCH_DETAILS__=!1)}const Pe=mf;function tf(e){return nf(e)}function nf(e,t){ef();const n=Xn();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:a,createComment:l,setText:f,setElementText:c,parentNode:u,nextSibling:m,setScopeId:v=rt,insertStaticContent:d}=e,g=(p,h,b,R=null,S=null,T=null,M=void 0,P=null,$=!!h.dynamicChildren)=>{if(p===h)return;p&&!Ut(p,h)&&(R=E(p),V(p,S,T,!0),p=null),h.patchFlag===-2&&($=!1,h.dynamicChildren=null);const{type:A,ref:W,shapeFlag:N}=h;switch(A){case Br:C(p,h,b,R);break;case ke:x(p,h,b,R);break;case pr:p==null&&y(h,b,R,M);break;case Fe:D(p,h,b,R,S,T,M,P,$);break;default:N&1?O(p,h,b,R,S,T,M,P,$):N&6?te(p,h,b,R,S,T,M,P,$):(N&64||N&128)&&A.process(p,h,b,R,S,T,M,P,$,H)}W!=null&&S&&Sr(W,p&&p.ref,T,h||p,!h)},C=(p,h,b,R)=>{if(p==null)r(h.el=a(h.children),b,R);else{const S=h.el=p.el;h.children!==p.children&&f(S,h.children)}},x=(p,h,b,R)=>{p==null?r(h.el=l(h.children||""),b,R):h.el=p.el},y=(p,h,b,R)=>{[p.el,p.anchor]=d(p.children,h,b,R,p.el,p.anchor)},w=({el:p,anchor:h},b,R)=>{let S;for(;p&&p!==h;)S=m(p),r(p,b,R),p=S;r(h,b,R)},_=({el:p,anchor:h})=>{let b;for(;p&&p!==h;)b=m(p),s(p),p=b;s(h)},O=(p,h,b,R,S,T,M,P,$)=>{h.type==="svg"?M="svg":h.type==="math"&&(M="mathml"),p==null?q(h,b,R,S,T,M,P,$):L(p,h,S,T,M,P,$)},q=(p,h,b,R,S,T,M,P)=>{let $,A;const{props:W,shapeFlag:N,transition:F,dirs:X}=p;if($=p.el=i(p.type,T,W&&W.is,W),N&8?c($,p.children):N&16&&U(p.children,$,null,R,S,ss(p,T),M,P),X&&Kt(p,null,R,"created"),z($,p,p.scopeId,M,R),W){for(const ge in W)ge!=="value"&&!kn(ge)&&o($,ge,null,W[ge],T,R);"value"in W&&o($,"value",null,W.value,T),(A=W.onVnodeBeforeMount)&&nt(A,R,p)}X&&Kt(p,null,R,"beforeMount");const re=rf(S,F);re&&F.beforeEnter($),r($,h,b),((A=W&&W.onVnodeMounted)||re||X)&&Pe(()=>{A&&nt(A,R,p),re&&F.enter($),X&&Kt(p,null,R,"mounted")},S)},z=(p,h,b,R,S)=>{if(b&&v(p,b),R)for(let T=0;T<R.length;T++)v(p,R[T]);if(S){let T=S.subTree;if(h===T||Rr(T.type)&&(T.ssContent===h||T.ssFallback===h)){const M=S.vnode;z(p,M,M.scopeId,M.slotScopeIds,S.parent)}}},U=(p,h,b,R,S,T,M,P,$=0)=>{for(let A=$;A<p.length;A++){const W=p[A]=P?Bt(p[A]):gt(p[A]);g(null,W,h,b,R,S,T,M,P)}},L=(p,h,b,R,S,T,M)=>{const P=h.el=p.el;let{patchFlag:$,dynamicChildren:A,dirs:W}=h;$|=p.patchFlag&16;const N=p.props||he,F=h.props||he;let X;if(b&&Wt(b,!1),(X=F.onVnodeBeforeUpdate)&&nt(X,b,h,p),W&&Kt(h,p,b,"beforeUpdate"),b&&Wt(b,!0),(N.innerHTML&&F.innerHTML==null||N.textContent&&F.textContent==null)&&c(P,""),A?K(p.dynamicChildren,A,P,b,R,ss(h,S),T):M||oe(p,h,P,null,b,R,ss(h,S),T,!1),$>0){if($&16)ee(P,N,F,b,S);else if($&2&&N.class!==F.class&&o(P,"class",null,F.class,S),$&4&&o(P,"style",N.style,F.style,S),$&8){const re=h.dynamicProps;for(let ge=0;ge<re.length;ge++){const de=re[ge],We=N[de],Me=F[de];(Me!==We||de==="value")&&o(P,de,We,Me,S,b)}}$&1&&p.children!==h.children&&c(P,h.children)}else!M&&A==null&&ee(P,N,F,b,S);((X=F.onVnodeUpdated)||W)&&Pe(()=>{X&&nt(X,b,h,p),W&&Kt(h,p,b,"updated")},R)},K=(p,h,b,R,S,T,M)=>{for(let P=0;P<h.length;P++){const $=p[P],A=h[P],W=$.el&&($.type===Fe||!Ut($,A)||$.shapeFlag&198)?u($.el):b;g($,A,W,null,R,S,T,M,!0)}},ee=(p,h,b,R,S)=>{if(h!==b){if(h!==he)for(const T in h)!kn(T)&&!(T in b)&&o(p,T,h[T],null,S,R);for(const T in b){if(kn(T))continue;const M=b[T],P=h[T];M!==P&&T!=="value"&&o(p,T,P,M,S,R)}"value"in b&&o(p,"value",h.value,b.value,S)}},D=(p,h,b,R,S,T,M,P,$)=>{const A=h.el=p?p.el:a(""),W=h.anchor=p?p.anchor:a("");let{patchFlag:N,dynamicChildren:F,slotScopeIds:X}=h;X&&(P=P?P.concat(X):X),p==null?(r(A,b,R),r(W,b,R),U(h.children||[],b,W,S,T,M,P,$)):N>0&&N&64&&F&&p.dynamicChildren?(K(p.dynamicChildren,F,b,S,T,M,P),(h.key!=null||S&&h===S.subTree)&&Ca(p,h,!0)):oe(p,h,b,W,S,T,M,P,$)},te=(p,h,b,R,S,T,M,P,$)=>{h.slotScopeIds=P,p==null?h.shapeFlag&512?S.ctx.activate(h,b,R,M,$):xe(h,b,R,S,T,M,$):$e(p,h,$)},xe=(p,h,b,R,S,T,M)=>{const P=p.component=wf(p,R,S);if(er(p)&&(P.ctx.renderer=H),Cf(P,!1,M),P.asyncDep){if(S&&S.registerDep(P,fe,M),!p.el){const $=P.subTree=Oe(ke);x(null,$,h,b)}}else fe(P,p,h,b,S,T,M)},$e=(p,h,b)=>{const R=h.component=p.component;if(pf(p,h,b))if(R.asyncDep&&!R.asyncResolved){Q(R,h,b);return}else R.next=h,R.update();else h.el=p.el,R.vnode=h},fe=(p,h,b,R,S,T,M)=>{const P=()=>{if(p.isMounted){let{next:N,bu:F,u:X,parent:re,vnode:ge}=p;{const Ge=Sa(p);if(Ge){N&&(N.el=ge.el,Q(p,N,M)),Ge.asyncDep.then(()=>{p.isUnmounted||P()});return}}let de=N,We;Wt(p,!1),N?(N.el=ge.el,Q(p,N,M)):N=ge,F&&fn(F),(We=N.props&&N.props.onVnodeBeforeUpdate)&&nt(We,re,N,ge),Wt(p,!0);const Me=os(p),lt=p.subTree;p.subTree=Me,g(lt,Me,u(lt.el),E(lt),p,S,T),N.el=Me.el,de===null&&hf(p,Me.el),X&&Pe(X,S),(We=N.props&&N.props.onVnodeUpdated)&&Pe(()=>nt(We,re,N,ge),S)}else{let N;const{el:F,props:X}=h,{bm:re,m:ge,parent:de,root:We,type:Me}=p,lt=Qt(h);if(Wt(p,!1),re&&fn(re),!lt&&(N=X&&X.onVnodeBeforeMount)&&nt(N,de,h),Wt(p,!0),F&&ae){const Ge=()=>{p.subTree=os(p),ae(F,p.subTree,p,S,null)};lt&&Me.__asyncHydrate?Me.__asyncHydrate(F,p,Ge):Ge()}else{We.ce&&We.ce._injectChildStyle(Me);const Ge=p.subTree=os(p);g(null,Ge,b,R,p,S,T),h.el=Ge.el}if(ge&&Pe(ge,S),!lt&&(N=X&&X.onVnodeMounted)){const Ge=h;Pe(()=>nt(N,de,Ge),S)}(h.shapeFlag&256||de&&Qt(de.vnode)&&de.vnode.shapeFlag&256)&&p.a&&Pe(p.a,S),p.isMounted=!0,h=b=R=null}};p.scope.on();const $=p.effect=new $l(P);p.scope.off();const A=p.update=$.run.bind($),W=p.job=$.runIfDirty.bind($);W.i=p,W.id=p.uid,$.scheduler=()=>bo(W),Wt(p,!0),A()},Q=(p,h,b)=>{h.component=p;const R=p.vnode.props;p.vnode=h,p.next=null,Ju(p,h.props,R,b),Zu(p,h.children,b),It(),Wo(p),Ot()},oe=(p,h,b,R,S,T,M,P,$=!1)=>{const A=p&&p.children,W=p?p.shapeFlag:0,N=h.children,{patchFlag:F,shapeFlag:X}=h;if(F>0){if(F&128){pt(A,N,b,R,S,T,M,P,$);return}else if(F&256){it(A,N,b,R,S,T,M,P,$);return}}X&8?(W&16&&He(A,S,T),N!==A&&c(b,N)):W&16?X&16?pt(A,N,b,R,S,T,M,P,$):He(A,S,T,!0):(W&8&&c(b,""),X&16&&U(N,b,R,S,T,M,P,$))},it=(p,h,b,R,S,T,M,P,$)=>{p=p||cn,h=h||cn;const A=p.length,W=h.length,N=Math.min(A,W);let F;for(F=0;F<N;F++){const X=h[F]=$?Bt(h[F]):gt(h[F]);g(p[F],X,b,null,S,T,M,P,$)}A>W?He(p,S,T,!0,!1,N):U(h,b,R,S,T,M,P,$,N)},pt=(p,h,b,R,S,T,M,P,$)=>{let A=0;const W=h.length;let N=p.length-1,F=W-1;for(;A<=N&&A<=F;){const X=p[A],re=h[A]=$?Bt(h[A]):gt(h[A]);if(Ut(X,re))g(X,re,b,null,S,T,M,P,$);else break;A++}for(;A<=N&&A<=F;){const X=p[N],re=h[F]=$?Bt(h[F]):gt(h[F]);if(Ut(X,re))g(X,re,b,null,S,T,M,P,$);else break;N--,F--}if(A>N){if(A<=F){const X=F+1,re=X<W?h[X].el:R;for(;A<=F;)g(null,h[A]=$?Bt(h[A]):gt(h[A]),b,re,S,T,M,P,$),A++}}else if(A>F)for(;A<=N;)V(p[A],S,T,!0),A++;else{const X=A,re=A,ge=new Map;for(A=re;A<=F;A++){const Je=h[A]=$?Bt(h[A]):gt(h[A]);Je.key!=null&&ge.set(Je.key,A)}let de,We=0;const Me=F-re+1;let lt=!1,Ge=0;const Tn=new Array(Me);for(A=0;A<Me;A++)Tn[A]=0;for(A=X;A<=N;A++){const Je=p[A];if(We>=Me){V(Je,S,T,!0);continue}let ht;if(Je.key!=null)ht=ge.get(Je.key);else for(de=re;de<=F;de++)if(Tn[de-re]===0&&Ut(Je,h[de])){ht=de;break}ht===void 0?V(Je,S,T,!0):(Tn[ht-re]=A+1,ht>=Ge?Ge=ht:lt=!0,g(Je,h[ht],b,null,S,T,M,P,$),We++)}const Uo=lt?sf(Tn):cn;for(de=Uo.length-1,A=Me-1;A>=0;A--){const Je=re+A,ht=h[Je],qo=Je+1<W?h[Je+1].el:R;Tn[A]===0?g(null,ht,b,qo,S,T,M,P,$):lt&&(de<0||A!==Uo[de]?k(ht,b,qo,2):de--)}}},k=(p,h,b,R,S=null)=>{const{el:T,type:M,transition:P,children:$,shapeFlag:A}=p;if(A&6){k(p.component.subTree,h,b,R);return}if(A&128){p.suspense.move(h,b,R);return}if(A&64){M.move(p,h,b,H);return}if(M===Fe){r(T,h,b);for(let N=0;N<$.length;N++)k($[N],h,b,R);r(p.anchor,h,b);return}if(M===pr){w(p,h,b);return}if(R!==2&&A&1&&P)if(R===0)P.beforeEnter(T),r(T,h,b),Pe(()=>P.enter(T),S);else{const{leave:N,delayLeave:F,afterLeave:X}=P,re=()=>{p.ctx.isUnmounted?s(T):r(T,h,b)},ge=()=>{N(T,()=>{re(),X&&X()})};F?F(T,re,ge):ge()}else r(T,h,b)},V=(p,h,b,R=!1,S=!1)=>{const{type:T,props:M,ref:P,children:$,dynamicChildren:A,shapeFlag:W,patchFlag:N,dirs:F,cacheIndex:X}=p;if(N===-2&&(S=!1),P!=null&&(It(),Sr(P,null,b,p,!0),Ot()),X!=null&&(h.renderCache[X]=void 0),W&256){h.ctx.deactivate(p);return}const re=W&1&&F,ge=!Qt(p);let de;if(ge&&(de=M&&M.onVnodeBeforeUnmount)&&nt(de,h,p),W&6)Be(p.component,b,R);else{if(W&128){p.suspense.unmount(b,R);return}re&&Kt(p,null,h,"beforeUnmount"),W&64?p.type.remove(p,h,b,H,R):A&&!A.hasOnce&&(T!==Fe||N>0&&N&64)?He(A,h,b,!1,!0):(T===Fe&&N&384||!S&&W&16)&&He($,h,b),R&&ze(p)}(ge&&(de=M&&M.onVnodeUnmounted)||re)&&Pe(()=>{de&&nt(de,h,p),re&&Kt(p,null,h,"unmounted")},b)},ze=p=>{const{type:h,el:b,anchor:R,transition:S}=p;if(h===Fe){ie(b,R);return}if(h===pr){_(p);return}const T=()=>{s(b),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:M,delayLeave:P}=S,$=()=>M(b,T);P?P(p.el,T,$):$()}else T()},ie=(p,h)=>{let b;for(;p!==h;)b=m(p),s(p),p=b;s(h)},Be=(p,h,b)=>{const{bum:R,scope:S,job:T,subTree:M,um:P,m:$,a:A,parent:W,slots:{__:N}}=p;Er($),Er(A),R&&fn(R),W&&j(N)&&N.forEach(F=>{W.renderCache[F]=void 0}),S.stop(),T&&(T.flags|=8,V(M,p,h,b)),P&&Pe(P,h),Pe(()=>{p.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},He=(p,h,b,R=!1,S=!1,T=0)=>{for(let M=T;M<p.length;M++)V(p[M],h,b,R,S)},E=p=>{if(p.shapeFlag&6)return E(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const h=m(p.anchor||p.el),b=h&&h[wu];return b?m(b):h};let B=!1;const I=(p,h,b)=>{p==null?h._vnode&&V(h._vnode,null,null,!0):g(h._vnode||null,p,h,null,null,null,b),h._vnode=p,B||(B=!0,Wo(),Ql(),B=!1)},H={p:g,um:V,m:k,r:ze,mt:xe,mc:U,pc:oe,pbc:K,n:E,o:e};let ne,ae;return t&&([ne,ae]=t(H)),{render:I,hydrate:ne,createApp:Ku(I,ne)}}function ss({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Wt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function rf(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ca(e,t,n=!1){const r=e.children,s=t.children;if(j(r)&&j(s))for(let o=0;o<r.length;o++){const i=r[o];let a=s[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=s[o]=Bt(s[o]),a.el=i.el),!n&&a.patchFlag!==-2&&Ca(i,a)),a.type===Br&&(a.el=i.el),a.type===ke&&!a.el&&(a.el=i.el)}}function sf(e){const t=e.slice(),n=[0];let r,s,o,i,a;const l=e.length;for(r=0;r<l;r++){const f=e[r];if(f!==0){if(s=n[n.length-1],e[s]<f){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<f?o=a+1:i=a;f<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Sa(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Sa(t)}function Er(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const of=Symbol.for("v-scx"),lf=()=>De(of);function vt(e,t,n){return xa(e,t,n)}function xa(e,t,n=he){const{immediate:r,deep:s,flush:o,once:i}=n,a=Te({},n),l=t&&r||!t&&o!=="post";let f;if(_n){if(o==="sync"){const v=lf();f=v.__watcherHandles||(v.__watcherHandles=[])}else if(!l){const v=()=>{};return v.stop=rt,v.resume=rt,v.pause=rt,v}}const c=Ae;a.call=(v,d,g)=>ft(v,c,d,g);let u=!1;o==="post"?a.scheduler=v=>{Pe(v,c&&c.suspense)}:o!=="sync"&&(u=!0,a.scheduler=(v,d)=>{d?v():bo(v)}),a.augmentJob=v=>{t&&(v.flags|=4),u&&(v.flags|=2,c&&(v.id=c.uid,v.i=c))};const m=vu(e,t,a);return _n&&(f?f.push(m):l&&m()),m}function af(e,t,n){const r=this.proxy,s=Ce(e)?e.includes(".")?Ea(r,e):()=>r[e]:e.bind(r,r);let o;Y(t)?o=t:(o=t.handler,n=t);const i=nr(this),a=xa(s,o.bind(r),n);return i(),a}function Ea(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const cf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ot(t)}Modifiers`]||e[`${zt(t)}Modifiers`];function uf(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||he;let s=n;const o=t.startsWith("update:"),i=o&&cf(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>Ce(c)?c.trim():c)),i.number&&(s=n.map(xl)));let a,l=r[a=dr(t)]||r[a=dr(ot(t))];!l&&o&&(l=r[a=dr(zt(t))]),l&&ft(l,e,6,s);const f=r[a+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,ft(f,e,6,s)}}function Ra(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},a=!1;if(!Y(e)){const l=f=>{const c=Ra(f,t,!0);c&&(a=!0,Te(i,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(me(e)&&r.set(e,null),null):(j(o)?o.forEach(l=>i[l]=null):Te(i,o),me(e)&&r.set(e,i),i)}function Dr(e,t){return!e||!Pr(t)?!1:(t=t.slice(2).replace(/Once$/,""),ue(e,t[0].toLowerCase()+t.slice(1))||ue(e,zt(t))||ue(e,t))}function os(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:a,emit:l,render:f,renderCache:c,props:u,data:m,setupState:v,ctx:d,inheritAttrs:g}=e,C=Cr(e);let x,y;try{if(n.shapeFlag&4){const _=s||r,O=_;x=gt(f.call(O,_,c,u,v,m,d)),y=a}else{const _=t;x=gt(_.length>1?_(u,{attrs:a,slots:i,emit:l}):_(u,null)),y=t.props?a:ff(a)}}catch(_){Dn.length=0,Zn(_,e,1),x=Oe(ke)}let w=x;if(y&&g!==!1){const _=Object.keys(y),{shapeFlag:O}=w;_.length&&O&7&&(o&&_.some(lo)&&(y=df(y,o)),w=$t(w,y,!1,!0))}return n.dirs&&(w=$t(w,null,!1,!0),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&gn(w,n.transition),x=w,Cr(C),x}const ff=e=>{let t;for(const n in e)(n==="class"||n==="style"||Pr(n))&&((t||(t={}))[n]=e[n]);return t},df=(e,t)=>{const n={};for(const r in e)(!lo(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function pf(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:a,patchFlag:l}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?ni(r,i,f):!!i;if(l&8){const c=t.dynamicProps;for(let u=0;u<c.length;u++){const m=c[u];if(i[m]!==r[m]&&!Dr(f,m))return!0}}}else return(s||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?ni(r,i,f):!0:!!i;return!1}function ni(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!Dr(n,o))return!0}return!1}function hf({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Rr=e=>e.__isSuspense;function mf(e,t){t&&t.pendingBranch?j(e)?t.effects.push(...e):t.effects.push(e):Yl(e)}const Fe=Symbol.for("v-fgt"),Br=Symbol.for("v-txt"),ke=Symbol.for("v-cmt"),pr=Symbol.for("v-stc"),Dn=[];let Qe=null;function J(e=!1){Dn.push(Qe=e?null:[])}function gf(){Dn.pop(),Qe=Dn[Dn.length-1]||null}let Kn=1;function ri(e,t=!1){Kn+=e,e<0&&Qe&&t&&(Qe.hasOnce=!0)}function Ta(e){return e.dynamicChildren=Kn>0?Qe||cn:null,gf(),Kn>0&&Qe&&Qe.push(e),e}function Z(e,t,n,r,s,o){return Ta(ye(e,t,n,r,s,o,!0))}function Ds(e,t,n,r,s){return Ta(Oe(e,t,n,r,s,!0))}function vn(e){return e?e.__v_isVNode===!0:!1}function Ut(e,t){return e.type===t.type&&e.key===t.key}const Aa=({key:e})=>e??null,hr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ce(e)||Ee(e)||Y(e)?{i:Ie,r:e,k:t,f:!!n}:e:null);function ye(e,t=null,n=null,r=0,s=null,o=e===Fe?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Aa(t),ref:t&&hr(t),scopeId:ea,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:Ie};return a?(Oo(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Ce(n)?8:16),Kn>0&&!i&&Qe&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&Qe.push(l),l}const Oe=vf;function vf(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===ua)&&(e=ke),vn(e)){const a=$t(e,t,!0);return n&&Oo(a,n),Kn>0&&!o&&Qe&&(a.shapeFlag&6?Qe[Qe.indexOf(e)]=a:Qe.push(a)),a.patchFlag=-2,a}if(Rf(e)&&(e=e.__vccOpts),t){t=_f(t);let{class:a,style:l}=t;a&&!Ce(a)&&(t.class=_e(a)),me(l)&&(_o(l)&&!j(l)&&(l=Te({},l)),t.style=Ze(l))}const i=Ce(e)?1:Rr(e)?128:ta(e)?64:me(e)?4:Y(e)?2:0;return ye(e,t,n,r,s,i,o,!0)}function _f(e){return e?_o(e)||ga(e)?Te({},e):e:null}function $t(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:a,transition:l}=e,f=t?Ia(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&Aa(f),ref:t&&t.ref?n&&o?j(o)?o.concat(hr(t)):[o,hr(t)]:hr(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&$t(e.ssContent),ssFallback:e.ssFallback&&$t(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&gn(c,l.clone(c)),c}function tr(e=" ",t=0){return Oe(Br,null,e,t)}function ct(e="",t=!1){return t?(J(),Ds(ke,null,e)):Oe(ke,null,e)}function gt(e){return e==null||typeof e=="boolean"?Oe(ke):j(e)?Oe(Fe,null,e.slice()):vn(e)?Bt(e):Oe(Br,null,String(e))}function Bt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:$t(e)}function Oo(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(j(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Oo(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!ga(t)?t._ctx=Ie:s===3&&Ie&&(Ie.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Y(t)?(t={default:t,_ctx:Ie},n=32):(t=String(t),r&64?(n=16,t=[tr(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ia(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=_e([t.class,r.class]));else if(s==="style")t.style=Ze([t.style,r.style]);else if(Pr(s)){const o=t[s],i=r[s];i&&o!==i&&!(j(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function nt(e,t,n,r=null){ft(e,t,7,[n,r])}const yf=pa();let bf=0;function wf(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||yf,o={uid:bf++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Al(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:_a(r,s),emitsOptions:Ra(r,s),emit:null,emitted:null,propsDefaults:he,inheritAttrs:r.inheritAttrs,ctx:he,data:he,props:he,attrs:he,slots:he,refs:he,setupState:he,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=uf.bind(null,o),e.ce&&e.ce(o),o}let Ae=null;const $o=()=>Ae||Ie;let Tr,Bs;{const e=Xn(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Tr=t("__VUE_INSTANCE_SETTERS__",n=>Ae=n),Bs=t("__VUE_SSR_SETTERS__",n=>_n=n)}const nr=e=>{const t=Ae;return Tr(e),e.scope.on(),()=>{e.scope.off(),Tr(t)}},si=()=>{Ae&&Ae.scope.off(),Tr(null)};function Oa(e){return e.vnode.shapeFlag&4}let _n=!1;function Cf(e,t=!1,n=!1){t&&Bs(t);const{props:r,children:s}=e.vnode,o=Oa(e);Gu(e,r,o,t),Qu(e,s,n||t);const i=o?Sf(e,t):void 0;return t&&Bs(!1),i}function Sf(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Bu);const{setup:r}=n;if(r){It();const s=e.setupContext=r.length>1?Ef(e):null,o=nr(e),i=Qn(r,e,0,[e.props,s]),a=bl(i);if(Ot(),o(),(a||e.sp)&&!Qt(e)&&So(e),a){if(i.then(si,si),t)return i.then(l=>{oi(e,l,t)}).catch(l=>{Zn(l,e,0)});e.asyncDep=i}else oi(e,i,t)}else $a(e,t)}function oi(e,t,n){Y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:me(t)&&(e.setupState=Gl(t)),$a(e,n)}let ii;function $a(e,t,n){const r=e.type;if(!e.render){if(!t&&ii&&!r.render){const s=r.template||To(e).template;if(s){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:l}=r,f=Te(Te({isCustomElement:o,delimiters:a},i),l);r.render=ii(s,f)}}e.render=r.render||rt}{const s=nr(e);It();try{Hu(e)}finally{Ot(),s()}}}const xf={get(e,t){return Ve(e,"get",""),e[t]}};function Ef(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,xf),slots:e.slots,emit:e.emit,expose:t}}function Hr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Gl(yo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Vn)return Vn[n](e)},has(t,n){return n in t||n in Vn}})):e.proxy}function Hs(e,t=!0){return Y(e)?e.displayName||e.name:e.name||t&&e.__name}function Rf(e){return Y(e)&&"__vccOpts"in e}const le=(e,t)=>mu(e,t,_n);function Xe(e,t,n){const r=arguments.length;return r===2?me(t)&&!j(t)?vn(t)?Oe(e,null,[t]):Oe(e,t):Oe(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&vn(n)&&(n=[n]),Oe(e,t,n))}const Tf="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Us;const li=typeof window<"u"&&window.trustedTypes;if(li)try{Us=li.createPolicy("vue",{createHTML:e=>e})}catch{}const Pa=Us?e=>Us.createHTML(e):e=>e,Af="http://www.w3.org/2000/svg",If="http://www.w3.org/1998/Math/MathML",Et=typeof document<"u"?document:null,ai=Et&&Et.createElement("template"),Of={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?Et.createElementNS(Af,e):t==="mathml"?Et.createElementNS(If,e):n?Et.createElement(e,{is:n}):Et.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>Et.createTextNode(e),createComment:e=>Et.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Et.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{ai.innerHTML=Pa(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=ai.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Lt="transition",In="animation",Wn=Symbol("_vtc"),La={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},$f=Te({},na,La),Pf=e=>(e.displayName="Transition",e.props=$f,e),l1=Pf((e,{slots:t})=>Xe(xu,Lf(e),t)),Gt=(e,t=[])=>{j(e)?e.forEach(n=>n(...t)):e&&e(...t)},ci=e=>e?j(e)?e.some(t=>t.length>1):e.length>1:!1;function Lf(e){const t={};for(const D in e)D in La||(t[D]=e[D]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:f=i,appearToClass:c=a,leaveFromClass:u=`${n}-leave-from`,leaveActiveClass:m=`${n}-leave-active`,leaveToClass:v=`${n}-leave-to`}=e,d=kf(s),g=d&&d[0],C=d&&d[1],{onBeforeEnter:x,onEnter:y,onEnterCancelled:w,onLeave:_,onLeaveCancelled:O,onBeforeAppear:q=x,onAppear:z=y,onAppearCancelled:U=w}=t,L=(D,te,xe,$e)=>{D._enterCancelled=$e,Jt(D,te?c:a),Jt(D,te?f:i),xe&&xe()},K=(D,te)=>{D._isLeaving=!1,Jt(D,u),Jt(D,v),Jt(D,m),te&&te()},ee=D=>(te,xe)=>{const $e=D?z:y,fe=()=>L(te,D,xe);Gt($e,[te,fe]),ui(()=>{Jt(te,D?l:o),St(te,D?c:a),ci($e)||fi(te,r,g,fe)})};return Te(t,{onBeforeEnter(D){Gt(x,[D]),St(D,o),St(D,i)},onBeforeAppear(D){Gt(q,[D]),St(D,l),St(D,f)},onEnter:ee(!1),onAppear:ee(!0),onLeave(D,te){D._isLeaving=!0;const xe=()=>K(D,te);St(D,u),D._enterCancelled?(St(D,m),hi()):(hi(),St(D,m)),ui(()=>{!D._isLeaving||(Jt(D,u),St(D,v),ci(_)||fi(D,r,C,xe))}),Gt(_,[D,xe])},onEnterCancelled(D){L(D,!1,void 0,!0),Gt(w,[D])},onAppearCancelled(D){L(D,!0,void 0,!0),Gt(U,[D])},onLeaveCancelled(D){K(D),Gt(O,[D])}})}function kf(e){if(e==null)return null;if(me(e))return[is(e.enter),is(e.leave)];{const t=is(e);return[t,t]}}function is(e){return kc(e)}function St(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Wn]||(e[Wn]=new Set)).add(t)}function Jt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Wn];n&&(n.delete(t),n.size||(e[Wn]=void 0))}function ui(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Mf=0;function fi(e,t,n,r){const s=e._endId=++Mf,o=()=>{s===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:a,propCount:l}=Nf(e,t);if(!i)return r();const f=i+"end";let c=0;const u=()=>{e.removeEventListener(f,m),o()},m=v=>{v.target===e&&++c>=l&&u()};setTimeout(()=>{c<l&&u()},a+1),e.addEventListener(f,m)}function Nf(e,t){const n=window.getComputedStyle(e),r=d=>(n[d]||"").split(", "),s=r(`${Lt}Delay`),o=r(`${Lt}Duration`),i=di(s,o),a=r(`${In}Delay`),l=r(`${In}Duration`),f=di(a,l);let c=null,u=0,m=0;t===Lt?i>0&&(c=Lt,u=i,m=o.length):t===In?f>0&&(c=In,u=f,m=l.length):(u=Math.max(i,f),c=u>0?i>f?Lt:In:null,m=c?c===Lt?o.length:l.length:0);const v=c===Lt&&/\b(transform|all)(,|$)/.test(r(`${Lt}Property`).toString());return{type:c,timeout:u,propCount:m,hasTransform:v}}function di(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>pi(n)+pi(e[r])))}function pi(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function hi(){return document.body.offsetHeight}function Vf(e,t,n){const r=e[Wn];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ar=Symbol("_vod"),ka=Symbol("_vsh"),Df={beforeMount(e,{value:t},{transition:n}){e[Ar]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):On(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),On(e,!0),r.enter(e)):r.leave(e,()=>{On(e,!1)}):On(e,t))},beforeUnmount(e,{value:t}){On(e,t)}};function On(e,t){e.style.display=t?e[Ar]:"none",e[ka]=!t}const Ma=Symbol("");function a1(e){const t=$o();if(!t)return;const n=t.ut=(s=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(o=>Ir(o,s))},r=()=>{const s=e(t.proxy);t.ce?Ir(t.ce,s):qs(t.subTree,s),n(s)};aa(()=>{Yl(r)}),nn(()=>{vt(r,rt,{flush:"post"});const s=new MutationObserver(r);s.observe(t.subTree.el.parentNode,{childList:!0}),En(()=>s.disconnect())})}function qs(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{qs(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)Ir(e.el,t);else if(e.type===Fe)e.children.forEach(n=>qs(n,t));else if(e.type===pr){let{el:n,anchor:r}=e;for(;n&&(Ir(n,t),n!==r);)n=n.nextSibling}}function Ir(e,t){if(e.nodeType===1){const n=e.style;let r="";for(const s in t)n.setProperty(`--${s}`,t[s]),r+=`--${s}: ${t[s]};`;n[Ma]=r}}const Bf=/(^|;)\s*display\s*:/;function Hf(e,t,n){const r=e.style,s=Ce(n);let o=!1;if(n&&!s){if(t)if(Ce(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&mr(r,a,"")}else for(const i in t)n[i]==null&&mr(r,i,"");for(const i in n)i==="display"&&(o=!0),mr(r,i,n[i])}else if(s){if(t!==n){const i=r[Ma];i&&(n+=";"+i),r.cssText=n,o=Bf.test(n)}}else t&&e.removeAttribute("style");Ar in e&&(e[Ar]=o?r.display:"",e[ka]&&(r.display="none"))}const mi=/\s*!important$/;function mr(e,t,n){if(j(n))n.forEach(r=>mr(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Uf(e,t);mi.test(n)?e.setProperty(zt(r),n.replace(mi,""),"important"):e[r]=n}}const gi=["Webkit","Moz","ms"],ls={};function Uf(e,t){const n=ls[t];if(n)return n;let r=ot(t);if(r!=="filter"&&r in e)return ls[t]=r;r=kr(r);for(let s=0;s<gi.length;s++){const o=gi[s]+r;if(o in e)return ls[t]=o}return t}const vi="http://www.w3.org/1999/xlink";function _i(e,t,n,r,s,o=Hc(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(vi,t.slice(6,t.length)):e.setAttributeNS(vi,t,n):n==null||o&&!El(n)?e.removeAttribute(t):e.setAttribute(t,o?"":ut(n)?String(n):n)}function yi(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Pa(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=El(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function Ur(e,t,n,r){e.addEventListener(t,n,r)}function qf(e,t,n,r){e.removeEventListener(t,n,r)}const bi=Symbol("_vei");function Ff(e,t,n,r,s=null){const o=e[bi]||(e[bi]={}),i=o[t];if(r&&i)i.value=r;else{const[a,l]=jf(t);if(r){const f=o[t]=Wf(r,s);Ur(e,a,f,l)}else i&&(qf(e,a,i,l),o[t]=void 0)}}const wi=/(?:Once|Passive|Capture)$/;function jf(e){let t;if(wi.test(e)){t={};let r;for(;r=e.match(wi);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):zt(e.slice(2)),t]}let as=0;const zf=Promise.resolve(),Kf=()=>as||(zf.then(()=>as=0),as=Date.now());function Wf(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;ft(Gf(r,n.value),t,5,[r])};return n.value=e,n.attached=Kf(),n}function Gf(e,t){if(j(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Ci=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Jf=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?Vf(e,r,i):t==="style"?Hf(e,n,r):Pr(t)?lo(t)||Ff(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Xf(e,t,r,i))?(yi(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&_i(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ce(r))?yi(e,ot(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),_i(e,t,r,i))};function Xf(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ci(t)&&Y(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Ci(t)&&Ce(n)?!1:t in e}const yn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return j(t)?n=>fn(t,n):t},At=Symbol("_assign"),Yf={deep:!0,created(e,t,n){e[At]=yn(n),Ur(e,"change",()=>{const r=e._modelValue,s=bn(e),o=e.checked,i=e[At];if(j(r)){const a=uo(r,s),l=a!==-1;if(o&&!l)i(r.concat(s));else if(!o&&l){const f=[...r];f.splice(a,1),i(f)}}else if(Sn(r)){const a=new Set(r);o?a.add(s):a.delete(s),i(a)}else i(Na(e,o))})},mounted:Si,beforeUpdate(e,t,n){e[At]=yn(n),Si(e,t,n)}};function Si(e,{value:t,oldValue:n},r){e._modelValue=t;let s;if(j(t))s=uo(t,r.props.value)>-1;else if(Sn(t))s=t.has(r.props.value);else{if(t===n)return;s=en(t,Na(e,!0))}e.checked!==s&&(e.checked=s)}const Qf={created(e,{value:t},n){e.checked=en(t,n.props.value),e[At]=yn(n),Ur(e,"change",()=>{e[At](bn(e))})},beforeUpdate(e,{value:t,oldValue:n},r){e[At]=yn(r),t!==n&&(e.checked=en(t,r.props.value))}},c1={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const s=Sn(t);Ur(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?xl(bn(i)):bn(i));e[At](e.multiple?s?new Set(o):o:o[0]),e._assigning=!0,Nr(()=>{e._assigning=!1})}),e[At]=yn(r)},mounted(e,{value:t}){xi(e,t)},beforeUpdate(e,t,n){e[At]=yn(n)},updated(e,{value:t}){e._assigning||xi(e,t)}};function xi(e,t){const n=e.multiple,r=j(t);if(!(n&&!r&&!Sn(t))){for(let s=0,o=e.options.length;s<o;s++){const i=e.options[s],a=bn(i);if(n)if(r){const l=typeof a;l==="string"||l==="number"?i.selected=t.some(f=>String(f)===String(a)):i.selected=uo(t,a)>-1}else i.selected=t.has(a);else if(en(bn(i),t)){e.selectedIndex!==s&&(e.selectedIndex=s);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function bn(e){return"_value"in e?e._value:e.value}function Na(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Zf=["ctrl","shift","alt","meta"],ed={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Zf.some(n=>e[`${n}Key`]&&!t.includes(n))},td=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const a=ed[t[i]];if(a&&a(s,t))return}return e(s,...o)})},nd={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},u1=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=s=>{if(!("key"in s))return;const o=zt(s.key);if(t.some(i=>i===o||nd[i]===o))return e(s)})},rd=Te({patchProp:Jf},Of);let Ei;function sd(){return Ei||(Ei=tf(rd))}const qr=(...e)=>{const t=sd().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=id(r);if(!s)return;const o=t._component;!Y(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,od(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function od(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function id(e){return Ce(e)?document.querySelector(e):e}const be=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},ld=["disabled","type"],ad={key:0,class:"loading"},cd={__name:"Button",props:{type:{type:String,default:"default",validator:e=>["default","primary","success","warning","danger"].includes(e)},size:{type:String,default:"default",validator:e=>["small","default","large"].includes(e)},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},nativeType:{type:String,default:"button",validator:e=>["button","submit","reset"].includes(e)}},emits:["click"],setup(e,{emit:t}){const n=e,r=t,s=le(()=>{const i=["btn"];return n.type!=="default"?i.push(`btn-${n.type}`):i.push("btn-default"),n.size!=="default"&&i.push(`btn-${n.size}`),n.loading&&i.push("btn-loading"),i.join(" ")}),o=i=>{!n.disabled&&!n.loading&&r("click",i)};return(i,a)=>(J(),Z("button",{class:_e(s.value),disabled:e.disabled,type:e.nativeType,onClick:o},[e.loading?(J(),Z("span",ad)):ct("",!0),Se(i.$slots,"default",{},void 0,!0)],10,ld))}},ud=be(cd,[["__scopeId","data-v-f0b3f2fd"]]);const fd={class:"input-wrapper"},dd=["type","value","placeholder","disabled","readonly","maxlength"],pd={__name:"Input",props:{modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},placeholder:{type:String,default:""},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:void 0},size:{type:String,default:"default",validator:e=>["small","default","large"].includes(e)}},emits:["update:modelValue","input","change","focus","blur"],setup(e,{expose:t,emit:n}){const r=e,s=n,o=we(null),i=we(!1),a=le(()=>{const m=["base-input"];return r.size!=="default"&&m.push(`base-input--${r.size}`),i.value&&m.push("base-input--focused"),r.disabled&&m.push("base-input--disabled"),m.join(" ")}),l=m=>{const v=m.target.value;s("update:modelValue",v),s("input",v,m)},f=m=>{s("change",m.target.value,m)},c=m=>{i.value=!0,s("focus",m)},u=m=>{i.value=!1,s("blur",m)};return t({focus:()=>o.value?.focus(),blur:()=>o.value?.blur()}),(m,v)=>(J(),Z("div",fd,[ye("input",{ref_key:"inputRef",ref:o,class:_e(a.value),type:e.type,value:e.modelValue,placeholder:e.placeholder,disabled:e.disabled,readonly:e.readonly,maxlength:e.maxlength,onInput:l,onChange:f,onFocus:c,onBlur:u},null,42,dd)]))}},hd=be(pd,[["__scopeId","data-v-47df032a"]]);const md={__name:"Form",props:{model:{type:Object,default:()=>({})},rules:{type:Object,default:()=>({})},labelPosition:{type:String,default:"right",validator:e=>["left","right","top"].includes(e)},labelWidth:{type:String,default:"100px"},inline:{type:Boolean,default:!1}},emits:["submit","validate"],setup(e,{expose:t,emit:n}){const r=e,s=n,o=we([]),i=le(()=>{const d=["base-form"];return r.inline&&d.push("base-form--inline"),d.push(`base-form--label-${r.labelPosition}`),d.join(" ")}),a=d=>{s("submit",d)},l=d=>{o.value.push(d)},f=d=>{const g=o.value.indexOf(d);g>-1&&o.value.splice(g,1)};return t({validate:d=>new Promise((g,C)=>{let x=!0,y=0;const w=[];if(o.value.length===0){d&&d(!0),g(!0);return}o.value.forEach(_=>{_.validate("",O=>{y++,O&&(x=!1,w.push(O)),y===o.value.length&&(d&&d(x,w),x?g(!0):C(w))})})}),validateField:(d,g)=>{const C=Array.isArray(d)?d:[d],x=o.value.filter(_=>C.includes(_.prop));if(x.length===0){g&&g();return}let y=!0,w=0;x.forEach(_=>{_.validate("",O=>{w++,O&&(y=!1),w===x.length&&g&&g(y)})})},resetFields:()=>{o.value.forEach(d=>{d.resetField()})},clearValidate:d=>{if(d){const g=Array.isArray(d)?d:[d];o.value.forEach(C=>{g.includes(C.prop)&&C.clearValidate()})}else o.value.forEach(g=>{g.clearValidate()})}}),hn("baseForm",{model:r.model,rules:r.rules,labelPosition:r.labelPosition,labelWidth:r.labelWidth,addFormItem:l,removeFormItem:f}),(d,g)=>(J(),Z("form",{class:_e(i.value),onSubmit:td(a,["prevent"])},[Se(d.$slots,"default",{},void 0,!0)],34))}},gd=be(md,[["__scopeId","data-v-39ff5420"]]);const vd={class:"base-form-item__content"},_d={key:0,class:"base-form-item__error"},yd={__name:"FormItem",props:{label:{type:String,default:""},prop:{type:String,default:""},rules:{type:[Object,Array],default:()=>[]},required:{type:Boolean,default:!1},labelWidth:{type:String,default:""}},setup(e,{expose:t}){const n=e,r=De("baseForm",{}),s=we(""),o=we(null),i=le(()=>{const d=["base-form-item"];return s.value&&d.push("base-form-item--error"),(n.required||f.value)&&d.push("base-form-item--required"),d.join(" ")}),a=le(()=>{const d=["base-form-item__label"];return(n.required||f.value)&&d.push("base-form-item__label--required"),d.join(" ")}),l=le(()=>{const d=n.labelWidth||r.labelWidth;return d&&r.labelPosition!=="top"?{width:d,minWidth:d}:{}}),f=le(()=>c().some(g=>g.required)),c=()=>{const d=r.rules?.[n.prop]||[],g=n.rules||[];return[].concat(d,g)},u=(d,g)=>{if(!n.prop||!r.model)return g&&g(),!0;const C=r.model[n.prop],x=c();if(x.length===0)return g&&g(),!0;for(const y of x)if(!(d&&y.trigger&&y.trigger!==d)){if(y.required&&(C==null||C==="")){const w=y.message||`${n.label}\u662F\u5FC5\u586B\u9879`;return s.value=w,g&&g(w),!1}if(C!=null&&C!==""){if(y.min&&String(C).length<y.min){const w=y.message||`${n.label}\u957F\u5EA6\u4E0D\u80FD\u5C11\u4E8E${y.min}\u4E2A\u5B57\u7B26`;return s.value=w,g&&g(w),!1}if(y.max&&String(C).length>y.max){const w=y.message||`${n.label}\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7${y.max}\u4E2A\u5B57\u7B26`;return s.value=w,g&&g(w),!1}if(y.pattern&&!y.pattern.test(String(C))){const w=y.message||`${n.label}\u683C\u5F0F\u4E0D\u6B63\u786E`;return s.value=w,g&&g(w),!1}if(y.validator&&typeof y.validator=="function")try{if(y.validator(y,C,_=>{_?(s.value=_.message||_,g&&g(_.message||_)):(s.value="",g&&g())})===!1){const _=y.message||`${n.label}\u9A8C\u8BC1\u5931\u8D25`;return s.value=_,g&&g(_),!1}}catch(w){const _=y.message||w.message||`${n.label}\u9A8C\u8BC1\u5931\u8D25`;return s.value=_,g&&g(_),!1}}}return s.value="",g&&g(),!0},m=()=>{n.prop&&r.model&&o.value!==void 0&&(r.model[n.prop]=o.value),s.value=""},v=()=>{s.value=""};return n.prop&&r.model&&vt(()=>r.model[n.prop],()=>{s.value&&u("change")}),nn(()=>{n.prop&&r.model&&(o.value=r.model[n.prop]),r.addFormItem&&r.addFormItem({prop:n.prop,validate:u,resetField:m,clearValidate:v})}),En(()=>{r.removeFormItem&&r.removeFormItem({prop:n.prop,validate:u,resetField:m,clearValidate:v})}),t({validate:u,resetField:m,clearValidate:v,prop:n.prop}),(d,g)=>(J(),Z("div",{class:_e(i.value)},[e.label?(J(),Z("label",{key:0,class:_e(a.value),style:Ze(l.value)},yt(e.label),7)):ct("",!0),ye("div",vd,[Se(d.$slots,"default",{},void 0,!0),s.value?(J(),Z("div",_d,yt(s.value),1)):ct("",!0)])],2))}},bd=be(yd,[["__scopeId","data-v-2592ce9c"]]);const wd={class:"container"},Cd={__name:"Container",setup(e){return(t,n)=>(J(),Z("div",wd,[Se(t.$slots,"default",{},void 0,!0)]))}},Sd=be(Cd,[["__scopeId","data-v-264e6643"]]);const xd={__name:"Aside",props:{width:{type:String,default:"220px"},collapsed:{type:Boolean,default:!1},collapsedWidth:{type:String,default:"54px"}},setup(e){const t=e,n=le(()=>{const s=["aside"];return t.collapsed&&s.push("collapsed"),s.join(" ")}),r=le(()=>({width:t.collapsed?t.collapsedWidth:t.width}));return(s,o)=>(J(),Z("aside",{class:_e(n.value),style:Ze(r.value)},[Se(s.$slots,"default",{},void 0,!0)],6))}},Ed=be(xd,[["__scopeId","data-v-56fd2527"]]);const Rd={class:"main"},Td={__name:"Main",setup(e){return(t,n)=>(J(),Z("main",Rd,[Se(t.$slots,"default",{},void 0,!0)]))}},Ad=be(Td,[["__scopeId","data-v-173b46c7"]]);const Id={__name:"Row",props:{gutter:{type:Number,default:0},justify:{type:String,default:"start",validator:e=>["start","end","center","space-around","space-between"].includes(e)},align:{type:String,default:"top",validator:e=>["top","middle","bottom"].includes(e)}},setup(e){const t=e,n=le(()=>{const s=["row"];return t.justify!=="start"&&s.push(`row-justify-${t.justify}`),t.align!=="top"&&s.push(`row-align-${t.align}`),s.join(" ")}),r=le(()=>{const s={};return t.gutter>0&&(s.marginLeft=`-${t.gutter/2}px`,s.marginRight=`-${t.gutter/2}px`),s});return provide("row",{gutter:t.gutter}),(s,o)=>(J(),Z("div",{class:_e(n.value),style:Ze(r.value)},[Se(s.$slots,"default",{},void 0,!0)],6))}},Od=be(Id,[["__scopeId","data-v-63d064ea"]]);const $d={__name:"Col",props:{span:{type:Number,default:24},offset:{type:Number,default:0},push:{type:Number,default:0},pull:{type:Number,default:0},xs:{type:[Number,Object],default:void 0},sm:{type:[Number,Object],default:void 0},md:{type:[Number,Object],default:void 0},lg:{type:[Number,Object],default:void 0},xl:{type:[Number,Object],default:void 0}},setup(e){const t=e,n=De("row",{gutter:0}),r=le(()=>{const o=["col"];return t.span!==24&&o.push(`col-${t.span}`),t.offset>0&&o.push(`col-offset-${t.offset}`),t.push>0&&o.push(`col-push-${t.push}`),t.pull>0&&o.push(`col-pull-${t.pull}`),["xs","sm","md","lg","xl"].forEach(a=>{const l=t[a];l!==void 0&&(typeof l=="number"?o.push(`col-${a}-${l}`):typeof l=="object"&&(l.span!==void 0&&o.push(`col-${a}-${l.span}`),l.offset!==void 0&&o.push(`col-${a}-offset-${l.offset}`),l.push!==void 0&&o.push(`col-${a}-push-${l.push}`),l.pull!==void 0&&o.push(`col-${a}-pull-${l.pull}`)))}),o.join(" ")}),s=le(()=>{const o={};return n.gutter>0&&(o.paddingLeft=`${n.gutter/2}px`,o.paddingRight=`${n.gutter/2}px`),o});return(o,i)=>(J(),Z("div",{class:_e(r.value),style:Ze(s.value)},[Se(o.$slots,"default",{},void 0,!0)],6))}},Pd=be($d,[["__scopeId","data-v-6f4b390d"]]);const Ld={__name:"Divider",props:{direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},contentPosition:{type:String,default:"center",validator:e=>["left","center","right"].includes(e)}},setup(e){const t=e,n=le(()=>{const s=["divider"];return t.direction==="vertical"?s.push("divider-vertical"):s.push("divider-horizontal"),s.join(" ")}),r=le(()=>{const s=["divider-content"];return t.direction==="horizontal"&&s.push(`divider-content-${t.contentPosition}`),s.join(" ")});return(s,o)=>(J(),Z("div",{class:_e(n.value)},[s.$slots.default?(J(),Z("span",{key:0,class:_e(r.value)},[Se(s.$slots,"default",{},void 0,!0)],2)):ct("",!0)],2))}},kd=be(Ld,[["__scopeId","data-v-8fca3f99"]]);const Md=["src","alt"],Nd={key:1,class:"avatar-icon","aria-hidden":"true"},Vd=["xlink:href"],Dd={key:2,class:"avatar-text"},Bd={__name:"Avatar",props:{size:{type:[Number,String],default:40,validator:e=>typeof e=="string"?["small","default","large"].includes(e):typeof e=="number"&&e>0},shape:{type:String,default:"circle",validator:e=>["circle","square"].includes(e)},src:{type:String,default:""},alt:{type:String,default:""},icon:{type:String,default:""},text:{type:String,default:""}},emits:["error"],setup(e,{emit:t}){const n=e,r=t,s=we(!1),o=le(()=>{const l=["avatar"];return typeof n.size=="string"&&l.push(`avatar-${n.size}`),n.shape==="square"&&l.push("avatar-square"),l.join(" ")}),i=le(()=>{const l={};return typeof n.size=="number"&&(l.width=`${n.size}px`,l.height=`${n.size}px`,l.lineHeight=`${n.size}px`,l.fontSize=`${Math.floor(n.size*.35)}px`),l}),a=l=>{s.value=!0,r("error",l)};return(l,f)=>(J(),Z("div",{class:_e(o.value),style:Ze(i.value)},[e.src?(J(),Z("img",{key:0,src:e.src,alt:e.alt,onError:a},null,40,Md)):e.icon?(J(),Z("svg",Nd,[ye("use",{"xlink:href":`#${e.icon}`},null,8,Vd)])):(J(),Z("span",Dd,[Se(l.$slots,"default",{},()=>[tr(yt(e.text),1)],!0)]))],6))}},Hd=be(Bd,[["__scopeId","data-v-b54355b9"]]);const Ud=["onClick"],qd={__name:"Carousel",props:{height:{type:String,default:"300px"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,default:"bottom",validator:e=>["bottom","top","none"].includes(e)},arrow:{type:String,default:"hover",validator:e=>["always","hover","never"].includes(e)}},emits:["change"],setup(e,{expose:t,emit:n}){const r=e,s=n,o=we(0),i=we(0);let a=null;const l=le(()=>({transform:`translateX(-${o.value*100}%)`})),f=le(()=>{const x=["carousel-indicators"];return x.push(`carousel-indicators-${r.indicatorPosition}`),x.join(" ")}),c=x=>{x!==o.value&&(o.value=x,s("change",x))},u=()=>{const x=(o.value+1)%i.value;c(x)},m=()=>{const x=(o.value-1+i.value)%i.value;c(x)},v=()=>{r.autoplay&&i.value>1&&(a=setInterval(u,r.interval))},d=()=>{a&&(clearInterval(a),a=null)};return hn("carousel",{addItem:()=>{i.value++},removeItem:()=>{i.value--}}),nn(()=>{v()}),En(()=>{d()}),t({next:u,prev:m,setCurrentIndex:c}),(x,y)=>(J(),Z("div",{class:"carousel",style:Ze({height:e.height})},[ye("div",{class:"carousel-container",style:Ze(l.value)},[Se(x.$slots,"default",{},void 0,!0)],4),e.indicatorPosition!=="none"?(J(),Z("div",{key:0,class:_e(f.value)},[(J(!0),Z(Fe,null,Vu(i.value,(w,_)=>(J(),Z("button",{key:_,class:_e(["carousel-indicator",{active:_===o.value}]),onClick:O=>c(_)},null,10,Ud))),128))],2)):ct("",!0),e.arrow!=="never"?(J(),Z("button",{key:1,class:"carousel-arrow carousel-arrow-left",onClick:m}," \u2039 ")):ct("",!0),e.arrow!=="never"?(J(),Z("button",{key:2,class:"carousel-arrow carousel-arrow-right",onClick:u}," \u203A ")):ct("",!0)],4))}},Fd=be(qd,[["__scopeId","data-v-b41008b0"]]);const jd={class:"carousel-item"},zd={__name:"CarouselItem",setup(e){const t=De("carousel",null);return nn(()=>{t?.addItem()}),En(()=>{t?.removeItem()}),(n,r)=>(J(),Z("div",jd,[Se(n.$slots,"default",{},void 0,!0)]))}},Kd=be(zd,[["__scopeId","data-v-d653f781"]]);const Wd={name:"BaseCard",props:{shadow:{type:String,default:"always",validator:e=>["always","hover","never"].includes(e)},bodyStyle:{type:Object,default:()=>({})}}},Gd={key:0,class:"base-card__header"};function Jd(e,t,n,r,s,o){return J(),Z("div",{class:_e(["base-card",{"base-card--shadow":n.shadow}])},[e.$slots.header?(J(),Z("div",Gd,[Se(e.$slots,"header",{},void 0,!0)])):ct("",!0),ye("div",{class:"base-card__body",style:Ze(n.bodyStyle)},[Se(e.$slots,"default",{},void 0,!0)],4)],2)}const Xd=be(Wd,[["render",Jd],["__scopeId","data-v-663e3da6"]]);const Yd={name:"BaseTimeline"},Qd={class:"base-timeline"};function Zd(e,t,n,r,s,o){return J(),Z("div",Qd,[Se(e.$slots,"default",{},void 0,!0)])}const ep=be(Yd,[["render",Zd],["__scopeId","data-v-d9f6b8e2"]]);const tp={name:"BaseTimelineItem",props:{timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},placement:{type:String,default:"bottom",validator:e=>["top","bottom"].includes(e)},type:{type:String,default:"",validator:e=>["primary","success","warning","danger","info",""].includes(e)},color:{type:String,default:""},size:{type:String,default:"normal",validator:e=>["normal","large"].includes(e)},icon:{type:String,default:""}},computed:{nodeClass(){const e=[`base-timeline-item__node--${this.size}`];return this.type&&e.push(`base-timeline-item__node--${this.type}`),e},nodeStyle(){const e={};return this.color&&(e.backgroundColor=this.color,e.borderColor=this.color),e},timestampClass(){return[`base-timeline-item__timestamp--${this.placement}`]}}},np={class:"base-timeline-item"},rp={class:"base-timeline-item__wrapper"},sp={class:"base-timeline-item__content"};function op(e,t,n,r,s,o){return J(),Z("div",np,[t[1]||(t[1]=ye("div",{class:"base-timeline-item__tail"},null,-1)),ye("div",{class:_e(["base-timeline-item__node",o.nodeClass]),style:Ze(o.nodeStyle)},[Se(e.$slots,"dot",{},()=>[t[0]||(t[0]=ye("div",{class:"base-timeline-item__node-normal"},null,-1))],!0)],6),ye("div",rp,[n.timestamp?(J(),Z("div",{key:0,class:_e(["base-timeline-item__timestamp",o.timestampClass])},yt(n.timestamp),3)):ct("",!0),ye("div",sp,[Se(e.$slots,"default",{},void 0,!0)])])])}const ip=be(tp,[["render",op],["__scopeId","data-v-deb04d8a"]]);const lp={name:"BaseSelect",props:{modelValue:{type:[String,Number,Boolean],default:""},placeholder:{type:String,default:"\u8BF7\u9009\u62E9"},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],data(){return{visible:!1,selectedLabel:""}},mounted(){this.updateSelectedLabel(),document.addEventListener("click",this.handleDocumentClick)},beforeUnmount(){document.removeEventListener("click",this.handleDocumentClick)},watch:{modelValue(){this.updateSelectedLabel()}},methods:{toggleDropdown(){this.disabled||(this.visible=!this.visible)},handleDocumentClick(e){this.$el.contains(e.target)||(this.visible=!1)},handleOptionClick(e,t){this.$emit("update:modelValue",e),this.$emit("change",e),this.selectedLabel=t,this.visible=!1},updateSelectedLabel(){this.$nextTick(()=>{const e=this.$el?.querySelectorAll(".base-option");e&&e.forEach(t=>{t.__vue__?.value===this.modelValue&&(this.selectedLabel=t.__vue__?.label||t.textContent)})})}},provide(){return{select:this}}},ap={key:0,class:"base-select__selected"},cp={key:1,class:"base-select__placeholder"},up={class:"base-select__dropdown"},fp={class:"base-select__options"};function dp(e,t,n,r,s,o){return J(),Z("div",{class:_e(["base-select",{"is-disabled":n.disabled}])},[ye("div",{class:_e(["base-select__input",{"is-focus":s.visible}]),onClick:t[0]||(t[0]=(...i)=>o.toggleDropdown&&o.toggleDropdown(...i))},[s.selectedLabel?(J(),Z("span",ap,yt(s.selectedLabel),1)):(J(),Z("span",cp,yt(n.placeholder),1)),ye("i",{class:_e(["base-select__arrow",{"is-reverse":s.visible}])},"\u25BC",2)],2),wo(ye("div",up,[ye("div",fp,[Se(e.$slots,"default",{},void 0,!0)])],512),[[Df,s.visible]])],2)}const pp=be(lp,[["render",dp],["__scopeId","data-v-7a185f90"]]);const hp={name:"BaseOption",props:{value:{type:[String,Number,Boolean],required:!0},label:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1}},inject:["select"],computed:{isSelected(){return this.select.modelValue===this.value}},methods:{handleClick(){this.disabled||this.select.handleOptionClick(this.value,this.label||this.$el.textContent)}}};function mp(e,t,n,r,s,o){return J(),Z("div",{class:_e(["base-option",{"is-selected":o.isSelected,"is-disabled":n.disabled}]),onClick:t[0]||(t[0]=(...i)=>o.handleClick&&o.handleClick(...i))},[Se(e.$slots,"default",{},()=>[tr(yt(n.label),1)],!0)],2)}const gp=be(hp,[["render",mp],["__scopeId","data-v-d95e9770"]]);const vp={name:"BaseCheckbox",props:{modelValue:{type:[Boolean,String,Number,Array],default:!1},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],computed:{model:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}},isChecked(){return Array.isArray(this.modelValue)?this.modelValue.includes(this.label):this.modelValue===!0}},methods:{handleChange(e){this.$emit("change",e.target.checked)}}},_p={class:"base-checkbox__input"},yp=["disabled","value"],bp={key:0,class:"base-checkbox__label"};function wp(e,t,n,r,s,o){return J(),Z("label",{class:_e(["base-checkbox",{"is-disabled":n.disabled,"is-checked":o.isChecked}])},[ye("span",_p,[t[2]||(t[2]=ye("span",{class:"base-checkbox__inner"},null,-1)),wo(ye("input",{type:"checkbox",class:"base-checkbox__original",disabled:n.disabled,value:n.label,"onUpdate:modelValue":t[0]||(t[0]=i=>o.model=i),onChange:t[1]||(t[1]=(...i)=>o.handleChange&&o.handleChange(...i))},null,40,yp),[[Yf,o.model]])]),e.$slots.default||n.label?(J(),Z("span",bp,[Se(e.$slots,"default",{},()=>[tr(yt(n.label),1)],!0)])):ct("",!0)],2)}const Cp=be(vp,[["render",wp],["__scopeId","data-v-27e2b100"]]);const Sp={name:"BaseRadio",props:{modelValue:{type:[String,Number,Boolean],default:""},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],computed:{model:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}},isChecked(){return this.modelValue===this.label}},methods:{handleChange(e){this.$emit("change",e.target.value)}}},xp={class:"base-radio__input"},Ep=["disabled","value"],Rp={key:0,class:"base-radio__label"};function Tp(e,t,n,r,s,o){return J(),Z("label",{class:_e(["base-radio",{"is-disabled":n.disabled,"is-checked":o.isChecked}])},[ye("span",xp,[t[2]||(t[2]=ye("span",{class:"base-radio__inner"},null,-1)),wo(ye("input",{type:"radio",class:"base-radio__original",disabled:n.disabled,value:n.label,"onUpdate:modelValue":t[0]||(t[0]=i=>o.model=i),onChange:t[1]||(t[1]=(...i)=>o.handleChange&&o.handleChange(...i))},null,40,Ep),[[Qf,o.model]])]),e.$slots.default||n.label?(J(),Z("span",Rp,[Se(e.$slots,"default",{},()=>[tr(yt(n.label),1)],!0)])):ct("",!0)],2)}const Ap=be(Sp,[["render",Tp],["__scopeId","data-v-c39e0420"]]);const Ip={name:"BaseRadioGroup",props:{modelValue:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)},textColor:{type:String,default:""},fill:{type:String,default:""}},emits:["update:modelValue","change"],watch:{modelValue(e){this.$emit("change",e)}},provide(){return{radioGroup:this}}},Op={class:"base-radio-group",role:"radiogroup"};function $p(e,t,n,r,s,o){return J(),Z("div",Op,[Se(e.$slots,"default",{},void 0,!0)])}const Pp=be(Ip,[["render",$p],["__scopeId","data-v-12a82aff"]]);const Lp={name:"BaseIcon",props:{name:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconClass(){return{[`base-icon--${this.name}`]:this.name}},iconStyle(){return{fontSize:typeof this.size=="number"?`${this.size}px`:this.size,color:this.color}},iconPath(){return{search:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116 65.6-158.4C296 211.3 352.2 188 412 188s116 23.3 158.4 65.6C612.7 296 636 352.2 636 412s-23.3 116-65.6 158.4z",plus:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z",warning:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z",document:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z",loading:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C637 83.6 579.4 72 520 72s-117 11.6-171.3 34.6a440.45 440.45 0 0 0-139.9 94.3 437.71 437.71 0 0 0-94.3 139.9C91.6 395 80 452.6 80 512s11.6 117 34.6 171.3a440.45 440.45 0 0 0 94.3 139.9 437.71 437.71 0 0 0 139.9 94.3C475 940.4 532.6 952 592 952c19.9 0 36 16.1 36 36s-16.1 36-36 36c-59.4 0-117-11.6-171.3-34.6a512.69 512.69 0 0 1-139.9-94.3c-40.8-35.4-73.4-76.3-94.3-139.9C163.6 709 152 651.4 152 592s11.6-117 34.6-171.3a512.69 512.69 0 0 1 94.3-139.9c35.4-40.8 76.3-73.4 139.9-94.3C467 163.6 524.6 152 584 152c19.9 0 36 16.1 36 36s-16.1 36-36 36z",jieru:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z",shezhi:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a449.4 449.4 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 28.9a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM512 701c-104.9 0-190-85.1-190-190s85.1-190 190-190 190 85.1 190 190-85.1 190-190 190z",windows:"M120.83 208.13l344.49-47.04v331.65H120.83V208.13zm0 607.78l344.49 47.04V531.3H120.83v284.61zm384.34 50.02L903.17 896V531.3H505.17v334.63zM505.17 128L903.17 96v435.3H505.17V128z",mac:"M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z",ios:"M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z",android:"M765.7 486.8L620.7 182.2c-3.4-7.2-11.1-11.7-19.5-11.7s-16.1 4.5-19.5 11.7L436.6 486.8a32.05 32.05 0 0 0 28.6 46.4c7.9 0 15.3-2.9 21.1-8.2l23.9-23.9h185.6l23.9 23.9c5.8 5.3 13.2 8.2 21.1 8.2 17.7 0 32-14.3 32-32 0-7.6-2.6-14.8-7.4-20.4zM334.1 548.9L149.9 757.1a32.06 32.06 0 0 0 0 45.3l208.2 208.2c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L195.2 757.1 403.4 548.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0z",xiazai:"M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z",expand:"M342 88H120c-17.7 0-32 14.3-32 32v222c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v174c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32zM342 856h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16H342V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v222c0 17.7 14.3 32 32 32h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0c17.7 0 32-14.3 32-32V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v174H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222z"}[this.name]||""}}},kp={key:0,viewBox:"0 0 1024 1024",width:"1em",height:"1em",fill:"currentColor"},Mp=["d"];function Np(e,t,n,r,s,o){return J(),Z("i",{class:_e(["base-icon",o.iconClass]),style:Ze(o.iconStyle)},[n.name?(J(),Z("svg",kp,[ye("path",{d:o.iconPath},null,8,Mp)])):Se(e.$slots,"default",{key:1},void 0,!0)],6)}const Vp=be(Lp,[["render",Np],["__scopeId","data-v-27fea9a9"]]);const Dp={name:"SvgIcon",props:{iconClass:{type:String,required:!0},className:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconName(){return`#icon-${this.iconClass}`},svgClass(){return this.className?"svg-icon "+this.className:"svg-icon"},svgStyle(){return{fontSize:typeof this.size=="number"?`${this.size}px`:this.size,color:this.color,width:"1em",height:"1em"}}}},Bp=["xlink:href","href"];function Hp(e,t,n,r,s,o){return J(),Z("svg",Ia({class:o.svgClass,style:o.svgStyle,"aria-hidden":"true"},Du(e.$listeners,!0)),[ye("use",{"xlink:href":o.iconName,href:o.iconName},null,8,Bp)],16)}const Up=be(Dp,[["render",Hp],["__scopeId","data-v-dae6fe16"]]),qp={template:`
    <div class="loading-overlay" v-if="visible">
      <div class="loading-content">
        <div class="loading"></div>
        <div v-if="text" class="loading-text">{{ text }}</div>
      </div>
    </div>
  `,data(){return{visible:!1,text:""}},methods:{show(e={}){this.visible=!0,this.text=e.text||""},hide(){this.visible=!1,this.text=""}}};class Fp{constructor(){this.instance=null,this.container=null}service(t={}){if(this.instance&&this.close(),this.container=document.createElement("div"),this.container.className="loading-service-container",t.fullscreen!==!1)document.body.appendChild(this.container);else if(t.target){const r=typeof t.target=="string"?document.querySelector(t.target):t.target;r?(r.appendChild(this.container),r.style.position="relative"):document.body.appendChild(this.container)}else document.body.appendChild(this.container);return this.instance=qr(qp),this.instance.mount(this.container).show(t),{close:()=>this.close()}}close(){this.instance&&(this.instance.unmount(),this.instance=null),this.container&&this.container.parentNode&&(this.container.parentNode.removeChild(this.container),this.container=null)}}const jp=new Fp,Fs={service:e=>jp.service(e)},zp={name:"BaseMessage",props:{message:{type:String,default:""},type:{type:String,default:"info",validator:e=>["success","warning","info","error"].includes(e)},showClose:{type:Boolean,default:!1},duration:{type:Number,default:3e3}},data(){return{visible:!0}},mounted(){this.duration>0&&setTimeout(()=>{this.close()},this.duration)},methods:{close(){this.visible=!1,setTimeout(()=>{this.$el.remove()},300)}},render(){return this.visible?Xe("div",{class:["base-message",`base-message--${this.type}`,{"base-message--closable":this.showClose}],style:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",zIndex:9999,padding:"12px 16px",borderRadius:"4px",color:"#fff",fontSize:"14px",boxShadow:"0 2px 12px 0 rgba(0, 0, 0, 0.1)",transition:"all 0.3s",backgroundColor:this.getBackgroundColor()}},[Xe("span",this.message),this.showClose&&Xe("span",{style:{marginLeft:"8px",cursor:"pointer",fontSize:"16px"},onClick:this.close},"\xD7")]):null},methods:{getBackgroundColor(){const e={success:"#67c23a",warning:"#e6a23c",error:"#f56c6c",info:"#909399"};return e[this.type]||e.info}}},Re=e=>{typeof e=="string"&&(e={message:e});const t=document.createElement("div");document.body.appendChild(t);const n=qr(zp,e);return n.mount(t),{close:()=>{n.unmount(),document.body.removeChild(t)}}};Re.success=e=>Re({message:e,type:"success"});Re.warning=e=>Re({message:e,type:"warning"});Re.error=e=>Re({message:e,type:"error"});Re.info=e=>Re({message:e,type:"info"});const Kp={name:"BaseMessageBox",props:{title:{type:String,default:"\u63D0\u793A"},message:{type:String,default:""},type:{type:String,default:"info",validator:e=>["success","warning","info","error"].includes(e)},showCancelButton:{type:Boolean,default:!1},confirmButtonText:{type:String,default:"\u786E\u5B9A"},cancelButtonText:{type:String,default:"\u53D6\u6D88"}},data(){return{visible:!0}},methods:{handleConfirm(){this.$emit("confirm"),this.close()},handleCancel(){this.$emit("cancel"),this.close()},close(){this.visible=!1,setTimeout(()=>{this.$el.remove()},300)}},render(){return this.visible?Xe("div",{class:"base-message-box-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:9999,display:"flex",alignItems:"center",justifyContent:"center"}},[Xe("div",{class:"base-message-box",style:{backgroundColor:"#fff",borderRadius:"4px",boxShadow:"0 2px 12px 0 rgba(0, 0, 0, 0.1)",minWidth:"300px",maxWidth:"500px",padding:"20px"}},[Xe("div",{style:{fontSize:"16px",fontWeight:"bold",marginBottom:"10px",color:"#303133"}},this.title),Xe("div",{style:{fontSize:"14px",color:"#606266",marginBottom:"20px",lineHeight:"1.5"}},this.message),Xe("div",{style:{textAlign:"right"}},[this.showCancelButton&&Xe("button",{style:{padding:"8px 16px",marginRight:"10px",border:"1px solid #dcdfe6",borderRadius:"4px",backgroundColor:"#fff",color:"#606266",cursor:"pointer"},onClick:this.handleCancel},this.cancelButtonText),Xe("button",{style:{padding:"8px 16px",border:"none",borderRadius:"4px",backgroundColor:"#409eff",color:"#fff",cursor:"pointer"},onClick:this.handleConfirm},this.confirmButtonText)])])]):null}},tn=e=>new Promise((t,n)=>{const r=document.createElement("div");document.body.appendChild(r);const s=qr(Kp,{...e,onConfirm:()=>{s.unmount(),document.body.removeChild(r),t("confirm")},onCancel:()=>{s.unmount(),document.body.removeChild(r),n("cancel")}});s.mount(r)});tn.confirm=(e,t="\u786E\u8BA4",n={})=>tn({message:e,title:t,showCancelButton:!0,...n});tn.alert=(e,t="\u63D0\u793A",n={})=>tn({message:e,title:t,showCancelButton:!1,...n});const Ri={"base-button":ud,"base-input":hd,"base-form":gd,"base-form-item":bd,"base-container":Sd,"base-aside":Ed,"base-main":Ad,"base-row":Od,"base-col":Pd,"base-divider":kd,"base-avatar":Hd,"base-carousel":Fd,"base-carousel-item":Kd,"base-card":Xd,"base-timeline":ep,"base-timeline-item":ip,"base-select":pp,"base-option":gp,"base-checkbox":Cp,"base-radio":Ap,"base-radio-group":Pp,"base-icon":Vp,"svg-icon":Up},Wp={install(e){Object.keys(Ri).forEach(t=>{e.component(t,Ri[t])}),e.config.globalProperties.$loading=Fs,e.config.globalProperties.$message=Re,e.config.globalProperties.$messageBox=tn}},js={appName:"ASec\u5B89\u5168\u5E73\u53F0",appLogo:"/src/assets/ASD.png",introduction:"ASec",showViteLogo:!1},Gp=e=>{e.config.globalProperties.$GIN_VUE_ADMIN=js},Jp={install:e=>{Gp(e)}},Xp="modulepreload",Yp=function(e,t){return new URL(e,t).href},Ti={},G=function(t,n,r){if(!n||n.length===0)return t();const s=document.getElementsByTagName("link");return Promise.all(n.map(o=>{if(o=Yp(o,r),o in Ti)return;Ti[o]=!0;const i=o.endsWith(".css"),a=i?'[rel="stylesheet"]':"";if(!!r)for(let c=s.length-1;c>=0;c--){const u=s[c];if(u.href===o&&(!i||u.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${a}`))return;const f=document.createElement("link");if(f.rel=i?"stylesheet":Xp,i||(f.as="script",f.crossOrigin=""),f.href=o,document.head.appendChild(f),i)return new Promise((c,u)=>{f.addEventListener("load",c),f.addEventListener("error",()=>u(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t())};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const an=typeof document<"u";function Va(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Qp(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Va(e.default)}const ce=Object.assign;function cs(e,t){const n={};for(const r in t){const s=t[r];n[r]=dt(s)?s.map(e):e(s)}return n}const Bn=()=>{},dt=Array.isArray,Da=/#/g,Zp=/&/g,eh=/\//g,th=/=/g,nh=/\?/g,Ba=/\+/g,rh=/%5B/g,sh=/%5D/g,Ha=/%5E/g,oh=/%60/g,Ua=/%7B/g,ih=/%7C/g,qa=/%7D/g,lh=/%20/g;function Po(e){return encodeURI(""+e).replace(ih,"|").replace(rh,"[").replace(sh,"]")}function ah(e){return Po(e).replace(Ua,"{").replace(qa,"}").replace(Ha,"^")}function zs(e){return Po(e).replace(Ba,"%2B").replace(lh,"+").replace(Da,"%23").replace(Zp,"%26").replace(oh,"`").replace(Ua,"{").replace(qa,"}").replace(Ha,"^")}function ch(e){return zs(e).replace(th,"%3D")}function uh(e){return Po(e).replace(Da,"%23").replace(nh,"%3F")}function fh(e){return e==null?"":uh(e).replace(eh,"%2F")}function Gn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const dh=/\/$/,ph=e=>e.replace(dh,"");function us(e,t,n="/"){let r,s={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),s=e(o)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=vh(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:Gn(i)}}function hh(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ai(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function mh(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&wn(t.matched[r],n.matched[s])&&Fa(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function wn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Fa(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!gh(e[n],t[n]))return!1;return!0}function gh(e,t){return dt(e)?Ii(e,t):dt(t)?Ii(t,e):e===t}function Ii(e,t){return dt(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function vh(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const kt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Jn;(function(e){e.pop="pop",e.push="push"})(Jn||(Jn={}));var Hn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Hn||(Hn={}));function _h(e){if(!e)if(an){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ph(e)}const yh=/^[^#]+#/;function bh(e,t){return e.replace(yh,"#")+t}function wh(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Fr=()=>({left:window.scrollX,top:window.scrollY});function Ch(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=wh(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Oi(e,t){return(history.state?history.state.position-t:-1)+e}const Ks=new Map;function Sh(e,t){Ks.set(e,t)}function xh(e){const t=Ks.get(e);return Ks.delete(e),t}let Eh=()=>location.protocol+"//"+location.host;function ja(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let a=s.includes(e.slice(o))?e.slice(o).length:1,l=s.slice(a);return l[0]!=="/"&&(l="/"+l),Ai(l,"")}return Ai(n,e)+r+s}function Rh(e,t,n,r){let s=[],o=[],i=null;const a=({state:m})=>{const v=ja(e,location),d=n.value,g=t.value;let C=0;if(m){if(n.value=v,t.value=m,i&&i===d){i=null;return}C=g?m.position-g.position:0}else r(v);s.forEach(x=>{x(n.value,d,{delta:C,type:Jn.pop,direction:C?C>0?Hn.forward:Hn.back:Hn.unknown})})};function l(){i=n.value}function f(m){s.push(m);const v=()=>{const d=s.indexOf(m);d>-1&&s.splice(d,1)};return o.push(v),v}function c(){const{history:m}=window;!m.state||m.replaceState(ce({},m.state,{scroll:Fr()}),"")}function u(){for(const m of o)m();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:f,destroy:u}}function $i(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Fr():null}}function Th(e){const{history:t,location:n}=window,r={value:ja(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,f,c){const u=e.indexOf("#"),m=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+l:Eh()+e+l;try{t[c?"replaceState":"pushState"](f,"",m),s.value=f}catch(v){console.error(v),n[c?"replace":"assign"](m)}}function i(l,f){const c=ce({},t.state,$i(s.value.back,l,s.value.forward,!0),f,{position:s.value.position});o(l,c,!0),r.value=l}function a(l,f){const c=ce({},s.value,t.state,{forward:l,scroll:Fr()});o(c.current,c,!0);const u=ce({},$i(r.value,l,null),{position:c.position+1},f);o(l,u,!1),r.value=l}return{location:r,state:s,push:a,replace:i}}function Ah(e){e=_h(e);const t=Th(e),n=Rh(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=ce({location:"",base:e,go:r,createHref:bh.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Ih(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Ah(e)}function Oh(e){return typeof e=="string"||e&&typeof e=="object"}function za(e){return typeof e=="string"||typeof e=="symbol"}const Ka=Symbol("");var Pi;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Pi||(Pi={}));function Cn(e,t){return ce(new Error,{type:e,[Ka]:!0},t)}function xt(e,t){return e instanceof Error&&Ka in e&&(t==null||!!(e.type&t))}const Li="[^/]+?",$h={sensitive:!1,strict:!1,start:!0,end:!0},Ph=/[.+*?^${}()[\]/\\]/g;function Lh(e,t){const n=ce({},$h,t),r=[];let s=n.start?"^":"";const o=[];for(const f of e){const c=f.length?[]:[90];n.strict&&!f.length&&(s+="/");for(let u=0;u<f.length;u++){const m=f[u];let v=40+(n.sensitive?.25:0);if(m.type===0)u||(s+="/"),s+=m.value.replace(Ph,"\\$&"),v+=40;else if(m.type===1){const{value:d,repeatable:g,optional:C,regexp:x}=m;o.push({name:d,repeatable:g,optional:C});const y=x||Li;if(y!==Li){v+=10;try{new RegExp(`(${y})`)}catch(_){throw new Error(`Invalid custom RegExp for param "${d}" (${y}): `+_.message)}}let w=g?`((?:${y})(?:/(?:${y}))*)`:`(${y})`;u||(w=C&&f.length<2?`(?:/${w})`:"/"+w),C&&(w+="?"),s+=w,v+=20,C&&(v+=-8),g&&(v+=-20),y===".*"&&(v+=-50)}c.push(v)}r.push(c)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function a(f){const c=f.match(i),u={};if(!c)return null;for(let m=1;m<c.length;m++){const v=c[m]||"",d=o[m-1];u[d.name]=v&&d.repeatable?v.split("/"):v}return u}function l(f){let c="",u=!1;for(const m of e){(!u||!c.endsWith("/"))&&(c+="/"),u=!1;for(const v of m)if(v.type===0)c+=v.value;else if(v.type===1){const{value:d,repeatable:g,optional:C}=v,x=d in f?f[d]:"";if(dt(x)&&!g)throw new Error(`Provided param "${d}" is an array but it is not repeatable (* or + modifiers)`);const y=dt(x)?x.join("/"):x;if(!y)if(C)m.length<2&&(c.endsWith("/")?c=c.slice(0,-1):u=!0);else throw new Error(`Missing required param "${d}"`);c+=y}}return c||"/"}return{re:i,score:r,keys:o,parse:a,stringify:l}}function kh(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Wa(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=kh(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(ki(r))return 1;if(ki(s))return-1}return s.length-r.length}function ki(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Mh={type:0,value:""},Nh=/[a-zA-Z0-9_]/;function Vh(e){if(!e)return[[]];if(e==="/")return[[Mh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${n})/"${f}": ${v}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let a=0,l,f="",c="";function u(){!f||(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),f="")}function m(){f+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(f&&u(),i()):l===":"?(u(),n=1):m();break;case 4:m(),n=r;break;case 1:l==="("?n=2:Nh.test(l)?m():(u(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:u(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),u(),i(),s}function Dh(e,t,n){const r=Lh(Vh(e.path),n),s=ce(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function Bh(e,t){const n=[],r=new Map;t=Di({strict:!1,end:!0,sensitive:!1},t);function s(u){return r.get(u)}function o(u,m,v){const d=!v,g=Ni(u);g.aliasOf=v&&v.record;const C=Di(t,u),x=[g];if("alias"in u){const _=typeof u.alias=="string"?[u.alias]:u.alias;for(const O of _)x.push(Ni(ce({},g,{components:v?v.record.components:g.components,path:O,aliasOf:v?v.record:g})))}let y,w;for(const _ of x){const{path:O}=_;if(m&&O[0]!=="/"){const q=m.record.path,z=q[q.length-1]==="/"?"":"/";_.path=m.record.path+(O&&z+O)}if(y=Dh(_,m,C),v?v.alias.push(y):(w=w||y,w!==y&&w.alias.push(y),d&&u.name&&!Vi(y)&&i(u.name)),Ga(y)&&l(y),g.children){const q=g.children;for(let z=0;z<q.length;z++)o(q[z],y,v&&v.children[z])}v=v||y}return w?()=>{i(w)}:Bn}function i(u){if(za(u)){const m=r.get(u);m&&(r.delete(u),n.splice(n.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=n.indexOf(u);m>-1&&(n.splice(m,1),u.record.name&&r.delete(u.record.name),u.children.forEach(i),u.alias.forEach(i))}}function a(){return n}function l(u){const m=qh(u,n);n.splice(m,0,u),u.record.name&&!Vi(u)&&r.set(u.record.name,u)}function f(u,m){let v,d={},g,C;if("name"in u&&u.name){if(v=r.get(u.name),!v)throw Cn(1,{location:u});C=v.record.name,d=ce(Mi(m.params,v.keys.filter(w=>!w.optional).concat(v.parent?v.parent.keys.filter(w=>w.optional):[]).map(w=>w.name)),u.params&&Mi(u.params,v.keys.map(w=>w.name))),g=v.stringify(d)}else if(u.path!=null)g=u.path,v=n.find(w=>w.re.test(g)),v&&(d=v.parse(g),C=v.record.name);else{if(v=m.name?r.get(m.name):n.find(w=>w.re.test(m.path)),!v)throw Cn(1,{location:u,currentLocation:m});C=v.record.name,d=ce({},m.params,u.params),g=v.stringify(d)}const x=[];let y=v;for(;y;)x.unshift(y.record),y=y.parent;return{name:C,path:g,params:d,matched:x,meta:Uh(x)}}e.forEach(u=>o(u));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:s}}function Mi(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Ni(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Hh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Hh(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Vi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Uh(e){return e.reduce((t,n)=>ce(t,n.meta),{})}function Di(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function qh(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Wa(e,t[o])<0?r=o:n=o+1}const s=Fh(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function Fh(e){let t=e;for(;t=t.parent;)if(Ga(t)&&Wa(e,t)===0)return t}function Ga({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function jh(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(Ba," "),i=o.indexOf("="),a=Gn(i<0?o:o.slice(0,i)),l=i<0?null:Gn(o.slice(i+1));if(a in t){let f=t[a];dt(f)||(f=t[a]=[f]),f.push(l)}else t[a]=l}return t}function Bi(e){let t="";for(let n in e){const r=e[n];if(n=ch(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(dt(r)?r.map(o=>o&&zs(o)):[r&&zs(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function zh(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=dt(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Kh=Symbol(""),Hi=Symbol(""),jr=Symbol(""),Lo=Symbol(""),Ws=Symbol("");function $n(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ht(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((a,l)=>{const f=m=>{m===!1?l(Cn(4,{from:n,to:t})):m instanceof Error?l(m):Oh(m)?l(Cn(2,{from:t,to:m})):(i&&r.enterCallbacks[s]===i&&typeof m=="function"&&i.push(m),a())},c=o(()=>e.call(r&&r.instances[s],t,n,f));let u=Promise.resolve(c);e.length<3&&(u=u.then(f)),u.catch(m=>l(m))})}function fs(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Va(l)){const c=(l.__vccOpts||l)[t];c&&o.push(Ht(c,n,r,i,a,s))}else{let f=l();o.push(()=>f.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const u=Qp(c)?c.default:c;i.mods[a]=c,i.components[a]=u;const v=(u.__vccOpts||u)[t];return v&&Ht(v,n,r,i,a,s)()}))}}return o}function Ui(e){const t=De(jr),n=De(Lo),r=le(()=>{const l=dn(e.to);return t.resolve(l)}),s=le(()=>{const{matched:l}=r.value,{length:f}=l,c=l[f-1],u=n.matched;if(!c||!u.length)return-1;const m=u.findIndex(wn.bind(null,c));if(m>-1)return m;const v=qi(l[f-2]);return f>1&&qi(c)===v&&u[u.length-1].path!==v?u.findIndex(wn.bind(null,l[f-2])):m}),o=le(()=>s.value>-1&&Yh(n.params,r.value.params)),i=le(()=>s.value>-1&&s.value===n.matched.length-1&&Fa(n.params,r.value.params));function a(l={}){if(Xh(l)){const f=t[dn(e.replace)?"replace":"push"](dn(e.to)).catch(Bn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:r,href:le(()=>r.value.href),isActive:o,isExactActive:i,navigate:a}}function Wh(e){return e.length===1?e[0]:e}const Gh=Co({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ui,setup(e,{slots:t}){const n=Yn(Ui(e)),{options:r}=De(jr),s=le(()=>({[Fi(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Fi(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Wh(t.default(n));return e.custom?o:Xe("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),Jh=Gh;function Xh(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Yh(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!dt(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function qi(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Fi=(e,t,n)=>e??t??n,Qh=Co({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=De(Ws),s=le(()=>e.route||r.value),o=De(Hi,0),i=le(()=>{let f=dn(o);const{matched:c}=s.value;let u;for(;(u=c[f])&&!u.components;)f++;return f}),a=le(()=>s.value.matched[i.value]);hn(Hi,le(()=>i.value+1)),hn(Kh,a),hn(Ws,s);const l=we();return vt(()=>[l.value,a.value,e.name],([f,c,u],[m,v,d])=>{c&&(c.instances[u]=f,v&&v!==c&&f&&f===m&&(c.leaveGuards.size||(c.leaveGuards=v.leaveGuards),c.updateGuards.size||(c.updateGuards=v.updateGuards))),f&&c&&(!v||!wn(c,v)||!m)&&(c.enterCallbacks[u]||[]).forEach(g=>g(f))},{flush:"post"}),()=>{const f=s.value,c=e.name,u=a.value,m=u&&u.components[c];if(!m)return ji(n.default,{Component:m,route:f});const v=u.props[c],d=v?v===!0?f.params:typeof v=="function"?v(f):v:null,C=Xe(m,ce({},d,t,{onVnodeUnmounted:x=>{x.component.isUnmounted&&(u.instances[c]=null)},ref:l}));return ji(n.default,{Component:C,route:f})||C}}});function ji(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Zh=Qh;function em(e){const t=Bh(e.routes,e),n=e.parseQuery||jh,r=e.stringifyQuery||Bi,s=e.history,o=$n(),i=$n(),a=$n(),l=au(kt);let f=kt;an&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=cs.bind(null,E=>""+E),u=cs.bind(null,fh),m=cs.bind(null,Gn);function v(E,B){let I,H;return za(E)?(I=t.getRecordMatcher(E),H=B):H=E,t.addRoute(H,I)}function d(E){const B=t.getRecordMatcher(E);B&&t.removeRoute(B)}function g(){return t.getRoutes().map(E=>E.record)}function C(E){return!!t.getRecordMatcher(E)}function x(E,B){if(B=ce({},B||l.value),typeof E=="string"){const h=us(n,E,B.path),b=t.resolve({path:h.path},B),R=s.createHref(h.fullPath);return ce(h,b,{params:m(b.params),hash:Gn(h.hash),redirectedFrom:void 0,href:R})}let I;if(E.path!=null)I=ce({},E,{path:us(n,E.path,B.path).path});else{const h=ce({},E.params);for(const b in h)h[b]==null&&delete h[b];I=ce({},E,{params:u(h)}),B.params=u(B.params)}const H=t.resolve(I,B),ne=E.hash||"";H.params=c(m(H.params));const ae=hh(r,ce({},E,{hash:ah(ne),path:H.path})),p=s.createHref(ae);return ce({fullPath:ae,hash:ne,query:r===Bi?zh(E.query):E.query||{}},H,{redirectedFrom:void 0,href:p})}function y(E){return typeof E=="string"?us(n,E,l.value.path):ce({},E)}function w(E,B){if(f!==E)return Cn(8,{from:B,to:E})}function _(E){return z(E)}function O(E){return _(ce(y(E),{replace:!0}))}function q(E){const B=E.matched[E.matched.length-1];if(B&&B.redirect){const{redirect:I}=B;let H=typeof I=="function"?I(E):I;return typeof H=="string"&&(H=H.includes("?")||H.includes("#")?H=y(H):{path:H},H.params={}),ce({query:E.query,hash:E.hash,params:H.path!=null?{}:E.params},H)}}function z(E,B){const I=f=x(E),H=l.value,ne=E.state,ae=E.force,p=E.replace===!0,h=q(I);if(h)return z(ce(y(h),{state:typeof h=="object"?ce({},ne,h.state):ne,force:ae,replace:p}),B||I);const b=I;b.redirectedFrom=B;let R;return!ae&&mh(r,H,I)&&(R=Cn(16,{to:b,from:H}),k(H,H,!0,!1)),(R?Promise.resolve(R):K(b,H)).catch(S=>xt(S)?xt(S,2)?S:pt(S):oe(S,b,H)).then(S=>{if(S){if(xt(S,2))return z(ce({replace:p},y(S.to),{state:typeof S.to=="object"?ce({},ne,S.to.state):ne,force:ae}),B||b)}else S=D(b,H,!0,p,ne);return ee(b,H,S),S})}function U(E,B){const I=w(E,B);return I?Promise.reject(I):Promise.resolve()}function L(E){const B=ie.values().next().value;return B&&typeof B.runWithContext=="function"?B.runWithContext(E):E()}function K(E,B){let I;const[H,ne,ae]=tm(E,B);I=fs(H.reverse(),"beforeRouteLeave",E,B);for(const h of H)h.leaveGuards.forEach(b=>{I.push(Ht(b,E,B))});const p=U.bind(null,E,B);return I.push(p),He(I).then(()=>{I=[];for(const h of o.list())I.push(Ht(h,E,B));return I.push(p),He(I)}).then(()=>{I=fs(ne,"beforeRouteUpdate",E,B);for(const h of ne)h.updateGuards.forEach(b=>{I.push(Ht(b,E,B))});return I.push(p),He(I)}).then(()=>{I=[];for(const h of ae)if(h.beforeEnter)if(dt(h.beforeEnter))for(const b of h.beforeEnter)I.push(Ht(b,E,B));else I.push(Ht(h.beforeEnter,E,B));return I.push(p),He(I)}).then(()=>(E.matched.forEach(h=>h.enterCallbacks={}),I=fs(ae,"beforeRouteEnter",E,B,L),I.push(p),He(I))).then(()=>{I=[];for(const h of i.list())I.push(Ht(h,E,B));return I.push(p),He(I)}).catch(h=>xt(h,8)?h:Promise.reject(h))}function ee(E,B,I){a.list().forEach(H=>L(()=>H(E,B,I)))}function D(E,B,I,H,ne){const ae=w(E,B);if(ae)return ae;const p=B===kt,h=an?history.state:{};I&&(H||p?s.replace(E.fullPath,ce({scroll:p&&h&&h.scroll},ne)):s.push(E.fullPath,ne)),l.value=E,k(E,B,I,p),pt()}let te;function xe(){te||(te=s.listen((E,B,I)=>{if(!Be.listening)return;const H=x(E),ne=q(H);if(ne){z(ce(ne,{replace:!0,force:!0}),H).catch(Bn);return}f=H;const ae=l.value;an&&Sh(Oi(ae.fullPath,I.delta),Fr()),K(H,ae).catch(p=>xt(p,12)?p:xt(p,2)?(z(ce(y(p.to),{force:!0}),H).then(h=>{xt(h,20)&&!I.delta&&I.type===Jn.pop&&s.go(-1,!1)}).catch(Bn),Promise.reject()):(I.delta&&s.go(-I.delta,!1),oe(p,H,ae))).then(p=>{p=p||D(H,ae,!1),p&&(I.delta&&!xt(p,8)?s.go(-I.delta,!1):I.type===Jn.pop&&xt(p,20)&&s.go(-1,!1)),ee(H,ae,p)}).catch(Bn)}))}let $e=$n(),fe=$n(),Q;function oe(E,B,I){pt(E);const H=fe.list();return H.length?H.forEach(ne=>ne(E,B,I)):console.error(E),Promise.reject(E)}function it(){return Q&&l.value!==kt?Promise.resolve():new Promise((E,B)=>{$e.add([E,B])})}function pt(E){return Q||(Q=!E,xe(),$e.list().forEach(([B,I])=>E?I(E):B()),$e.reset()),E}function k(E,B,I,H){const{scrollBehavior:ne}=e;if(!an||!ne)return Promise.resolve();const ae=!I&&xh(Oi(E.fullPath,0))||(H||!I)&&history.state&&history.state.scroll||null;return Nr().then(()=>ne(E,B,ae)).then(p=>p&&Ch(p)).catch(p=>oe(p,E,B))}const V=E=>s.go(E);let ze;const ie=new Set,Be={currentRoute:l,listening:!0,addRoute:v,removeRoute:d,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:g,resolve:x,options:e,push:_,replace:O,go:V,back:()=>V(-1),forward:()=>V(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:fe.add,isReady:it,install(E){const B=this;E.component("RouterLink",Jh),E.component("RouterView",Zh),E.config.globalProperties.$router=B,Object.defineProperty(E.config.globalProperties,"$route",{enumerable:!0,get:()=>dn(l)}),an&&!ze&&l.value===kt&&(ze=!0,_(s.location).catch(ne=>{}));const I={};for(const ne in kt)Object.defineProperty(I,ne,{get:()=>l.value[ne],enumerable:!0});E.provide(jr,B),E.provide(Lo,zl(I)),E.provide(Ws,l);const H=E.unmount;ie.add(E),E.unmount=function(){ie.delete(E),ie.size<1&&(f=kt,te&&te(),te=null,l.value=kt,ze=!1,Q=!1),H()}}};function He(E){return E.reduce((B,I)=>B.then(()=>L(I)),Promise.resolve())}return Be}function tm(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(f=>wn(f,a))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(f=>wn(f,l))||s.push(l))}return[n,r,s]}function f1(){return De(jr)}function d1(e){return De(Lo)}const nm=[{path:"/",redirect:"/login"},{path:"/status",name:"Status",component:()=>G(()=>import("./status.a7da319c.js"),["./status.a7da319c.js","./secondaryAuth.75febeaf.js","./verifyCode.5ac023d8.js","./verifyCode.978f9466.css","./secondaryAuth.21aaca5f.css","./status.e8a70bba.css"],import.meta.url)},{path:"/verify",name:"verify",component:()=>G(()=>import("./verify.0a32ff20.js"),[],import.meta.url)},{path:"/appverify",name:"appverify",component:()=>G(()=>import("./appverify.a30e8e3f.js"),["./appverify.a30e8e3f.js","./appverify.1430be1b.css"],import.meta.url)},{path:"/login",name:"Login",component:()=>G(()=>import("./index.8981ad9e.js"),["./index.8981ad9e.js","./index.9f1ca410.css"],import.meta.url)},{path:"/client",name:"Client",component:()=>G(()=>import("./index.a0b7d2e3.js"),["./index.a0b7d2e3.js","./header.055d9003.js","./ASD.492c8837.js","./header.d5f050d9.css","./menu.188806bb.js","./menu.64359975.css","./index.6b45d132.css"],import.meta.url),children:[{path:"/client/login",name:"ClientNewLogin",component:()=>G(()=>import("./login.10ee0941.js"),["./login.10ee0941.js","./index.8981ad9e.js","./index.9f1ca410.css"],import.meta.url)},{path:"/client/main",name:"ClientMain",component:()=>G(()=>import("./main.15b19a5e.js"),["./main.15b19a5e.js","./index.aa6174f6.js","./index.743849cd.css","./main.48e9044e.css"],import.meta.url)},{path:"/client/setting",name:"ClientSetting",component:()=>G(()=>import("./setting.10afe9c5.js"),["./setting.10afe9c5.js","./setting.1e43613a.css"],import.meta.url)}]},{path:"/clientLogin",name:"ClientLogin",component:()=>G(()=>import("./clientLogin.f0f0c188.js"),[],import.meta.url)},{path:"/downloadWin",name:"downloadWin",component:()=>G(()=>import("./downloadWin.a30787fd.js"),["./downloadWin.a30787fd.js","./ASD.492c8837.js","./browser.ff6109de.js","./downloadWin.128fe51a.css"],import.meta.url)},{path:"/wx_oauth_callback",name:"WxOAuthCallback",component:()=>G(()=>import("./wx_oauth_callback.65954852.js"),[],import.meta.url)},{path:"/oauth2_result",name:"OAuth2Result",component:()=>G(()=>import("./oauth2_result.fa32ec2d.js"),["./oauth2_result.fa32ec2d.js","./secondaryAuth.75febeaf.js","./verifyCode.5ac023d8.js","./verifyCode.978f9466.css","./secondaryAuth.21aaca5f.css","./oauth2_result.c4afb049.css"],import.meta.url)},{path:"/oauth2_premises",name:"OAuth2Premises",component:()=>G(()=>import("./oauth2_premises.ef184f57.js"),["./oauth2_premises.ef184f57.js","./oauth2_premises.987b2776.css"],import.meta.url)}],Ye=em({history:Ih(),routes:nm});Ye.beforeEach(async(e,t,n)=>{const r=window.location.href,s=window.location.origin;if(logger.log("Router beforeEach Current URL:",r,"origin:",s),!r.startsWith(s+"/#/")){console.log("Hash is not at the correct position");const o=r.indexOf("#");let i;if(o===-1)i=`${s}/#${r.substring(s.length)}`;else{let a=r.substring(s.length,o);const l=r.substring(o);a=a.replace(/^\/\?/,"&"),console.log("beforeHash:",a),console.log("afterHash:",l),i=`${s}/${l}${a}`}console.log("Final new URL:",i),window.location.replace(i);return}logger.log("Proceeding with normal navigation"),n()});var Gs=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function rm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ja={exports:{}},ko={exports:{}},Xa=function(t,n){return function(){for(var s=new Array(arguments.length),o=0;o<s.length;o++)s[o]=arguments[o];return t.apply(n,s)}},sm=Xa,Rn=Object.prototype.toString;function Ya(e){return Rn.call(e)==="[object Array]"}function Js(e){return typeof e>"u"}function om(e){return e!==null&&!Js(e)&&e.constructor!==null&&!Js(e.constructor)&&typeof e.constructor.isBuffer=="function"&&e.constructor.isBuffer(e)}function im(e){return Rn.call(e)==="[object ArrayBuffer]"}function lm(e){return typeof FormData<"u"&&e instanceof FormData}function am(e){var t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&e.buffer instanceof ArrayBuffer,t}function cm(e){return typeof e=="string"}function um(e){return typeof e=="number"}function Qa(e){return e!==null&&typeof e=="object"}function fm(e){return Rn.call(e)==="[object Date]"}function dm(e){return Rn.call(e)==="[object File]"}function pm(e){return Rn.call(e)==="[object Blob]"}function Za(e){return Rn.call(e)==="[object Function]"}function hm(e){return Qa(e)&&Za(e.pipe)}function mm(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}function gm(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}function vm(){return typeof navigator<"u"&&(navigator.product==="ReactNative"||navigator.product==="NativeScript"||navigator.product==="NS")?!1:typeof window<"u"&&typeof document<"u"}function zr(e,t){if(!(e===null||typeof e>"u"))if(typeof e!="object"&&(e=[e]),Ya(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.call(null,e[s],s,e)}function ec(){var e={};function t(s,o){typeof e[o]=="object"&&typeof s=="object"?e[o]=ec(e[o],s):e[o]=s}for(var n=0,r=arguments.length;n<r;n++)zr(arguments[n],t);return e}function Xs(){var e={};function t(s,o){typeof e[o]=="object"&&typeof s=="object"?e[o]=Xs(e[o],s):typeof s=="object"?e[o]=Xs({},s):e[o]=s}for(var n=0,r=arguments.length;n<r;n++)zr(arguments[n],t);return e}function _m(e,t,n){return zr(t,function(s,o){n&&typeof s=="function"?e[o]=sm(s,n):e[o]=s}),e}var et={isArray:Ya,isArrayBuffer:im,isBuffer:om,isFormData:lm,isArrayBufferView:am,isString:cm,isNumber:um,isObject:Qa,isUndefined:Js,isDate:fm,isFile:dm,isBlob:pm,isFunction:Za,isStream:hm,isURLSearchParams:mm,isStandardBrowserEnv:vm,forEach:zr,merge:ec,deepMerge:Xs,extend:_m,trim:gm},sn=et;function zi(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var tc=function(t,n,r){if(!n)return t;var s;if(r)s=r(n);else if(sn.isURLSearchParams(n))s=n.toString();else{var o=[];sn.forEach(n,function(l,f){l===null||typeof l>"u"||(sn.isArray(l)?f=f+"[]":l=[l],sn.forEach(l,function(u){sn.isDate(u)?u=u.toISOString():sn.isObject(u)&&(u=JSON.stringify(u)),o.push(zi(f)+"="+zi(u))}))}),s=o.join("&")}if(s){var i=t.indexOf("#");i!==-1&&(t=t.slice(0,i)),t+=(t.indexOf("?")===-1?"?":"&")+s}return t},ym=et;function Kr(){this.handlers=[]}Kr.prototype.use=function(t,n){return this.handlers.push({fulfilled:t,rejected:n}),this.handlers.length-1};Kr.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)};Kr.prototype.forEach=function(t){ym.forEach(this.handlers,function(r){r!==null&&t(r)})};var bm=Kr,wm=et,Cm=function(t,n,r){return wm.forEach(r,function(o){t=o(t,n)}),t},ds,Ki;function nc(){return Ki||(Ki=1,ds=function(t){return!!(t&&t.__CANCEL__)}),ds}var Sm=et,xm=function(t,n){Sm.forEach(t,function(s,o){o!==n&&o.toUpperCase()===n.toUpperCase()&&(t[n]=s,delete t[o])})},ps,Wi;function Em(){return Wi||(Wi=1,ps=function(t,n,r,s,o){return t.config=n,r&&(t.code=r),t.request=s,t.response=o,t.isAxiosError=!0,t.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},t}),ps}var hs,Gi;function rc(){if(Gi)return hs;Gi=1;var e=Em();return hs=function(n,r,s,o,i){var a=new Error(n);return e(a,r,s,o,i)},hs}var ms,Ji;function Rm(){if(Ji)return ms;Ji=1;var e=rc();return ms=function(n,r,s){var o=s.config.validateStatus;!o||o(s.status)?n(s):r(e("Request failed with status code "+s.status,s.config,null,s.request,s))},ms}var gs,Xi;function Tm(){return Xi||(Xi=1,gs=function(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)}),gs}var vs,Yi;function Am(){return Yi||(Yi=1,vs=function(t,n){return n?t.replace(/\/+$/,"")+"/"+n.replace(/^\/+/,""):t}),vs}var _s,Qi;function Im(){if(Qi)return _s;Qi=1;var e=Tm(),t=Am();return _s=function(r,s){return r&&!e(s)?t(r,s):s},_s}var ys,Zi;function Om(){if(Zi)return ys;Zi=1;var e=et,t=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];return ys=function(r){var s={},o,i,a;return r&&e.forEach(r.split(`
`),function(f){if(a=f.indexOf(":"),o=e.trim(f.substr(0,a)).toLowerCase(),i=e.trim(f.substr(a+1)),o){if(s[o]&&t.indexOf(o)>=0)return;o==="set-cookie"?s[o]=(s[o]?s[o]:[]).concat([i]):s[o]=s[o]?s[o]+", "+i:i}}),s},ys}var bs,el;function $m(){if(el)return bs;el=1;var e=et;return bs=e.isStandardBrowserEnv()?function(){var n=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a"),s;function o(i){var a=i;return n&&(r.setAttribute("href",a),a=r.href),r.setAttribute("href",a),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:r.pathname.charAt(0)==="/"?r.pathname:"/"+r.pathname}}return s=o(window.location.href),function(a){var l=e.isString(a)?o(a):a;return l.protocol===s.protocol&&l.host===s.host}}():function(){return function(){return!0}}(),bs}var ws,tl;function Pm(){if(tl)return ws;tl=1;var e=et;return ws=e.isStandardBrowserEnv()?function(){return{write:function(r,s,o,i,a,l){var f=[];f.push(r+"="+encodeURIComponent(s)),e.isNumber(o)&&f.push("expires="+new Date(o).toGMTString()),e.isString(i)&&f.push("path="+i),e.isString(a)&&f.push("domain="+a),l===!0&&f.push("secure"),document.cookie=f.join("; ")},read:function(r){var s=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return s?decodeURIComponent(s[3]):null},remove:function(r){this.write(r,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}(),ws}var Cs,nl;function rl(){if(nl)return Cs;nl=1;var e=et,t=Rm(),n=tc,r=Im(),s=Om(),o=$m(),i=rc();return Cs=function(l){return new Promise(function(c,u){var m=l.data,v=l.headers;e.isFormData(m)&&delete v["Content-Type"];var d=new XMLHttpRequest;if(l.auth){var g=l.auth.username||"",C=l.auth.password||"";v.Authorization="Basic "+btoa(g+":"+C)}var x=r(l.baseURL,l.url);if(d.open(l.method.toUpperCase(),n(x,l.params,l.paramsSerializer),!0),d.timeout=l.timeout,d.onreadystatechange=function(){if(!(!d||d.readyState!==4)&&!(d.status===0&&!(d.responseURL&&d.responseURL.indexOf("file:")===0))){var O="getAllResponseHeaders"in d?s(d.getAllResponseHeaders()):null,q=!l.responseType||l.responseType==="text"?d.responseText:d.response,z={data:q,status:d.status,statusText:d.statusText,headers:O,config:l,request:d};t(c,u,z),d=null}},d.onabort=function(){!d||(u(i("Request aborted",l,"ECONNABORTED",d)),d=null)},d.onerror=function(){u(i("Network Error",l,null,d)),d=null},d.ontimeout=function(){var O="timeout of "+l.timeout+"ms exceeded";l.timeoutErrorMessage&&(O=l.timeoutErrorMessage),u(i(O,l,"ECONNABORTED",d)),d=null},e.isStandardBrowserEnv()){var y=Pm(),w=(l.withCredentials||o(x))&&l.xsrfCookieName?y.read(l.xsrfCookieName):void 0;w&&(v[l.xsrfHeaderName]=w)}if("setRequestHeader"in d&&e.forEach(v,function(O,q){typeof m>"u"&&q.toLowerCase()==="content-type"?delete v[q]:d.setRequestHeader(q,O)}),e.isUndefined(l.withCredentials)||(d.withCredentials=!!l.withCredentials),l.responseType)try{d.responseType=l.responseType}catch(_){if(l.responseType!=="json")throw _}typeof l.onDownloadProgress=="function"&&d.addEventListener("progress",l.onDownloadProgress),typeof l.onUploadProgress=="function"&&d.upload&&d.upload.addEventListener("progress",l.onUploadProgress),l.cancelToken&&l.cancelToken.promise.then(function(O){!d||(d.abort(),u(O),d=null)}),m===void 0&&(m=null),d.send(m)})},Cs}var Ke=et,sl=xm,Lm={"Content-Type":"application/x-www-form-urlencoded"};function ol(e,t){!Ke.isUndefined(e)&&Ke.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}function km(){var e;return(typeof XMLHttpRequest<"u"||typeof process<"u"&&Object.prototype.toString.call(process)==="[object process]")&&(e=rl()),e}var Wr={adapter:km(),transformRequest:[function(t,n){return sl(n,"Accept"),sl(n,"Content-Type"),Ke.isFormData(t)||Ke.isArrayBuffer(t)||Ke.isBuffer(t)||Ke.isStream(t)||Ke.isFile(t)||Ke.isBlob(t)?t:Ke.isArrayBufferView(t)?t.buffer:Ke.isURLSearchParams(t)?(ol(n,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):Ke.isObject(t)?(ol(n,"application/json;charset=utf-8"),JSON.stringify(t)):t}],transformResponse:[function(t){if(typeof t=="string")try{t=JSON.parse(t)}catch{}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(t){return t>=200&&t<300}};Wr.headers={common:{Accept:"application/json, text/plain, */*"}};Ke.forEach(["delete","get","head"],function(t){Wr.headers[t]={}});Ke.forEach(["post","put","patch"],function(t){Wr.headers[t]=Ke.merge(Lm)});var sc=Wr,il=et,Ss=Cm,Mm=nc(),Nm=sc;function xs(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var Vm=function(t){xs(t),t.headers=t.headers||{},t.data=Ss(t.data,t.headers,t.transformRequest),t.headers=il.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),il.forEach(["delete","get","head","post","put","patch","common"],function(s){delete t.headers[s]});var n=t.adapter||Nm.adapter;return n(t).then(function(s){return xs(t),s.data=Ss(s.data,s.headers,t.transformResponse),s},function(s){return Mm(s)||(xs(t),s&&s.response&&(s.response.data=Ss(s.response.data,s.response.headers,t.transformResponse))),Promise.reject(s)})},Mt=et,oc=function(t,n){n=n||{};var r={},s=["url","method","params","data"],o=["headers","auth","proxy"],i=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];Mt.forEach(s,function(c){typeof n[c]<"u"&&(r[c]=n[c])}),Mt.forEach(o,function(c){Mt.isObject(n[c])?r[c]=Mt.deepMerge(t[c],n[c]):typeof n[c]<"u"?r[c]=n[c]:Mt.isObject(t[c])?r[c]=Mt.deepMerge(t[c]):typeof t[c]<"u"&&(r[c]=t[c])}),Mt.forEach(i,function(c){typeof n[c]<"u"?r[c]=n[c]:typeof t[c]<"u"&&(r[c]=t[c])});var a=s.concat(o).concat(i),l=Object.keys(n).filter(function(c){return a.indexOf(c)===-1});return Mt.forEach(l,function(c){typeof n[c]<"u"?r[c]=n[c]:typeof t[c]<"u"&&(r[c]=t[c])}),r},Or=et,Dm=tc,ll=bm,Bm=Vm,ic=oc;function rr(e){this.defaults=e,this.interceptors={request:new ll,response:new ll}}rr.prototype.request=function(t){typeof t=="string"?(t=arguments[1]||{},t.url=arguments[0]):t=t||{},t=ic(this.defaults,t),t.method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var n=[Bm,void 0],r=Promise.resolve(t);for(this.interceptors.request.forEach(function(o){n.unshift(o.fulfilled,o.rejected)}),this.interceptors.response.forEach(function(o){n.push(o.fulfilled,o.rejected)});n.length;)r=r.then(n.shift(),n.shift());return r};rr.prototype.getUri=function(t){return t=ic(this.defaults,t),Dm(t.url,t.params,t.paramsSerializer).replace(/^\?/,"")};Or.forEach(["delete","get","head","options"],function(t){rr.prototype[t]=function(n,r){return this.request(Or.merge(r||{},{method:t,url:n}))}});Or.forEach(["post","put","patch"],function(t){rr.prototype[t]=function(n,r,s){return this.request(Or.merge(s||{},{method:t,url:n,data:r}))}});var Hm=rr,Es,al;function lc(){if(al)return Es;al=1;function e(t){this.message=t}return e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,Es=e,Es}var Rs,cl;function Um(){if(cl)return Rs;cl=1;var e=lc();function t(n){if(typeof n!="function")throw new TypeError("executor must be a function.");var r;this.promise=new Promise(function(i){r=i});var s=this;n(function(i){s.reason||(s.reason=new e(i),r(s.reason))})}return t.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},t.source=function(){var r,s=new t(function(i){r=i});return{token:s,cancel:r}},Rs=t,Rs}var Ts,ul;function qm(){return ul||(ul=1,Ts=function(t){return function(r){return t.apply(null,r)}}),Ts}var fl=et,Fm=Xa,gr=Hm,jm=oc,zm=sc;function ac(e){var t=new gr(e),n=Fm(gr.prototype.request,t);return fl.extend(n,gr.prototype,t),fl.extend(n,t),n}var bt=ac(zm);bt.Axios=gr;bt.create=function(t){return ac(jm(bt.defaults,t))};bt.Cancel=lc();bt.CancelToken=Um();bt.isCancel=nc();bt.all=function(t){return Promise.all(t)};bt.spread=qm();ko.exports=bt;ko.exports.default=bt;(function(e){e.exports=ko.exports})(Ja);const Km=rm(Ja.exports);function Wm(e){return{all:e=e||new Map,on:function(t,n){var r=e.get(t);r?r.push(n):e.set(t,[n])},off:function(t,n){var r=e.get(t);r&&(n?r.splice(r.indexOf(n)>>>0,1):e.set(t,[]))},emit:function(t,n){var r=e.get(t);r&&r.slice().map(function(s){s(n)}),(r=e.get("*"))&&r.slice().map(function(s){s(t,n)})}}}const Mo=Wm(),Gm=document.location.protocol+"//"+document.location.host;let cc="";cc=Gm;const pe=Km.create({baseURL:cc,timeout:99999});let $r=0,vr;const Jm=()=>{$r++,vr&&clearTimeout(vr),vr=setTimeout(()=>{$r>0&&Mo.emit("showLoading")},400)},Ys=()=>{$r--,$r<=0&&(clearTimeout(vr),Mo.emit("closeLoading"))};pe.interceptors.request.use(e=>{const t=mn();return e.donNotShowLoading||Jm(),e.url.match(/(\w+\/){0}\w+/)[0],e.headers={"Content-Type":"application/json",...e.headers},t.token.accessToken&&(e.url.includes("refresh_token")?e.headers.Authorization=`${t.token.tokenType} ${t.token.refreshToken}`:e.headers.Authorization=`${t.token.tokenType} ${t.token.accessToken}`),e},e=>(Ys(),Re({showClose:!0,message:e,type:"error"}),e));pe.interceptors.response.use(e=>{const t=mn();return Ys(),e.headers["new-token"]&&t.setToken(e.headers["new-token"]),logger.log("\u8BF7\u6C42\uFF1A",{request_url:e.config.url,response:e}),e.status===200||e.status===204||e.status===201||e.headers.success==="true"?e:(Re({showClose:!0,message:e.data.msg||decodeURI(e.headers.msg),type:"error"}),e.data.data&&e.data.data.reload&&(t.token="",localStorage.clear(),Ye.push({name:"Login",replace:!0})),e.data.msg?e.data:e)},e=>{const t=mn();if(Ys(),!e.response){tn.confirm(`
        <p>\u68C0\u6D4B\u5230\u8BF7\u6C42\u9519\u8BEF</p>
        <p>${e}</p>
        `,"\u8BF7\u6C42\u62A5\u9519",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"\u7A0D\u540E\u91CD\u8BD5",cancelButtonText:"\u53D6\u6D88"});return}switch(e.response.status){case 500:tn.confirm(`
        <p>\u68C0\u6D4B\u5230\u63A5\u53E3\u9519\u8BEF${e}</p>
        <p>\u9519\u8BEF\u7801<span style="color:red"> 500 </span>\uFF1A\u6B64\u7C7B\u9519\u8BEF\u5185\u5BB9\u5E38\u89C1\u4E8E\u540E\u53F0panic\uFF0C\u8BF7\u5148\u67E5\u770B\u540E\u53F0\u65E5\u5FD7\uFF0C\u5982\u679C\u5F71\u54CD\u60A8\u6B63\u5E38\u4F7F\u7528\u53EF\u5F3A\u5236\u767B\u51FA\u6E05\u7406\u7F13\u5B58</p>
        `,"\u63A5\u53E3\u62A5\u9519",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"\u6E05\u7406\u7F13\u5B58",cancelButtonText:"\u53D6\u6D88"}).then(()=>{const r=mn();r.token="",localStorage.clear(),Ye.push({name:"Login",replace:!0})});break;case 404:Re({showClose:!0,message:e.response.data.error,type:"error"});break;case 401:t.authFailureLoginOut();const n=window.localStorage.getItem("refresh_times")||0;window.localStorage.setItem("refresh_times",Number(n)+1);break;default:console.log(e.response),Re({showClose:!0,message:e.response.data.errorMessage||e.response.data.error,type:"error"});break}return e});const No=new XMLHttpRequest;No.open("GET",document.location,!1);No.send(null);const je=No.getResponseHeader("X-Corp-ID")||"default",Xm=e=>pe({url:"/auth/login/v1/user",method:"post",data:JSON.stringify(e)}),Ym=e=>pe({url:`/auth/admin/realms/${je}/users`,method:"get",params:e}),Qm=e=>pe({url:`/auth/admin/realms/${je}/users/count`,method:"get",params:e}),Zm=e=>pe({url:`/auth/admin/realms/${je}/users/${e}`,method:"delete"}),eg=e=>pe({url:"/user/setSelfInfo",method:"put",data:e}),tg=e=>pe({url:"/auth/user/v1/login_user",method:"get"}),ng=e=>{const t=e.id;return delete e.id,pe({url:`/auth/admin/realms/${je}/users/${t}`,method:"put",data:e})},rg=e=>pe({url:`/auth/admin/realms/${je}/roles`,method:"get",data:e}),sg=e=>pe({url:`/auth/admin/realms/${je}/users/${e}/groups`,method:"get"}),og=e=>pe({url:`/auth/admin/realms/${je}/groups`,method:"get",params:e}),ig=e=>pe({url:"/console/v1/user/director_types",method:"get",params:e}),lg=e=>pe({url:`/auth/admin/realms/${je}/groups/count`,method:"get",params:e}),ag=(e,t)=>pe({url:`/auth/admin/realms/${je}/groups/${e}/members`,method:"get",params:t}),cg=e=>(delete e.id,pe({url:`/auth/admin/realms/${je}/groups`,method:"post",data:e})),ug=e=>{const t=e.id;return delete e.id,pe({url:`/auth/admin/realms/${je}/groups/${t}`,method:"put",data:e})},fg=e=>{const t=e.id;return delete e.id,pe({url:`/auth/admin/realms/${je}/groups/${t}/children`,method:"post",data:e})},dg=e=>pe({url:`/auth/admin/realms/${je}/groups/${e}`,method:"delete"}),pg=e=>pe({url:`/auth/admin/realms/${je}/groups/${e}`,method:"get"}),hg=e=>pe({url:`/auth/admin/realms/${je}/users`,method:"post",data:e}),mg=e=>pe({url:"/auth/user/v1/logout",method:"post",data:""});var gg=!1;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let uc;const Gr=e=>uc=e,fc=Symbol();function Qs(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Un;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Un||(Un={}));function vg(){const e=Il(!0),t=e.run(()=>we({}));let n=[],r=[];const s=yo({install(o){Gr(s),s._a=o,o.provide(fc,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return!this._a&&!gg?r.push(o):n.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const dc=()=>{};function dl(e,t,n,r=dc){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&Ol()&&qc(s),s}function on(e,...t){e.slice().forEach(n=>{n(...t)})}const _g=e=>e(),pl=Symbol(),As=Symbol();function Zs(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];Qs(s)&&Qs(r)&&e.hasOwnProperty(n)&&!Ee(r)&&!Ft(r)?e[n]=Zs(s,r):e[n]=r}return e}const yg=Symbol();function bg(e){return!Qs(e)||!e.hasOwnProperty(yg)}const{assign:Nt}=Object;function wg(e){return!!(Ee(e)&&e.effect)}function Cg(e,t,n,r){const{state:s,actions:o,getters:i}=t,a=n.state.value[e];let l;function f(){a||(n.state.value[e]=s?s():{});const c=fu(n.state.value[e]);return Nt(c,o,Object.keys(i||{}).reduce((u,m)=>(u[m]=yo(le(()=>{Gr(n);const v=n._s.get(e);return i[m].call(v,v)})),u),{}))}return l=pc(e,f,t,n,r,!0),l}function pc(e,t,n={},r,s,o){let i;const a=Nt({actions:{}},n),l={deep:!0};let f,c,u=[],m=[],v;const d=r.state.value[e];!o&&!d&&(r.state.value[e]={}),we({});let g;function C(U){let L;f=c=!1,typeof U=="function"?(U(r.state.value[e]),L={type:Un.patchFunction,storeId:e,events:v}):(Zs(r.state.value[e],U),L={type:Un.patchObject,payload:U,storeId:e,events:v});const K=g=Symbol();Nr().then(()=>{g===K&&(f=!0)}),c=!0,on(u,L,r.state.value[e])}const x=o?function(){const{state:L}=n,K=L?L():{};this.$patch(ee=>{Nt(ee,K)})}:dc;function y(){i.stop(),u=[],m=[],r._s.delete(e)}const w=(U,L="")=>{if(pl in U)return U[As]=L,U;const K=function(){Gr(r);const ee=Array.from(arguments),D=[],te=[];function xe(Q){D.push(Q)}function $e(Q){te.push(Q)}on(m,{args:ee,name:K[As],store:O,after:xe,onError:$e});let fe;try{fe=U.apply(this&&this.$id===e?this:O,ee)}catch(Q){throw on(te,Q),Q}return fe instanceof Promise?fe.then(Q=>(on(D,Q),Q)).catch(Q=>(on(te,Q),Promise.reject(Q))):(on(D,fe),fe)};return K[pl]=!0,K[As]=L,K},_={_p:r,$id:e,$onAction:dl.bind(null,m),$patch:C,$reset:x,$subscribe(U,L={}){const K=dl(u,U,L.detached,()=>ee()),ee=i.run(()=>vt(()=>r.state.value[e],D=>{(L.flush==="sync"?c:f)&&U({storeId:e,type:Un.direct,events:v},D)},Nt({},l,L)));return K},$dispose:y},O=Yn(_);r._s.set(e,O);const z=(r._a&&r._a.runWithContext||_g)(()=>r._e.run(()=>(i=Il()).run(()=>t({action:w}))));for(const U in z){const L=z[U];if(Ee(L)&&!wg(L)||Ft(L))o||(d&&bg(L)&&(Ee(L)?L.value=d[U]:Zs(L,d[U])),r.state.value[e][U]=L);else if(typeof L=="function"){const K=w(L,U);z[U]=K,a.actions[U]=L}}return Nt(O,z),Nt(se(O),z),Object.defineProperty(O,"$state",{get:()=>r.state.value[e],set:U=>{C(L=>{Nt(L,U)})}}),r._p.forEach(U=>{Nt(O,i.run(()=>U({store:O,app:r._a,pinia:r,options:a})))}),d&&o&&n.hydrate&&n.hydrate(O.$state,d),f=!0,c=!0,O}/*! #__NO_SIDE_EFFECTS__ */function Vo(e,t,n){let r,s;const o=typeof t=="function";typeof e=="string"?(r=e,s=o?n:t):(s=e,r=e.id);function i(a,l){const f=Wu();return a=a||(f?De(fc,null):null),a&&Gr(a),a=uc,a._s.has(r)||(o?pc(r,t,s,a):Cg(r,s,a)),a._s.get(r)}return i.$id=r,i}const Sg=Object.assign({"../view/app/index.vue":()=>G(()=>import("./index.aa6174f6.js"),["./index.aa6174f6.js","./index.743849cd.css"],import.meta.url),"../view/client/download.vue":()=>G(()=>import("./download.507b8fc0.js"),["./download.507b8fc0.js","./browser.ff6109de.js","./download.2946a7b0.css"],import.meta.url),"../view/client/header.vue":()=>G(()=>import("./header.055d9003.js"),["./header.055d9003.js","./ASD.492c8837.js","./header.d5f050d9.css"],import.meta.url),"../view/client/index.vue":()=>G(()=>import("./index.a0b7d2e3.js"),["./index.a0b7d2e3.js","./header.055d9003.js","./ASD.492c8837.js","./header.d5f050d9.css","./menu.188806bb.js","./menu.64359975.css","./index.6b45d132.css"],import.meta.url),"../view/client/login.vue":()=>G(()=>import("./login.10ee0941.js"),["./login.10ee0941.js","./index.8981ad9e.js","./index.9f1ca410.css"],import.meta.url),"../view/client/main.vue":()=>G(()=>import("./main.15b19a5e.js"),["./main.15b19a5e.js","./index.aa6174f6.js","./index.743849cd.css","./main.48e9044e.css"],import.meta.url),"../view/client/menu.vue":()=>G(()=>import("./menu.188806bb.js"),["./menu.188806bb.js","./menu.64359975.css"],import.meta.url),"../view/client/setting.vue":()=>G(()=>import("./setting.10afe9c5.js"),["./setting.10afe9c5.js","./setting.1e43613a.css"],import.meta.url),"../view/error/index.vue":()=>G(()=>import("./index.67f52dd7.js"),["./index.67f52dd7.js","./index.e1fc439c.css"],import.meta.url),"../view/error/reload.vue":()=>G(()=>import("./reload.7ebb72fc.js"),[],import.meta.url),"../view/layout/aside/asideComponent/asyncSubmenu.vue":()=>G(()=>import("./asyncSubmenu.4ad0e585.js"),["./asyncSubmenu.4ad0e585.js","./asyncSubmenu.afb59362.css"],import.meta.url),"../view/layout/aside/asideComponent/index.vue":()=>G(()=>import("./index.2c60b68d.js"),["./index.2c60b68d.js","./menuItem.5f1822c7.js","./menuItem.e4bfa733.css","./asyncSubmenu.4ad0e585.js","./asyncSubmenu.afb59362.css"],import.meta.url),"../view/layout/aside/asideComponent/menuItem.vue":()=>G(()=>import("./menuItem.5f1822c7.js"),["./menuItem.5f1822c7.js","./menuItem.e4bfa733.css"],import.meta.url),"../view/layout/aside/historyComponent/history.vue":()=>G(()=>import("./history.0356210a.js"),["./history.0356210a.js","./index-browser-esm.c2d3b5c9.js","./history.45e0d923.css"],import.meta.url),"../view/layout/aside/index.vue":()=>G(()=>import("./index.e5a9e31d.js"),["./index.e5a9e31d.js","./index.2c60b68d.js","./menuItem.5f1822c7.js","./menuItem.e4bfa733.css","./asyncSubmenu.4ad0e585.js","./asyncSubmenu.afb59362.css","./index.c6b67cfa.css"],import.meta.url),"../view/layout/bottomInfo/bottomInfo.vue":()=>G(()=>import("./bottomInfo.446dc627.js"),["./bottomInfo.446dc627.js","./bottomInfo.844a8d22.css"],import.meta.url),"../view/layout/index.vue":()=>G(()=>import("./index.db16e7a4.js"),["./index.db16e7a4.js","./ASD.492c8837.js","./index.e5a9e31d.js","./index.2c60b68d.js","./menuItem.5f1822c7.js","./menuItem.e4bfa733.css","./asyncSubmenu.4ad0e585.js","./asyncSubmenu.afb59362.css","./index.c6b67cfa.css","./index-browser-esm.c2d3b5c9.js","./index.61ddf96c.css"],import.meta.url),"../view/layout/screenfull/index.vue":()=>G(()=>import("./index.5c2ee242.js"),["./index.5c2ee242.js","./index.1cb1996e.css"],import.meta.url),"../view/layout/search/search.vue":()=>G(()=>import("./search.be4ff8fb.js"),["./search.be4ff8fb.js","./index.5c2ee242.js","./index.1cb1996e.css","./search.83c559bf.css"],import.meta.url),"../view/layout/setting/index.vue":()=>G(()=>import("./index.093ffda5.js"),["./index.093ffda5.js","./index.e2e12561.css"],import.meta.url),"../view/login/clientLogin.vue":()=>G(()=>import("./clientLogin.f0f0c188.js"),[],import.meta.url),"../view/login/dingtalk/dingtalk.vue":()=>G(()=>import("./dingtalk.64e6a0d2.js"),[],import.meta.url),"../view/login/downloadWin.vue":()=>G(()=>import("./downloadWin.a30787fd.js"),["./downloadWin.a30787fd.js","./ASD.492c8837.js","./browser.ff6109de.js","./downloadWin.128fe51a.css"],import.meta.url),"../view/login/feishu/feishu.vue":()=>G(()=>import("./feishu.d1928f9b.js"),[],import.meta.url),"../view/login/index.vue":()=>G(()=>import("./index.8981ad9e.js"),["./index.8981ad9e.js","./index.9f1ca410.css"],import.meta.url),"../view/login/localLogin/localLogin.vue":()=>G(()=>import("./localLogin.00f453db.js"),["./localLogin.00f453db.js","./localLogin.a927f263.css"],import.meta.url),"../view/login/oauth2/oauth2.vue":()=>G(()=>import("./oauth2.cdc437fb.js"),["./oauth2.cdc437fb.js","./oauth2.79676400.css"],import.meta.url),"../view/login/oauth2/oauth2_premises.vue":()=>G(()=>import("./oauth2_premises.ef184f57.js"),["./oauth2_premises.ef184f57.js","./oauth2_premises.987b2776.css"],import.meta.url),"../view/login/oauth2/oauth2_result.vue":()=>G(()=>import("./oauth2_result.fa32ec2d.js"),["./oauth2_result.fa32ec2d.js","./secondaryAuth.75febeaf.js","./verifyCode.5ac023d8.js","./verifyCode.978f9466.css","./secondaryAuth.21aaca5f.css","./oauth2_result.c4afb049.css"],import.meta.url),"../view/login/secondaryAuth/secondaryAuth.vue":()=>G(()=>import("./secondaryAuth.75febeaf.js"),["./secondaryAuth.75febeaf.js","./verifyCode.5ac023d8.js","./verifyCode.978f9466.css","./secondaryAuth.21aaca5f.css"],import.meta.url),"../view/login/secondaryAuth/verifyCode.vue":()=>G(()=>import("./verifyCode.5ac023d8.js"),["./verifyCode.5ac023d8.js","./verifyCode.978f9466.css"],import.meta.url),"../view/login/sms/sms.vue":()=>G(()=>import("./sms.06abfe55.js"),["./sms.06abfe55.js","./sms.ef70f8fb.css"],import.meta.url),"../view/login/verify.vue":()=>G(()=>import("./verify.0a32ff20.js"),[],import.meta.url),"../view/login/wx/status.vue":()=>G(()=>import("./status.a7da319c.js"),["./status.a7da319c.js","./secondaryAuth.75febeaf.js","./verifyCode.5ac023d8.js","./verifyCode.978f9466.css","./secondaryAuth.21aaca5f.css","./status.e8a70bba.css"],import.meta.url),"../view/login/wx/wechat.vue":()=>G(()=>import("./wechat.82fc9960.js"),["./wechat.82fc9960.js","./wechat.3b1b375f.css"],import.meta.url),"../view/login/wx/wx_oauth_callback.vue":()=>G(()=>import("./wx_oauth_callback.65954852.js"),[],import.meta.url),"../view/resource/appverify.vue":()=>G(()=>import("./appverify.a30e8e3f.js"),["./appverify.a30e8e3f.js","./appverify.1430be1b.css"],import.meta.url),"../view/routerHolder.vue":()=>G(()=>import("./routerHolder.251bca9a.js"),[],import.meta.url)}),xg=Object.assign({}),hc=e=>{e.forEach(t=>{t.component?t.component.split("/")[0]==="view"?t.component=hl(Sg,t.component):t.component.split("/")[0]==="plugin"&&(t.component=hl(xg,t.component)):delete t.component,t.children&&hc(t.children)})};function hl(e,t){const s=Object.keys(e).filter(o=>o.replace("../","")===t)[0];return e[s]}const Eg=()=>new Promise(function(e,t){e({code:0,data:{menus:[{ID:9,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"clientLogin",name:"clientLogin",hidden:!0,component:"view/login/clientLogin.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"\u5BA2\u6237\u7AEF\u767B\u9646",topTitle:"\u5BA2\u6237\u7AEF\u767B\u9646",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"9",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"dashboard",name:"dashboard",hidden:!1,component:"view/app/index.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"\u5E94\u7528\u95E8\u6237",topTitle:"",icon:"icon-zuhu-yingyongliebiao",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"download",name:"download",hidden:!1,component:"view/client/download.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"\u5BA2\u6237\u7AEF\u4E0B\u8F7D",topTitle:"\u5BA2\u6237\u7AEF\u4E0B\u8F7D",icon:"icon-zuhu-kehuduanxiazai",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:8,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"person",name:"person",hidden:!0,component:"view/person/person.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"\u4E2A\u4EBA\u4FE1\u606F",topTitle:"\u4E2A\u4EBA\u4FE1\u606F",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"8",children:null,parameters:[],btns:null}]},msg:"\u83B7\u53D6\u6210\u529F"})}),eo=[],to=[],Rg=[],no={},mc=(e,t)=>{e&&e.forEach(n=>{(!n.children||n.children.every(r=>r.hidden))&&n.name!=="404"&&!n.hidden&&eo.push({label:n.meta.title,value:n.name}),n.meta.btns=n.btns,n.meta.hidden=n.hidden,n.meta.defaultMenu===!0?to.push({...n,path:`/${n.path}`}):(t[n.name]=n,n.children&&n.children.length>0&&mc(n.children,t))})},gc=e=>{e&&e.forEach(t=>{(t.children&&t.children.some(n=>n.meta.keepAlive)||t.meta.keepAlive)&&t.component&&t.component().then(n=>{Rg.push(n.default.name),no[t.name]=n.default.name}),t.children&&t.children.length>0&&gc(t.children)})},vc=Vo("router",()=>{const e=we([]),t=i=>{const a=[];i.forEach(l=>{no[l.name]&&a.push(no[l.name])}),e.value=Array.from(new Set(a))};Mo.on("setKeepAlive",t);const n=we([]),r=we(eo),s={};return{asyncRouters:n,routerList:r,keepAliveRouters:e,SetAsyncRouter:async()=>{const i=[{path:"/layout",name:"layout",component:"view/layout/index.vue",meta:{title:"\u5E95\u5C42layout"},children:[]},{path:"/appverify",name:"appverify",component:"view/resource/appverify.vue",meta:{title:"appverify"},children:[]}],l=(await Eg()).data.menus;return l&&l.push({path:"404",name:"404",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/index.vue"},{path:"reload",name:"Reload",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/reload.vue"}),mc(l,s),i[0].children=l,to.length!==0&&i.push(...to),i.push({path:"/:catchAll(.*)",redirect:"/layout/404"}),hc(i),gc(l),n.value=i,r.value=eo,logger.log({asyncRouters:n.value}),logger.log({routerList:r.value}),!0},routeMap:s}});var Tg=function(t,n){if(n=n.split(":")[0],t=+t,!t)return!1;switch(n){case"http":case"ws":return t!==80;case"https":case"wss":return t!==443;case"ftp":return t!==21;case"gopher":return t!==70;case"file":return!1}return t!==0},Do={},Ag=Object.prototype.hasOwnProperty,Ig;function ml(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch{return null}}function gl(e){try{return encodeURIComponent(e)}catch{return null}}function Og(e){for(var t=/([^=?#&]+)=?([^&]*)/g,n={},r;r=t.exec(e);){var s=ml(r[1]),o=ml(r[2]);s===null||o===null||s in n||(n[s]=o)}return n}function $g(e,t){t=t||"";var n=[],r,s;typeof t!="string"&&(t="?");for(s in e)if(Ag.call(e,s)){if(r=e[s],!r&&(r===null||r===Ig||isNaN(r))&&(r=""),s=gl(s),r=gl(r),s===null||r===null)continue;n.push(s+"="+r)}return n.length?t+n.join("&"):""}Do.stringify=$g;Do.parse=Og;var _c=Tg,Jr=Do,Pg=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,yc=/[\n\r\t]/g,Lg=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,bc=/:\d+$/,kg=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,Mg=/^[a-zA-Z]:/;function Bo(e){return(e||"").toString().replace(Pg,"")}var ro=[["#","hash"],["?","query"],function(t,n){return _t(n.protocol)?t.replace(/\\/g,"/"):t},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],vl={hash:1,query:1};function wc(e){var t;typeof window<"u"?t=window:typeof Gs<"u"?t=Gs:typeof self<"u"?t=self:t={};var n=t.location||{};e=e||n;var r={},s=typeof e,o;if(e.protocol==="blob:")r=new wt(unescape(e.pathname),{});else if(s==="string"){r=new wt(e,{});for(o in vl)delete r[o]}else if(s==="object"){for(o in e)o in vl||(r[o]=e[o]);r.slashes===void 0&&(r.slashes=Lg.test(e.href))}return r}function _t(e){return e==="file:"||e==="ftp:"||e==="http:"||e==="https:"||e==="ws:"||e==="wss:"}function Cc(e,t){e=Bo(e),e=e.replace(yc,""),t=t||{};var n=kg.exec(e),r=n[1]?n[1].toLowerCase():"",s=!!n[2],o=!!n[3],i=0,a;return s?o?(a=n[2]+n[3]+n[4],i=n[2].length+n[3].length):(a=n[2]+n[4],i=n[2].length):o?(a=n[3]+n[4],i=n[3].length):a=n[4],r==="file:"?i>=2&&(a=a.slice(2)):_t(r)?a=n[4]:r?s&&(a=a.slice(2)):i>=2&&_t(t.protocol)&&(a=n[4]),{protocol:r,slashes:s||_t(r),slashesCount:i,rest:a}}function Ng(e,t){if(e==="")return t;for(var n=(t||"/").split("/").slice(0,-1).concat(e.split("/")),r=n.length,s=n[r-1],o=!1,i=0;r--;)n[r]==="."?n.splice(r,1):n[r]===".."?(n.splice(r,1),i++):i&&(r===0&&(o=!0),n.splice(r,1),i--);return o&&n.unshift(""),(s==="."||s==="..")&&n.push(""),n.join("/")}function wt(e,t,n){if(e=Bo(e),e=e.replace(yc,""),!(this instanceof wt))return new wt(e,t,n);var r,s,o,i,a,l,f=ro.slice(),c=typeof t,u=this,m=0;for(c!=="object"&&c!=="string"&&(n=t,t=null),n&&typeof n!="function"&&(n=Jr.parse),t=wc(t),s=Cc(e||"",t),r=!s.protocol&&!s.slashes,u.slashes=s.slashes||r&&t.slashes,u.protocol=s.protocol||t.protocol||"",e=s.rest,(s.protocol==="file:"&&(s.slashesCount!==2||Mg.test(e))||!s.slashes&&(s.protocol||s.slashesCount<2||!_t(u.protocol)))&&(f[3]=[/(.*)/,"pathname"]);m<f.length;m++){if(i=f[m],typeof i=="function"){e=i(e,u);continue}o=i[0],l=i[1],o!==o?u[l]=e:typeof o=="string"?(a=o==="@"?e.lastIndexOf(o):e.indexOf(o),~a&&(typeof i[2]=="number"?(u[l]=e.slice(0,a),e=e.slice(a+i[2])):(u[l]=e.slice(a),e=e.slice(0,a)))):(a=o.exec(e))&&(u[l]=a[1],e=e.slice(0,a.index)),u[l]=u[l]||r&&i[3]&&t[l]||"",i[4]&&(u[l]=u[l].toLowerCase())}n&&(u.query=n(u.query)),r&&t.slashes&&u.pathname.charAt(0)!=="/"&&(u.pathname!==""||t.pathname!=="")&&(u.pathname=Ng(u.pathname,t.pathname)),u.pathname.charAt(0)!=="/"&&_t(u.protocol)&&(u.pathname="/"+u.pathname),_c(u.port,u.protocol)||(u.host=u.hostname,u.port=""),u.username=u.password="",u.auth&&(a=u.auth.indexOf(":"),~a?(u.username=u.auth.slice(0,a),u.username=encodeURIComponent(decodeURIComponent(u.username)),u.password=u.auth.slice(a+1),u.password=encodeURIComponent(decodeURIComponent(u.password))):u.username=encodeURIComponent(decodeURIComponent(u.auth)),u.auth=u.password?u.username+":"+u.password:u.username),u.origin=u.protocol!=="file:"&&_t(u.protocol)&&u.host?u.protocol+"//"+u.host:"null",u.href=u.toString()}function Vg(e,t,n){var r=this;switch(e){case"query":typeof t=="string"&&t.length&&(t=(n||Jr.parse)(t)),r[e]=t;break;case"port":r[e]=t,_c(t,r.protocol)?t&&(r.host=r.hostname+":"+t):(r.host=r.hostname,r[e]="");break;case"hostname":r[e]=t,r.port&&(t+=":"+r.port),r.host=t;break;case"host":r[e]=t,bc.test(t)?(t=t.split(":"),r.port=t.pop(),r.hostname=t.join(":")):(r.hostname=t,r.port="");break;case"protocol":r.protocol=t.toLowerCase(),r.slashes=!n;break;case"pathname":case"hash":if(t){var s=e==="pathname"?"/":"#";r[e]=t.charAt(0)!==s?s+t:t}else r[e]=t;break;case"username":case"password":r[e]=encodeURIComponent(t);break;case"auth":var o=t.indexOf(":");~o?(r.username=t.slice(0,o),r.username=encodeURIComponent(decodeURIComponent(r.username)),r.password=t.slice(o+1),r.password=encodeURIComponent(decodeURIComponent(r.password))):r.username=encodeURIComponent(decodeURIComponent(t))}for(var i=0;i<ro.length;i++){var a=ro[i];a[4]&&(r[a[1]]=r[a[1]].toLowerCase())}return r.auth=r.password?r.username+":"+r.password:r.username,r.origin=r.protocol!=="file:"&&_t(r.protocol)&&r.host?r.protocol+"//"+r.host:"null",r.href=r.toString(),r}function Dg(e){(!e||typeof e!="function")&&(e=Jr.stringify);var t,n=this,r=n.host,s=n.protocol;s&&s.charAt(s.length-1)!==":"&&(s+=":");var o=s+(n.protocol&&n.slashes||_t(n.protocol)?"//":"");return n.username?(o+=n.username,n.password&&(o+=":"+n.password),o+="@"):n.password?(o+=":"+n.password,o+="@"):n.protocol!=="file:"&&_t(n.protocol)&&!r&&n.pathname!=="/"&&(o+="@"),(r[r.length-1]===":"||bc.test(n.hostname)&&!n.port)&&(r+=":"),o+=r+n.pathname,t=typeof n.query=="object"?e(n.query):n.query,t&&(o+=t.charAt(0)!=="?"?"?"+t:t),n.hash&&(o+=n.hash),o}wt.prototype={set:Vg,toString:Dg};wt.extractProtocol=Cc;wt.location=wc;wt.trimLeft=Bo;wt.qs=Jr;var Bg=wt;const p1=e=>pe({url:"/auth/login/v1/cache",method:"post",data:e}),Hg=e=>pe({url:"/auth/login/v1/user/third",method:"post",data:e}),Ug=(e,t,n)=>pe({url:`/auth/login/v1/callback/${e}`,method:"get",params:{code:t,state:n}}),Sc=()=>pe({url:"/auth/authz/v1/user/refresh_token",method:"get",donNotShowLoading:!0}),qg=600*1e3;let Is=!1;function qn(e,t){setInterval(()=>{Is||(Is=!0,Sc().then(n=>{console.log("---refreshToken--"),n.status===200?n.data.code===-1?(console.log("\u5237\u65B0token\u5931\u8D25\uFF0C\u9000\u51FA\u81F3\u767B\u5F55"),e()):(console.log("\u5237\u65B0token\u6210\u529F\uFF0C\u4FDD\u5B58token"),t(n.data)):(console.log("\u5237\u65B0token\u5931\u8D25\uFF0C\u9000\u51FA\u81F3\u767B\u5F55"),e())}).catch(()=>{console.log("---refreshToken err--"),e()}).finally(()=>{Is=!1}))},qg)}const h1=e=>pe({url:"/auth/login/v1/send_sms",method:"post",data:e}),Fg=e=>pe({url:"/auth/login/v1/sms_verify",method:"post",data:e}),m1=e=>pe({url:"/auth/login/v1/sms_key",method:"post",data:e}),mn=Vo("user",()=>{const e=we(null),t=we({id:"",name:"",groupId:"",groupName:"",corpId:"",sourceId:"",phone:"",email:"",avatar:"",roles:[],sideMode:"dark",activeColor:"#4D70FF",baseColor:"#fff"}),n=we(window.localStorage.getItem("token")||""),r=we(window.localStorage.getItem("loginType")||"");try{n.value=n.value?JSON.parse(n.value):""}catch{console.log("---\u6E05\u7406localStorage\u4E2D\u7684token---"),window.localStorage.removeItem("token"),n.value=""}const s=k=>{t.value=k},o=k=>{n.value=k},i=k=>{r.value=k},a=()=>{n.value="",window.localStorage.removeItem("token"),Ye.push({name:"Init",replace:!0})},l=(k={})=>{t.value={...t.value,...k}},f=async k=>{const V=await tg();return V.status===200&&s(V.data.userInfo),V},c=async k=>{const V=await ng(k);return V.code===0?"":V},u=async k=>{const V=await Zm(k);return V.code===0?"":V},m=async(k,V,ze)=>{e.value=Fs.service({fullscreen:!0,text:"\u767B\u5F55\u4E2D\uFF0C\u8BF7\u7A0D\u5019..."});try{let ie="";switch(V){case"qiyewx":case"qiyewx_oauth":case"feishu":case"dingtalk":case"oauth2":case"cas":case"msad":case"ldap":ie=await Hg(k),i(ze);break;case"accessory":ie=await Fg(k);break;default:ie=await Xm(k),i(ze);break}const Be=ie.data.msg;if(ie.status===200){if(ie.data.code===-1||ie.data?.data?.status===1)return Re({showClose:!0,message:Be,type:"error"}),e.value.close(),{code:-1};{if(ie.data.data){if(ie.data.data.secondary)return e.value.close(),{isSecondary:!0,secondary:ie.data.data.secondary,uniqKey:ie.data.data.uniqKey,contactType:ie.data.data.contactType,hasContactInfo:ie.data.data.hasContactInfo,secondaryType:ie.data.secondaryType,userName:ie.data.data.userName,user_id:ie.data.data.userID};o(ie.data.data)}await f(),qn(d,o);const He=vc();await He.SetAsyncRouter(),He.asyncRouters.forEach(R=>{Ye.addRoute(R)});const B=window.location.href.replace(/#/g,"&"),I=Bg(B,!0);let H={},ne=null,ae=null;try{const R=localStorage.getItem("client_params");if(R){const S=JSON.parse(R);ne=S.type,ae=S.wp}}catch(R){console.warn("LoginIn: \u83B7\u53D6localStorage\u53C2\u6570\u5931\u8D25:",R)}const p=window.location.search,b=new URLSearchParams(p).get("type");if(I.query?.redirect||I.query?.redirect_url){let R="";return I.query?.redirect?R=I.query?.redirect.indexOf("?")>-1?I.query?.redirect.substring(I.query?.redirect.indexOf("?")+1):"":I.query?.redirect_url&&(R=I.query?.redirect_url.indexOf("?")>-1?I.query?.redirect_url.substring(I.query?.redirect_url.indexOf("?")+1):""),R.split("&").forEach(function(S){const T=S.split("=");H[T[0]]=T[1]}),ne&&(H.type=ne),ae&&(H.wp=ae),e.value.close(),window.localStorage.setItem("refresh_times",0),V==="qiyewx_oauth"||(window.location.href=I.query?.redirect||I.query?.redirect_url),!0}else H={type:ne||I.query.type},(ae||I.query.wp)&&(H.wp=ae||I.query.wp);return I.query.wp&&(H.wp=I.query.wp),await Ye.push({name:"dashboard",query:H}),e.value.close(),!0}}else Re({showClose:!0,message:Be,type:"error"}),e.value.close()}catch{Re({showClose:!0,message:"\u670D\u52A1\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01",type:"error"}),e.value.close()}},v=async(k,V,ze)=>{try{e.value=Fs.service({fullscreen:!0,text:"\u5904\u7406\u767B\u5F55\u4E2D..."});const ie=await Ug(k,V,ze);if(ie.status===200&&ie.data){const Be=ie.data;if(Be.needSecondary)return e.value.close(),{isSecondary:!0,uniqKey:Be.uniqKey};if(Be.token)return o({accessToken:Be.token,refreshToken:Be.refresh_token,expireIn:Be.expires_in,tokenType:Be.token_type||"Bearer"}),await f(),e.value.close(),!0}return e.value.close(),!1}catch(ie){return console.error("OAuth2\u767B\u5F55\u5904\u7406\u5931\u8D25:",ie),e.value.close(),Re({showClose:!0,message:ie.message||"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5",type:"error"}),!1}},d=async()=>{qn();const k=await mg();console.log("\u767B\u51FAres",k),k.status===200?k.data.code===-1?Re({showClose:!0,message:k.data.msg,type:"error"}):k.data.redirectUrl?(console.log("\u68C0\u6D4B\u5230OAuth2\u767B\u51FAURL\uFF0C\u6B63\u5728\u91CD\u5B9A\u5411:",k.data.redirectUrl),C(),window.location.href=k.data.redirectUrl):(Ye.push({name:"Login",replace:!0}),C()):Re({showClose:!0,message:"\u670D\u52A1\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01",type:"error"})},g=async()=>{qn(),C(),Ye.push({name:"Login",replace:!0}),window.location.reload()},C=async()=>{sessionStorage.clear(),window.localStorage.removeItem("userInfo"),window.localStorage.removeItem("token"),n.value=""},x=async k=>{(await eg({sideMode:k})).code===0&&(t.value.sideMode=k,Re({type:"success",message:"\u8BBE\u7F6E\u6210\u529F"}))},y=async k=>{const V=await rg(k);return V.code===0?"":V},w=async k=>{const V=await sg(k);return V.code===0?"":V},_=async k=>{const V=await getUserRole(k);return V.code===0?"":V},O=async k=>{const V=await og(k);return V.code===0?"":V},q=async()=>{const k=await ig();return k.code===0?"":k},z=async k=>{const V=await lg(k);return V.code===0?"":V},U=async k=>{const V=await pg(k);return V.code===0?"":V},L=async(k,V)=>{const ze=await ag(k,V);return ze.code===0?"":ze},K=async k=>{const V=await fg(k);return V.code===0?"":V},ee=async k=>{const V=await cg(k);return V.code===0?"":V},D=async k=>{const V=await ug(k);return V.code===0?"":V},te=async k=>{const V=await dg(k);return V.code===0?"":V},xe=async k=>{delete k.id;const V=await hg(k);return V.code===0?"":V},$e=async k=>{const V=await Ym(k);return V.code===0?"":V},fe=async k=>{const V=await Qm(k);return V.code===0?"":V},Q="dark",oe="#273444",it="#fff",pt="#4D70FF";return vt(()=>n.value,()=>{window.localStorage.setItem("token",JSON.stringify(n.value))}),vt(()=>r.value,()=>{window.localStorage.setItem("loginType",r.value)}),{userInfo:t,token:n,loginType:r,NeedInit:a,ResetUserInfo:l,GetUserInfo:f,LoginIn:m,LoginOut:d,authFailureLoginOut:g,changeSideMode:x,mode:Q,sideMode:oe,setToken:o,baseColor:it,activeColor:pt,loadingInstance:e,ClearStorage:C,GetOrganize:O,GetOrganizeDetails:U,UpdateOrganize:D,CreateOrganize:ee,DelOrganize:te,AddSubgroup:K,CreateUser:xe,GetUserList:$e,GetUserListCount:fe,UpdateUser:c,DeleteUser:u,GetRoles:y,GetGroupMembers:L,GetOrganizeCount:z,GetUserOrigin:q,GetUserGroups:w,GetUserRole:_,handleOAuth2Login:v}}),xc=Vo("app",{state:()=>({isClient:!1,clientType:"windows"}),actions:{setIsClient(){let e=/QtWebEngine/.test(navigator.userAgent);e||urlHashParams&&urlHashParams.get("asec_client")&&(e=!0),this.isClient=e}}}),jg=(e,t)=>{const n=/\$\{(.+?)\}/,r=/\$\{(.+?)\}/g,s=e.match(r);return s&&s.forEach(o=>{const i=o.match(n)[1],a=t.params[i]||t.query[i];e=e.replace(o,a)}),e};function _l(e,t){return e?`${jg(e,t)} - ${js.appName}`:`${js.appName}`}var Ec={exports:{}};/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */(function(e,t){(function(n,r){e.exports=r()})(Gs,function(){var n={};n.version="0.2.0";var r=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};n.configure=function(d){var g,C;for(g in d)C=d[g],C!==void 0&&d.hasOwnProperty(g)&&(r[g]=C);return this},n.status=null,n.set=function(d){var g=n.isStarted();d=s(d,r.minimum,1),n.status=d===1?null:d;var C=n.render(!g),x=C.querySelector(r.barSelector),y=r.speed,w=r.easing;return C.offsetWidth,a(function(_){r.positionUsing===""&&(r.positionUsing=n.getPositioningCSS()),l(x,i(d,y,w)),d===1?(l(C,{transition:"none",opacity:1}),C.offsetWidth,setTimeout(function(){l(C,{transition:"all "+y+"ms linear",opacity:0}),setTimeout(function(){n.remove(),_()},y)},y)):setTimeout(_,y)}),this},n.isStarted=function(){return typeof n.status=="number"},n.start=function(){n.status||n.set(0);var d=function(){setTimeout(function(){!n.status||(n.trickle(),d())},r.trickleSpeed)};return r.trickle&&d(),this},n.done=function(d){return!d&&!n.status?this:n.inc(.3+.5*Math.random()).set(1)},n.inc=function(d){var g=n.status;return g?(typeof d!="number"&&(d=(1-g)*s(Math.random()*g,.1,.95)),g=s(g+d,0,.994),n.set(g)):n.start()},n.trickle=function(){return n.inc(Math.random()*r.trickleRate)},function(){var d=0,g=0;n.promise=function(C){return!C||C.state()==="resolved"?this:(g===0&&n.start(),d++,g++,C.always(function(){g--,g===0?(d=0,n.done()):n.set((d-g)/d)}),this)}}(),n.render=function(d){if(n.isRendered())return document.getElementById("nprogress");c(document.documentElement,"nprogress-busy");var g=document.createElement("div");g.id="nprogress",g.innerHTML=r.template;var C=g.querySelector(r.barSelector),x=d?"-100":o(n.status||0),y=document.querySelector(r.parent),w;return l(C,{transition:"all 0 linear",transform:"translate3d("+x+"%,0,0)"}),r.showSpinner||(w=g.querySelector(r.spinnerSelector),w&&v(w)),y!=document.body&&c(y,"nprogress-custom-parent"),y.appendChild(g),g},n.remove=function(){u(document.documentElement,"nprogress-busy"),u(document.querySelector(r.parent),"nprogress-custom-parent");var d=document.getElementById("nprogress");d&&v(d)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var d=document.body.style,g="WebkitTransform"in d?"Webkit":"MozTransform"in d?"Moz":"msTransform"in d?"ms":"OTransform"in d?"O":"";return g+"Perspective"in d?"translate3d":g+"Transform"in d?"translate":"margin"};function s(d,g,C){return d<g?g:d>C?C:d}function o(d){return(-1+d)*100}function i(d,g,C){var x;return r.positionUsing==="translate3d"?x={transform:"translate3d("+o(d)+"%,0,0)"}:r.positionUsing==="translate"?x={transform:"translate("+o(d)+"%,0)"}:x={"margin-left":o(d)+"%"},x.transition="all "+g+"ms "+C,x}var a=function(){var d=[];function g(){var C=d.shift();C&&C(g)}return function(C){d.push(C),d.length==1&&g()}}(),l=function(){var d=["Webkit","O","Moz","ms"],g={};function C(_){return _.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(O,q){return q.toUpperCase()})}function x(_){var O=document.body.style;if(_ in O)return _;for(var q=d.length,z=_.charAt(0).toUpperCase()+_.slice(1),U;q--;)if(U=d[q]+z,U in O)return U;return _}function y(_){return _=C(_),g[_]||(g[_]=x(_))}function w(_,O,q){O=y(O),_.style[O]=q}return function(_,O){var q=arguments,z,U;if(q.length==2)for(z in O)U=O[z],U!==void 0&&O.hasOwnProperty(z)&&w(_,z,U);else w(_,q[1],q[2])}}();function f(d,g){var C=typeof d=="string"?d:m(d);return C.indexOf(" "+g+" ")>=0}function c(d,g){var C=m(d),x=C+g;f(C,g)||(d.className=x.substring(1))}function u(d,g){var C=m(d),x;!f(d,g)||(x=C.replace(" "+g+" "," "),d.className=x.substring(1,x.length-1))}function m(d){return(" "+(d.className||"")+" ").replace(/\s+/gi," ")}function v(d){d&&d.parentNode&&d.parentNode.removeChild(d)}return n})})(Ec);const sr=Ec.exports,zg=(e,t)=>["/client","/client/login","/client/setting"].includes(e.path)?(logger.log("\u5BA2\u6237\u7AEF\u76F4\u63A5\u8FD4\u56DE"),!0):(logger.log("\u5BA2\u6237\u7AEF\u67E5\u8BE2\u767B\u5F55\u72B6\u6001:",e.path),{name:"ClientNewLogin",query:{redirect:e.href,debug:logger.debug}});let ur=0;const fr=["Login","Init","ClientLogin","Status","downloadWin","WxOAuthCallback","OAuth2Result","OAuth2Premises"],yl=async e=>{logger.log("----getRouter---");const t=vc();await t.SetAsyncRouter(),await e.GetUserInfo(),t.asyncRouters.forEach(r=>{Ye.addRoute(r)})};async function so(e){if(e.matched.some(t=>t.meta.keepAlive)&&e.matched&&e.matched.length>2)for(let t=1;t<e.matched.length;t++){const n=e.matched[t-1];n.name==="layout"&&(e.matched.splice(t,1),await so(e)),typeof n.components.default=="function"&&(await n.components.default(),await so(e))}}const Kg=e=>(logger.log("socket\u8FDE\u63A5\u5F00\u59CB"),new Promise((t,n)=>{const r={action:2,msg:"",platform:document.location.hostname},s=we({}),o=we("ws://127.0.0.1:50001"),i=navigator.platform;(i.indexOf("Mac")===0||i==="MacIntel")&&(o.value="wss://127.0.0.1:50001");const a=async()=>{s.value=new WebSocket(o.value);let c;const u=()=>{c=setTimeout(()=>{console.log("WebSocket\u8FDE\u63A5\u8D85\u65F6"),f(),t()},2e3)};s.value.onopen=()=>{logger.log("socket\u8FDE\u63A5\u6210\u529F"),u(),l(JSON.stringify(r))},s.value.onmessage=async m=>{if(logger.log("-------e--------"),logger.log(JSON.parse(m.data)),clearTimeout(c),m?.data)try{const v=JSON.parse(m.data);if(!v.msg.token){t();return}const d={accessToken:v.msg.token,expireIn:3600,refreshToken:v.msg.refreshToken,refreshExpireIn:604800,tokenType:"Bearer"};await e.setToken(d);const g=await Sc();g.status===200&&((g?.data?.code||g?.data?.code!==-1)&&(await e.setToken(g.data),await e.GetUserInfo(),t()),t()),t()}catch{await f(),t()}await f(),t()},s.value.onerror=()=>{console.log("socket\u8FDE\u63A5\u9519\u8BEF"),clearTimeout(c),t()}},l=c=>{s.value.send(c)},f=()=>{logger.log("socket\u65AD\u5F00\u94FE\u63A5"),s.value.close()};logger.log(`asecagent://?web=${JSON.stringify(r)}`),a()}));Ye.beforeEach(async(e,t)=>{if(sr.start(),xc().isClient)return zg(e);const r=mn();e.meta.matched=[...e.matched],await so(e);let s=r.token;document.title=_l(e.meta.title,e),e.name=="WxOAuthCallback"||e.name=="verify"?document.title="":document.title=_l(e.meta.title,e),logger.log("\u8DEF\u7531\u53C2\u6570\uFF1A",{whiteList:fr,to:e,from:t});const o=window.localStorage.getItem("refresh_times")||0;return(!s||s==='""')&&Number(o)<5&&e.name!=="Login"&&(await Kg(r),s=r.token),fr.includes(e.name)?s&&!["downloadWin","Login","WxOAuthCallback","OAuth2Callback"].includes(e.name)?(!ur&&fr.indexOf(t.name)<0&&(ur++,await yl(r),logger.log("getRouter")),r.userInfo?(logger.log("dashboard"),{name:"dashboard"}):(qn(),await r.ClearStorage(),logger.log("\u5F3A\u5236\u9000\u51FA\u8D26\u53F7"),{name:"Login",query:{redirect:document.location.hash}})):(logger.log("\u76F4\u63A5\u8FD4\u56DE"),!0):(logger.log("\u4E0D\u5728\u767D\u540D\u5355\u4E2D:",s),s?!ur&&fr.indexOf(t.name)<0?(ur++,await yl(r),logger.log("\u521D\u59CB\u5316\u52A8\u6001\u8DEF\u7531:",r.token),r.token?(logger.log("\u8FD4\u56DEto"),{...e,replace:!1}):(logger.log("\u8FD4\u56DElogin"),{name:"Login",query:{redirect:e.href}})):e.matched.length?(qn(r.LoginOut,r.setToken),logger.log("\u8FD4\u56DErefresh"),!0):(console.log("404:",e.matched),{path:"/layout/404"}):(logger.log("\u4E0D\u5728\u767D\u540D\u5355\u4E2D\u5E76\u4E14\u672A\u767B\u5F55\u7684\u65F6\u5019"),{name:"Login",query:{redirect:document.location.hash}}))});Ye.afterEach(()=>{sr.done()});Ye.onError(()=>{sr.remove()});const Wg={install:e=>{const t=mn();e.directive("auth",{mounted:function(n,r){const s=t.userInfo;let o="";switch(Object.prototype.toString.call(r.value)){case"[object Array]":o="Array";break;case"[object String]":o="String";break;case"[object Number]":o="Number";break;default:o="";break}if(o===""){n.parentNode.removeChild(n);return}let a=r.value.toString().split(",").some(l=>Number(l)===s.id);r.modifiers.not&&(a=!a),a||n.parentNode.removeChild(n)}})}},Gg=vg();const Jg={name:"App",created(){const e=De("$keycloak");logger.log("App created: ",e)}},Xg={id:"app"};function Yg(e,t,n,r,s,o){const i=Nu("router-view");return J(),Z("div",Xg,[Oe(i)])}const Qg=be(Jg,[["render",Yg]]),Zg=`
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="display: none;">
  <!-- \u57FA\u7840\u56FE\u6807 -->
  <symbol id="icon-search" viewBox="0 0 1024 1024">
    <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116 65.6-158.4C296 211.3 352.2 188 412 188s116 23.3 158.4 65.6C612.7 296 636 352.2 636 412s-23.3 116-65.6 158.4z"/>
  </symbol>
  
  <symbol id="icon-plus" viewBox="0 0 1024 1024">
    <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"/>
  </symbol>
  
  <symbol id="icon-warning" viewBox="0 0 1024 1024">
    <path d="M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"/>
  </symbol>
  
  <symbol id="icon-document" viewBox="0 0 1024 1024">
    <path d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z"/>
  </symbol>
  
  <!-- \u9879\u76EE\u7279\u5B9A\u56FE\u6807 -->
  <symbol id="icon-jieru" viewBox="0 0 1024 1024">
    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z"/>
  </symbol>
  
  <symbol id="icon-shezhi" viewBox="0 0 1024 1024">
    <path d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a449.4 449.4 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 28.9a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM512 701c-104.9 0-190-85.1-190-190s85.1-190 190-190 190 85.1 190 190-85.1 190-190 190z"/>
  </symbol>
  
  <symbol id="icon-windows" viewBox="0 0 1024 1024">
    <path d="M120.83 208.13l344.49-47.04v331.65H120.83V208.13zm0 607.78l344.49 47.04V531.3H120.83v284.61zm384.34 50.02L903.17 896V531.3H505.17v334.63zM505.17 128L903.17 96v435.3H505.17V128z"/>
  </symbol>
  
  <symbol id="icon-mac" viewBox="0 0 1024 1024">
    <path d="M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z"/>
  </symbol>
  
  <symbol id="icon-ios" viewBox="0 0 1024 1024">
    <path d="M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z"/>
  </symbol>
  
  <symbol id="icon-android" viewBox="0 0 1024 1024">
    <path d="M765.7 486.8L620.7 182.2c-3.4-7.2-11.1-11.7-19.5-11.7s-16.1 4.5-19.5 11.7L436.6 486.8a32.05 32.05 0 0 0 28.6 46.4c7.9 0 15.3-2.9 21.1-8.2l23.9-23.9h185.6l23.9 23.9c5.8 5.3 13.2 8.2 21.1 8.2 17.7 0 32-14.3 32-32 0-7.6-2.6-14.8-7.4-20.4zM334.1 548.9L149.9 757.1a32.06 32.06 0 0 0 0 45.3l208.2 208.2c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L195.2 757.1 403.4 548.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0z"/>
  </symbol>
  
  <symbol id="icon-xiazai" viewBox="0 0 1024 1024">
    <path d="M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"/>
  </symbol>
  
  <symbol id="icon-expand" viewBox="0 0 1024 1024">
    <path d="M342 88H120c-17.7 0-32 14.3-32 32v222c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v174c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32zM342 856h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16H342V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v222c0 17.7 14.3 32 32 32h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0c17.7 0 32-14.3 32-32V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v174H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222z"/>
  </symbol>
</svg>
`;function e1(){if(typeof document<"u"){const e=document.createElement("div");e.innerHTML=Zg,e.style.display="none",document.body.insertBefore(e,document.body.firstChild)}}logger.log(navigator.userAgent);logger.log(document.location.href);sr.configure({showSpinner:!1,ease:"ease",speed:500});sr.start();const t1=/msie|trident/i.test(navigator.userAgent);t1&&alert(`
    \u5BF9\u4E0D\u8D77\uFF0C\u60A8\u6B63\u5728\u4F7F\u7528\u7684\u6D4F\u89C8\u5668\u7248\u672C\u8FC7\u4F4E\u3002
    \u672C\u7F51\u7AD9\u4E0D\u652F\u6301IE\u6D4F\u89C8\u5668\uFF0C\u8BF7\u4F7F\u7528\u73B0\u4EE3\u6D4F\u89C8\u5668\uFF08\u5982Chrome\u3001Firefox\u3001Edge\u7B49\uFF09\u4EE5\u83B7\u5F97\u66F4\u597D\u7684\u6D4F\u89C8\u4F53\u9A8C\u3002
  `);const Rc=qr(Qg);Rc.config.productionTip=!1;const n1=document.location.protocol+"//"+document.location.host,Ho=new XMLHttpRequest;Ho.open("GET",document.location,!1);Ho.send(null);Ho.getResponseHeader("X-Corp-ID");let Tc="";Tc=n1+"/auth";logger.log(`url:${Tc}`);e1();Rc.use(Jp).use(Gg).use(Wg).use(Ye).use(Wp).mount("#app");const oo=xc();oo.setIsClient();logger.log("\u662F\u5426\u662F\u5BA2\u6237\u7AEF:",oo.isClient,"\u5BA2\u6237\u7AEF\u7C7B\u578B:",oo.clientType);export{o1 as A,G as B,_e as C,Yn as D,En as E,Fe as F,nn as G,td as H,Ze as I,a1 as J,Mo as K,Fs as L,Re as M,i1 as N,wo as O,Se as P,jg as Q,Df as R,vc as S,l1 as T,Nr as U,c1 as V,s1 as W,p1 as X,Gs as Y,u1 as Z,be as _,f1 as a,mn as b,le as c,Z as d,ye as e,Ds as f,ct as g,Nu as h,Vu as i,Oe as j,tr as k,De as l,dn as m,h1 as n,J as o,hn as p,Km as q,we as r,m1 as s,yt as t,d1 as u,Fg as v,bu as w,pe as x,r1 as y,vt as z};
