/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
System.register(["./menuItem-legacy.53c63200.js","./asyncSubmenu-legacy.8d643ebd.js","./index-legacy.e6617eb1.js"],(function(e,n){"use strict";var t,r,u,o,i,f,l,c,s,a,d,h;return{setters:[function(e){t=e.default},function(e){r=e.default},function(e){u=e.c,o=e.h,i=e.o,f=e.f,l=e.w,c=e.d,s=e.F,a=e.i,d=e.g,h=e.A}],execute:function(){e("default",Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:function(){return null}},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){var n=e,m=u((function(){return n.routerInfo.children&&n.routerInfo.children.filter((function(e){return!e.hidden})).length?r:t}));return function(n,t){var r=o("AsideComponent");return e.routerInfo.hidden?d("",!0):(i(),f(h(m.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:l((function(){return[e.routerInfo.children&&e.routerInfo.children.length?(i(!0),c(s,{key:0},a(e.routerInfo.children,(function(n){return i(),f(r,{key:n.name,"is-collapse":!1,"router-info":n,theme:e.theme},null,8,["router-info","theme"])})),128)):d("",!0)]})),_:1},8,["is-collapse","theme","router-info"]))}}}))}}}));
