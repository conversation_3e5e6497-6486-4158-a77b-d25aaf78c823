<!-- eslint-disable vue/no-multiple-template-root -->
<template>
  <div class="login-page">
    <div class="content">
      <div class="left-panel">
        <!-- <h2 class="slogan">让办公无界，让数据无忧！</h2> -->
        <!--<img src="@/assets/login_building.png" alt="宣传图" class="image">-->
        <!-- <div class="icons">
          <img src="@/assets/aq.png" alt="图标1">
          <img src="@/assets/sd.png" alt="图标2">
          <img src="@/assets/cj.png" alt="图标3">
        </div> -->
      </div>
      <div class="right-panel">
        <!-- 服务器配置状态 -->
        <div v-if="showServerConfig">
          <ServerConfig @server-configured="handleServerConfigured" />
        </div>

        <!-- 正常登录状态 -->
        <div v-else-if="!isSecondary">
        <span v-if="auth_type === 'local'" class="title">本地账号登录</span>
          <span v-else-if="isShowTitle" class="title">
            <div style="text-align: center">
              <span class="title" style="height:24px;line-height: 24px;margin: 0 auto;color: #0082ef;font-size: 20px;text-align: center">
                <svg class="icon" aria-hidden="true" style="height: 24px;width: 29px;vertical-align: top;margin-right: 8px;display: inline-block">
                  <use :xlink:href="'#icon-auth-'+ auth_type" />
                </svg>
                {{ attrs.name }}
              </span>
            </div>
          </span>
          <div v-if="auth_id" class="login_panel_form">
            <!-- <component :is="getLoginType"></component> -->
            <component :is="getLoginType" :auth_id="auth_id" :auth_info="attrs" />
            <!-- <LocalLogin v-if="auth_type==='local'" :auth_id="auth_id"></LocalLogin> -->
          </div>
          <div v-if="auth_data.length > 0" class="auth-switcher">
            <div class="auth-switcher-title">
              其他登录方式
            </div>
            <div class="auth-switcher-container">
              <button
                v-if="currentAuthIndex > 0"
                class="auth-nav-btn auth-nav-prev"
                @click="scrollToPrev"
                :disabled="currentAuthIndex === 0"
              >
                <base-icon name="chevron-left" />
              </button>

              <div class="auth-methods-wrapper">
                <div
                  class="auth-methods-container"
                  ref="authMethodsContainer"
                  :style="{ transform: `translateX(-${currentAuthIndex * itemWidth}px)` }"
                >
                  <div
                    v-for="item in auth_data"
                    :key="item.id"
                    class="auth-method-item"
                    @click="selectAuthType(item)"
                  >
                    <div class="auth-method-icon" :data-auth-type="item.type">
                      <base-icon v-if="item.type === 'dingtalk'">
                        <svg class="icon" aria-hidden="true" style="height: 32px; width: 32px; color: #f4a261;">
                          <use xlink:href="#icon-auth-dingtalk" />
                        </svg>
</base-icon>
                      <base-icon :name="item.type" />
                    </div>
                    <div class="auth-method-name">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
              </div>

              <button
                v-if="currentAuthIndex < maxIndex"
                class="auth-nav-btn auth-nav-next"
                @click="scrollToNext"
                :disabled="currentAuthIndex >= maxIndex"
              >
                <base-icon name="chevron-right" />
              </button>
            </div>
          </div>
        </div>
        
        <!-- 二次认证等待状态 -->
        <div v-else class="auth-waiting">
          <div class="waiting-icon">
            <svg class="icon" aria-hidden="true" style="height: 32px; width: 32px; color: #f4a261;">
              <use :xlink:href="`#icon-auth-${originalAuthType || auth_type}`" />
            </svg>
          </div>
          <h4 class="waiting-title">{{ (originalAttrs.name || attrs.name) }} 登录成功</h4>
          <p class="waiting-message">需要进行安全验证以确保账户安全</p>
          <div class="security-tips">
            <base-icon name="shield" style="color: #67c23a;" />
            <span>为了您的账户安全，请完成二次身份验证</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 二次认证弹窗 -->
    <SecondaryAuth
        v-if="isSecondary"
        :auth-info="{ 
            uniqKey: uniqKey,
            contactType: contactType,        
            hasContactInfo: hasContactInfo, 
        }"
        :auth-id="auth_id"
        :user-name="userName" 
        :last-id="last_id"
        :auth-methods="authMethods"
        @verification-success="handleSecondarySuccess"
        @cancel="handleSecondaryCancel"
      />
  </div>
</template>

<script>
export default {
  name: 'Login',
}
</script>

<script setup>
import { ref, computed, provide, watch, defineAsyncComponent } from 'vue'
import { getIdpList } from '@/api/config'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import { Loading } from '@/components/base'
import agentApi from '@/api/agentApi'

// 懒加载登录组件，带加载状态和错误处理
const LocalLogin = defineAsyncComponent({
  loader: () => import('./localLogin/localLogin.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const Wechat = defineAsyncComponent({
  loader: () => import('./wx/wechat.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">微信组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const Feishu = defineAsyncComponent({
  loader: () => import('./feishu/feishu.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">飞书组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const Dingtalk = defineAsyncComponent({
  loader: () => import('./dingtalk/dingtalk.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">钉钉组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const OAuth2 = defineAsyncComponent({
  loader: () => import('./oauth2/oauth2.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const Sms = defineAsyncComponent({
  loader: () => import('@/view/login/sms/sms.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">短信组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const SecondaryAuth = defineAsyncComponent({
  loader: () => import('./secondaryAuth/secondaryAuth.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">二次认证组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})

const ServerConfig = defineAsyncComponent({
  loader: () => import('./serverConfig/serverConfig.vue'),
  loadingComponent: Loading,
  errorComponent: {
    template: '<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'
  },
  delay: 200,
  timeout: 3000
})
const route = useRoute()

const refresh_auth = ref(0)
const auth_list = ref([])
const auth_type = ref('local')
const template_type = ref('')
const auth_id = ref('')
const last_id = ref('')
const attrs = ref([])
const secondary = ref([])
const isSecondary = ref(false)
const showServerConfig = ref(false)
const uniqKey = ref()
const userName = ref('')
const notPhone = ref(false)
const contactType = ref('')           // 已存在，联系方式类型
const hasContactInfo = ref(false)   
// 添加保存原始认证方式的变量
const originalAuthId = ref('')
const originalAuthType = ref('')
const originalTemplateType = ref('')
const originalAttrs = ref({})  // 是否有联系方式

// 认证方式切换相关状态
const currentAuthIndex = ref(0)
const itemWidth = ref(80) // 每个认证方式项的宽度
const visibleItems = ref(3) // 可见的认证方式数量
const authMethodsContainer = ref(null)
const auth_data = computed(() => {
  const currentId = isSecondary.value ? originalAuthId.value : auth_id.value
  return auth_list.value.filter(item => item.id !== currentId)
})

// 计算最大索引
const maxIndex = computed(() => {
  return Math.max(0, auth_data.value.length - visibleItems.value)
})

const userStore = useUserStore()
const secondary_data = computed(() => {
  return secondary.value.filter(item => item.id !== auth_id.value)
})
// 页面渲染前调用
// setup(async() => {
//   try {
//     const idpListData = await getIdpList()
//     if (idpListData.status === 200) {
//       auth_list.value = idpListData.data.idpList
//       auth_id.value = auth_list.value[0].id
//       auth_type.value = auth_list.value[0].type
//       console.log('------')
//       console.log(auth_id.value)
//     }
//     console.log('--idpList---')
//     console.log(idpListData)
//   } catch (error) {
//     console.error(error)
//   }
// })

const extractClientParams = () => {
  const clientParams = {}
  
  // 1. 从当前路由query参数获取
  if (route.query.type) {
    clientParams.type = route.query.type
  }
  if (route.query.wp) {
    clientParams.wp = route.query.wp
  }
  
  // 2. 从redirect参数中提取客户端参数
  if (route.query.redirect && Object.keys(clientParams).length === 0) {
    try {
      const redirectUrl = decodeURIComponent(route.query.redirect)
      // console.log('解码后的redirectUrl:', redirectUrl)
      
      // 处理Hash路由格式: #/layout/dashboard?type=client&wp=50001
      if (redirectUrl.includes('?')) {
        const queryPart = redirectUrl.substring(redirectUrl.indexOf('?') + 1)
        const params = new URLSearchParams(queryPart)
        
        if (params.get('type')) {
          clientParams.type = params.get('type')
        }
        if (params.get('wp')) {
          clientParams.wp = params.get('wp')
        }
        
        // console.log('从redirect提取的客户端参数:', { type: params.get('type'), wp: params.get('wp') })
      }
    } catch (e) {
      console.warn('解析redirect参数失败:', e)
    }
  }
  
  // console.log('最终提取的客户端参数:', clientParams)
  return clientParams
}

// 检查是否需要显示服务器配置
const isShowServerConfig = () => {
  if (agentApi.isClient()) {
    // 检查 URL 参数中是否有 WebUrl
    let webUrl = urlHashParams ? urlHashParams.get('WebUrl') : ''
    // 判断webUrl是否格式 http协议+域名或ip+端口, 如http://*************:8080
    // 解析webUrl
    try {
      const url = new URL(webUrl)
      webUrl = `${url.protocol}//${url.host}`
    } catch (error) {
      webUrl = ''
      console.warn('解析 WebUrl 参数失败:', error)
    }

    if (webUrl) {
      // 有 WebUrl 参数，不需要显示配置页面
      return false
    }

    // 检查是否有保存的服务器地址
    const savedHost = localStorage.getItem('server_host')
    if (!savedHost) {
      // 没有保存的服务器地址，需要显示配置页面
      return true
    }
  }

  return false
}

// 处理服务器配置完成
const handleServerConfigured = (serverUrl) => {
  logger.log('服务器配置完成:', serverUrl)
  showServerConfig.value = false
  // 重新初始化登录页面
  init()
}

const init = async() => {
  try {
    // 检查是否需要显示服务器配置
    if (isShowServerConfig()) {
      showServerConfig.value = true
      return
    }

    const clientParams = extractClientParams()
    if (Object.keys(clientParams).length > 0) {
      localStorage.setItem('client_params', JSON.stringify(clientParams))
      sessionStorage.setItem('client_params', JSON.stringify(clientParams))
      // console.log('登录页初始化时保存客户端参数:', clientParams)
    }
    const idpListData = await getIdpList()
    if (idpListData.status === 200) {
      auth_list.value = idpListData.data.idpList
      const idp_id = route.query.idp_id || userStore.loginType
      if (idp_id && idp_id !== 'undefined') {
        let is_idp = false
        for (const item of idpListData.data.idpList) {
          if (idp_id === item.id) {
            is_idp = true
            auth_id.value = item.id
            auth_type.value = item.type
            template_type.value = item.templateType
            attrs.value = item.attrs
            attrs.value.name = item.name
            attrs.value.authType = item.type
          }
        }
        if (!is_idp) {
          last_id.value = auth_list.value[0]?.id
          auth_id.value = auth_list.value[0]?.id
          auth_type.value = auth_list.value[0]?.type
          template_type.value = auth_list.value[0]?.templateType
          attrs.value = auth_list.value[0]?.attrs
          attrs.value.name = auth_list.value[0].name
          attrs.value.authType = auth_list.value[0]?.type
        }
      } else {
        last_id.value = auth_list.value[0]?.id
        auth_id.value = auth_list.value[0]?.id
        auth_type.value = auth_list.value[0]?.type
        template_type.value = auth_list.value[0]?.templateType
        attrs.value = auth_list.value[0]?.attrs
        attrs.value.name = auth_list.value[0].name
        attrs.value.authType = auth_list.value[0]?.type
      }
      ++refresh_auth.value
    }
  } catch (error) {
    console.error('获取认证列表失败:', error)
    if (agentApi.isClient()) {
      showServerConfig.value = true
    }
  }
}
init()

const getLoginType = computed(() => {
  switch (auth_type.value) {
    case 'local':
    case 'msad':
    case 'ldap':
    case 'web':
      return LocalLogin
    case 'email':
      return LocalLogin
    case 'qiyewx':
      return Wechat
    case 'feishu':
      return Feishu
    case 'dingtalk':
      return Dingtalk
    case 'oauth2':
    case 'cas':
      return OAuth2
    case 'sms':
      return Sms
      // return SecondaryAuth
    default:
      switch (template_type.value) {
        case 'oauth2':
          return OAuth2
      }
      return 'local'
  }
})



const authMethods = computed(() => {
  const methods = [
    {
      type: 'sms',
      name: '短信验证',
      icon: 'duanxin',
      available: contactType.value === 'phone' 
    },
    {
      type: 'email',
      name: '邮箱验证',
      icon: 'email',
      available: contactType.value === 'email' 
    }
  ]
  return methods
})

const handleSecondaryCancel = () => {  
  // 设置 isSecondary 为 false
  isSecondary.value = false
  
  // 重置二次认证相关状态
  secondary.value = []
  uniqKey.value = ''
  userName.value = ''
  contactType.value = ''
  hasContactInfo.value = false
  
  // 恢复到原始认证方式
  if (originalAuthId.value) {
    auth_id.value = originalAuthId.value
    auth_type.value = originalAuthType.value
    template_type.value = originalTemplateType.value
    attrs.value = { ...originalAttrs.value }
    
    // 清空保存的原始状态
    originalAuthId.value = ''
    originalAuthType.value = ''
    originalTemplateType.value = ''
    originalAttrs.value = {}
  }
  
  // 触发重新渲染
  ++refresh_auth.value
  
  console.log('取消后恢复的状态:', {
    isSecondary: isSecondary.value,
    auth_id: auth_id.value,
    auth_type: auth_type.value
  })
}

const handleSecondarySuccess = async (result) => {
  const loadingInstance = Loading.service({
    fullscreen: true,
    text: '认证成功，正在跳转...',
  })

  try {
    // 构建跳转URL，保留客户端参数
    let targetUrl = route.query.redirect_url || '/'

    if (result.clientParams) {
      const params = new URLSearchParams()
      params.set('type', result.clientParams.type)
      if (result.clientParams.wp) {
        params.set('wp', result.clientParams.wp)
      }
      targetUrl += (targetUrl.includes('?') ? '&' : '?') + params.toString()
    }

    window.location.href = targetUrl
  } finally {
    loadingInstance?.close()
  }
}


// 是否打开浏览器方式，内嵌则提供标题和图标
const isOpenBrowser = () => {
  if (auth_type.value === 'cas') {
    return parseInt(attrs.value.casOpenType) === 1
  }
  if (template_type.value === 'oauth2') {
    return parseInt(attrs.value.oauth2OpenType) === 1
  }
  return false
}

// 是否显示标题
const isShowTitle = computed(() => {
  // 使用includes
  if (['dingtalk', 'feishu', 'qiyewx'].includes(auth_type.value)) {
    return false
  }
  if (template_type.value === 'oauth2' || auth_type.value === 'cas') {
    return isOpenBrowser()
  }
  return true;
})

// 认证方式切换方法
const scrollToPrev = () => {
  if (currentAuthIndex.value > 0) {
    currentAuthIndex.value--
  }
}

const scrollToNext = () => {
  if (currentAuthIndex.value < maxIndex.value) {
    currentAuthIndex.value++
  }
}

const selectAuthType = (auth_info) => {
  // 统一处理所有认证类型
  last_id.value = auth_info.id
  attrs.value = auth_info.attrs || {}
  attrs.value.name = auth_info.name
  attrs.value.authType = auth_info.type

  // 区分是否为辅助认证
  if (isSecondary.value) {
    // 辅助认证的额外处理
    attrs.value.uniqKey = uniqKey.value
    attrs.value.notPhone = notPhone.value
  }

  auth_id.value = auth_info.id
  auth_type.value = auth_info.type
  template_type.value = auth_info.templateType
  ++refresh_auth.value
}

watch(isSecondary, async(newData, oldData) => {  
  if (isSecondary.value) {
    // 进入二次认证时，保存当前的认证方式
    originalAuthId.value = auth_id.value
    originalAuthType.value = auth_type.value
    originalTemplateType.value = template_type.value
    originalAttrs.value = { ...attrs.value }

    console.log('二次认证数据:', {
    secondary: secondary.value,
    secondaryLength: secondary.value.length
    })
    
    // 选择第一个二次认证方式
    if (secondary.value.length > 0) {
      selectAuthType(secondary.value[0])
    }
  }
})
provide('secondary', secondary)
provide('isSecondary', isSecondary)
provide('uniqKey', uniqKey)
provide('userName', userName)
provide('notPhone', notPhone)
provide('last_id', last_id)
provide('contactType', contactType)
provide('hasContactInfo', hasContactInfo)
</script>

<style lang="css">
@import "@/style/index.css";
</style>
<style lang="scss">
.login-page {
  .auth-class:hover {
    .avatar {
      border: 1px #204ED9 solid !important;
    }
  }
  .title {
    text-align: center;
    display: block;
    width: 100%;
  }
}

.auth-waiting {
  text-align: center;
  padding: 30px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #dee2e6;
  
  .waiting-icon {
    margin-bottom: 15px;
  }
  
  .waiting-title {
    font-size: 16px;
    color: #495057;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .waiting-message {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 15px;
  }
  
  .security-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    background-color: #f0f9ff;
    border-radius: 6px;
    font-size: 12px;
    color: #1f2937;
  }
}

// 懒加载组件错误状态样式
.error-component {
  text-align: center;
  padding: 20px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  margin: 10px 0;

  &:before {
    content: "⚠️";
    display: block;
    font-size: 24px;
    margin-bottom: 8px;
  }
}

// 认证方式切换器样式
.auth-switcher {
  margin-top: 24px;

  .auth-switcher-title {
    text-align: center;
    color: #929298;
    font-size: 14px;
    margin-bottom: 20px;
    position: relative;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 80px;
      height: 1px;
      background: #e8e8e8;
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
    }
  }

  .auth-switcher-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .auth-nav-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: 1px solid #e8e8e8;
      border-radius: 50%;
      background: #ffffff;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 2;

      &:hover:not(:disabled) {
        border-color: #1890ff;
        color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      &.auth-nav-next {
        margin-left: 8px;
      }
    }

    .auth-methods-wrapper {
      flex: 1;
      max-width: 320px;
      overflow: hidden;
      position: relative;

      .auth-methods-container {
        display: flex;
        transition: transform 0.3s ease;

        .auth-method-item {
          flex: 0 0 80px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 12px 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 8px;

          &:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          .auth-method-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f5f5f7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: #ffffff;
            font-size: 18px;
            transition: all 0.3s ease;
          }

          .auth-method-name {
            font-size: 12px;
            color: #666;
            text-align: center;
            line-height: 1.2;
            max-width: 64px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &:hover .auth-method-icon {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }
}
</style>