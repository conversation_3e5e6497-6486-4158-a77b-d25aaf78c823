/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
import{x as e,_ as a,y as l,u as t,r as n,c as s,b as i,z as u,p as o,h as r,o as v,d as c,e as d,f as p,j as m,m as h,F as y,k as g,t as f,g as _,A as w,B as b,i as C,C as k,L as x,D as L}from"./index.4f1b43e7.js";const T={class:"login-page"},P={class:"content"},O={class:"right-panel"},E={key:0},I={key:0,class:"title"},q={key:1,class:"title"},j={style:{"text-align":"center"}},R={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},S={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},A=["xlink:href"],D={key:2,class:"login_panel_form"},V={key:3,class:"auth-switcher"},U={class:"auth-switcher-container"},$=["disabled"],K={class:"auth-methods-wrapper"},N=["onClick"],z=["data-auth-type"],H={class:"auth-method-name"},J=["disabled"],M={class:"auth-waiting"},W={class:"waiting-icon"},B={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},F=["xlink:href"],X={class:"waiting-title"},G={class:"security-tips"},Q=a(Object.assign({name:"Login"},{setup(a){const Q=l({loader:()=>k((()=>import("./localLogin.1401197f.js")),["./localLogin.1401197f.js","./index.4f1b43e7.js","./index.cf9ef43f.css","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Y=l({loader:()=>k((()=>import("./wechat.a4a85bd4.js")),["./wechat.a4a85bd4.js","./index.4f1b43e7.js","./index.cf9ef43f.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Z=l({loader:()=>k((()=>import("./feishu.94aa0821.js")),["./feishu.94aa0821.js","./index.4f1b43e7.js","./index.cf9ef43f.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ee=l({loader:()=>k((()=>import("./dingtalk.30a7c2a7.js")),["./dingtalk.30a7c2a7.js","./index.4f1b43e7.js","./index.cf9ef43f.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ae=l({loader:()=>k((()=>import("./oauth2.7d4babf7.js")),["./oauth2.7d4babf7.js","./index.4f1b43e7.js","./index.cf9ef43f.css","./oauth2.03d0b5c4.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=l({loader:()=>k((()=>import("./sms.071860b3.js")),["./sms.071860b3.js","./index.4f1b43e7.js","./index.cf9ef43f.css","./sms.844b2c56.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=l({loader:()=>k((()=>import("./secondaryAuth.af467b62.js")),["./secondaryAuth.af467b62.js","./index.4f1b43e7.js","./index.cf9ef43f.css","./verifyCode.3ef2161c.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=l({loader:()=>k((()=>import("./serverConfig.7c65ab97.js")),["./serverConfig.7c65ab97.js","./index.4f1b43e7.js","./index.cf9ef43f.css","./serverConfig.7b10f103.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=t(),ie=n(0),ue=n([]),oe=n("local"),re=n(""),ve=n(""),ce=n(""),de=n([]),pe=n([]),me=n(!1),he=n(!1),ye=n(),ge=n(""),fe=n(!1),_e=n(""),we=n(!1),be=n(""),Ce=n(""),ke=n(""),xe=n({}),Le=n(0),Te=n(80),Pe=n(4),Oe=n(null),Ee=s((()=>{const e=me.value?be.value:ve.value;return ue.value.filter((a=>a.id!==e))})),Ie=s((()=>Math.max(0,Ee.value.length-Pe.value))),qe=i();s((()=>pe.value.filter((e=>e.id!==ve.value))));const je=e=>{logger.log("服务器配置完成:",e),he.value=!1,Re()},Re=async()=>{var a,l,t,n,s,i,u,o,r,v,c,d;try{if((()=>{if(L.isClient()){let a=urlHashParams?urlHashParams.get("WebUrl"):"";try{const e=new URL(a);a=`${e.protocol}//${e.host}`}catch(e){a="",console.warn("解析 WebUrl 参数失败:",e)}if(a)return!1;if(!localStorage.getItem("server_host"))return!0}return!1})())return void(he.value=!0);const p=(()=>{const e={};if(se.query.type&&(e.type=se.query.type),se.query.wp&&(e.wp=se.query.wp),se.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(se.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){ue.value=m.data.idpList;const e=se.query.idp_id||qe.loginType;if(e&&"undefined"!==e){let u=!1;for(const a of m.data.idpList)e===a.id&&(u=!0,ve.value=a.id,oe.value=a.type,re.value=a.templateType,de.value=a.attrs,de.value.name=a.name,de.value.authType=a.type);u||(ce.value=null==(a=ue.value[0])?void 0:a.id,ve.value=null==(l=ue.value[0])?void 0:l.id,oe.value=null==(t=ue.value[0])?void 0:t.type,re.value=null==(n=ue.value[0])?void 0:n.templateType,de.value=null==(s=ue.value[0])?void 0:s.attrs,de.value.name=ue.value[0].name,de.value.authType=null==(i=ue.value[0])?void 0:i.type)}else ce.value=null==(u=ue.value[0])?void 0:u.id,ve.value=null==(o=ue.value[0])?void 0:o.id,oe.value=null==(r=ue.value[0])?void 0:r.type,re.value=null==(v=ue.value[0])?void 0:v.templateType,de.value=null==(c=ue.value[0])?void 0:c.attrs,de.value.name=ue.value[0].name,de.value.authType=null==(d=ue.value[0])?void 0:d.type;++ie.value}}catch(p){console.error("获取认证列表失败:",p),L.isClient()&&(he.value=!0)}};Re();const Se=s((()=>{switch(oe.value){case"local":case"msad":case"ldap":case"web":case"email":return Q;case"qiyewx":return Y;case"feishu":return Z;case"dingtalk":return ee;case"oauth2":case"cas":return ae;case"sms":return le;default:return"oauth2"===re.value?ae:"local"}})),Ae=s((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===_e.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===_e.value}])),De=()=>{me.value=!1,pe.value=[],ye.value="",ge.value="",_e.value="",we.value=!1,be.value&&(ve.value=be.value,oe.value=Ce.value,re.value=ke.value,de.value={...xe.value},be.value="",Ce.value="",ke.value="",xe.value={}),++ie.value,console.log("取消后恢复的状态:",{isSecondary:me.value,auth_id:ve.value,auth_type:oe.value})},Ve=async e=>{const a=x.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=se.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},Ue=s((()=>!["dingtalk","feishu","qiyewx"].includes(oe.value)&&("oauth2"!==re.value&&"cas"!==oe.value||("cas"===oe.value?1===parseInt(de.value.casOpenType):"oauth2"===re.value&&1===parseInt(de.value.oauth2OpenType))))),$e=()=>{Le.value>0&&Le.value--},Ke=()=>{Le.value<Ie.value&&Le.value++},Ne=e=>{ce.value=e.id,de.value=e.attrs||{},de.value.name=e.name,de.value.authType=e.type,me.value&&(de.value.uniqKey=ye.value,de.value.notPhone=fe.value),ve.value=e.id,oe.value=e.type,re.value=e.templateType,++ie.value};return u(me,(async(e,a)=>{me.value&&(be.value=ve.value,Ce.value=oe.value,ke.value=re.value,xe.value={...de.value},console.log("二次认证数据:",{secondary:pe.value,secondaryLength:pe.value.length}),pe.value.length>0&&Ne(pe.value[0]))})),o("secondary",pe),o("isSecondary",me),o("uniqKey",ye),o("userName",ge),o("notPhone",fe),o("last_id",ce),o("contactType",_e),o("hasContactInfo",we),(e,a)=>{const l=r("base-icon");return v(),c("div",T,[d("div",P,[a[3]||(a[3]=d("div",{class:"left-panel"},[p(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),p('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),p(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),d("div",O,[p(" 服务器配置状态 "),he.value?(v(),c("div",E,[m(h(ne),{onServerConfigured:je})])):me.value?(v(),c(y,{key:2},[p(" 二次认证等待状态 "),d("div",M,[d("div",W,[(v(),c("svg",B,[d("use",{"xlink:href":`#icon-auth-${Ce.value||oe.value}`},null,8,F)]))]),d("h4",X,f(xe.value.name||de.value.name)+" 登录成功",1),a[2]||(a[2]=d("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),d("div",G,[m(l,{name:"shield",style:{color:"#67c23a"}}),a[1]||(a[1]=d("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(v(),c(y,{key:1},[p(" 正常登录状态 "),d("div",null,["local"===oe.value?(v(),c("span",I,"本地账号登录")):Ue.value?(v(),c("span",q,[d("div",j,[d("span",R,[(v(),c("svg",S,[d("use",{"xlink:href":"#icon-auth-"+oe.value},null,8,A)])),g(" "+f(de.value.name),1)])])])):p("v-if",!0),ve.value?(v(),c("div",D,[p(' <component :is="getLoginType"></component> '),(v(),_(w(Se.value),{auth_id:ve.value,auth_info:de.value},null,8,["auth_id","auth_info"])),p(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):p("v-if",!0),Ee.value.length>0?(v(),c("div",V,[a[0]||(a[0]=d("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),d("div",U,[Le.value>0?(v(),c("button",{key:0,class:"auth-nav-btn auth-nav-prev",onClick:$e,disabled:0===Le.value},[m(l,{name:"chevron-left"})],8,$)):p("v-if",!0),d("div",K,[d("div",{class:"auth-methods-container",ref_key:"authMethodsContainer",ref:Oe,style:b({transform:`translateX(-${Le.value*Te.value}px)`})},[(v(!0),c(y,null,C(Ee.value,(e=>{return v(),c("div",{key:e.id,class:"auth-method-item",onClick:a=>Ne(e)},[d("div",{class:"auth-method-icon","data-auth-type":e.type},[m(l,{name:(a=e.type,{local:"user",msad:"windows",ldap:"server",email:"mail",qiyewx:"wechat",feishu:"feishu",dingtalk:"dingtalk",oauth2:"shield",cas:"shield",sms:"phone",web:"globe"}[a]||"user")},null,8,["name"])],8,z),d("div",H,f(e.name),1)],8,N);var a})),128))],4)]),Le.value<Ie.value?(v(),c("button",{key:1,class:"auth-nav-btn auth-nav-next",onClick:Ke,disabled:Le.value>=Ie.value},[m(l,{name:"chevron-right"})],8,J)):p("v-if",!0)])])):p("v-if",!0)])],2112))])]),p(" 二次认证弹窗 "),me.value?(v(),_(h(te),{key:0,"auth-info":{uniqKey:ye.value,contactType:_e.value,hasContactInfo:we.value},"auth-id":ve.value,"user-name":ge.value,"last-id":ce.value,"auth-methods":Ae.value,onVerificationSuccess:Ve,onCancel:De},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):p("v-if",!0)])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]);export{Q as default};
