{"version": 3, "file": "##.min.js", "names": ["global", "lastRegisterDeclare", "systemJSPrototype", "System", "constructor", "prototype", "systemRegister", "register", "name", "deps", "declare", "apply", "this", "arguments", "getRegister", "call", "length", "registerDeclare", "_export", "_context", "defaultExport", "hasDefaultExport", "declaration", "value", "__use<PERSON>efault", "default", "execute", "exportName", "Object", "hasOwnProperty", "self"], "sources": ["named-exports.js"], "sourcesContent": ["(function () {\n\n  /*\r\n   * Named exports support for legacy module formats in SystemJS 2.0\r\n   * \r\n   * Note: This extra is deprecated as the behaviour is now the default in core,\r\n   *       so will be removed in the next major.\r\n   */\r\n  (function (global) {\r\n    var systemJSPrototype = global.System.constructor.prototype;\r\n\r\n    // hook System.register to know the last declaration binding\r\n    var lastRegisterDeclare;\r\n    var systemRegister = systemJSPrototype.register;\r\n    systemJSPrototype.register = function (name, deps, declare) {\r\n      lastRegisterDeclare = typeof name === 'string' ? declare : deps;\r\n      systemRegister.apply(this, arguments);\r\n    };\r\n\r\n    var getRegister = systemJSPrototype.getRegister;\r\n    systemJSPrototype.getRegister = function () {\r\n      var register = getRegister.call(this);\r\n      // if it is an actual System.register call, then its ESM\r\n      // -> dont add named exports\r\n      if (!register || register[1] === lastRegisterDeclare || register[1].length === 0)\r\n        return register;\r\n\r\n      // otherwise it was provided by a custom instantiator\r\n      // -> extend the registration with named exports support\r\n      var registerDeclare = register[1];\r\n      register[1] = function (_export, _context) {\r\n        // hook the _export function to note the default export\r\n        var defaultExport, hasDefaultExport = false;\r\n        var declaration = registerDeclare.call(this, function (name, value) {\r\n          if (typeof name === 'object' && name && name.__useDefault)\r\n            defaultExport = name.default, hasDefaultExport = true;\r\n          else if (name === 'default')\r\n            defaultExport = value;\r\n          else if (name === '__useDefault')\r\n            hasDefaultExport = true;\r\n          _export(name, value);\r\n        }, _context);\r\n        // hook the execute function\r\n        var execute = declaration.execute;\r\n        if (execute)\r\n          declaration.execute = function () {\r\n            execute.call(this);\r\n            // do a bulk export of the default export object\r\n            // to export all its names as named exports\r\n\r\n            if (hasDefaultExport)\r\n              for (var exportName in defaultExport) {\r\n                if (\r\n                  Object.prototype.hasOwnProperty.call(defaultExport,  exportName) // Check if epoxrt name is not inherited, safe for Object.create(null)\r\n                  && exportName !== 'default' // default is not a named export\r\n                ) {\r\n                  _export(exportName, defaultExport[exportName]);\r\n                }\r\n              }\r\n          };\r\n        return declaration;\r\n      };\r\n      return register;\r\n    };\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CAQE,SAAWA,GACT,IAGIC,EAHAC,EAAoBF,EAAOG,OAAOC,YAAYC,UAI9CC,EAAiBJ,EAAkBK,SACvCL,EAAkBK,SAAW,SAAUC,EAAMC,EAAMC,GACjDT,EAAsC,iBAATO,EAAoBE,EAAUD,EAC3DH,EAAeK,MAAMC,KAAMC,UAC7B,EAEA,IAAIC,EAAcZ,EAAkBY,YACpCZ,EAAkBY,YAAc,WAC9B,IAAIP,EAAWO,EAAYC,KAAKH,MAGhC,IAAKL,GAAYA,EAAS,KAAON,GAA8C,IAAvBM,EAAS,GAAGS,OAClE,OAAOT,EAIT,IAAIU,EAAkBV,EAAS,GAiC/B,OAhCAA,EAAS,GAAK,SAAUW,EAASC,GAE/B,IAAIC,EAAeC,GAAmB,EAClCC,EAAcL,EAAgBF,KAAKH,MAAM,SAAUJ,EAAMe,GACvC,iBAATf,GAAqBA,GAAQA,EAAKgB,cAC3CJ,EAAgBZ,EAAKiB,QAASJ,GAAmB,GACjC,YAATb,EACPY,EAAgBG,EACA,iBAATf,IACPa,GAAmB,GACrBH,EAAQV,EAAMe,EAChB,GAAGJ,GAECO,EAAUJ,EAAYI,QAiB1B,OAhBIA,IACFJ,EAAYI,QAAU,WAKpB,GAJAA,EAAQX,KAAKH,MAITS,EACF,IAAK,IAAIM,KAAcP,EAEnBQ,OAAOvB,UAAUwB,eAAed,KAAKK,EAAgBO,IACnC,YAAfA,GAEHT,EAAQS,EAAYP,EAAcO,GAG1C,GACKL,CACT,EACOf,CACT,CACD,CAxDD,CAwDmB,oBAATuB,KAAuBA,KAAO9B"}