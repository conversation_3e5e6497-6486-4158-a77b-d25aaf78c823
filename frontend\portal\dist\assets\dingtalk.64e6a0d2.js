/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{a as y,r as f,b as m,z as _,o as d,d as l,e as a,k as w,X as k}from"./index.d0594432.js";const x={style:{"text-align":"center"}},v={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},S={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},T={name:"Dingtalk",mounted(){this.loadThirdPartyScript()},methods:{loadThirdPartyScript(){const e=document.createElement("script");e.src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js",e.onload=()=>{this.doSomethingWithThirdPartyLibrary()},document.body.appendChild(e)}}},K=Object.assign(T,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(e){const c=y(),s=f(0),o=e,u=async()=>{const n={type:"dingtalk",data:{idpId:o.auth_id}},t=await k(n);if(t.status===200)return t.data.uniqKey};m();const r=async()=>{await u();const n=o.auth_info.dingtalkAppKey,t=window.location.host,p=`${window.location.protocol}//${t}`;setTimeout(()=>{window.DTFrameLogin({id:"self_defined_element",width:300,height:300},{redirect_uri:encodeURIComponent(p),client_id:n,scope:"openid",response_type:"code",state:o.auth_id,prompt:"consent"},i=>{const{redirectUrl:q,authCode:h,state:g}=i;c.push({name:"Status",query:{code:h,state:g,auth_type:"dingtalk"},replace:!0})},i=>{i&&console.error("\u9489\u9489\u767B\u5F55\u9519\u8BEF:",i)})},100)};return r(),_(o,async(n,t)=>{s.value++,await r()}),(n,t)=>(d(),l("div",{key:s.value},[a("div",x,[a("span",v,[(d(),l("svg",S,t[0]||(t[0]=[a("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),t[1]||(t[1]=w(" \u9489\u9489\u8BA4\u8BC1 "))])]),t[2]||(t[2]=a("div",{id:"self_defined_element",class:"self-defined-classname"},null,-1))]))}});export{K as default};
