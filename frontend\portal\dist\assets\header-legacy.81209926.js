/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function o(e){for(var o=1;o<arguments.length;o++){var r=null!=arguments[o]?arguments[o]:{};o%2?t(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function n(t,o,n){return(o=function(t){var o=function(t,o){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,o||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==e(o)?o:o+""}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n,t}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,o="function"==typeof Symbol?Symbol:{},n=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function l(o,n,r,a){var l=n&&n.prototype instanceof s?n:s,c=Object.create(l.prototype);return i(c,"_invoke",function(o,n,r){var i,a,l,s=0,c=r||[],u=!1,p={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,o){return i=t,a=0,l=e,p.n=o,d}};function h(o,n){for(a=o,l=n,t=0;!u&&s&&!r&&t<c.length;t++){var r,i=c[t],h=p.p,g=i[2];o>3?(r=g===n)&&(l=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=h&&((r=o<2&&h<i[1])?(a=0,p.v=n,p.n=i[1]):h<g&&(r=o<3||i[0]>n||n>g)&&(i[4]=o,i[5]=n,p.n=g,a=0))}if(r||o>1)return d;throw u=!0,n}return function(r,c,g){if(s>1)throw TypeError("Generator is already running");for(u&&1===c&&h(c,g),a=c,l=g;(t=a<2?e:l)||!u;){i||(a?a<3?(a>1&&(p.n=-1),h(a,l)):p.n=l:p.v=l);try{if(s=2,i){if(a||(r="next"),t=i[r]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,a<2&&(a=0)}else 1===a&&(t=i.return)&&t.call(i),a<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),a=1);i=e}else if((t=(u=p.n<0)?l:o.call(n,p))!==d)break}catch(t){i=e,a=1,l=t}finally{s=1}}return{value:t,done:u}}}(o,r,a),!0),c}var d={};function s(){}function c(){}function u(){}t=Object.getPrototypeOf;var p=[][n]?t(t([][n]())):(i(t={},n,(function(){return this})),t),h=u.prototype=s.prototype=Object.create(p);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,i(e,a,"GeneratorFunction")),e.prototype=Object.create(h),e}return c.prototype=u,i(h,"constructor",u),i(u,"constructor",c),c.displayName="GeneratorFunction",i(u,a,"GeneratorFunction"),i(h),i(h,a,"Generator"),i(h,n,(function(){return this})),i(h,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:l,m:g}})()}function i(e,t,o,n){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,o,n){if(t)r?r(e,t,{value:o,enumerable:!n,configurable:!n,writable:!n}):e[t]=o;else{var a=function(t,o){i(e,t,(function(e){return this._invoke(t,o,e)}))};a("next",0),a("throw",1),a("return",2)}},i(e,t,o,n)}function a(e,t,o,n,r,i,a){try{var l=e[i](a),d=l.value}catch(e){return void o(e)}l.done?t(d):Promise.resolve(d).then(n,r)}function l(e){return function(){var t=this,o=arguments;return new Promise((function(n,r){var i=e.apply(t,o);function l(e){a(i,n,r,l,d,"next",e)}function d(e){a(i,n,r,l,d,"throw",e)}l(void 0)}))}}System.register(["./index-legacy.21dbeba9.js","./ASD-legacy.b6ffb1bc.js"],(function(e,t){"use strict";var n,i,a,d,s,c,u,p,h,g,f,m,w,b=document.createElement("style");return b.textContent='@charset "UTF-8";.layout-header[data-v-30488b75]{height:42px;display:flex;justify-content:space-between;align-items:center;background:linear-gradient(315deg,#536CE6,#647be9);box-shadow:0 2px 6px rgba(46,60,128,.2);color:#fff}.layout-header .header-title[data-v-30488b75]{line-height:42px;font-size:18px;font-weight:500}.layout-header .header-logo[data-v-30488b75]{height:42px;display:flex;align-items:center}.layout-header .header-logo img[data-v-30488b75]{max-width:79px;max-height:28px}.layout-header #u-electron-drag[data-v-30488b75]{display:flex;flex:1;height:100%;-webkit-app-region:drag}.layout-header .right-wrapper[data-v-30488b75]{display:flex;align-items:center;height:100%}.layout-header .right-wrapper>li[data-v-30488b75]:hover{background:#4256b8}.layout-header .right-wrapper .user-divider[data-v-30488b75]{width:1px;height:14px;margin-left:16px;margin-right:16px;background:#e6e6e6}.layout-header .right-wrapper .base-dropdown[data-v-30488b75]{position:relative;display:inline-block}.layout-header .right-wrapper .user-info[data-v-30488b75]{display:flex;align-items:center;height:42px;padding:0 14px;cursor:pointer}.layout-header .right-wrapper .user-info .user-face[data-v-30488b75]{width:32px;height:32px;border-radius:50%;overflow:hidden;margin-right:6px}.layout-header .right-wrapper .user-info .user-face img[data-v-30488b75]{width:100%;height:100%;display:block}.layout-header .right-wrapper .user-info .user-name[data-v-30488b75]{color:#fff;display:inline-block;max-width:100px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;word-break:break-all}.layout-header .right-wrapper .dropdown-menu[data-v-30488b75]{position:absolute;top:100%;right:0;z-index:1000;width:114px;background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1)}.layout-header .right-wrapper .dropdown-menu .dropdown-item[data-v-30488b75]{display:flex;padding-left:40px;position:relative;align-items:center;justify-content:flex-start;width:98px;height:28px;margin:8px;border-radius:4px;font-size:14px;color:#282a33;cursor:pointer;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular}.layout-header .right-wrapper .dropdown-menu .dropdown-item .dropdown-item-icon[data-v-30488b75]{position:absolute;font-size:14px;display:flex;align-items:center;width:14px;height:14px;top:9px;left:10px}.layout-header .right-wrapper .dropdown-menu .dropdown-item .dropdown-item-text[data-v-30488b75]{width:56px;height:20px;position:absolute;right:8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular!important;text-align:left;line-height:20px}.layout-header .right-wrapper .dropdown-menu .dropdown-item[data-v-30488b75]:hover{background-color:#ff4d4d;color:#fff}.layout-header .right-wrapper .dropdown-menu .dropdown-item[data-v-30488b75]:active{background-color:#e6e8eb}.layout-header .right-wrapper .set-icon-wrapper[data-v-30488b75],.layout-header .right-wrapper .menu-msg[data-v-30488b75]{width:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;height:42px;position:relative}.layout-header .right-wrapper .set-icon-wrapper .icon-shezhi[data-v-30488b75],.layout-header .right-wrapper .menu-msg .icon-shezhi[data-v-30488b75]{color:#bac4f5;font-size:18px}.layout-header .right-wrapper .is-message[data-v-30488b75]:after{content:"";position:absolute;top:17px;right:13px;width:8px;height:8px;border-radius:50%;background:#FF4D4D}.layout-header .right-wrapper .window-operate[data-v-30488b75],.layout-header .right-wrapper #ui-headNav-header-li-msg_list[data-v-30488b75]{width:24px;height:100%;margin-left:4px;filter:brightness(1.5);display:flex;align-items:center;justify-content:center;cursor:pointer}.layout-header .right-wrapper .window-operate .iconfont[data-v-30488b75],.layout-header .right-wrapper #ui-headNav-header-li-msg_list .iconfont[data-v-30488b75]{color:#bac4f5;font-size:16px}.count-title[data-v-30488b75]{color:#686e84}.count-title i[data-v-30488b75]{font-style:normal;color:#3c404d}.s-title[data-v-30488b75]{margin-top:18px;margin-left:18px;font-size:13px;line-height:18px;font-weight:500;color:#3c404d}.s-content[data-v-30488b75]{padding:24px 32px 29px;font-size:13px;line-height:18px}.s-content .s-text[data-v-30488b75]{color:#686e84}.change-reg-info[data-v-30488b75]{padding-left:8px;line-height:20px;font-size:14px;font-weight:500;color:#3c404d}body .el-dialog-ip-box{width:260px}body .el-dialog-ip-box .el-message-box__content{padding:20px 15px}.s-content .el-radio{margin-right:13px}.s-content .el-radio .el-radio__label{padding-left:8px;font-size:13px;color:#3c404d;line-height:18px}#ip-info-dialog .ip-content{margin-top:24px;margin-bottom:24px;padding:0 24px;line-height:20px;font-size:14px;color:#3c404d}#ip-info-dialog .netcard-list{margin-top:16px;padding:0 24px}#ip-info-dialog .netcard-list li{display:flex;align-items:center;line-height:20px;font-size:14px;color:#3c404d;margin-bottom:10px}#ip-info-dialog .netcard-list li:last-child{margin-bottom:24px}#ip-info-dialog .netcard-list li i{font-size:16px;margin-left:16px}#ip-info-dialog .netcard-list li .icon-lianjie{color:#29cc88}#ip-info-dialog .netcard-list li .icon-duankailianjie{color:#e65353}#ip-info-dialog .el-dialog__footer button{height:40px;line-height:40px;border-bottom-right-radius:4px}.loginout-m-confirm-dialog .v-header{line-height:45px;border-bottom:1px solid #EDEDF1;padding:0 24px;font-size:16px;color:#3c404d}.loginout-m-confirm-dialog .v-header i{font-size:16px;color:#ffbf00;margin-right:6px;font-weight:400}.loginout-m-confirm-dialog .outline-tips{padding:24px;line-height:20px;color:#3c404d;font-size:14px}\n',document.head.appendChild(b),{setters:[function(e){n=e._,i=e.D,a=e.h,d=e.G,s=e.o,c=e.d,u=e.e,p=e.f,h=e.H,g=e.t,f=e.j,m=e.I},function(e){w=e._}],execute:function(){var b=""+new URL("avator.bd83723a.png",t.meta.url).href,x={name:"ClientHeader",data:function(){return{drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1,dropdownVisible:!1}},computed:{isAccess:function(){return!1}},watch:{userId:function(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted:function(){},beforeDestroy:function(){},methods:{minimizeWnd:function(){i.minimizeWnd()},maximizeWndOrNot:function(){this.isMaxWindow?(i.normalnizeWnd(),this.isMaxWindow=!1):(i.maximizeWnd(),this.isMaxWindow=!0)},toggleDropdown:function(){this.dropdownVisible=!this.dropdownVisible},closeDropdown:function(){this.dropdownVisible=!1},dropdownVisiHandle:function(){},closeWnd:function(){return l(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:i.hideWend();case 1:return e.a(2)}}),e)})))()},setHandle:function(e){var t=this;return l(r().m((function o(){var n;return r().w((function(o){for(;;)switch(o.n){case 0:"changeLange"===e?(n=t.$i18n.locale,setLang("zh"===n?"en":"zh")):"changeMode"===e&&t.changeMode();case 1:return o.a(2)}}),o)})))()},userMenuHandle:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(this.closeDropdown(),this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},logoutHandle:function(){var e=this;return l(r().m((function t(){var n,a,l,d;return r().w((function(t){for(;;)switch(t.n){case 0:if(e.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1!==e.logoutType){t.n=9;break}if(t.p=1,!e.isSsoAuth()){t.n=3;break}return t.n=2,ssoLogout(_.get(e.clientInfo,"accessStatus.lastAuthType"));case 2:n=t.v;case 3:return t.n=4,proxyApi.cutoffDevice({device_id:_.get(e.clientInfo,"detail.DeviceID",0),remark:"LogOut"});case 4:if(a=t.v,0===parseInt(_.get(a,"errcode"))){t.n=5;break}return _.get(a,"errmsg")||e.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),loading.destory(),t.a(2);case 5:return commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),t.n=6,i.logOut({IsCredibleDevice:_.get(e.clientInfo,"detail.IsTrustDev","0")});case 6:e.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,e.isDot1xMode&&e.setClientInfo(_.merge({},e.clientInfo,{basic:{IsOnline:0}})),e.setAuthInfo(o(o({},e.authInfo),{basic:{}})),e.setClientInfo(o(o({},e.clientInfo),{accessStatus:{}})),l=(new Date).getTime(),e.$router.push({name:"message",params:{forceTo:!0},query:{t:l}}),_.isString(n)&&""!==n&&(console.log("logoutUrl:".logoutUrl),i.windowOpenUrl(n)),t.n=8;break;case 7:t.p=7,d=t.v,console.error("退出登录错误",d);case 8:loading.destory();case 9:return t.a(2)}}),t,null,[[1,7]])})))()},getCountMenuWidth:function(){var e=this;return l(r().m((function t(){var o,n,a;return r().w((function(t){for(;;)switch(t.n){case 0:return o=e.isZtpUser?44:0,n=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0),t.p=1,t.n=2,i.init();case 2:return t.n=3,i.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(n)+o});case 3:t.n=5;break;case 4:t.p=4,a=t.v,console.warn("设置标题尺寸失败:",a);case 5:return t.a(2)}}),t,null,[[1,4]])})))()},hdEventHandle:function(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer:function(){this.deviceInnerBack=!1},changeVisible:function(e){this.drawer=e}}},v={class:"layout-header"},y={id:"u-header-menu",class:"right-wrapper"},k={id:"u-avator",ref:"countMenu"},O={class:"user-info"},S={class:"user-name"},j={class:"dropdown-menu header-count-menu"};e("default",n(x,[["render",function(e,t,o,n,r,i){var l=a("base-icon"),x=d("click-outside");return s(),c("div",v,[t[5]||(t[5]=u("div",{class:"header-logo"},[p("如果图片加载失败就隐藏"),u("img",{src:w,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),t[6]||(t[6]=u("div",{id:"u-electron-drag"},null,-1)),u("ul",y,[u("li",k,[h((s(),c("div",{class:"base-dropdown",id:"ui-headNav-header-div-account_info",onClick:t[1]||(t[1]=function(){return i.toggleDropdown&&i.toggleDropdown.apply(i,arguments)})},[u("div",O,[t[2]||(t[2]=u("div",{class:"user-face"},[u("img",{src:b,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),u("span",S,g(r.username),1)]),h(u("div",j,[u("div",{class:"dropdown-item",id:"ui-headNav-header-li-cancel_account",onClick:t[0]||(t[0]=function(e){return i.userMenuHandle("lougOut")})},[f(l,{class:"dropdown-item-icon",name:"logout"}),t[3]||(t[3]=u("span",{class:"dropdown-item-text"},"注销登录",-1))])],512),[[m,r.dropdownVisible]])])),[[x,i.closeDropdown]])],512),t[4]||(t[4]=u("div",{class:"user-divider"},null,-1)),f(l,{class:"window-operate",name:r.isMaxWindow?"fullscreen_exit":"fullscreen",onClick:i.maximizeWndOrNot},null,8,["name","onClick"]),f(l,{class:"window-operate",name:"minus",onClick:i.minimizeWnd},null,8,["onClick"]),f(l,{class:"window-operate",name:"close",style:{"margin-right":"16px"},onClick:i.closeWnd},null,8,["onClick"])])])}],["__scopeId","data-v-30488b75"],["__file","D:/asec-platform/frontend/portal/src/view/client/header.vue"]]))}}}))}();
