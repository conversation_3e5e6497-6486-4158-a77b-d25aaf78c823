{"version": 3, "file": "##.min.js", "names": ["resolveIfNotPlainOrUrl", "relUrl", "parentUrl", "indexOf", "replace", "backslashRegEx", "slice", "length", "pathname", "parentProtocol", "segmented", "lastIndexOf", "output", "segmentIndex", "i", "push", "pop", "join", "baseUrl", "document", "baseEl", "querySelector", "href", "location", "lastSepIndex", "split", "global", "systemJSPrototype", "System", "constructor", "prototype", "moduleTypesRegEx", "_should<PERSON><PERSON>ch", "shouldFetch", "bind", "url", "test", "jsonContentType", "cssContentType", "wasmContentType", "fetch", "options", "then", "res", "passThrough", "ok", "contentType", "headers", "get", "json", "Response", "Blob", "JSON", "stringify", "type", "text", "source", "match", "quotes", "relUrl1", "relUrl2", "WebAssembly", "compileStreaming", "arrayBuffer", "compile", "module", "wasmModules", "Object", "create", "deps", "setterSources", "<PERSON><PERSON><PERSON>", "imports", "for<PERSON>ach", "impt", "key", "self"], "sources": ["module-types.js"], "sourcesContent": ["(function () {\n\n  var hasDocument = typeof document !== 'undefined';\r\n\r\n  var baseUrl;\r\n\r\n  if (hasDocument) {\r\n    var baseEl = document.querySelector('base[href]');\r\n    if (baseEl)\r\n      baseUrl = baseEl.href;\r\n  }\r\n\r\n  if (!baseUrl && typeof location !== 'undefined') {\r\n    baseUrl = location.href.split('#')[0].split('?')[0];\r\n    var lastSepIndex = baseUrl.lastIndexOf('/');\r\n    if (lastSepIndex !== -1)\r\n      baseUrl = baseUrl.slice(0, lastSepIndex + 1);\r\n  }\r\n\r\n  var backslashRegEx = /\\\\/g;\r\n  function resolveIfNotPlainOrUrl (relUrl, parentUrl) {\r\n    if (relUrl.indexOf('\\\\') !== -1)\r\n      relUrl = relUrl.replace(backslashRegEx, '/');\r\n    // protocol-relative\r\n    if (relUrl[0] === '/' && relUrl[1] === '/') {\r\n      return parentUrl.slice(0, parentUrl.indexOf(':') + 1) + relUrl;\r\n    }\r\n    // relative-url\r\n    else if (relUrl[0] === '.' && (relUrl[1] === '/' || relUrl[1] === '.' && (relUrl[2] === '/' || relUrl.length === 2 && (relUrl += '/')) ||\r\n        relUrl.length === 1  && (relUrl += '/')) ||\r\n        relUrl[0] === '/') {\r\n      var parentProtocol = parentUrl.slice(0, parentUrl.indexOf(':') + 1);\r\n      // Disabled, but these cases will give inconsistent results for deep backtracking\r\n      //if (parentUrl[parentProtocol.length] !== '/')\r\n      //  throw Error('Cannot resolve');\r\n      // read pathname from parent URL\r\n      // pathname taken to be part after leading \"/\"\r\n      var pathname;\r\n      if (parentUrl[parentProtocol.length + 1] === '/') {\r\n        // resolving to a :// so we need to read out the auth and host\r\n        if (parentProtocol !== 'file:') {\r\n          pathname = parentUrl.slice(parentProtocol.length + 2);\r\n          pathname = pathname.slice(pathname.indexOf('/') + 1);\r\n        }\r\n        else {\r\n          pathname = parentUrl.slice(8);\r\n        }\r\n      }\r\n      else {\r\n        // resolving to :/ so pathname is the /... part\r\n        pathname = parentUrl.slice(parentProtocol.length + (parentUrl[parentProtocol.length] === '/'));\r\n      }\r\n\r\n      if (relUrl[0] === '/')\r\n        return parentUrl.slice(0, parentUrl.length - pathname.length - 1) + relUrl;\r\n\r\n      // join together and split for removal of .. and . segments\r\n      // looping the string instead of anything fancy for perf reasons\r\n      // '../../../../../z' resolved to 'x/y' is just 'z'\r\n      var segmented = pathname.slice(0, pathname.lastIndexOf('/') + 1) + relUrl;\r\n\r\n      var output = [];\r\n      var segmentIndex = -1;\r\n      for (var i = 0; i < segmented.length; i++) {\r\n        // busy reading a segment - only terminate on '/'\r\n        if (segmentIndex !== -1) {\r\n          if (segmented[i] === '/') {\r\n            output.push(segmented.slice(segmentIndex, i + 1));\r\n            segmentIndex = -1;\r\n          }\r\n        }\r\n\r\n        // new segment - check if it is relative\r\n        else if (segmented[i] === '.') {\r\n          // ../ segment\r\n          if (segmented[i + 1] === '.' && (segmented[i + 2] === '/' || i + 2 === segmented.length)) {\r\n            output.pop();\r\n            i += 2;\r\n          }\r\n          // ./ segment\r\n          else if (segmented[i + 1] === '/' || i + 1 === segmented.length) {\r\n            i += 1;\r\n          }\r\n          else {\r\n            // the start of a new segment as below\r\n            segmentIndex = i;\r\n          }\r\n        }\r\n        // it is the start of a new segment\r\n        else {\r\n          segmentIndex = i;\r\n        }\r\n      }\r\n      // finish reading out the last segment\r\n      if (segmentIndex !== -1)\r\n        output.push(segmented.slice(segmentIndex));\r\n      return parentUrl.slice(0, parentUrl.length - pathname.length) + output.join('');\r\n    }\r\n  }\r\n\r\n  /*\r\n   * Import maps implementation\r\n   *\r\n   * To make lookups fast we pre-resolve the entire import map\r\n   * and then match based on backtracked hash lookups\r\n   *\r\n   */\r\n\r\n  function resolveUrl (relUrl, parentUrl) {\r\n    return resolveIfNotPlainOrUrl(relUrl, parentUrl) || (relUrl.indexOf(':') !== -1 ? relUrl : resolveIfNotPlainOrUrl('./' + relUrl, parentUrl));\r\n  }\n\n  /*\r\n   * Loads JSON, CSS, Wasm module types based on file extension\r\n   * filters and content type verifications\r\n   */\r\n  (function(global) {\r\n    var systemJSPrototype = global.System.constructor.prototype;\r\n\r\n    var moduleTypesRegEx = /^[^#?]+\\.(css|html|json|wasm)([?#].*)?$/;\r\n    var _shouldFetch = systemJSPrototype.shouldFetch.bind(systemJSPrototype);\r\n    systemJSPrototype.shouldFetch = function (url) {\r\n      return _shouldFetch(url) || moduleTypesRegEx.test(url);\r\n    };\r\n\r\n    var jsonContentType = /^application\\/json(;|$)/;\r\n    var cssContentType = /^text\\/css(;|$)/;\r\n    var wasmContentType = /^application\\/wasm(;|$)/;\r\n\r\n    var fetch = systemJSPrototype.fetch;\r\n    systemJSPrototype.fetch = function (url, options) {\r\n      return fetch(url, options)\r\n      .then(function (res) {\r\n        if (options.passThrough)\r\n          return res;\r\n\r\n        if (!res.ok)\r\n          return res;\r\n        var contentType = res.headers.get('content-type');\r\n        if (jsonContentType.test(contentType))\r\n          return res.json()\r\n          .then(function (json) {\r\n            return new Response(new Blob([\r\n              'System.register([],function(e){return{execute:function(){e(\"default\",' + JSON.stringify(json) + ')}}})'\r\n            ], {\r\n              type: 'application/javascript'\r\n            }));\r\n          });\r\n        if (cssContentType.test(contentType))\r\n          return res.text()\r\n          .then(function (source) {\r\n            source = source.replace(/url\\(\\s*(?:([\"'])((?:\\\\.|[^\\n\\\\\"'])+)\\1|((?:\\\\.|[^\\s,\"'()\\\\])+))\\s*\\)/g, function (match, quotes, relUrl1, relUrl2) {\r\n              return ['url(', quotes, resolveUrl(relUrl1 || relUrl2, url), quotes, ')'].join('');\r\n            });\r\n            return new Response(new Blob([\r\n              'System.register([],function(e){return{execute:function(){var s=new CSSStyleSheet();s.replaceSync(' + JSON.stringify(source) + ');e(\"default\",s)}}})'\r\n            ], {\r\n              type: 'application/javascript'\r\n            }));\r\n          });\r\n        if (wasmContentType.test(contentType))\r\n          return (WebAssembly.compileStreaming ? WebAssembly.compileStreaming(res) : res.arrayBuffer().then(WebAssembly.compile))\r\n          .then(function (module) {\r\n            if (!global.System.wasmModules)\r\n              global.System.wasmModules = Object.create(null);\r\n            global.System.wasmModules[url] = module;\r\n            // we can only set imports if supported (eg early Safari doesnt support)\r\n            var deps = [];\r\n            var setterSources = [];\r\n            if (WebAssembly.Module.imports)\r\n              WebAssembly.Module.imports(module).forEach(function (impt) {\r\n                var key = JSON.stringify(impt.module);\r\n                if (deps.indexOf(key) === -1) {\r\n                  deps.push(key);\r\n                  setterSources.push('function(m){i[' + key + ']=m}');\r\n                }\r\n              });\r\n            return new Response(new Blob([\r\n              'System.register([' + deps.join(',') + '],function(e){var i={};return{setters:[' + setterSources.join(',') +\r\n              '],execute:function(){return WebAssembly.instantiate(System.wasmModules[' + JSON.stringify(url) +\r\n              '],i).then(function(m){e(m.exports)})}}})'\r\n            ], {\r\n              type: 'application/javascript'\r\n            }));\r\n          });\r\n        return res;\r\n      });\r\n    };\r\n  })(typeof self !== 'undefined' ? self : global);\n\n})();\n"], "mappings": "CAAA,WAoBE,SAASA,EAAwBC,EAAQC,GAIvC,IAH8B,IAA1BD,EAAOE,QAAQ,QACjBF,EAASA,EAAOG,QAAQC,EAAgB,MAExB,MAAdJ,EAAO,IAA4B,MAAdA,EAAO,GAC9B,OAAOC,EAAUI,MAAM,EAAGJ,EAAUC,QAAQ,KAAO,GAAKF,EAGrD,GAAkB,MAAdA,EAAO,KAA6B,MAAdA,EAAO,IAA4B,MAAdA,EAAO,KAA6B,MAAdA,EAAO,IAAgC,IAAlBA,EAAOM,SAAiBN,GAAU,OAC3G,IAAlBA,EAAOM,SAAkBN,GAAU,OACrB,MAAdA,EAAO,GAAY,CACrB,IAMIO,EANAC,EAAiBP,EAAUI,MAAM,EAAGJ,EAAUC,QAAQ,KAAO,GAsBjE,GAXIK,EAJyC,MAAzCN,EAAUO,EAAeF,OAAS,GAEb,UAAnBE,GACFD,EAAWN,EAAUI,MAAMG,EAAeF,OAAS,IAC/BD,MAAME,EAASL,QAAQ,KAAO,GAGvCD,EAAUI,MAAM,GAKlBJ,EAAUI,MAAMG,EAAeF,QAA+C,MAArCL,EAAUO,EAAeF,UAG7D,MAAdN,EAAO,GACT,OAAOC,EAAUI,MAAM,EAAGJ,EAAUK,OAASC,EAASD,OAAS,GAAKN,EAStE,IAJA,IAAIS,EAAYF,EAASF,MAAM,EAAGE,EAASG,YAAY,KAAO,GAAKV,EAE/DW,EAAS,GACTC,GAAgB,EACXC,EAAI,EAAGA,EAAIJ,EAAUH,OAAQO,KAEd,IAAlBD,EACmB,MAAjBH,EAAUI,KACZF,EAAOG,KAAKL,EAAUJ,MAAMO,EAAcC,EAAI,IAC9CD,GAAgB,GAKM,MAAjBH,EAAUI,GAEQ,MAArBJ,EAAUI,EAAI,IAAoC,MAArBJ,EAAUI,EAAI,IAAcA,EAAI,IAAMJ,EAAUH,OAKnD,MAArBG,EAAUI,EAAI,IAAcA,EAAI,IAAMJ,EAAUH,OACvDO,GAAK,EAILD,EAAeC,GATfF,EAAOI,MACPF,GAAK,GAaPD,EAAeC,EAMnB,OAFsB,IAAlBD,GACFD,EAAOG,KAAKL,EAAUJ,MAAMO,IACvBX,EAAUI,MAAM,EAAGJ,EAAUK,OAASC,EAASD,QAAUK,EAAOK,KAAK,GAC9E,CACF,CAhGA,IAEIC,EAEJ,GAJsC,oBAAbC,SAIR,CACf,IAAIC,EAASD,SAASE,cAAc,cAChCD,IACFF,EAAUE,EAAOE,KACrB,CAEA,IAAKJ,GAA+B,oBAAbK,SAA0B,CAE/C,IAAIC,GADJN,EAAUK,SAASD,KAAKG,MAAM,KAAK,GAAGA,MAAM,KAAK,IACtBd,YAAY,MACjB,IAAlBa,IACFN,EAAUA,EAAQZ,MAAM,EAAGkB,EAAe,GAC9C,CAEA,IAAInB,EAAiB,OAiGrB,SAAUqB,GACR,IAAIC,EAAoBD,EAAOE,OAAOC,YAAYC,UAE9CC,EAAmB,0CACnBC,EAAeL,EAAkBM,YAAYC,KAAKP,GACtDA,EAAkBM,YAAc,SAAUE,GACxC,OAAOH,EAAaG,IAAQJ,EAAiBK,KAAKD,EACpD,EAEA,IAAIE,EAAkB,0BAClBC,EAAiB,kBACjBC,EAAkB,0BAElBC,EAAQb,EAAkBa,MAC9Bb,EAAkBa,MAAQ,SAAUL,EAAKM,GACvC,OAAOD,EAAML,EAAKM,GACjBC,MAAK,SAAUC,GACd,GAAIF,EAAQG,YACV,OAAOD,EAET,IAAKA,EAAIE,GACP,OAAOF,EACT,IAAIG,EAAcH,EAAII,QAAQC,IAAI,gBAClC,OAAIX,EAAgBD,KAAKU,GAChBH,EAAIM,OACVP,MAAK,SAAUO,GACd,OAAO,IAAIC,SAAS,IAAIC,KAAK,CAC3B,wEAA0EC,KAAKC,UAAUJ,GAAQ,SAChG,CACDK,KAAM,2BAEV,IACEhB,EAAeF,KAAKU,GACfH,EAAIY,OACVb,MAAK,SAAUc,GAId,OAHAA,EAASA,EAAOpD,QAAQ,0EAA0E,SAAUqD,EAAOC,EAAQC,EAASC,GAClI,MAAO,CAAC,OAAQF,GA5CPzD,EA4C0B0D,GAAWC,EA5C7B1D,EA4CsCiC,EA3C1DnC,EAAuBC,EAAQC,MAAwC,IAAzBD,EAAOE,QAAQ,KAAcF,EAASD,EAAuB,KAAOC,EAAQC,KA2C1DwD,EAAQ,KAAKzC,KAAK,IA5C3F,IAAqBhB,EAAQC,CA6CnB,IACO,IAAIgD,SAAS,IAAIC,KAAK,CAC3B,oGAAsGC,KAAKC,UAAUG,GAAU,wBAC9H,CACDF,KAAM,2BAEV,IACEf,EAAgBH,KAAKU,IACfe,YAAYC,iBAAmBD,YAAYC,iBAAiBnB,GAAOA,EAAIoB,cAAcrB,KAAKmB,YAAYG,UAC7GtB,MAAK,SAAUuB,GACTvC,EAAOE,OAAOsC,cACjBxC,EAAOE,OAAOsC,YAAcC,OAAOC,OAAO,OAC5C1C,EAAOE,OAAOsC,YAAY/B,GAAO8B,EAEjC,IAAII,EAAO,GACPC,EAAgB,GASpB,OARIT,YAAYU,OAAOC,SACrBX,YAAYU,OAAOC,QAAQP,GAAQQ,SAAQ,SAAUC,GACnD,IAAIC,EAAMvB,KAAKC,UAAUqB,EAAKT,SACH,IAAvBI,EAAKlE,QAAQwE,KACfN,EAAKtD,KAAK4D,GACVL,EAAcvD,KAAK,iBAAmB4D,EAAM,QAEhD,IACK,IAAIzB,SAAS,IAAIC,KAAK,CAC3B,oBAAsBkB,EAAKpD,KAAK,KAAO,0CAA4CqD,EAAcrD,KAAK,KACtG,0EAA4EmC,KAAKC,UAAUlB,GAC3F,4CACC,CACDmB,KAAM,2BAEV,IACKX,CACT,GACF,CACD,CAxED,CAwEmB,oBAATiC,KAAuBA,KAAOlD,OAEzC,CA9LD"}