/*! 
 Build based on gin-vue-admin 
 Time : 1749637440000 */
import{_ as e,J as t,r as a,z as n,h as o,o as s,f as u,w as l,d as r,C as c,g as i,e as f,t as m,F as d,P as p}from"./index.8e727eba.js";const v={key:0,class:"gva-subMenu"},b=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({bd446398:x.value,"660a68d3":I.value})));const b=e,h=a(b.theme.activeBackground),I=a(b.theme.activeText),x=a(b.theme.normalText);return n((()=>b.theme),(()=>{h.value=b.theme.activeBackground,I.value=b.theme.activeText,x.value=b.theme.normalText})),(t,a)=>{const n=o("el-sub-menu");return s(),u(n,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(d,{key:1},[e.routerInfo.meta.icon?(s(),r("i",{key:0,class:c(["iconfont",e.routerInfo.meta.icon])},null,2)):i("",!0),f("span",null,m(e.routerInfo.meta.title),1)],64)):(s(),r("div",v,[e.routerInfo.meta.icon?(s(),r("i",{key:0,class:c(["iconfont",e.routerInfo.meta.icon])},null,2)):i("",!0),f("span",null,m(e.routerInfo.meta.title),1)]))])),default:l((()=>[p(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-0ecf9b8a"]]);export{b as default};
