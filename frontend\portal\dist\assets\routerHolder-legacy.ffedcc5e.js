/*! 
 Build based on gin-vue-admin 
 Time : 1749628938000 */
System.register(["./index-legacy.8fdee67f.js"],(function(e,n){"use strict";var t,u,r,i,o,f,l,a,c,d,s;return{setters:[function(e){t=e.S,u=e.h,r=e.o,i=e.d,o=e.j,f=e.w,l=e.T,a=e.f,c=e.W,d=e.m,s=e.A}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[o(v,null,{default:f((function(e){var t=e.Component;return[o(l,{mode:"out-in",name:"el-fade-in-linear"},{default:f((function(){return[(r(),a(c,{include:d(n).keepAliveRouters},[(r(),a(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
