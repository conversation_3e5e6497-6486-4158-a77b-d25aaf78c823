<template>
  <div 
    ref="scrollContainer"
    :class="[
      'base-scrollbar',
      { 'base-scrollbar--always': always }
    ]"
    :style="containerStyle"
    @scroll="handleScroll"
  >
    <div 
      ref="scrollContent"
      class="base-scrollbar__content"
      :style="contentStyle"
    >
      <slot />
    </div>
    
    <!-- 垂直滚动条 -->
    <div 
      v-show="showVerticalBar"
      class="base-scrollbar__bar base-scrollbar__bar--vertical"
      @mousedown="handleBarMouseDown('vertical', $event)"
    >
      <div 
        class="base-scrollbar__thumb"
        :style="verticalThumbStyle"
      />
    </div>
    
    <!-- 水平滚动条 -->
    <div 
      v-show="showHorizontalBar"
      class="base-scrollbar__bar base-scrollbar__bar--horizontal"
      @mousedown="handleBarMouseDown('horizontal', $event)"
    >
      <div 
        class="base-scrollbar__thumb"
        :style="horizontalThumbStyle"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseScrollbar',
  props: {
    height: {
      type: String,
      default: ''
    },
    maxHeight: {
      type: String,
      default: ''
    },
    always: {
      type: Boolean,
      default: false
    },
    tag: {
      type: String,
      default: 'div'
    }
  },
  data() {
    return {
      scrollTop: 0,
      scrollLeft: 0,
      scrollHeight: 0,
      scrollWidth: 0,
      clientHeight: 0,
      clientWidth: 0,
      isDragging: false,
      dragDirection: '',
      startY: 0,
      startX: 0,
      startScrollTop: 0,
      startScrollLeft: 0
    }
  },
  computed: {
    containerStyle() {
      const style = {}
      if (this.height) style.height = this.height
      if (this.maxHeight) style.maxHeight = this.maxHeight
      return style
    },
    contentStyle() {
      return {}
    },
    showVerticalBar() {
      return this.always || this.scrollHeight > this.clientHeight
    },
    showHorizontalBar() {
      return this.always || this.scrollWidth > this.clientWidth
    },
    verticalThumbStyle() {
      const height = Math.max((this.clientHeight / this.scrollHeight) * 100, 10)
      const top = (this.scrollTop / (this.scrollHeight - this.clientHeight)) * (100 - height)
      return {
        height: `${height}%`,
        transform: `translateY(${top}%)`
      }
    },
    horizontalThumbStyle() {
      const width = Math.max((this.clientWidth / this.scrollWidth) * 100, 10)
      const left = (this.scrollLeft / (this.scrollWidth - this.clientWidth)) * (100 - width)
      return {
        width: `${width}%`,
        transform: `translateX(${left}%)`
      }
    }
  },
  mounted() {
    this.updateScrollInfo()
    this.addEventListeners()
  },
  beforeUnmount() {
    this.removeEventListeners()
  },
  methods: {
    updateScrollInfo() {
      const container = this.$refs.scrollContainer
      if (!container) return
      
      this.scrollTop = container.scrollTop
      this.scrollLeft = container.scrollLeft
      this.scrollHeight = container.scrollHeight
      this.scrollWidth = container.scrollWidth
      this.clientHeight = container.clientHeight
      this.clientWidth = container.clientWidth
    },
    handleScroll() {
      this.updateScrollInfo()
    },
    handleBarMouseDown(direction, event) {
      event.preventDefault()
      this.isDragging = true
      this.dragDirection = direction
      
      if (direction === 'vertical') {
        this.startY = event.clientY
        this.startScrollTop = this.scrollTop
      } else {
        this.startX = event.clientX
        this.startScrollLeft = this.scrollLeft
      }
      
      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)
    },
    handleMouseMove(event) {
      if (!this.isDragging) return
      
      const container = this.$refs.scrollContainer
      if (!container) return
      
      if (this.dragDirection === 'vertical') {
        const deltaY = event.clientY - this.startY
        const scrollRatio = deltaY / this.clientHeight
        const newScrollTop = this.startScrollTop + scrollRatio * this.scrollHeight
        container.scrollTop = Math.max(0, Math.min(newScrollTop, this.scrollHeight - this.clientHeight))
      } else {
        const deltaX = event.clientX - this.startX
        const scrollRatio = deltaX / this.clientWidth
        const newScrollLeft = this.startScrollLeft + scrollRatio * this.scrollWidth
        container.scrollLeft = Math.max(0, Math.min(newScrollLeft, this.scrollWidth - this.clientWidth))
      }
    },
    handleMouseUp() {
      this.isDragging = false
      this.dragDirection = ''
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)
    },
    addEventListeners() {
      window.addEventListener('resize', this.updateScrollInfo)
    },
    removeEventListeners() {
      window.removeEventListener('resize', this.updateScrollInfo)
    },
    scrollTo(options) {
      const container = this.$refs.scrollContainer
      if (!container) return
      
      if (typeof options === 'number') {
        container.scrollTop = options
      } else {
        container.scrollTo(options)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.base-scrollbar {
  position: relative;
  overflow: hidden;
  height: 100%;
  
  &__content {
    height: 100%;
    overflow: auto;
    
    &::-webkit-scrollbar {
      display: none;
    }
    
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  &__bar {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    &--vertical {
      top: 0;
      right: 2px;
      width: 6px;
      height: 100%;
    }
    
    &--horizontal {
      bottom: 2px;
      left: 0;
      width: 100%;
      height: 6px;
    }
  }
  
  &__thumb {
    position: relative;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.5);
    }
  }
  
  &:hover &__bar,
  &--always &__bar {
    opacity: 1;
  }
}
</style>
