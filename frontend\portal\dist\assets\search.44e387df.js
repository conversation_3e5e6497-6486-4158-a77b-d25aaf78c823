/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import{_ as a,a as e,U as s,r as l,h as o,o as u,d as n,j as c,w as t,E as r,e as v,F as i,i as d,m as p,f as m,G as b,T as f,H as g,g as h,V as k,Q as y}from"./index.5a1fa56a.js";import x from"./index.a625eec6.js";const I={class:"search-component"},_={class:"transition-box",style:{display:"inline-block"}},j={key:0,class:"user-box"},w={key:1,class:"user-box"},C={key:2,class:"user-box"},V={key:3,class:"user-box"},B=a(Object.assign({name:"BtnBox"},{setup(a){const B=e(),T=s(),q=l(""),U=()=>{B.push({name:q.value}),q.value=""},E=l(!1),F=l(!0),G=()=>{E.value=!1,setTimeout((()=>{F.value=!0}),500)},H=l(null),L=async()=>{F.value=!1,E.value=!0,await k(),H.value.focus()},O=l(!1),Q=()=>{O.value=!0,y.emit("reload"),setTimeout((()=>{O.value=!1}),500)},z=()=>{window.open("https://support.qq.com/product/371961")};return(a,e)=>{const s=o("base-option"),l=o("base-select");return u(),n("div",I,[c(f,{name:"el-fade-in-linear"},{default:t((()=>[r(v("div",_,[c(l,{ref_key:"searchInput",ref:H,modelValue:q.value,"onUpdate:modelValue":e[0]||(e[0]=a=>q.value=a),filterable:"",placeholder:"请选择",onBlur:G,onChange:U},{default:t((()=>[(u(!0),n(i,null,d(p(T).routerList,(a=>(u(),m(s,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],512),[[b,E.value]])])),_:1}),F.value?(u(),n("div",j,[v("div",{class:g(["gvaIcon gvaIcon-refresh",[O.value?"reloading":""]]),onClick:Q},null,2)])):h("",!0),F.value?(u(),n("div",w,[v("div",{class:"gvaIcon gvaIcon-search",onClick:L})])):h("",!0),F.value?(u(),n("div",C,[c(x,{class:"search-icon",style:{cursor:"pointer"}})])):h("",!0),F.value?(u(),n("div",V,[v("div",{class:"gvaIcon gvaIcon-customer-service",onClick:z})])):h("",!0)])}}}),[["__scopeId","data-v-97ccbcef"]]);export{B as default};
