/*! 
 Build based on gin-vue-admin 
 Time : 1749637440000 */
import{_ as e,J as t,r as a,z as o,h as n,o as r,d as l,e as i,t as s,j as u,f as c,w as m,C as f,g as p}from"./index.8e727eba.js";const d={key:0,style:{height:"34px","margin-top":"6px","margin-bottom":"6px",border:"4px","line-height":"34px","margin-left":"14px",background:"#2F3C4B","padding-left":"35px","margin-right":"29px"}},h={style:{"font-size":"12px",color:"#FFFFFF",opacity:"1"}},v={key:1,class:"gva-menu-item"},x={class:"gva-menu-item-title"},g={name:"MenuItem",setup(){}},y=e(Object.assign(g,{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON><PERSON>an},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"70ffae96":y.value,"78c72156":I.value,"7cd627c8":b.value,ef6dec18:F.value})));const g=e,y=a(g.theme.activeBackground),F=a(g.theme.activeText),I=a(g.theme.normalText),k=a(g.theme.hoverBackground),b=a(g.theme.hoverText);return o((()=>g.theme),(()=>{y.value=g.theme.activeBackground,F.value=g.theme.activeText,I.value=g.theme.normalText,k.value=g.theme.hoverBackground,b.value=g.theme.hoverText})),(t,a)=>{const o=n("base-icon"),g=n("el-tooltip"),y=n("el-menu-item");return e.routerInfo.meta.isDisabled?(r(),l("div",d,[i("span",h,s(e.routerInfo.meta.title),1),u(o,{color:"#FFFFFF",size:"12px",style:{"padding-left":"17px"},name:"plus"})])):(r(),c(y,{key:1,index:e.routerInfo.name},{default:m((()=>[e.isCollapse?(r(),c(g,{key:0,class:"box-item",effect:"light",content:e.routerInfo.meta.title,placement:"right"},{default:m((()=>[e.routerInfo.meta.icon?(r(),l("i",{key:0,class:f(["iconfont",e.routerInfo.meta.icon])},null,2)):p("",!0)])),_:1},8,["content"])):(r(),l("div",v,[e.routerInfo.meta.icon?(r(),l("i",{key:0,class:f(["iconfont",e.routerInfo.meta.icon])},null,2)):p("",!0),i("span",x,s(e.routerInfo.meta.title),1)]))])),_:1},8,["index"]))}}}),[["__scopeId","data-v-d04f943e"]]);export{y as default};
