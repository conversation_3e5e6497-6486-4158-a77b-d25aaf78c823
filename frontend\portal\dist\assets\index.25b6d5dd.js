/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
import{_ as e,b as a,a as o,u as t,U as s,r as n,N as i,Q as l,c,V as r,p as d,h as u,E as v,o as m,g as p,w as f,e as g,I as h,j as y,B as b,f as w,d as x,T as k,F as I,i as C,k as F,t as _,m as j,S,G as N,W as U,X as z,A}from"./index.4f1b43e7.js";import{_ as M}from"./ASD.492c8837.js";import O from"./index.3d98cd61.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.24fc3129.js";import"./menuItem.d93b2c2b.js";import"./asyncSubmenu.2fe68933.js";
/*! js-cookie v3.0.5 | MIT */function D(e){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var t in o)e[t]=o[t]}return e}var R=function e(a,o){function t(e,t,s){if("undefined"!=typeof document){"number"==typeof(s=D({},o,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var n="";for(var i in s)s[i]&&(n+="; "+i,!0!==s[i]&&(n+="="+s[i].split(";")[0]));return document.cookie=e+"="+a.write(t,e)+n}}return Object.create({set:t,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var o=document.cookie?document.cookie.split("; "):[],t={},s=0;s<o.length;s++){var n=o[s].split("="),i=n.slice(1).join("=");try{var l=decodeURIComponent(n[0]);if(t[l]=a.read(i,l),e===l)break}catch(c){}}return e?t[e]:t}},remove:function(e,a){t(e,"",D({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,D({},this.attributes,a))},withConverter:function(a){return e(D({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const B={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},E={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={class:"header-row"},T={class:"header-col"},$={class:"header-cont"},V={class:"header-content pd-0"},G={class:"breadcrumb-col"},J={class:"breadcrumb"},W={class:"user-col"},H={class:"right-box"},P={class:"dp-flex justify-content-center align-items height-full width-full"},Q={class:"header-avatar",style:{cursor:"pointer"}},X={style:{"margin-right":"9px",color:"#252631"}},q={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},K={key:0,class:"dropdown-menu"},Y=e(Object.assign({name:"Layout"},{setup(e){const D=a(),Y=o(),Z=t(),ee=s(),ae=n(!0),oe=n(!1),te=n(!1),se=n("7"),ne=()=>{document.body.clientWidth;te.value=!1,oe.value=!1,ae.value=!0};ne();const ie=n(!1);i((()=>{l.emit("collapse",ae.value),l.emit("mobile",te.value),l.on("reload",ue),l.on("showLoading",(()=>{ie.value=!0})),l.on("closeLoading",(()=>{ie.value=!1})),window.onresize=()=>(ne(),l.emit("collapse",ae.value),void l.emit("mobile",te.value)),D.loadingInstance&&D.loadingInstance.close()})),c((()=>"dark"===D.sideMode?"#fff":"light"===D.sideMode?"#273444":D.baseColor));const le=c((()=>"dark"===D.sideMode?"#273444":"light"===D.sideMode?"#fff":D.sideMode)),ce=c((()=>Z.meta.matched)),re=n(!0);let de=null;const ue=async()=>{de&&window.clearTimeout(de),de=window.setTimeout((async()=>{if(Z.meta.keepAlive)re.value=!1,await r(),re.value=!0;else{const e=Z.meta.title;Y.push({name:"Reload",params:{title:e}})}}),400)},ve=n(!1),me=n(!1),pe=()=>{ae.value=!ae.value,oe.value=!ae.value,ve.value=!ae.value,l.emit("collapse",ae.value)},fe=()=>{me.value=!me.value},ge=()=>{Y.push({name:"person"})};return d("day",se),(e,a)=>{const o=u("base-aside"),t=u("router-view"),s=u("base-main"),i=u("base-container"),l=v("loading");return m(),p(i,{class:"layout-cont"},{default:f((()=>[g("div",{class:h([[oe.value?"openside":"hideside",te.value?"mobile":""],"layout-wrapper"])},[g("div",{class:h([[ve.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(ve.value=!ve.value,oe.value=!!ae.value,void pe()))},null,2),y(o,{class:"main-cont main-left gva-aside",collapsed:ae.value},{default:f((()=>[g("div",{class:h(["tilte",[oe.value?"openlogoimg":"hidelogoimg"]]),style:b({background:le.value})},[a[3]||(a[3]=g("img",{alt:"",class:"logoimg",src:M},null,-1)),w("          <div>"),w('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),w('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),w("          </div>")],6),y(O,{class:"aside"}),g("div",{class:"footer",style:b({background:le.value})},[g("div",{class:"menu-total",onClick:pe},[ae.value?(m(),x("svg",B,a[4]||(a[4]=[g("use",{"xlink:href":"#icon-expand"},null,-1)]))):(m(),x("svg",E,a[5]||(a[5]=[g("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),w(" 分块滑动功能 "),y(s,{class:"main-cont main-right"},{default:f((()=>[y(k,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[g("div",{style:b({width:`calc(100% - ${te.value?"0px":ae.value?"54px":"220px"})`}),class:"topfix"},[g("div",L,[g("div",T,[g("header",$,[g("div",V,[a[10]||(a[10]=g("div",{class:"header-menu-col",style:{"z-index":"100"}},[w('                      <div class="menu-total" @click="totalCollapse">'),w('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),w('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),w("                      </div>")],-1)),g("div",G,[g("nav",J,[(m(!0),x(I,null,C(ce.value.slice(1,ce.value.length),(e=>(m(),x("div",{key:e.path,class:"breadcrumb-item"},[F(_(j(S)(e.meta.topTitle||"",j(Z)))+" ",1),"总览"===e.meta.title?N((m(),x("select",{key:0,"onUpdate:modelValue":a[1]||(a[1]=e=>se.value=e),class:"day-select form-select"},[...a[6]||(a[6]=[g("option",{value:"7"},"最近7天",-1),g("option",{value:"30"},"最近30天",-1),g("option",{value:"90"},"最近90天",-1)])],512)),[[U,se.value]]):w("v-if",!0)])))),128))])]),g("div",W,[g("div",H,[w("                        <Search />"),g("div",{class:"dropdown",onClick:fe},[g("div",P,[g("span",Q,[w(" 展示当前登录用户名 "),g("span",X,_(j(D).userInfo.displayName?j(D).userInfo.displayName:j(D).userInfo.name),1),(m(),x("svg",q,a[7]||(a[7]=[g("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),me.value?(m(),x("div",K,[w(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),g("div",{class:"dropdown-item",onClick:ge},a[8]||(a[8]=[g("svg",{class:"icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-avatar"})],-1),F(" 个人信息 ")])),g("div",{class:"dropdown-item",onClick:a[2]||(a[2]=e=>(async()=>{document.location.protocol,document.location.host;const e={action:1,msg:"",platform:document.location.hostname},a=n({}),o=n("ws://127.0.0.1:50001"),t=navigator.platform;0!==t.indexOf("Mac")&&"MacIntel"!==t||(o.value="wss://127.0.0.1:50001");const s=async e=>{console.log(e,"0"),await a.value.send(e)},i=async()=>{console.log("socket断开链接"),await a.value.close()};console.log(`asecagent://?web=${JSON.stringify(e)}`),await D.LoginOut(),a.value=new WebSocket(o.value),a.value.onopen=async()=>{console.log("socket连接成功"),await s(JSON.stringify(e))},a.value.onmessage=async e=>{console.log(e),await i()},a.value.onerror=()=>{console.log("socket连接错误")},R.remove("asce_sms")})())},a[9]||(a[9]=[g("svg",{class:"icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-reading-lamp"})],-1),F(" 登 出 ")]))])):w("v-if",!0)]),w('                        <base-button type="text"'),w('                                   class="iconfont icon-rizhi1"'),w('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),w('                                   @click="toLog"'),w("                        >日志中心"),w("                        </base-button>")])])])])])]),w(" 当前面包屑用路由自动生成可根据需求修改 "),w('\r\n            :to="{ path: item.path }" 暂时注释不用'),w('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)])),_:1}),re.value?N((m(),p(t,{key:0,"element-loading-text":"正在加载中",class:"admin-box"},{default:f((({Component:e})=>[g("div",null,[y(k,{mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[(m(),p(z,{include:j(ee).keepAliveRouters},[(m(),p(A(e)))],1032,["include"]))])),_:2},1024)])])),_:1})),[[l,ie.value]]):w("v-if",!0),w("        <BottomInfo />"),w("        <setting />")])),_:1})],2)])),_:1})}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]);export{Y as default};
