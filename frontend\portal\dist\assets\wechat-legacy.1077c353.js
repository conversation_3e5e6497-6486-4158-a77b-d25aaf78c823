/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
!function(){function t(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,u,a=[],c=!0,f=!1;try{if(i=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;c=!1}else for(;!(c=(r=i.call(e)).done)&&(a.push(r.value),a.length!==n);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=e.return&&(u=e.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.toStringTag||"@@toStringTag";function a(e,o,i,u){var a=o&&o.prototype instanceof f?o:f,l=Object.create(a.prototype);return r(l,"_invoke",function(e,r,o){var i,u,a,f=0,l=o||[],s=!1,d={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(n,e){return i=n,u=0,a=t,d.n=e,c}};function p(e,r){for(u=e,a=r,n=0;!s&&f&&!o&&n<l.length;n++){var o,i=l[n],p=d.p,y=i[2];e>3?(o=y===r)&&(a=i[(u=i[4])?5:(u=3,3)],i[4]=i[5]=t):i[0]<=p&&((o=e<2&&p<i[1])?(u=0,d.v=r,d.n=i[1]):p<y&&(o=e<3||i[0]>r||r>y)&&(i[4]=e,i[5]=r,d.n=y,u=0))}if(o||e>1)return c;throw s=!0,r}return function(o,l,y){if(f>1)throw TypeError("Generator is already running");for(s&&1===l&&p(l,y),u=l,a=y;(n=u<2?t:a)||!s;){i||(u?u<3?(u>1&&(d.n=-1),p(u,a)):d.n=a:d.v=a);try{if(f=2,i){if(u||(o="next"),n=i[o]){if(!(n=n.call(i,a)))throw TypeError("iterator result is not an object");if(!n.done)return n;a=n.value,u<2&&(u=0)}else 1===u&&(n=i.return)&&n.call(i),u<2&&(a=TypeError("The iterator does not provide a '"+o+"' method"),u=1);i=t}else if((n=(s=d.n<0)?a:e.call(r,d))!==c)break}catch(n){i=t,u=1,a=n}finally{f=1}}return{value:n,done:s}}}(e,i,u),!0),l}var c={};function f(){}function l(){}function s(){}n=Object.getPrototypeOf;var d=[][i]?n(n([][i]())):(r(n={},i,(function(){return this})),n),p=s.prototype=f.prototype=Object.create(d);function y(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,r(t,u,"GeneratorFunction")),t.prototype=Object.create(p),t}return l.prototype=s,r(p,"constructor",s),r(s,"constructor",l),l.displayName="GeneratorFunction",r(s,u,"GeneratorFunction"),r(p),r(p,u,"Generator"),r(p,i,(function(){return this})),r(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:a,m:y}})()}function r(t,n,e,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,n,e,o){if(n)i?i(t,n,{value:e,enumerable:!o,configurable:!o,writable:!o}):t[n]=e;else{var u=function(n,e){r(t,n,(function(t){return this._invoke(n,e,t)}))};u("next",0),u("throw",1),u("return",2)}},r(t,n,e,o)}function o(t,n,e,r,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void e(t)}a.done?n(c):Promise.resolve(c).then(r,o)}function i(t){return function(){var n=this,e=arguments;return new Promise((function(r,i){var u=t.apply(n,e);function a(t){o(u,r,i,a,c,"next",t)}function c(t){o(u,r,i,a,c,"throw",t)}a(void 0)}))}}System.register(["./index-legacy.00b16b45.js"],(function(n,r){"use strict";var o,u,a,c,f,l,s,d,p,y=document.createElement("style");return y.textContent='@charset "UTF-8";.wechat-class{height:320px;overflow:hidden}\n',document.head.appendChild(y),{setters:[function(t){o=t._,u=t.r,a=t.u,c=t.z,f=t.o,l=t.d,s=t.f,d=t.e,p=t.Y}],execute:function(){var r=Object.assign({name:"Wechat"},{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup:function(n){var r=u(0),o=n,y=function(){var t=i(e().m((function t(){var n,r;return e().w((function(t){for(;;)switch(t.n){case 0:return n={type:"qiyewx",data:{idpId:o.auth_id}},t.n=1,p(n);case 1:if(200!==(r=t.v).status){t.n=2;break}return t.a(2,r.data.uniqKey);case 2:return t.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),v=a(),h=function(){var n=i(e().m((function n(){var r,i,u,a,c,f,l,s,d,p,h,w,m,g;return e().w((function(n){for(;;)switch(n.n){case 0:if(i=window.location.host,u=window.location.protocol,a="".concat(u,"//").concat(i,"/#/status"),null!==(r=v.query)&&void 0!==r&&r.redirect)s=(null===(c=v.query)||void 0===c?void 0:c.redirect.indexOf("?"))>-1?null===(f=v.query)||void 0===f?void 0:f.redirect.substring((null===(l=v.query)||void 0===l?void 0:l.redirect.indexOf("?"))+1):"",a=a+"?"+s;else if(v.query){for(d=new URLSearchParams,p=0,h=Object.entries(v.query);p<h.length;p++)w=t(h[p],2),m=w[0],g=w[1],d.append(m,g);a=a+"?"+d.toString()}return n.n=1,y();case 1:setTimeout((function(){window.getQRCode({id:"qr_login",appid:o.auth_info.wxCorpId,agentid:o.auth_info.wxAgentId,redirect_uri:encodeURIComponent(a+"&auth_type=qiyewx"),state:o.auth_id,href:"",lang:"zh"});var t=document.querySelector("iframe");t.contentWindow.location.href!==t.src?console.log("iframe已重新加载"):console.log("iframe未重新加载")}),100);case 2:return n.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return h(),c(o,function(){var t=i(e().m((function t(n,o){return e().w((function(t){for(;;)switch(t.n){case 0:return r.value++,t.n=1,h();case 1:return t.a(2)}}),t)})));return function(n,e){return t.apply(this,arguments)}}()),function(t,n){return f(),l("div",{key:r.value},[s('    <div style="text-align: center">'),s('      <span class="title">企业微信认证</span>'),s("    </div>"),n[0]||(n[0]=d("div",{id:"qr_login",slot:"content",class:"wechat-class"},null,-1))])}}});n("default",o(r,[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wechat.vue"]]))}}}))}();
