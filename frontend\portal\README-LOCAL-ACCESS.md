# 本地文件访问配置

这个配置允许你直接双击 `dist/index.html` 在浏览器中运行应用，无需启动开发服务器。

## 构建命令

```bash
pnpm build-local
```

## 技术实现

### Vite 配置修改
- **保留** `@vitejs/plugin-legacy` 插件以支持更广泛的浏览器兼容性
- 使用 Legacy 构建版本在本地文件环境下运行
- 配置 `terser` 作为压缩工具
- 自动生成现代版本和 Legacy 版本

### 解决 CORS 问题
- 移除包含 `import.meta` 的现代浏览器检测脚本
- 隐藏现代版本的主脚本，强制使用 Legacy 版本
- 保留 `nomodule` 脚本用于 Legacy 浏览器支持
- 修复代码中的同步 XMLHttpRequest 请求，在本地文件环境下跳过执行

### 依赖配置
- 使用：`@vitejs/plugin-legacy`、`terser`
- 目标浏览器：`['Android > 39', 'Chrome >= 60', 'Safari >= 10.1', 'iOS >= 10.3', 'Firefox >= 54', 'Edge >= 15']`

### 后处理脚本
构建完成后自动运行 `scripts/post-build.js` 来：
- 移除包含 `import.meta` 的现代浏览器检测脚本
- 移除现代浏览器加载逻辑脚本
- 隐藏现代版本主脚本，强制使用 Legacy 版本
- 保留 Legacy polyfill 和入口脚本

### 代码修复

#### XMLHttpRequest 修复
修复了以下文件中的同步 XMLHttpRequest 请求：
- `src/api/user.js` - 获取 X-Corp-ID header
- `src/main.js` - 获取 X-Corp-ID header
- `src/view/app/index.vue` - 获取 X-Corp-ID header

这些请求在本地文件环境下会被跳过，使用默认值。

#### import.meta 修复
修复了以下文件中的 `import.meta.env` 使用：
- `src/utils/request.js` - 环境变量获取
- `src/main.js` - 环境变量获取
- `src/core/gin-vue-admin.js` - 注释中的环境变量引用

添加了安全的环境变量获取函数：
```javascript
const getEnvVar = (key, defaultValue = '') => {
  try {
    // 检查是否在本地文件环境中
    if (typeof window !== 'undefined' && window.location && window.location.protocol === 'file:') {
      return defaultValue
    }
    // 使用 eval 来避免静态分析错误
    const importMeta = eval('import.meta')
    return (importMeta && importMeta.env) ? importMeta.env[key] || defaultValue : defaultValue
  } catch (e) {
    return defaultValue
  }
}
```

这确保了在本地文件环境下不会出现 `import.meta` 相关错误。

### 路由修复
修复了 `src/permission.js` 中的路由守卫逻辑：
- 检测本地文件环境（`file://` 协议）
- 跳过 WebSocket 认证和用户信息获取
- 直接加载动态路由并设置默认用户信息
- 自动重定向到应用门户页面（`/layout/dashboard`）

这解决了本地文件环境下的重定向循环问题。

## 使用方法

1. **构建应用**：
   ```bash
   pnpm build-local
   ```

2. **直接打开**：
   - 双击 `D:\asec-platform\frontend\portal\dist\index.html`
   - 或在浏览器地址栏输入：`file:///D:/asec-platform/frontend/portal/dist/index.html`

3. **应用会自动**：
   - 检测本地文件环境
   - 使用 Legacy 版本运行
   - 跳过认证流程
   - 加载应用门户页面

## 技术细节

### HTML 输出结构
```html
<!-- 隐藏的现代版本脚本 -->
<!-- Modern script hidden for local file access -->

<!-- CSS 样式 -->
<link rel="stylesheet" href="./assets/index.d9de825b.css">

<!-- Legacy polyfill -->
<script nomodule src="./assets/polyfills-legacy.2df03659.js"></script>

<!-- Legacy 入口脚本 -->
<script nomodule data-src="./assets/index-legacy.7e8ba759.js">
  System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))
</script>
```

### 浏览器兼容性
- 支持所有现代浏览器
- 支持 IE11+ (通过 Legacy 版本)
- 支持本地文件协议访问
- 无需 CORS 配置

## 故障排除

如果遇到问题：

1. **检查浏览器控制台**是否有错误信息
2. **确保使用现代浏览器**（Chrome、Firefox、Edge、Safari）
3. **避免使用 IE 浏览器**（会显示不支持提示）
4. **重新构建**：`rm -rf dist && pnpm build-local`

## 注意事项

- 本配置专门为本地文件访问优化
- 生产环境建议使用标准的 `pnpm build` 命令
- Legacy 版本文件较大，但兼容性更好
- 应用会自动检测环境并使用合适的版本
