# 本地文件访问支持

## 概述

本项目已经配置为支持直接在浏览器中打开本地构建文件，无需启动 HTTP 服务器。

## 使用方法

1. **构建项目**：
   ```bash
   pnpm build-local
   # 或
   pnpm build
   ```

2. **直接打开文件**：
   - 在文件管理器中导航到 `dist/index.html`
   - 双击文件或右键选择"用浏览器打开"
   - 或者直接在浏览器地址栏输入：`file:///D:/asec-platform/frontend/portal/dist/index.html`

## 技术实现

### 移除了 legacyPlugin
- 删除了 `@vitejs/plugin-legacy` 依赖
- 移除了 `terser` 依赖（改用 `esbuild` 压缩）
- 更新构建目标为 `es2020`

### 解决 CORS 问题
- 使用 IIFE 格式构建（而非 ES 模块）
- 自动移除 HTML 中的 `type="module"` 属性
- 内联动态导入以避免模块加载问题

### 构建配置
```javascript
build: {
  target: ['es2020', 'chrome63', 'firefox67', 'safari11.1'],
  rollupOptions: {
    output: {
      format: 'iife',
      inlineDynamicImports: true,
    }
  },
}
```

### 后处理脚本
构建完成后自动运行 `scripts/post-build.js` 来：
- 移除 HTML 中的 `type="module"` 属性
- 移除 `crossorigin` 属性（避免本地文件 CORS 问题）

## 支持的浏览器

- Chrome 63+
- Firefox 67+
- Safari 11.1+
- Edge 79+

## 注意事项

1. **文件路径**：确保所有资源路径都是相对路径（已配置）
2. **安全限制**：某些浏览器功能在 `file://` 协议下可能受限
3. **开发调试**：建议开发时仍使用 `pnpm serve` 启动开发服务器

## 测试

可以使用 `test-local.html` 文件测试本地文件访问是否正常工作。
