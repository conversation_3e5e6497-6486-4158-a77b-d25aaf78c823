<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Local File Test</title>
</head>
<body>
    <h1>Testing Local File Access</h1>
    <p>If you can see this page and the script below runs without CORS errors, then local file access is working.</p>
    <div id="result"></div>
    
    <script>
        try {
            document.getElementById('result').innerHTML = '<p style="color: green;">✅ JavaScript is working! Local file access is successful.</p>';
            console.log('Local file access test passed');
        } catch (error) {
            document.getElementById('result').innerHTML = '<p style="color: red;">❌ Error: ' + error.message + '</p>';
            console.error('Local file access test failed:', error);
        }
    </script>
</body>
</html>
