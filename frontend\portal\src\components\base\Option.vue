<template>
  <div 
    class="base-option"
    :class="{ 
      'is-selected': isSelected,
      'is-disabled': disabled 
    }"
    @click="handleClick"
  >
    <slot>{{ label }}</slot>
  </div>
</template>

<script>
export default {
  name: 'BaseOption',
  props: {
    value: {
      type: [String, Number, Boolean],
      required: true
    },
    label: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  inject: ['select', 'registerOption', 'unregisterOption'],
  computed: {
    isSelected() {
      return this.select.modelValue === this.value
    },
    displayLabel() {
      return this.label || this.$slots.default?.()?.[0]?.children || ''
    }
  },
  mounted() {
    // 注册选项到父组件
    this.registerOption({
      value: this.value,
      label: this.displayLabel
    })
  },
  beforeUnmount() {
    // 从父组件注销选项
    this.unregisterOption({
      value: this.value,
      label: this.displayLabel
    })
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.select.handleOptionClick(this.value, this.displayLabel)
    }
  }
}
</script>

<style scoped>
.base-option {
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.base-option:hover {
  background-color: #f5f7fa;
}

.base-option.is-selected {
  color: #536ce6;
  background-color: #f0f9ff;
}

.base-option.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.base-option.is-disabled:hover {
  background-color: transparent;
}
</style>
