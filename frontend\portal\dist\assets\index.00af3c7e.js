/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
import{x as e,r as a,K as l,N as s,O as n,b as t,u as o,D as c,h as r,o as i,d as u,e as p,j as d,w as v,f as m,_ as f,F as b,i as g,g as y,k,t as h,J as _,P as w,B as W,M as S}from"./index.e2f48a61.js";const D={class:"person"},x={class:"el-search"},C={class:"category-title"},U={class:"apps-container"},O={key:0,class:"status-badge"},I={class:"icon-wrapper"},T={class:"tooltip-content text-center"},A={key:0},E={key:1},F={class:"app-info"},P={class:"app-name"},N=f(Object.assign({name:"AppPage"},{setup(f){const N=a(""),V=a(null),B=a([]),J=a([]),$=a("1"),L=a(!1),M=a("standard"),j=l([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),q=a(null),z=a(!1),R=(e,a="success",l=3e3)=>{S({message:e,type:a,duration:l})},X=async e=>new Promise(((a,l)=>{let s,n=!1;(async()=>{try{const t=await new Promise(((e,a)=>{if(q.value&&q.value.readyState===WebSocket.OPEN)return void e(q.value);const l=new WebSocket("ws://localhost:50001");z.value=!0,l.onopen=()=>{console.log("WebSocket Connected"),q.value=l,z.value=!1,e(l)},l.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&R(a,"error")},l.onclose=()=>{console.log("WebSocket Disconnected"),q.value=null,z.value=!1},l.onerror=e=>{console.error("WebSocket Error:",e),z.value=!1,a(e)},setTimeout((()=>{z.value&&(z.value=!1,l.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};s=setTimeout((()=>{n||(t.close(),l(new Error("启动超时：未收到响应")))}),3e3),t.onmessage=e=>{n=!0,clearTimeout(s);const t=e.data;t.startsWith("Ok")?a():l(new Error(t))},t.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(t){clearTimeout(s),l(t)}})()}));s((()=>{q.value&&(q.value.close(),q.value=null)}));const H=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let l=0;for(let s=0;s<e.length;s++)l+=e.charCodeAt(s);return a[l%a.length]},G=()=>{L.value=!0},K=e=>{V.value=parseInt(e),J.value=e?B.value.filter((a=>a.id===parseInt(e))):B.value},Q=()=>{if(!N.value)return void(J.value=B.value);const e=N.value.toLowerCase().trim();J.value=B.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))},Y=async()=>{try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(console.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",e),B.value=e,J.value=e,e.length>0&&(V.value=e[0].id,$.value=e[0].id.toString())}}catch(a){console.error("API调用出错:",a)}};n((()=>{Y()}));const Z=t(),ee=o().query;let ae=null;try{if(!c.isClient()){const e=new XMLHttpRequest;e.open("GET",document.location,!1),e.send(null),ae=e.getResponseHeader("X-Corp-ID")}}catch(se){console.warn("无法获取 X-Corp-ID header，使用默认值:",se)}const le={action:0,msg:{token:Z.token.accessToken,refreshToken:Z.token.refreshToken,realm:ae||"default"},platform:document.location.hostname};{const e=ee.wp||50001,l=a({}),s=a(`ws://127.0.0.1:${e}`),n=navigator.platform;0!==n.indexOf("Mac")&&"MacIntel"!==n||(s.value=`wss://127.0.0.1:${e}`);const t=()=>{l.value=new WebSocket(s.value),l.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(le))},l.value.onmessage=e=>{console.log(e),c()},l.value.onerror=()=>{console.log("socket连接错误:"+s.value),window.location.href=`asecagent://?web=${JSON.stringify(le)}`}},o=e=>{console.log(e,"0"),l.value.send(e)},c=()=>{console.log("socket断开链接"),l.value.close()};console.log(`asecagent://?web=${JSON.stringify(le)}`),t()}return(e,a)=>{const l=r("base-input"),s=r("base-button"),n=r("base-option"),t=r("base-select"),o=r("base-header"),c=r("el-menu-item"),f=r("el-menu"),S=r("base-aside"),V=r("base-avatar"),q=r("el-tooltip"),z=r("el-link"),Y=r("base-main"),Z=r("base-container");return i(),u("div",null,[p("div",D,[d(o,null,{default:v((()=>[a[3]||(a[3]=p("span",{class:"el-title"},"我的应用",-1)),p("span",x,[d(l,{class:"el-search-input",modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=e=>N.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:Q,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),d(s,{class:"el-search-btn",icon:"Refresh",size:"small"}),d(t,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:M.value,"onUpdate:modelValue":a[1]||(a[1]=e=>M.value=e),placeholder:"Select",size:"small"},{default:v((()=>[(i(!0),u(b,null,g(j,(e=>(i(),y(n,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),m('\r\n        <div class="el-row">\r\n          <span class="el-recent-access">最新访问</span>\r\n          <span class="el-recent-data">\r\n            <span class="el-recent-item">\r\n              最新访问1\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问2\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问3\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <base-icon class="el-recent-clear" name="close" title="清空" />\r\n          </span>\r\n        </div>\r\n        ')])),_:1,__:[3]}),m(" 主体内容区域：使用 el-container 实现左右布局 "),d(Z,null,{default:v((()=>[m(" 左侧分类导航 "),d(S,{width:"96px",class:"category-aside"},{default:v((()=>[d(f,{class:"category-menu",onSelect:K,"default-active":$.value},{default:v((()=>[d(c,{index:"0",onClick:a[2]||(a[2]=e=>K(null))},{default:v((()=>a[4]||(a[4]=[k(" 全部 ")]))),_:1,__:[4]}),(i(!0),u(b,null,g(B.value,(e=>(i(),y(c,{key:e.id,index:e.id.toString()},{default:v((()=>[k(h(e.name),1)])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),m(" 右侧应用列表 "),d(Y,{class:"app-main"},{default:v((()=>[(i(!0),u(b,null,g(J.value,(e=>(i(),u("div",{key:e.id,class:"category-section"},[m(" 分类标题 "),p("h3",C,h(e.name),1),m(" 应用列表 "),p("div",U,[(i(!0),u(b,null,g(e.apps,(e=>(i(),u("div",{key:e.id,class:_(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(i(),u("div",O," 维护中 ")):m("v-if",!0),d(z,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:w((a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const a=e.WebUrl.substring(3);try{R("正在启动爱尔企业浏览器...","info"),await X(a),R("启动成功","success")}catch(se){R("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)),["prevent"])},{default:v((()=>[p("div",I,[d(q,{effect:"light",placement:"bottom"},{content:v((()=>[p("div",T,[e.WebUrl?(i(),u("span",A,h(e.WebUrl),1)):(i(),u("span",E,"暂无访问地址"))])])),default:v((()=>[d(V,{shape:"square",size:48,src:e.icon,onError:G,style:W(!e.icon||L.value?`background-color: ${H(e.app_name)} !important`:"")},{default:v((()=>[k(h(!e.icon||L.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["src","style"])])),_:2},1024)]),p("div",F,[p("div",P,h(e.app_name),1),a[5]||(a[5]=p("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])])),_:2},1032,["disabled","onClick"])],2)))),128))])])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]);export{N as default};
