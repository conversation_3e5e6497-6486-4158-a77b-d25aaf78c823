/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import n from"./header.055d9003.js";import r from"./menu.188806bb.js";import{h as _,o as e,d as c,j as t,e as o,f as i}from"./index.d0594432.js";import"./ASD.492c8837.js";const l={class:"layout-page"},u={class:"layout-wrap"},d={id:"layoutMain",class:"layout-main"},p={name:"Client"},B=Object.assign(p,{setup(m){return(a,f)=>{const s=_("router-view");return e(),c("div",l,[t(n),o("div",u,[t(r),o("div",d,[(e(),i(s,{key:a.$route.fullPath}))])])])}}});export{B as default};
