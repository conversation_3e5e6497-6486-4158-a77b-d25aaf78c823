/*! 
 Build based on gin-vue-admin 
 Time : 1749604390000 */
import a from"./header.055d9003.js";import s from"./menu.188806bb.js";import{h as t,o as e,d as o,j as r,e as i,f as l}from"./index.d0594432.js";import"./ASD.492c8837.js";const u={class:"layout-page"},d={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},n=Object.assign({name:"Client"},{setup:n=>(n,p)=>{const c=t("router-view");return e(),o("div",u,[r(a),i("div",d,[r(s),i("div",m,[(e(),l(c,{key:n.$route.fullPath}))])])])}});export{n as default};
