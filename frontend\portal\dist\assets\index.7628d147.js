/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
import a from"./header.503f24b4.js";import s from"./menu.53c8d528.js";import{h as t,o as e,d as o,j as r,e as i,f as l}from"./index.29054947.js";import"./ASD.492c8837.js";const u={class:"layout-page"},m={class:"layout-wrap"},n={id:"layoutMain",class:"layout-main"},d=Object.assign({name:"Client"},{setup:d=>(d,c)=>{const p=t("router-view");return e(),o("div",u,[r(a),i("div",m,[r(s),i("div",n,[(e(),l(p,{key:d.$route.fullPath}))])])])}});export{d as default};
