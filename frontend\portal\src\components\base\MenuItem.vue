<template>
  <li 
    :class="[
      'base-menu-item',
      { 'base-menu-item--active': isActive },
      { 'base-menu-item--disabled': disabled },
      { 'base-menu-item--collapse': isCollapse }
    ]"
    @click="handleClick"
  >
    <div class="base-menu-item__content">
      <slot />
    </div>
  </li>
</template>

<script>
export default {
  name: 'BaseMenuItem',
  props: {
    index: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  inject: {
    menu: {
      default: null
    },
    activeIndex: {
      default: () => ''
    },
    setActiveIndex: {
      default: () => {}
    },
    collapse: {
      default: () => false
    }
  },
  computed: {
    isActive() {
      return this.activeIndex() === this.index
    },
    isCollapse() {
      return this.collapse()
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.setActiveIndex(this.index)
    }
  }
}
</script>

<style lang="scss" scoped>
.base-menu-item {
  position: relative;
  list-style: none;
  margin: 6px 0;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  
  &__content {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    color: inherit;
    transition: all 0.3s ease;
  }
  
  &:hover:not(&--disabled) {
    background-color: rgba(255, 255, 255, 0.1);
    
    .base-menu-item__content {
      color: #ffffff;
    }
  }
  
  &--active {
    margin-left: 12px;
    margin-right: 12px;
    background-color: #536ce6;
    
    .base-menu-item__content {
      color: #ffffff;
    }
    
    &:hover {
      background-color: #536ce6;
    }
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: transparent;
    }
  }
  
  &--collapse {
    .base-menu-item__content {
      padding: 0 15px;
      justify-content: center;
    }
  }
}

// 深度选择器，影响子组件样式
:deep(.base-icon) {
  margin-right: 8px;
  font-size: 16px;
  
  .base-menu-item--collapse & {
    margin-right: 0;
  }
}

:deep(span) {
  .base-menu-item--collapse & {
    display: none;
  }
}
</style>
