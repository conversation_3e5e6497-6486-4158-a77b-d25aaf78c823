/**
 * js和qt通讯插件
 * 这里把js和qt通讯写成一个vue插件
 */

import _ from 'lodash'
import IpcClient from './ipcClient'

const ipcPlugin = {
  init() {
    // @notice 返回的是一个Promise数据
    return new IpcClient().then(ipcClientInstance => {
      // 创建一个包含IPC方法的对象
      const ipcClient = {
        /**
         * js向客户端发起通讯
         *
         * @param {string} moudle 客户端定义的moudle
         * @param {string} action 客户端定义的action
         * @param {object} params 参数
         * @param {object} config 配置
        */
        $ipcSend: (moudle, action, params = {}, config = {}) => {
          if (_.isNil(moudle) || _.isNil(action) || _.isEmpty(moudle) || _.isEmpty(action)) {
            throw new Error('module或action不能为空')
          }
          if (params && !_.isObject(params)) {
            throw new Error('params必须为object类型')
          }
          config = _.merge({
            isNeedId: true, // 是否需要id，即是否需要等待返回
            timeout: {
              time: false// 超时时间，false为一直等待
            }
          }, config)
          return ipcClientInstance.send(moudle, action, params, config)
        },

        /**
         * js监听客户端端发送过来的信号
         *
         * @param {string} moudle 客户端定义的moudle
         * @param {string} action 客户端定义的action
         * @param {callback} function 回调函数
        */
        $ipcOn: (module, eventName, callback) => {
          ipcClientInstance.on(module, eventName, callback)
        },

        /**
         * js取消监听客户端端发送过来的信号
         *
         * @param {string} moudle 客户端定义的moudle
         * @param {string} action 客户端定义的action
         * @param {callback} function 回调函数
        */
        $ipcOff: (module, eventName, callback) => {
          ipcClientInstance.off(module, eventName, callback)
        },

        $processId: ipcClientInstance.processId
      }

      return ipcClient
    })
  }
}

export default ipcPlugin
