/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function a(e){for(var a=1;a<arguments.length;a++){var r=null!=arguments[a]?arguments[a]:{};a%2?t(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function n(t,a,n){return(a=function(t){var a=function(t,a){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,a||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(t)}(t,"string");return"symbol"==e(a)?a:a+""}(a))in t?Object.defineProperty(t,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[a]=n,t}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,a="function"==typeof Symbol?Symbol:{},n=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function p(a,n,r,i){var p=n&&n.prototype instanceof l?n:l,s=Object.create(p.prototype);return o(s,"_invoke",function(a,n,r){var o,i,p,l=0,s=r||[],u=!1,d={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,a){return o=t,i=0,p=e,d.n=a,c}};function f(a,n){for(i=a,p=n,t=0;!u&&l&&!r&&t<s.length;t++){var r,o=s[t],f=d.p,g=o[2];a>3?(r=g===n)&&(p=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=f&&((r=a<2&&f<o[1])?(i=0,d.v=n,d.n=o[1]):f<g&&(r=a<3||o[0]>n||n>g)&&(o[4]=a,o[5]=n,d.n=g,i=0))}if(r||a>1)return c;throw u=!0,n}return function(r,s,g){if(l>1)throw TypeError("Generator is already running");for(u&&1===s&&f(s,g),i=s,p=g;(t=i<2?e:p)||!u;){o||(i?i<3?(i>1&&(d.n=-1),f(i,p)):d.n=p:d.v=p);try{if(l=2,o){if(i||(r="next"),t=o[r]){if(!(t=t.call(o,p)))throw TypeError("iterator result is not an object");if(!t.done)return t;p=t.value,i<2&&(i=0)}else 1===i&&(t=o.return)&&t.call(o),i<2&&(p=TypeError("The iterator does not provide a '"+r+"' method"),i=1);o=e}else if((t=(u=d.n<0)?p:a.call(n,d))!==c)break}catch(t){o=e,i=1,p=t}finally{l=1}}return{value:t,done:u}}}(a,r,i),!0),s}var c={};function l(){}function s(){}function u(){}t=Object.getPrototypeOf;var d=[][n]?t(t([][n]())):(o(t={},n,(function(){return this})),t),f=u.prototype=l.prototype=Object.create(d);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,o(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=u,o(f,"constructor",u),o(u,"constructor",s),s.displayName="GeneratorFunction",o(u,i,"GeneratorFunction"),o(f),o(f,i,"Generator"),o(f,n,(function(){return this})),o(f,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:p,m:g}})()}function o(e,t,a,n){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}o=function(e,t,a,n){if(t)r?r(e,t,{value:a,enumerable:!n,configurable:!n,writable:!n}):e[t]=a;else{var i=function(t,a){o(e,t,(function(e){return this._invoke(t,a,e)}))};i("next",0),i("throw",1),i("return",2)}},o(e,t,a,n)}function i(e,t,a,n,r,o,i){try{var p=e[o](i),c=p.value}catch(e){return void a(e)}p.done?t(c):Promise.resolve(c).then(n,r)}function p(e){return function(){var t=this,a=arguments;return new Promise((function(n,r){var o=e.apply(t,a);function p(e){i(o,n,r,p,c,"next",e)}function c(e){i(o,n,r,p,c,"throw",e)}p(void 0)}))}}System.register(["./index-legacy.e6617eb1.js"],(function(e,t){"use strict";var n,o,i,c,l,s,u,d,f,g,m,h,v,b,x,y,w,k,_,S,O,P,j,F,C=document.createElement("style");return C.textContent='@charset "UTF-8";.person[data-v-852a538b]{background:#FFFFFF;border-radius:4px;height:100vh;max-height:calc(100vh - 68px)}.person .el-header[data-v-852a538b]{justify-content:space-between;font-weight:Medium;color:#282a33;font-size:16px;line-height:22px;padding:0 15px;display:flex;align-items:center}.person .el-header .el-title[data-v-852a538b]{padding-top:19px}.person .el-header .el-search[data-v-852a538b]{padding-top:16px;display:flex;justify-content:space-between}.person .el-header .el-search-input[data-v-852a538b]{width:200px;height:28px;border-radius:4px;font-size:14px}.person .el-header .el-search-input>.el-input__wrapper[data-v-852a538b]{background:#f5f5f7}.person .el-header .el-search-btn[data-v-852a538b]{font-size:14px!important;margin-left:10px;min-height:28px;padding:0;width:28px;height:28px;background:#f5f5f7;border-radius:4px}.person .el-header .el-search-select[data-v-852a538b]{margin-left:10px;padding:3px 0 0;width:80px;height:20px!important}.person .el-header .el-search-select[data-v-852a538b] .el-input__wrapper{background-color:transparent!important;box-shadow:none!important;border:none!important;padding:0!important;line-height:1!important;height:20px!important}.person .el-header .el-search-select[data-v-852a538b] .el-input__suffix{padding:0!important;height:20px!important}.person .el-header .el-search-select[data-v-852a538b] .el-input__suffix-inner{margin-left:-28px}.person .el-header .el-search-select[data-v-852a538b] .el-input__inner{height:20px!important;line-height:20px!important}.person .el-header .search-input[data-v-852a538b]{width:300px}.person .el-header .search-input[data-v-852a538b] .el-input__wrapper{border-radius:4px;background-color:#f5f7fa}.person .el-header .search-input[data-v-852a538b] .el-input__wrapper.is-focus{background-color:#fff}.person .el-header .search-input[data-v-852a538b] .el-input__inner{height:32px;line-height:32px;font-size:14px}.person .el-header .search-input[data-v-852a538b] .el-input__inner::placeholder{color:#909399}.person .category-aside .category-menu[data-v-852a538b]{border-right:none}.person .category-aside .category-menu .el-menu-item[data-v-852a538b]{height:28px;line-height:28px;font-size:14px;color:#606266;position:relative;transition:all .3s;cursor:pointer;border-radius:4px;margin:4px 0 4px 20px}.person .category-aside .category-menu .el-menu-item[data-v-852a538b]:not(.is-active){background-color:transparent;color:#606266}.person .category-aside .category-menu .el-menu-item[data-v-852a538b]:hover:not(.is-active){background-color:#f0f7ff;color:#409eff}.person .category-aside .category-menu .el-menu-item[data-v-852a538b]:focus{outline:none;background-color:#f0f7ff}.person .category-aside .category-menu .el-menu-item[data-v-852a538b]:focus:not(.is-active){color:#409eff}.person .category-aside .category-menu .el-menu-item.is-active[data-v-852a538b]{background-color:#536ce6;color:#fff;font-weight:500}.person .category-aside .category-menu .el-menu-item[data-v-852a538b]:active{background-color:#dcedff}.person .app-main[data-v-852a538b]{padding:12px 20px;overflow-y:auto}.person .app-main .category-section[data-v-852a538b]{margin-bottom:24px}.person .app-main .category-section .category-title[data-v-852a538b]{font-size:16px;font-weight:700;margin-bottom:12px;margin-top:0;padding-bottom:6px;width:fit-content;min-width:100px;max-width:200px;line-height:1.2}.person .app-main .category-section .apps-container[data-v-852a538b]{display:grid;grid-template-columns:repeat(auto-fill,265px);gap:16px;padding:16px 0}.person .app-main .category-section .apps-container .app-card[data-v-852a538b]{width:257px;height:64px;margin-right:8px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px;position:relative;display:flex;justify-content:center}.person .app-main .category-section .apps-container .app-card[data-v-852a538b] .el-link{display:flex;flex-direction:column;align-items:center;justify-content:space-between;width:100%;height:100%;padding:0}.person .app-main .category-section .apps-container .app-card[data-v-852a538b] .el-link__inner{width:100%!important;height:100%!important}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-852a538b]{width:48px;height:48px;margin-left:13px;display:flex;align-items:center}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-852a538b] .el-avatar{width:48px;height:48px;color:#fff;font-size:16px;display:flex;align-items:center;justify-content:center;text-align:center}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-852a538b] .el-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.person .app-main .category-section .apps-container .app-card .app-info[data-v-852a538b]{width:100%;display:flex;flex-direction:column;justify-content:center}.person .app-main .category-section .apps-container .app-card .app-info .app-name[data-v-852a538b]{height:20px;width:56px;font-size:14px;font-family:PingFang SC,PingFang SC-Medium;font-weight:700;text-align:left;color:#282a33;line-height:20px;margin-left:12px;margin-top:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.person .app-main .category-section .apps-container .app-card .app-info .app-remark[data-v-852a538b]{margin-left:12px;width:168px;height:17px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#686e84;line-height:17px}.person .app-main .category-section .apps-container .app-card .status-badge[data-v-852a538b]{position:absolute;top:-6px;right:-6px;padding:1px 6px;border-radius:8px;font-size:11px;background-color:#d3c5a5;color:#000;font-weight:500;z-index:2}.person .app-main .category-section .apps-container .app-card[data-v-852a538b] .el-link:hover .icon-wrapper{transform:scale(1.05)}.person .app-main .category-section .apps-container .app-card.disabled[data-v-852a538b] .el-link{cursor:not-allowed}.person .app-main .category-section .apps-container .app-card.disabled[data-v-852a538b] .el-link:hover .icon-wrapper{transform:none}.person .app-main .category-section .apps-container .app-card.disabled .icon-wrapper[data-v-852a538b]{opacity:.6}.person .app-main .category-section .apps-container .app-card.disabled[data-v-852a538b] .el-avatar{filter:grayscale(100%)}.person .app-main .category-section[data-v-852a538b]:first-child{margin-top:0}.person .app-main .el-recent-access[data-v-852a538b]{margin-left:4px;margin-top:3px;width:56px;height:20px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#282a33;line-height:20px}.person .app-main .el-recent-data[data-v-852a538b]{margin-left:15px;height:20px}.person .app-main .el-recent-item[data-v-852a538b]{margin-top:2px;margin-right:6px;height:25px;padding:4px 0 4px 6px;background:#f5f6fe;border-radius:4px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;color:#536ce6}.person .app-main .el-recent-icon[data-v-852a538b]{width:8px;height:8px;margin:8px 6px 8px 5px}.person .app-main .el-recent-clear[data-v-852a538b]{opacity:.6;margin-top:6px;position:absolute;width:14px;height:14px;right:6px}@media screen and (max-width: 1439px){.apps-container[data-v-852a538b]{grid-template-columns:repeat(auto-fill,180px)}.apps-container .app-card[data-v-852a538b]{width:180px;height:200px}}@media screen and (max-width: 1023px){.apps-container[data-v-852a538b]{grid-template-columns:repeat(auto-fill,200px)}.apps-container .app-card[data-v-852a538b]{width:200px;height:220px}}@media screen and (max-width: 767px){.apps-container[data-v-852a538b]{grid-template-columns:repeat(2,1fr)}.apps-container .app-card[data-v-852a538b]{width:100%;height:auto;aspect-ratio:1/1.125}}.tooltip-content[data-v-852a538b]{width:200px;text-align:center}.web-link[data-v-852a538b]{color:#409eff;text-decoration:none;word-break:break-all}.el-select__popper[data-v-852a538b]{background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1);margin-left:-10px!important;right:15px;top:110px;max-width:88px}.el-select__popper .el-select-dropdown__item[data-v-852a538b]{width:72px;height:28px;border-radius:4px;margin-left:7px;margin-bottom:4px;padding:0 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;line-height:20px;display:flex;align-items:center;background:#f5f5f7!important}.el-select__popper .el-select-dropdown__item.selected[data-v-852a538b]{color:#fff;background:#536ce6!important}.text-center[data-v-852a538b]{text-align:center}.web-link[data-v-852a538b]{color:#409eff;text-decoration:none}.web-link[data-v-852a538b]:hover{text-decoration:underline}.el-message{white-space:pre-line!important;line-height:1.5!important;padding:12px 20px!important}\n',document.head.appendChild(C),{setters:[function(e){n=e.x,o=e.r,i=e.D,c=e.E,l=e.G,s=e.b,u=e.u,d=e.h,f=e.o,g=e.d,m=e.e,h=e.j,v=e.w,b=e._,x=e.F,y=e.i,w=e.f,k=e.k,_=e.t,S=e.C,O=e.g,P=e.H,j=e.I,F=e.M}],execute:function(){var t={class:"person"},C={class:"el-search"},W={class:"category-title"},z={class:"apps-container"},D={key:0,class:"status-badge"},T={class:"icon-wrapper"},E={class:"tooltip-content text-center"},U={key:0},I={key:1},R={class:"app-info"},A={class:"app-name"},G=Object.assign({name:"AppPage"},{setup:function(e){var b=o(""),G=o(null),N=o([]),M=o([]),V=o("1"),B=o(!1),J=o("standard"),L=i([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),q=o(null),H=o(!1),X=function(e){F({message:e,type:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",duration:arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3})},K=function(){var e=p(r().m((function e(t){return r().w((function(e){for(;;)if(0===e.n)return e.a(2,new Promise((function(e,a){var n,o=!1,i=function(){var i=p(r().m((function i(){var p,c,l;return r().w((function(r){for(;;)switch(r.n){case 0:return r.p=0,r.n=1,new Promise((function(e,t){if(q.value&&q.value.readyState===WebSocket.OPEN)e(q.value);else{var a=new WebSocket("ws://localhost:50001");H.value=!0,a.onopen=function(){console.log("WebSocket Connected"),q.value=a,H.value=!1,e(a)},a.onmessage=function(e){var t=e.data;t.startsWith("Ok")||t.startsWith("Failed")&&X(t,"error")},a.onclose=function(){console.log("WebSocket Disconnected"),q.value=null,H.value=!1},a.onerror=function(e){console.error("WebSocket Error:",e),H.value=!1,t(e)},setTimeout((function(){H.value&&(H.value=!1,a.close(),t(new Error("连接超时")))}),5e3)}}));case 1:p=r.v,c={action:3,msg:t},n=setTimeout((function(){o||(p.close(),a(new Error("启动超时：未收到响应")))}),3e3),p.onmessage=function(t){o=!0,clearTimeout(n);var r=t.data;r.startsWith("Ok")?e():a(new Error(r))},p.send(JSON.stringify(c)),console.log("发送消息:",c),r.n=3;break;case 2:r.p=2,l=r.v,clearTimeout(n),a(l);case 3:return r.a(2)}}),i,null,[[0,2]])})));return function(){return i.apply(this,arguments)}}();i()})))}),e)})));return function(t){return e.apply(this,arguments)}}(),Q=function(){var e=p(r().m((function e(t){var a;return r().w((function(e){for(;;)switch(e.n){case 0:if(t.WebUrl&&!t.maint){e.n=1;break}return e.a(2);case 1:if(!t.WebUrl.toLowerCase().startsWith("cs:")){e.n=6;break}return a=t.WebUrl.substring(3),e.p=2,X("正在启动爱尔企业浏览器...","info"),e.n=3,K(a);case 3:X("启动成功","success"),e.n=5;break;case 4:e.p=4,e.v,X("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3);case 5:e.n=7;break;case 6:window.open(t.WebUrl,"_blank");case 7:return e.a(2)}}),e,null,[[2,4]])})));return function(t){return e.apply(this,arguments)}}();c((function(){q.value&&(q.value.close(),q.value=null)}));var Y=function(e){for(var t=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],a=0,n=0;n<e.length;n++)a+=e.charCodeAt(n);return t[a%t.length]},Z=function(){B.value=!0},$=function(e){G.value=parseInt(e),M.value=e?N.value.filter((function(t){return t.id===parseInt(e)})):N.value},ee=function(){if(b.value){var e=b.value.toLowerCase().trim();M.value=N.value.map((function(t){return a(a({},t),{},{apps:t.apps.filter((function(t){return t.app_name.toLowerCase().includes(e)}))})})).filter((function(e){return e.apps.length>0}))}else M.value=N.value},te=function(){var e=p(r().m((function e(){var t,a,o,i;return r().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,n({url:"/console/v1/application/getuserapp",method:"get"});case 1:t=e.v,a=t.data,console.log("API返回数据:",a),0===a.code&&a.data&&(o=a.data.map((function(e,t){return{id:t+1,name:e.category,apps:e.apps.map((function(e){return{id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl}}))}})),console.log("格式化后的数据:",o),N.value=o,M.value=o,o.length>0&&(G.value=o[0].id,V.value=o[0].id.toString())),e.n=3;break;case 2:e.p=2,i=e.v,console.error("API调用出错:",i);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();l((function(){te()}));var ae=s(),ne=u().query,re=null;try{if("file:"!==document.location.protocol){var oe=new XMLHttpRequest;oe.open("GET",document.location,!1),oe.send(null),re=oe.getResponseHeader("X-Corp-ID")}}catch(fe){console.warn("无法获取 X-Corp-ID header，使用默认值:",fe)}var ie={action:0,msg:{token:ae.token.accessToken,refreshToken:ae.token.refreshToken,realm:re||"default"},platform:document.location.hostname},pe=ne.wp||50001,ce=o({}),le=o("ws://127.0.0.1:".concat(pe)),se=navigator.platform;0!==se.indexOf("Mac")&&"MacIntel"!==se||(le.value="wss://127.0.0.1:".concat(pe));var ue=function(e){console.log(e,"0"),ce.value.send(e)},de=function(){console.log("socket断开链接"),ce.value.close()};return console.log("asecagent://?web=".concat(JSON.stringify(ie))),ce.value=new WebSocket(le.value),ce.value.onopen=function(){console.log("socket连接成功"),ue(JSON.stringify(ie))},ce.value.onmessage=function(e){console.log(e),de()},ce.value.onerror=function(){console.log("socket连接错误:"+le.value),window.location.href="asecagent://?web=".concat(JSON.stringify(ie))},function(e,a){var n=d("base-input"),r=d("base-button"),o=d("base-option"),i=d("base-select"),p=d("el-header"),c=d("el-menu-item"),l=d("el-menu"),s=d("base-aside"),u=d("base-avatar"),F=d("el-tooltip"),G=d("el-link"),q=d("base-main"),H=d("base-container");return f(),g("div",null,[m("div",t,[h(p,null,{default:v((function(){return[a[3]||(a[3]=m("span",{class:"el-title"},"我的应用",-1)),m("span",C,[h(n,{class:"el-search-input",modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=function(e){return b.value=e}),placeholder:"搜索应用","prefix-icon":"Search",onInput:ee,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),h(r,{class:"el-search-btn",icon:"Refresh",size:"small"}),h(i,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:J.value,"onUpdate:modelValue":a[1]||(a[1]=function(e){return J.value=e}),placeholder:"Select",size:"small"},{default:v((function(){return[(f(!0),g(x,null,y(L,(function(e){return f(),w(o,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])]})),_:1,__:[3]}),h(H,null,{default:v((function(){return[h(s,{width:"96px",class:"category-aside"},{default:v((function(){return[h(l,{class:"category-menu",onSelect:$,"default-active":V.value},{default:v((function(){return[h(c,{index:"0",onClick:a[2]||(a[2]=function(e){return $(null)})},{default:v((function(){return a[4]||(a[4]=[k(" 全部 ")])})),_:1,__:[4]}),(f(!0),g(x,null,y(N.value,(function(e){return f(),w(c,{key:e.id,index:e.id.toString()},{default:v((function(){return[k(_(e.name),1)]})),_:2},1032,["index"])})),128))]})),_:1},8,["default-active"])]})),_:1}),h(q,{class:"app-main"},{default:v((function(){return[(f(!0),g(x,null,y(M.value,(function(e){return f(),g("div",{key:e.id,class:"category-section"},[m("h3",W,_(e.name),1),m("div",z,[(f(!0),g(x,null,y(e.apps,(function(e){return f(),g("div",{key:e.id,class:S(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(f(),g("div",D," 维护中 ")):O("",!0),h(G,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:P((function(t){return Q(e)}),["prevent"])},{default:v((function(){return[m("div",T,[h(F,{effect:"light",placement:"bottom"},{content:v((function(){return[m("div",E,[e.WebUrl?(f(),g("span",U,_(e.WebUrl),1)):(f(),g("span",I,"暂无访问地址"))])]})),default:v((function(){return[h(u,{shape:"square",size:48,src:e.icon,onError:Z,style:j(!e.icon||B.value?"background-color: ".concat(Y(e.app_name)," !important"):"")},{default:v((function(){return[k(_(!e.icon||B.value?e.app_name.slice(0,2):""),1)]})),_:2},1032,["src","style"])]})),_:2},1024)]),m("div",R,[m("div",A,_(e.app_name),1),a[5]||(a[5]=m("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])]})),_:2},1032,["disabled","onClick"])],2)})),128))])])})),128))]})),_:1})]})),_:1})])])}}});e("default",b(G,[["__scopeId","data-v-852a538b"]]))}}}))}();
