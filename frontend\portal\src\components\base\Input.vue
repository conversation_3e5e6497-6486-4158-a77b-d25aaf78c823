<template>
  <div class="input-wrapper">
    <input
      ref="inputRef"
      :class="inputClass"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :maxlength="maxlength"
      :autocomplete="autocomplete"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: ''
  },
  autocomplete: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  maxlength: {
    type: [String, Number],
    default: undefined
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'input', 'change', 'focus', 'blur'])

const inputRef = ref(null)
const focused = ref(false)

const inputClass = computed(() => {
  const classes = ['base-input']

  if (props.size !== 'default') {
    classes.push(`base-input--${props.size}`)
  }

  if (focused.value) {
    classes.push('base-input--focused')
  }

  if (props.disabled) {
    classes.push('base-input--disabled')
  }

  return classes.join(' ')
})

const handleInput = (event) => {
  const value = event.target.value
  emit('update:modelValue', value)
  emit('input', value, event)
}

const handleChange = (event) => {
  emit('change', event.target.value, event)
}

const handleFocus = (event) => {
  focused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  focused.value = false
  emit('blur', event)
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped>
.input-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.base-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s, box-shadow 0.2s;
  outline: none;
  box-sizing: border-box;
}

.base-input:hover {
  border-color: #c0c4cc;
}

.base-input--focused {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.base-input--disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.base-input--small {
  padding: 5px 8px;
  font-size: 12px;
}

.base-input--large {
  padding: 12px 16px;
  font-size: 16px;
}
</style>
