/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function c(e,o,a,i){var c=o&&o.prototype instanceof u?o:u,l=Object.create(c.prototype);return r(l,"_invoke",function(e,r,o){var a,i,c,u=0,l=o||[],f=!1,p={p:0,n:0,v:t,a:v,f:v.bind(t,4),d:function(e,r){return a=e,i=0,c=t,p.n=r,s}};function v(e,r){for(i=e,c=r,n=0;!f&&u&&!o&&n<l.length;n++){var o,a=l[n],v=p.p,d=a[2];e>3?(o=d===r)&&(c=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=t):a[0]<=v&&((o=e<2&&v<a[1])?(i=0,p.v=r,p.n=a[1]):v<d&&(o=e<3||a[0]>r||r>d)&&(a[4]=e,a[5]=r,p.n=d,i=0))}if(o||e>1)return s;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&v(l,d),i=l,c=d;(n=i<2?t:c)||!f;){a||(i?i<3?(i>1&&(p.n=-1),v(i,c)):p.n=c:p.v=c);try{if(u=2,a){if(i||(o="next"),n=a[o]){if(!(n=n.call(a,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,i<2&&(i=0)}else 1===i&&(n=a.return)&&n.call(a),i<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=t}else if((n=(f=p.n<0)?c:e.call(r,p))!==s)break}catch(n){a=t,i=1,c=n}finally{u=1}}return{value:n,done:f}}}(e,a,i),!0),l}var s={};function u(){}function l(){}function f(){}n=Object.getPrototypeOf;var p=[][a]?n(n([][a]())):(r(n={},a,(function(){return this})),n),v=f.prototype=u.prototype=Object.create(p);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,r(e,i,"GeneratorFunction")),e.prototype=Object.create(v),e}return l.prototype=f,r(v,"constructor",f),r(f,"constructor",l),l.displayName="GeneratorFunction",r(f,i,"GeneratorFunction"),r(v),r(v,i,"Generator"),r(v,a,(function(){return this})),r(v,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:d}})()}function r(e,t,n,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}r=function(e,t,n,o){if(t)a?a(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{var i=function(t,n){r(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},r(e,t,n,o)}function t(e,r,t,n,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void t(e)}c.done?r(s):Promise.resolve(s).then(n,o)}System.register(["./index-legacy.21dbeba9.js"],(function(r,n){"use strict";var o,a,i,c,s,u,l,f,p,v,d,g,m,h=document.createElement("style");return h.textContent='@charset "UTF-8";.server-config[data-v-17e3a57d]{width:100%;max-width:400px;margin:0 auto;padding:20px}.server-config .config-header[data-v-17e3a57d]{display:flex;align-items:center;justify-content:center;margin-bottom:30px}.server-config .config-header .title[data-v-17e3a57d]{font-size:20px;font-weight:600;color:#333}.server-config .config-form[data-v-17e3a57d]{margin-bottom:30px}.server-config .config-form .label[data-v-17e3a57d]{display:block;margin-bottom:8px;font-weight:500;color:#333}.server-config .config-form .input-tip[data-v-17e3a57d]{margin-top:6px;font-size:12px;color:#999;line-height:1.4}.server-config .config-form .submit-button[data-v-17e3a57d]{width:100%;height:44px;font-size:16px;font-weight:500}.server-config .config-tips .tip-item[data-v-17e3a57d]{display:flex;align-items:center;margin-bottom:12px;font-size:14px;color:#666}.server-config .config-tips .tip-item[data-v-17e3a57d]:last-child{margin-bottom:0}\n',document.head.appendChild(h),{setters:[function(e){o=e._,a=e.r,i=e.K,c=e.h,s=e.o,u=e.d,l=e.e,f=e.j,p=e.w,v=e.k,d=e.$,g=e.a1,m=e.M}],execute:function(){var n={class:"server-config"},h={class:"config-form"},b={class:"config-tips"},y={class:"tip-item"},x={class:"tip-item"},w=Object.assign({name:"ServerConfig"},{emits:["server-configured"],setup:function(r,o){var w=o.emit,_=a(null),k=a(!1),j=i({serverUrl:""}),U={serverUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的服务器地址（需包含 http:// 或 https://）",trigger:"blur"}]},O=function(){var r,n=(r=e().m((function r(){var t,n,o;return e().w((function(e){for(;;)switch(e.n){case 0:if(_.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,_.value.validate();case 2:if(e.v){e.n=3;break}return e.a(2);case 3:if(k.value=!0,!g(j.serverUrl)){e.n=10;break}return e.p=4,e.n=5,fetch("".concat(j.serverUrl,"/auth/idp/list"),{method:"GET",headers:{"Content-Type":"application/json"},timeout:5e3});case 5:if(!(t=e.v).ok&&401!==t.status){e.n=6;break}m.success("服务器连接成功！"),w("server-configured",j.serverUrl),e.n=7;break;case 6:throw new Error("服务器响应错误: ".concat(t.status));case 7:e.n=9;break;case 8:e.p=8,n=e.v,console.warn("服务器连接测试失败，但仍然保存配置:",n),m.warning("服务器地址已保存，但连接测试失败，请检查网络或服务器状态"),w("server-configured",j.serverUrl);case 9:e.n=11;break;case 10:m.error("服务器地址格式错误");case 11:e.n=13;break;case 12:e.p=12,o=e.v,console.error("配置服务器失败:",o),m.error("配置失败，请检查服务器地址格式");case 13:return e.p=13,k.value=!1,e.f(13);case 14:return e.a(2)}}),r,null,[[4,8],[1,12,13,14]])})),function(){var e=this,n=arguments;return new Promise((function(o,a){var i=r.apply(e,n);function c(e){t(i,o,a,c,s,"next",e)}function s(e){t(i,o,a,c,s,"throw",e)}c(void 0)}))});return function(){return n.apply(this,arguments)}}(),T=localStorage.getItem("server_host");return T&&(j.serverUrl=T),function(e,r){var t=c("base-input"),o=c("base-form-item"),a=c("base-button"),i=c("base-form"),g=c("base-icon");return s(),u("div",n,[r[5]||(r[5]=l("div",{class:"config-header"},[l("span",{class:"title"},"平台地址")],-1)),l("div",h,[f(i,{ref_key:"serverForm",ref:_,model:j,rules:U,onKeyup:d(O,["enter"])},{default:p((function(){return[f(o,{prop:"serverUrl"},{default:p((function(){return[f(t,{modelValue:j.serverUrl,"onUpdate:modelValue":r[0]||(r[0]=function(e){return j.serverUrl=e}),size:"large",placeholder:"输入您连接的平台服务器地址","suffix-icon":"link"},null,8,["modelValue"]),r[1]||(r[1]=l("div",{class:"input-tip"},' 请输入平台地址，如：https://*************" ',-1))]})),_:1,__:[1]}),f(o,null,{default:p((function(){return[f(a,{type:"primary",size:"large",class:"submit-button",loading:k.value,onClick:O},{default:p((function(){return r[2]||(r[2]=[v(" 连接服务器 ")])})),_:1,__:[2]},8,["loading"])]})),_:1})]})),_:1},8,["model"])]),l("div",b,[l("div",y,[f(g,{name:"warning",style:{color:"#f4a261","margin-right":"6px"}}),r[3]||(r[3]=l("span",null,"请确保服务器地址正确且网络连通",-1))]),l("div",x,[f(g,{name:"check",style:{color:"#67c23a","margin-right":"6px"}}),r[4]||(r[4]=l("span",null,"配置成功后将自动跳转到登录页面",-1))])])])}}});r("default",o(w,[["__scopeId","data-v-17e3a57d"],["__file","D:/asec-platform/frontend/portal/src/view/login/serverConfig/serverConfig.vue"]]))}}}))}();
