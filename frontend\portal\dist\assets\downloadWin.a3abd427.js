/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
import{_ as e,Q as a,r as o,O as l,R as t,h as n,o as s,g as i,w as r,j as d,J as c,e as u,B as v,T as m,d as p,k as g,f as w,M as f}from"./index.e2f48a61.js";import{_ as h}from"./ASD.492c8837.js";import{g as b}from"./browser.40ec6957.js";const y={style:{background:"'#273444'"}},x={class:"downloadWin"},k={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},_={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},F={key:1,class:"download-complete"},T=e(Object.assign({name:"downloadWin"},{setup(e){a((e=>({"dc46818e-activeBackground":e.activeBackground,"dc46818e-normalText":e.normalText})));const T=o(!1),z=o(!0),B=o(!1),L=o("1"),j=o({});j.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const E=o(!1),R=o(0),S=o(!1);let C=0;const O=()=>{const e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(B.value=!1,z.value=!1,T.value=!0):(B.value=!1,z.value=!0,T.value=!1)};O();const W=o(!1);l((()=>{t.emit("collapse",T.value),t.emit("mobile",B.value),t.on("showLoading",(()=>{W.value=!0})),t.on("closeLoading",(()=>{W.value=!1})),window.onresize=()=>(O(),t.emit("collapse",T.value),void t.emit("mobile",B.value))}));const D=o("#1f2a36"),M=o(!1),U=()=>{T.value=!T.value,z.value=!T.value,M.value=!T.value,t.emit("collapse",T.value)},$=e=>100===e?"下载完成":`${e}%`,q=async(e,a)=>{try{const o=await H(e);P(o,a)}catch(o){if(C<3&&"网络连接超时"===o.message)return C++,q(e,a);throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${o.message}`)}},H=e=>new Promise(((a,o)=>{const l=new XMLHttpRequest;l.open("GET",e,!0),l.responseType="blob",l.timeout=3e5;let t=Date.now();l.onprogress=e=>{if(e.lengthComputable){const a=e.loaded/e.total*100;R.value=Math.round(a)}else{const a=(Date.now()-t)/1e3,o=60*(e.loaded/a),l=e.loaded/o*100;R.value=Math.min(99,Math.round(l))}},l.onload=()=>{200===l.status?a(l.response):o(new Error(`HTTP 错误: ${l.status}`))},l.onerror=()=>{o(new Error("网络错误"))},l.ontimeout=()=>{o(new Error("网络连接超时"))},l.send()})),P=(e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const o=document.createElement("a"),l=document.querySelector("body");o.href=window.URL.createObjectURL(e),o.download=a,o.style.display="none",l.appendChild(o),o.click(),l.removeChild(o),window.URL.revokeObjectURL(o.href)}};return(e,a)=>{const o=n("base-row"),l=n("base-icon"),t=n("el-menu-item"),O=n("el-menu"),W=n("el-scrollbar"),H=n("base-aside"),P=n("el-link"),A=n("el-progress"),G=n("base-main"),I=n("base-container");return s(),i(I,{class:"layout-cont"},{default:r((()=>[d(I,{class:c([z.value?"openside":"hideside",B.value?"mobile":""])},{default:r((()=>[d(o,{class:c([M.value?"shadowBg":""]),onClick:a[0]||(a[0]=e=>(M.value=!M.value,z.value=!!T.value,void U()))},null,8,["class"]),d(H,{class:"main-cont main-left gva-aside"},{default:r((()=>[u("div",{class:c(["tilte",[z.value?"openlogoimg":"hidelogoimg"]]),style:v({background:D.value})},a[2]||(a[2]=[u("img",{alt:"",class:"logoimg",src:h},null,-1)]),6),u("div",y,[d(W,{style:{height:"calc(100vh - 110px)"}},{default:r((()=>[d(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:r((()=>[d(O,{collapse:T.value,"collapse-transition":!1,"default-active":L.value,"background-color":j.value.background,"active-text-color":j.value.activeText,class:"el-menu-vertical","unique-opened":""},{default:r((()=>[d(t,{index:"1"},{default:r((()=>[d(l,{name:"xiazai",size:"16px"}),a[3]||(a[3]=u("span",null,"客户端下载",-1))])),_:1,__:[3]})])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})]),u("div",{class:"footer",style:v({background:D.value})},[u("div",{class:"menu-total",onClick:U},[T.value?(s(),i(l,{key:0,color:"#FFFFFF",size:"14px",name:"expand"})):(s(),i(l,{key:1,color:"#FFFFFF",size:"14px",name:"fold"}))])],4)])),_:1}),d(G,{class:"main-cont main-right client"},{default:r((()=>[u("div",x,[u("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:a[1]||(a[1]=e=>(async e=>{if("windows"===e){E.value=!0,R.value=0,S.value=!1,C=0;try{const a=await b({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);{const e=window.location.port,o=new URL(a.data.data.download_url);let l;e?o.toString().includes("asec-deploy")?l=a.data.data.download_url:(o.port=e,l=o.toString()):(o.port="",l=o.toString());const t=e?a.data.data.latest_filename.replace(/@(\d+)/,`@${e}`):a.data.data.latest_filename;await q(l,t),S.value=!0,f({type:"success",message:"下载完成"})}}catch(a){f({type:"error",message:a.message||"下载失败，请联系管理员"})}finally{E.value=!1,setTimeout((()=>{S.value=!1}),3e3)}}})("windows"))},[(s(),p("svg",k,a[4]||(a[4]=[u("use",{"xlink:href":"#icon-windows"},null,-1)]))),(s(),p("svg",_,a[5]||(a[5]=[u("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[8]||(a[8]=u("br",null,null,-1)),d(P,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:r((()=>a[6]||(a[6]=[g(" Windows客户端 ")]))),_:1,__:[6]}),d(P,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:r((()=>a[7]||(a[7]=[g(" 点击下载Windows客户端 ")]))),_:1,__:[7]}),E.value?(s(),i(A,{key:0,percentage:R.value,format:$,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):w("v-if",!0),S.value?(s(),p("div",F,"下载完成")):w("v-if",!0)])])])),_:1})])),_:1},8,["class"])])),_:1})}}}),[["__scopeId","data-v-dc46818e"],["__file","D:/asec-platform/frontend/portal/src/view/login/downloadWin.vue"]]);export{T as default};
