/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
import{_ as e,D as t,h as o,E as s,o as i,d as a,e as n,f as r,G as d,t as l,j as c,H as u}from"./index.342352cf.js";import{_ as h}from"./ASD.492c8837.js";const g=""+new URL("avator.bd83723a.png",import.meta.url).href,m={class:"layout-header"},w={id:"u-header-menu",class:"right-wrapper"},p={id:"u-avator",ref:"countMenu"},f={class:"user-info"},v={class:"user-name"},y={class:"dropdown-menu header-count-menu"};const I=e({name:"ClientHeader",data:()=>({drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duany<PERSON>",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1,dropdownVisible:!1}),computed:{isAccess:()=>!1},watch:{userId(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted(){},beforeDestroy(){},methods:{minimizeWnd(){t.minimizeWnd()},maximizeWndOrNot(){this.isMaxWindow?(t.normalnizeWnd(),this.isMaxWindow=!1):(t.maximizeWnd(),this.isMaxWindow=!0)},toggleDropdown(){this.dropdownVisible=!this.dropdownVisible},closeDropdown(){this.dropdownVisible=!1},dropdownVisiHandle(){},async closeWnd(){t.hideWend()},async setHandle(e){if("changeLange"===e){const e=this.$i18n.locale;setLang("zh"===e?"en":"zh")}else"changeMode"===e&&this.changeMode()},userMenuHandle(e,t={}){switch(this.closeDropdown(),this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},async logoutHandle(){if(this.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1===this.logoutType){try{let e;this.isSsoAuth()&&(e=await ssoLogout(_.get(this.clientInfo,"accessStatus.lastAuthType")));const o=await proxyApi.cutoffDevice({device_id:_.get(this.clientInfo,"detail.DeviceID",0),remark:"LogOut"});if(0!==parseInt(_.get(o,"errcode")))return _.get(o,"errmsg")||this.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),void loading.destory();commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),await t.logOut({IsCredibleDevice:_.get(this.clientInfo,"detail.IsTrustDev","0")}),this.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,this.isDot1xMode&&this.setClientInfo(_.merge({},this.clientInfo,{basic:{IsOnline:0}})),this.setAuthInfo({...this.authInfo,basic:{}}),this.setClientInfo({...this.clientInfo,accessStatus:{}});const s=(new Date).getTime();this.$router.push({name:"message",params:{forceTo:!0},query:{t:s}}),_.isString(e)&&""!==e&&(console.log("logoutUrl:".logoutUrl),t.windowOpenUrl(e))}catch(e){console.error("退出登录错误",e)}loading.destory()}},async getCountMenuWidth(){const e=this.isZtpUser?44:0,o=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);try{await t.init(),await t.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(o)+e})}catch(s){console.warn("设置标题尺寸失败:",s)}},hdEventHandle(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer(){this.deviceInnerBack=!1},changeVisible(e){this.drawer=e}}},[["render",function(e,t,I,D,k,b){const x=o("base-icon"),W=s("click-outside");return i(),a("div",m,[t[5]||(t[5]=n("div",{class:"header-logo"},[r("如果图片加载失败就隐藏"),n("img",{src:h,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),t[6]||(t[6]=n("div",{id:"u-electron-drag"},null,-1)),n("ul",w,[n("li",p,[d((i(),a("div",{class:"base-dropdown",id:"ui-headNav-header-div-account_info",onClick:t[1]||(t[1]=(...e)=>b.toggleDropdown&&b.toggleDropdown(...e))},[n("div",f,[t[2]||(t[2]=n("div",{class:"user-face"},[n("img",{src:g,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),n("span",v,l(k.username),1)]),d(n("div",y,[n("div",{class:"dropdown-item",id:"ui-headNav-header-li-cancel_account",onClick:t[0]||(t[0]=e=>b.userMenuHandle("lougOut"))},[c(x,{class:"dropdown-item-icon",name:"logout"}),t[3]||(t[3]=n("span",{class:"dropdown-item-text"},"注销登录",-1))])],512),[[u,k.dropdownVisible]])])),[[W,b.closeDropdown]])],512),t[4]||(t[4]=n("div",{class:"user-divider"},null,-1)),c(x,{class:"window-operate",name:k.isMaxWindow?"fullscreen_exit":"fullscreen",onClick:b.maximizeWndOrNot},null,8,["name","onClick"]),c(x,{class:"window-operate",name:"minus",onClick:b.minimizeWnd},null,8,["onClick"]),c(x,{class:"window-operate",name:"close",style:{"margin-right":"16px"},onClick:b.closeWnd},null,8,["onClick"])])])}],["__scopeId","data-v-30488b75"],["__file","D:/asec-platform/frontend/portal/src/view/client/header.vue"]]);export{I as default};
