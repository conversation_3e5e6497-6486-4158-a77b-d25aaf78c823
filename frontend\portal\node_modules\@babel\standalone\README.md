# @babel/standalone

> Standalone build of Babel for use in non-Node.js environments.

See our website [@babel/standalone](https://babeljs.io/docs/babel-standalone) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20standalone%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/standalone
```

or using yarn:

```sh
yarn add @babel/standalone --dev
```
