/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
System.register(["./index-legacy.d09203da.js"],(function(e,n){"use strict";var t,u,r,i,o,a,l,c,d,f,s;return{setters:[function(e){t=e.S,u=e.h,r=e.o,i=e.d,o=e.j,a=e.w,l=e.T,c=e.f,d=e.W,f=e.m,s=e.A}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[o(v,null,{default:a((function(e){var t=e.Component;return[o(l,{mode:"out-in",name:"el-fade-in-linear"},{default:a((function(){return[(r(),c(d,{include:f(n).keepAliveRouters},[(r(),c(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
