<template>
  <div class="setting-page">
    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="setting-container">
        <!-- 标签页导航 -->
        <div class="tabs-header">
          <div
            class="tab-item"
            :class="{ active: activeTab === 'general' }"
            @click="activeTab = 'general'"
          >
            通用设置
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'version' }"
            @click="activeTab = 'version'"
          >
            版本信息
          </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tabs-content">
          <!-- 通用设置页面 -->
          <div v-if="activeTab === 'general'" class="tab-panel">
            <div class="setting-section">
              <div class="setting-item setting-platformAddress">
                <label class="setting-label">平台地址</label>
                <base-input
                  v-model="platformAddress"
                  placeholder="输入您连接的平台服务器地址"
                  class="setting-input"
                  clearable
                />
              </div>

              <div class="setting-item">
                <label class="setting-label">启动选项</label>
                <div class="checkbox-group">
                  <base-checkbox v-model="autoStart" class="setting-checkbox">
                    开机自启动
                  </base-checkbox>
                  <base-checkbox v-model="autoConnect" class="setting-checkbox">
                    启动后自动连接
                  </base-checkbox>
                </div>
              </div>
            </div>
          </div>

          <!-- 版本信息页面 -->
          <div v-if="activeTab === 'version'" class="tab-panel">
            <div class="setting-section setting-update">
              <div class="setting-item">
                <label class="setting-label">更新选项</label>
                <div class="checkbox-group">
                  <base-checkbox v-model="autoUpdate" class="setting-checkbox">
                    自动检查更新
                  </base-checkbox>
                </div>
              </div>

              <div class="setting-item">
                <label class="setting-label">更新检查频率</label>
                <base-select v-model="updateFrequency" class="setting-select" placeholder="请选择">
                  <base-option label="每天" value="daily"></base-option>
                  <base-option label="每周" value="weekly"></base-option>
                  <base-option label="每月" value="monthly"></base-option>
                </base-select>
              </div>
            </div>
            <div class="about-section">
              <h3 class="about-title">关于安全客户端</h3>
              <div class="version-info">
                <div class="version-item">
                  <span class="version-label">当前版本</span>
                  <div class="version-value-group">
                    <span class="version-value">{{ appVersion }}</span>
                    <base-button text type="primary" size="small" @click="checkUpdate">
                      检查更新
                    </base-button>
                  </div>
                </div>
                <div class="version-item">
                  <span class="version-label">构建时间</span>
                  <span class="version-value">{{ buildTime }}</span>
                </div>
                <div class="version-item">
                  <span class="version-label">上次更新时间</span>
                  <span class="version-value">{{ lastUpdateTime }}</span>
                </div>
              </div>
              <div class="copyright">
                <p>© 2025 Security Systems Inc. 保留所有权利</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Message } from '@/components/base'

// 响应式数据
const activeTab = ref('general')
const platformAddress = ref('')
const autoStart = ref(false)
const autoConnect = ref(true) // 默认选中，如图片所示
const autoUpdate = ref(true) // 自动检查更新，默认选中
const updateFrequency = ref('daily') // 更新频率，默认每天

// 版本信息
const appVersion = ref('2.5.0')
const buildTime = ref('2025.03.21 09:00')
const lastUpdateTime = ref('2025.03.21 09:00')

// 初始化设置
onMounted(() => {
  loadSettings()
})

// 导航到其他页面
const navigateTo = (page) => {
  Message.info(`导航到${page}页面`)
  // 这里可以添加路由跳转逻辑
  // router.push(`/${page}`)
}

// 检查更新
const checkUpdate = async () => {
  Message.info('正在检查更新...')

  // 模拟检查更新过程
  setTimeout(() => {
    Message.success('当前已是最新版本')
  }, 1500)
}

// 加载设置
const loadSettings = () => {
  // 从本地存储或API加载设置
  const savedSettings = localStorage.getItem('appSettings')
  if (savedSettings) {
    const settings = JSON.parse(savedSettings)
    platformAddress.value = settings.platformAddress || ''
    autoStart.value = settings.autoStart || false
    autoConnect.value = settings.autoConnect !== undefined ? settings.autoConnect : true
    autoUpdate.value = settings.autoUpdate !== undefined ? settings.autoUpdate : true
    updateFrequency.value = settings.updateFrequency || 'daily'
  }
}

// 保存设置
const handleSave = () => {
  const settings = {
    platformAddress: platformAddress.value,
    autoStart: autoStart.value,
    autoConnect: autoConnect.value,
    autoUpdate: autoUpdate.value,
    updateFrequency: updateFrequency.value
  }

  // 保存到本地存储
  localStorage.setItem('appSettings', JSON.stringify(settings))

  Message.success('设置保存成功')

  // 这里可以添加其他保存逻辑，比如发送到服务器
}

// 取消操作
const handleCancel = () => {
  // 重新加载设置，恢复到保存前的状态
  loadSettings()
  Message.info('已取消修改')
}
</script>

<style lang="scss" scoped>
.setting-page {
  display: flex;
  height: 100vh;
  font-family: PingFang SC, PingFang SC-Regular;
}

// 左侧导航栏
.sidebar {
  width: 60px;
  background: #667eea;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  .sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .menu-item {
      width: 48px;
      height: 48px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: rgba(255, 255, 255, 0.7);
      font-size: 10px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
      }

      &.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      .menu-icon {
        width: 16px;
        height: 16px;
        margin-bottom: 4px;

        // 使用简单的几何形状模拟图标
        &::before {
          content: '';
          display: block;
          width: 100%;
          height: 100%;
          background: currentColor;
          border-radius: 2px;
        }
      }
    }
  }
}

// 主内容区域
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.setting-container {
  height: calc(100% - 38px);
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

// 标签页头部
.tabs-header {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;

  .tab-item {
    padding: 16px 24px;
    cursor: pointer;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      color: #536ce6;
      background: rgba(64, 158, 255, 0.05);
    }

    &.active {
      color: #536ce6;
      border-bottom-color: #536ce6;
      background: white;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: white;
      }
    }
  }
}

// 标签页内容
.tabs-content {
  min-height: 400px;

  .tab-panel {
    padding: 32px;

    .setting-update {
      padding-bottom: 24px; 
      margin-bottom: 32px;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

// 设置项样式
.setting-section {
  .setting-item {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .setting-platformAddress {
    padding-bottom: 24px; 
    border-bottom: 1px solid #f0f0f0;
  }

  .setting-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 12px;
  }

  .setting-input {
    width: 320px;

    :deep(.el-input__inner) {
      height: 40px;
      border-radius: 6px;

      &:focus {
        border-color: #536ce6;
      }
    }
  }

  .setting-select {
    width: 200px;

    :deep(.el-select__wrapper) {
      height: 40px;
      border-radius: 6px;
    }
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .setting-checkbox {
      :deep(.el-checkbox__label) {
        font-size: 14px;
        color: #606266;
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #536ce6;
        border-color: #536ce6;
      }
    }
  }
}

// 关于部分样式
.about-section {
  .about-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 24px 0;
  }
}

// 版本信息样式
.version-info {
  .version-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;

    &:last-child {
      border-bottom: none;
    }

    .version-label {
      font-size: 14px;
      color: #606266;
      flex-shrink: 0;
    }

    .version-value {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }

    .version-value-group {
      display: flex;
      align-items: center;
      gap: 12px;

      .version-value {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

// 版权信息
.copyright {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;

  p {
    font-size: 12px;
    color: #909399;
    margin: 0;
    text-align: center;
  }
}

// 底部操作按钮
.setting-footer {
  padding: 20px 32px;
  border-top: 1px solid #e4e7ed;
  background: #f5f7fa;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .setting-page {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    flex-direction: row;
    padding: 16px;

    .sidebar-menu {
      flex-direction: row;
      justify-content: center;
    }
  }

  .main-content {
    padding: 24px 32px 24px 32px;
  }

  .setting-container {
    margin: 0;
    border-radius: 0;
  }

  .tabs-content .tab-panel {
    padding: 20px;
  }

  .setting-footer {
    padding: 16px 20px;
  }

  .tabs-header .tab-item {
    padding: 12px 16px;
    font-size: 13px;
  }

  .setting-section .setting-select {
    width: 100%;
  }
}
</style>
