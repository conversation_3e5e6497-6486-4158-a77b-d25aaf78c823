import agentApi from '@/api/agentApi'

export const routerClientBefore = (to, from) => {
  const whiteListPath = ['/client', '/client/login', '/client/setting']
  // 白名单路径，不校验权限
  const isWhiteListPath = whiteListPath.includes(to.path)
  if (isWhiteListPath) {
    logger.log('客户端直接返回')
    return true
  }
  //logger.log('不校验登录')
  //return true
  logger.log('客户端查询登录状态:', to.path)
  // 客户端获取token TODO 判断登录状态
  let query = agentApi.getClientParams()
  query.redirect = to.href
  return {
    name: 'ClientNewLogin',
    query: query
  }
}

