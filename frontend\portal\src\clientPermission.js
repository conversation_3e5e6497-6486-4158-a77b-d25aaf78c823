export const routerClientBefore = (to, from) => {
  const whiteListPath = ['/client', '/client/login', '/client/setting']
  // 白名单路径，不校验权限
  const isWhiteListPath = whiteListPath.includes(to.path)
  if (isWhiteListPath) {
    logger.log('客户端直接返回')
    return true
  }
  logger.log('客户端查询登录状态:', to.path)
  // 客户端获取token TODO 判断登录状态
  return {
    name: 'ClientNewLogin',
    query: { redirect: to.href,  asec_debug: logger.debug},
  }
}

