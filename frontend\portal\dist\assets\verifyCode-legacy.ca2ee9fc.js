/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function u(e,a,o,i){var u=a&&a.prototype instanceof s?a:s,l=Object.create(u.prototype);return t(l,"_invoke",function(e,t,a){var o,i,u,s=0,l=a||[],f=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return o=e,i=0,u=n,d.n=t,c}};function p(e,t){for(i=e,u=t,r=0;!f&&s&&!a&&r<l.length;r++){var a,o=l[r],p=d.p,v=o[2];e>3?(a=v===t)&&(u=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=n):o[0]<=p&&((a=e<2&&p<o[1])?(i=0,d.v=t,d.n=o[1]):p<v&&(a=e<3||o[0]>t||t>v)&&(o[4]=e,o[5]=t,d.n=v,i=0))}if(a||e>1)return c;throw f=!0,t}return function(a,l,v){if(s>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,v),i=l,u=v;(r=i<2?n:u)||!f;){o||(i?i<3?(i>1&&(d.n=-1),p(i,u)):d.n=u:d.v=u);try{if(s=2,o){if(i||(a="next"),r=o[a]){if(!(r=r.call(o,u)))throw TypeError("iterator result is not an object");if(!r.done)return r;u=r.value,i<2&&(i=0)}else 1===i&&(r=o.return)&&r.call(o),i<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=n}else if((r=(f=d.n<0)?u:e.call(t,d))!==c)break}catch(r){o=n,i=1,u=r}finally{s=1}}return{value:r,done:f}}}(e,o,i),!0),l}var c={};function s(){}function l(){}function f(){}r=Object.getPrototypeOf;var d=[][o]?r(r([][o]())):(t(r={},o,(function(){return this})),r),p=f.prototype=s.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,t(e,i,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,t(p,"constructor",f),t(f,"constructor",l),l.displayName="GeneratorFunction",t(f,i,"GeneratorFunction"),t(p),t(p,i,"Generator"),t(p,o,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:u,m:v}})()}function t(e,n,r,a){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,n,r,a){if(n)o?o(e,n,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[n]=r;else{var i=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,r,a)}function n(e,t,n,r,a,o,i){try{var u=e[o](i),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,a)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var i=e.apply(t,r);function u(e){n(i,a,o,u,c,"next",e)}function c(e){n(i,a,o,u,c,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.f131204f.js"],(function(t,n){"use strict";var a,o,i,u,c,s,l,f,d,p,v,y,h,m,_,g,b,x,w=document.createElement("style");return w.textContent='@charset "UTF-8";.verify-code .title[data-v-0d05eb23]{height:60px;font-size:24px;text-align:center}.verify-code .message-text[data-v-0d05eb23]{margin-bottom:40px;text-align:center;line-height:1.5}\n',document.head.appendChild(w),{setters:[function(e){a=e.r,o=e.l,i=e.c,u=e.b,c=e.h,s=e.o,l=e.d,f=e.e,d=e.t,p=e.m,v=e.j,y=e.w,h=e.f,m=e.g,_=e._,g=e.n,b=e.M,x=e.k}],execute:function(){var n={class:"verify-code"},w={style:{top:"10px","margin-bottom":"25px","text-align":"center"}},k={class:"title"},T={key:0,class:"message-text"},j={key:1,class:"message-text"},C={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},O={style:{"text-align":"center"}},S=Object.assign({name:"VerifyCode"},{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}},secondaryType:{type:String,default:"sms"}},emits:["verification-success","back","cancel"],setup:function(t,_){var S=_.emit,I=a(""),P=o("userName");o("last_id"),o("isSecondary");var G=t;console.log("verifyCode组件接收到的属性:",{secondaryType:G.secondaryType,authInfo:G.auth_info,canVerify:"email"===G.secondaryType?!1!==G.auth_info.hasEmail:!1!==G.auth_info.notPhone});var N,E=S,V=i((function(){return void 0!==G.auth_info.hasContactInfo?G.auth_info.hasContactInfo:(G.secondaryType,!1!==G.auth_info.hasContactInfo)})),q=a(60),F=function(){clearInterval(N)},z=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:if(V.value){e.n=1;break}return e.a(2);case 1:return n={uniq_key:G.auth_info.uniqKey,idp_id:G.auth_id},e.p=2,e.n=3,g(n);case 3:200===(r=e.v).status&&-1!==r.data.code?(q.value=60,N=setInterval((function(){q.value--,0===q.value&&F()}),1e3)):(b({showClose:!0,message:r.data.msg,type:"error"}),q.value=0),e.n=5;break;case 4:e.p=4,e.v,b({showClose:!0,message:"发送验证码失败",type:"error"}),q.value=0;case 5:return e.a(2)}}),t,null,[[2,4]])})));return function(){return t.apply(this,arguments)}}();z();var K=u(),U=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:return n={uniq_key:G.auth_info.uniqKey,auth_code:I.value,user_name:G.userName||P.value,idp_id:G.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},e.n=1,K.LoginIn(n,"accessory");case 1:if(-1!==(r=e.v).code){e.n=2;break}return e.a(2);case 2:E("verification-success",r);case 3:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),A=function(){E("cancel")};return function(e,r){var a=c("base-button"),o=c("base-input");return s(),l("div",n,[f("div",w,[f("span",k,d("email"===t.secondaryType?"邮件认证":"短信认证"),1)]),f("div",null,[V.value?(s(),l("div",T,"验证码已发送至您账号("+d(t.userName||p(P))+")关联的"+d("email"===t.secondaryType?"邮箱":"手机")+"，请注意查收",1)):(s(),l("div",j,"您的账号("+d(t.userName||p(P))+")未关联"+d("email"===t.secondaryType?"邮箱":"手机号码")+"，请联系管理员！",1)),V.value?(s(),l("div",C,[v(o,{modelValue:I.value,"onUpdate:modelValue":r[0]||(r[0]=function(e){return I.value=e}),placeholder:"email"===t.secondaryType?"邮箱验证码":"短信验证码",class:"input-with-select"},{append:y((function(){return[v(a,{type:"info",disabled:q.value>0,onClick:z},{default:y((function(){return[x("重新发送 "+d(q.value>0?"(".concat(q.value,"秒)"):""),1)]})),_:1},8,["disabled"])]})),_:1},8,["modelValue","placeholder"])])):h("v-if",!0),f("div",O,[V.value?(s(),m(a,{key:0,type:"primary",size:"large",disabled:!I.value,onClick:U},{default:y((function(){return r[1]||(r[1]=[x("确 定 ")])})),_:1,__:[1]},8,["disabled"])):h("v-if",!0),v(a,{type:"info",size:"large",onClick:A},{default:y((function(){return r[2]||(r[2]=[x("取 消 ")])})),_:1,__:[2]})])])])}}});t("default",_(S,[["__scopeId","data-v-0d05eb23"],["__file","D:/asec-platform/frontend/portal/src/view/login/secondaryAuth/verifyCode.vue"]]))}}}))}();
