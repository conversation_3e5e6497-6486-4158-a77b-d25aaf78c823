<template>
  <div class="oauth-result-container">
    <div class="loading-box" v-if="!showSecondaryAuth">
      <base-icon class="loading-icon" name="loading" size="40" />
      <div class="message">{{ message }}</div>
    </div>
    
    <!-- 使用SecondaryAuth组件代替Sms组件 -->
    <div v-if="showSecondaryAuth" class="secondary-auth-container">
      <SecondaryAuth
        :auth-info="{ 
          uniqKey: smsUniqKey,
          contactType: contactType,
          hasContactInfo: hasContactInfo
        }"
        :auth-id="smsIdpId"
        :user-name="smsUserName" 
        :last-id="smsIdpId"
        :auth-methods="authMethods"
        @verification-success="handleSmsSuccess"
        @cancel="handleCancelAuth"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'OAuth2Result'
}
</script>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'
import { ref, computed,onMounted, provide } from 'vue' 
import { Message } from '@/components/base'

import SecondaryAuth from '@/view/login/secondaryAuth/secondaryAuth.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const message = ref('正在处理认证信息...')

// 二次认证相关变量
const showSecondaryAuth = ref(false)
const smsUniqKey = ref('')
const smsUserID = ref('')
const smsUserName = ref('')
const smsIdpId = ref('')
const redirectUrl = ref('')
const contactType = ref('phone') // 默认为手机
const hasContactInfo = ref(true) // 默认有联系信息

// 提供认证相关的响应式变量
provide('userName', smsUserName)
provide('last_id', smsIdpId)
provide('isSecondary', ref(true))
provide('contactType', contactType)
provide('hasContactInfo', hasContactInfo)

// 计算可用的认证方法
const authMethods = computed(() => {
  return [
    {
      type: 'sms',
      name: '短信验证',
      icon: 'duanxin',
      available: contactType.value === 'phone' 
    },
    {
      type: 'email',
      name: '邮箱验证',
      icon: 'email',
      available: contactType.value === 'email' 
    }
  ]
})

// 处理短信验证成功
const handleSmsSuccess = async (result) => {
  message.value = '认证成功，正在跳转...'
  
  // 构建跳转URL，保留客户端参数
  let targetUrl = decodeURIComponent(redirectUrl.value || '/')
  
  if (result.clientParams) {
    const params = new URLSearchParams()
    params.set('type', result.clientParams.type)
    if (result.clientParams.wp) {
      params.set('wp', result.clientParams.wp)
    }
    targetUrl += (targetUrl.includes('?') ? '&' : '?') + params.toString()
  }
  
  window.location.href = targetUrl
}

// 处理取消认证
const handleCancelAuth = () => {
  showSecondaryAuth.value = false
  message.value = '已取消验证，正在返回登录页...'
  
  // 构建返回登录页的URL，保留客户端参数
  const queryParams = new URLSearchParams()
  if (route.query.idp_id) {
    queryParams.set('idp_id', route.query.idp_id)
  }
  if (route.query.redirect_url) {
    queryParams.set('redirect', route.query.redirect_url)
  }
  if (route.query.type === 'client') {
    queryParams.set('type', 'client')
    if (route.query.wp) {
      queryParams.set('wp', route.query.wp)
    }
  }
  
  const loginUrl = queryParams.toString() ? 
    `/login?${queryParams.toString()}` : '/login'
    

  router.push(loginUrl)
}

onMounted(async () => {
  try {
    const { auth_token, idp_id, redirect_url, login_type, auth_error } = route.query

    // 管理后台发送测试使用
    if (login_type === 'is_test') {
      message.value = '测试完成'
      Message.success('测试完成，正在关闭窗口...')
      setTimeout(() => window.close(), 2000);
      return
    }

    if (auth_error) {
      throw new Error(auth_error)
    }

    if (!auth_token) {
      throw new Error('缺少有效的认证令牌')
    }
    
    // 记录登录类型
    localStorage.setItem('loginType', login_type)
    
    // 构造与企业微信一致的认证信息格式
    const authData = {
      clientId: 'client_portal',
      grantType: 'implicit',
      redirect_uri: redirect_url,
      idpId: idp_id,
      authWeb: {
          authWebToken: auth_token
      }
    }
    
    // 调用统一的登录流程
    message.value = '验证登录信息...'
        const loginResult = await userStore.LoginIn(authData, login_type, idp_id)
        // 添加详细日志
        // console.log('登录结果:', loginResult)
        // console.log('用户名:', loginResult.userName)
        // console.log('用户ID:', loginResult.user_id)
        // console.log('IDP ID:', loginResult.idpId || idp_id)
        // console.log('是否需要二次认证:', loginResult.isSecondary)
        // console.log('绑定手机号:', loginResult.notPhone)
        // console.log('uniqKey:', loginResult.uniqKey)
        
        // console.log('登录结果:', loginResult)
        
        // 判断是否需要二次认证 - 修改判断条件以匹配实际返回结构
        if (typeof loginResult === 'object' && loginResult !== null && loginResult.isSecondary) {
          message.value = '需要进行二次认证...'
          
          // 设置二次认证所需信息
          contactType.value = loginResult.contactType || 'phone'
          hasContactInfo.value = loginResult.hasContactInfo || false
          
          const secondaryIdpId = loginResult.secondary && 
          Array.isArray(loginResult.secondary) && 
          loginResult.secondary.length > 0 ? 
          loginResult.secondary[0].id : idp_id;

          // 设置二次认证组件所需参数
          smsUniqKey.value = loginResult.uniqKey
          smsUserID.value = loginResult.user_id
          smsUserName.value = loginResult.userName
          smsIdpId.value = secondaryIdpId
          redirectUrl.value = redirect_url || '/'
          showSecondaryAuth.value = true
        } else if (loginResult === true) {
          // 普通登录成功
          message.value = '认证成功，正在跳转...'
          redirectUrl.value = redirect_url || '/'
          /*await router.push({
            name: 'verify',
            query: { 
              redirect_url: decodeURIComponent(redirectUrl.value || '/'),
              auth_type: login_type
            }
          })*/
        } else {
          throw new Error('登录处理失败')
        }
  } catch (error) {
      console.error('处理错误:', error)
      
      // 提取用户友好的错误消息
      let friendlyMessage = '认证失败，请稍后再试'
      
      try {
        // 更全面的错误消息提取逻辑
        if (error.response?.data) {
          // 优先使用 API 响应中的错误信息
          friendlyMessage = error.response.data.error_description || 
                            error.response.data.message || 
                            error.response.data.error ||
                            friendlyMessage;
        } else if (error.message) {
          let errMsg = error.message;
          
          // 处理完整日志行中的错误 (caller=auth.go:xxx msg=登录失败: error: ...)
          if (errMsg.includes('msg=登录失败')) {
            const msgPart = errMsg.split('msg=登录失败:')[1] || errMsg;
            errMsg = msgPart.trim();
          }
          
          // 1. 先尝试提取 JSON 格式
          if (errMsg.includes('{')) {
            try {
              const jsonStart = errMsg.indexOf('{');
              const jsonPart = errMsg.substring(jsonStart);
              const errorObj = JSON.parse(jsonPart);
              if (errorObj && errorObj.message) {
                friendlyMessage = errorObj.message;
              }
            } catch (e) {
              // JSON 解析失败，继续使用其他方法
            }
          }
          
          // 2. 使用更精确的正则提取 "message = xxx" 模式
          if (friendlyMessage === '认证失败，请稍后再试' && errMsg.includes('message =')) {
            // 匹配 message = 之后的内容，直到 metadata = 或字符串结束
            const msgRegex = /message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/;
            const matches = errMsg.match(msgRegex);
            if (matches && matches[1]) {
              friendlyMessage = matches[1].trim();
            }
          }
          
          // 3. 提取 reason = XXX_ERROR 后的内容
          if (friendlyMessage === '认证失败，请稍后再试' && errMsg.includes('reason =')) {
            const reasonRegex = /reason\s*=\s*(\w+)/;
            const matches = errMsg.match(reasonRegex);
            if (matches && matches[1]) {
              // 将大写下划线格式转为更友好的格式
              const reason = matches[1].replace(/_/g, ' ').toLowerCase();
              friendlyMessage = `认证失败: ${reason}`;
            }
          }
          
          // 4. 如果所有方法都失败，使用第一行内容
          if (friendlyMessage === '认证失败，请稍后再试') {
            friendlyMessage = errMsg.split('\n')[0];
            // 去掉过长的技术细节
            if (friendlyMessage.length > 100) {
              friendlyMessage = friendlyMessage.substring(0, 97) + '...';
            }
          }
        }
      } catch (e) {
        console.error('处理错误消息时发生异常:', e);
        // 保持默认友好消息
      }
      
      // 更新界面显示和消息提示
      message.value = friendlyMessage
      Message.error(friendlyMessage)
      
      setTimeout(() => {
        router.push({ name: 'Login' })
      }, 2000)
    }
})
</script>

<style scoped>
.oauth-result-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}
.loading-box {
  background: #fff;
  padding: 40px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 320px;
}
.secondary-auth-container {
  background: transparent;
  padding: 30px;
  border-radius: 8px;
  box-shadow: none;
  min-width: 340px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
}
.loading-icon {
  animation: rotate 2s linear infinite;
  color: #409EFF;
}
.message {
  margin: 20px 0;
  font-size: 16px;
  color: #606266;
}
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>