/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as N,u as j,a as k,b as A,r,p as d,c as M,G as L,M as I,h as b,o as f,d as g,j as v,w as O,e as E,t as V,g as q}from"./index.d0594432.js";import B from"./secondaryAuth.75febeaf.js";import"./verifyCode.5ac023d8.js";const D={class:"oauth-result-container"},K={key:0,class:"loading-box"},$={class:"message"},W={key:1,class:"secondary-auth-container"},z={name:"OAuth2Result"},G=Object.assign(z,{setup(J){const c=j(),w=k(),C=A(),i=r("\u6B63\u5728\u5904\u7406\u8BA4\u8BC1\u4FE1\u606F..."),m=r(!1),S=r(""),x=r(""),_=r(""),p=r(""),h=r(""),u=r("phone"),y=r(!0);d("userName",_),d("last_id",p),d("isSecondary",r(!0)),d("contactType",u),d("hasContactInfo",y);const P=M(()=>[{type:"sms",name:"\u77ED\u4FE1\u9A8C\u8BC1",icon:"duanxin",available:u.value==="phone"},{type:"email",name:"\u90AE\u7BB1\u9A8C\u8BC1",icon:"email",available:u.value==="email"}]),U=async t=>{i.value="\u8BA4\u8BC1\u6210\u529F\uFF0C\u6B63\u5728\u8DF3\u8F6C...";let e=decodeURIComponent(h.value||"/");if(t.clientParams){const s=new URLSearchParams;s.set("type",t.clientParams.type),t.clientParams.wp&&s.set("wp",t.clientParams.wp),e+=(e.includes("?")?"&":"?")+s.toString()}window.location.href=e},R=()=>{m.value=!1,i.value="\u5DF2\u53D6\u6D88\u9A8C\u8BC1\uFF0C\u6B63\u5728\u8FD4\u56DE\u767B\u5F55\u9875...";const t=new URLSearchParams;c.query.idp_id&&t.set("idp_id",c.query.idp_id),c.query.redirect_url&&t.set("redirect",c.query.redirect_url),c.query.type==="client"&&(t.set("type","client"),c.query.wp&&t.set("wp",c.query.wp));const e=t.toString()?`/login?${t.toString()}`:"/login";w.push(e)};return L(async()=>{try{const{auth_token:t,idp_id:e,redirect_url:s,login_type:a,auth_error:n}=c.query;if(a==="is_test"){i.value="\u6D4B\u8BD5\u5B8C\u6210",I.success("\u6D4B\u8BD5\u5B8C\u6210\uFF0C\u6B63\u5728\u5173\u95ED\u7A97\u53E3..."),setTimeout(()=>window.close(),2e3);return}if(n)throw new Error(n);if(!t)throw new Error("\u7F3A\u5C11\u6709\u6548\u7684\u8BA4\u8BC1\u4EE4\u724C");localStorage.setItem("loginType",a);const l={clientId:"client_portal",grantType:"implicit",redirect_uri:s,idpId:e,authWeb:{authWebToken:t}};i.value="\u9A8C\u8BC1\u767B\u5F55\u4FE1\u606F...";const o=await C.LoginIn(l,a,e);if(typeof o=="object"&&o!==null&&o.isSecondary){i.value="\u9700\u8981\u8FDB\u884C\u4E8C\u6B21\u8BA4\u8BC1...",u.value=o.contactType||"phone",y.value=o.hasContactInfo||!1;const T=o.secondary&&Array.isArray(o.secondary)&&o.secondary.length>0?o.secondary[0].id:e;S.value=o.uniqKey,x.value=o.user_id,_.value=o.userName,p.value=T,h.value=s||"/",m.value=!0}else if(o===!0)i.value="\u8BA4\u8BC1\u6210\u529F\uFF0C\u6B63\u5728\u8DF3\u8F6C...",h.value=s||"/";else throw new Error("\u767B\u5F55\u5904\u7406\u5931\u8D25")}catch(t){console.error("\u5904\u7406\u9519\u8BEF:",t);let e="\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5";try{if(t.response?.data)e=t.response.data.error_description||t.response.data.message||t.response.data.error||e;else if(t.message){let s=t.message;if(s.includes("msg=\u767B\u5F55\u5931\u8D25")&&(s=(s.split("msg=\u767B\u5F55\u5931\u8D25:")[1]||s).trim()),s.includes("{"))try{const a=s.indexOf("{"),n=s.substring(a),l=JSON.parse(n);l&&l.message&&(e=l.message)}catch{}if(e==="\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5"&&s.includes("message =")){const a=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,n=s.match(a);n&&n[1]&&(e=n[1].trim())}if(e==="\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5"&&s.includes("reason =")){const a=/reason\s*=\s*(\w+)/,n=s.match(a);n&&n[1]&&(e=`\u8BA4\u8BC1\u5931\u8D25: ${n[1].replace(/_/g," ").toLowerCase()}`)}e==="\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5"&&(e=s.split(`
`)[0],e.length>100&&(e=e.substring(0,97)+"..."))}}catch(s){console.error("\u5904\u7406\u9519\u8BEF\u6D88\u606F\u65F6\u53D1\u751F\u5F02\u5E38:",s)}i.value=e,I.error(e),setTimeout(()=>{w.push({name:"Login"})},2e3)}}),(t,e)=>{const s=b("base-icon"),a=b("el-icon");return f(),g("div",D,[m.value?q("",!0):(f(),g("div",K,[v(a,{class:"loading-icon",size:40},{default:O(()=>[v(s,{name:"loading"})]),_:1}),E("div",$,V(i.value),1)])),m.value?(f(),g("div",W,[v(B,{"auth-info":{uniqKey:S.value,contactType:u.value,hasContactInfo:y.value},"auth-id":p.value,"user-name":_.value,"last-id":p.value,"auth-methods":P.value,onVerificationSuccess:U,onCancel:R},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])])):q("",!0)])}}}),X=N(G,[["__scopeId","data-v-7cf917e2"]]);export{X as default};
