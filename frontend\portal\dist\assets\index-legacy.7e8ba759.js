/*! 
 Build based on gin-vue-admin 
 Time : 1749618054000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function l(e,o,a,i){var l=o&&o.prototype instanceof u?o:u,s=Object.create(l.prototype);return t(s,"_invoke",function(e,t,o){var a,i,l,u=0,s=o||[],f=!1,d={p:0,n:0,v:n,a:p,f:p.bind(n,4),d:function(e,t){return a=e,i=0,l=n,d.n=t,c}};function p(e,t){for(i=e,l=t,r=0;!f&&u&&!o&&r<s.length;r++){var o,a=s[r],p=d.p,v=a[2];e>3?(o=v===t)&&(l=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=n):a[0]<=p&&((o=e<2&&p<a[1])?(i=0,d.v=t,d.n=a[1]):p<v&&(o=e<3||a[0]>t||t>v)&&(a[4]=e,a[5]=t,d.n=v,i=0))}if(o||e>1)return c;throw f=!0,t}return function(o,s,v){if(u>1)throw TypeError("Generator is already running");for(f&&1===s&&p(s,v),i=s,l=v;(r=i<2?n:l)||!f;){a||(i?i<3?(i>1&&(d.n=-1),p(i,l)):d.n=l:d.v=l);try{if(u=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,l)))throw TypeError("iterator result is not an object");if(!r.done)return r;l=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=n}else if((r=(f=d.n<0)?l:e.call(t,d))!==c)break}catch(r){a=n,i=1,l=r}finally{u=1}}return{value:r,done:f}}}(e,a,i),!0),s}var c={};function u(){}function s(){}function f(){}r=Object.getPrototypeOf;var d=[][a]?r(r([][a]())):(t(r={},a,(function(){return this})),r),p=f.prototype=u.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,t(e,i,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=f,t(p,"constructor",f),t(f,"constructor",s),s.displayName="GeneratorFunction",t(f,i,"GeneratorFunction"),t(p),t(p,i,"Generator"),t(p,a,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:v}})()}function t(e,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,o){if(n)a?a(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var i=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function l(e){n(i,o,a,l,c,"next",e)}function c(e){n(i,o,a,l,c,"throw",e)}l(void 0)}))}}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){v(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(e,t,n){return t=c(t),function(e,t){if(t&&("object"==b(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,l()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s(e,t)}function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,h(r.key),r)}}function p(e,t,n){return t&&d(e.prototype,t),n&&d(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function v(e,t,n){return(t=h(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){var t=function(e,t){if("object"!=b(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==b(t)?t:t+""}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||x(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e){return function(e){if(Array.isArray(e))return w(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||x(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function y(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=x(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==n.return||n.return()}finally{if(l)throw a}}}}function x(e,t){if(e){if("string"==typeof e)return w(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?w(e,t):void 0}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}System.register([],(function(t,n){"use strict";var o=document.createElement("style");return o.textContent='@charset "UTF-8";::-webkit-scrollbar-track-piece{background-color:#f8f8f8}::-webkit-scrollbar{width:9px;height:9px}::-webkit-scrollbar-thumb{background-color:#ddd;background-clip:padding-box;min-height:28px;border-radius:4px}::-webkit-scrollbar-thumb:hover{background-color:#bbb}:root{--primary-color: #4D70FF;--menu-item-height: 56px}.gva-search-box{padding:24px 24px 2px;background-color:#fff;border-radius:2px;margin-bottom:12px}.gva-form-box,.gva-table-box{padding:24px;background-color:#fff;border-radius:2px}.gva-pagination{display:flex;justify-content:flex-end}.gva-pagination .btn-prev,.gva-pagination .btn-next,.gva-pagination .number,.gva-pagination .btn-quicknext{display:inline-flex;justify-content:center;align-items:center;width:32px;height:32px}.gva-pagination .btn-prev{padding-right:6px}.gva-pagination .btn-next{padding-left:6px}.gva-pagination .active,.gva-pagination .is-active{background:var(--primary-color, #4D70FF);border-radius:2px;color:#fff!important}*{box-sizing:border-box}body{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif;font-size:14px;line-height:1.5;color:#333;background-color:#f5f5f5}.container{display:flex;min-height:100vh}.aside{width:220px;background-color:#263444;transition:width .3s;overflow:hidden}.aside.collapsed{width:54px}.main{flex:1;display:flex;flex-direction:column}.header{height:60px;background-color:#fff;border-bottom:1px solid #e8e8e8;display:flex;align-items:center;padding:0 20px;box-shadow:0 1px 4px rgba(0,21,41,.08)}.content{flex:1;padding:20px}.row{display:flex;flex-wrap:wrap;margin-left:-12px;margin-right:-12px}.col{padding-left:12px;padding-right:12px;flex:1}.col-1{flex:0 0 8.333333%;max-width:8.333333%}.col-2{flex:0 0 16.666667%;max-width:16.666667%}.col-3{flex:0 0 25%;max-width:25%}.col-4{flex:0 0 33.333333%;max-width:33.333333%}.col-5{flex:0 0 41.666667%;max-width:41.666667%}.col-6{flex:0 0 50%;max-width:50%}.col-7{flex:0 0 58.333333%;max-width:58.333333%}.col-8{flex:0 0 66.666667%;max-width:66.666667%}.col-9{flex:0 0 75%;max-width:75%}.col-10{flex:0 0 83.333333%;max-width:83.333333%}.col-11{flex:0 0 91.666667%;max-width:91.666667%}.col-12{flex:0 0 100%;max-width:100%}.card{background-color:#fff;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,.1);margin-bottom:20px;overflow:hidden}.card-header{padding:16px 20px;border-bottom:1px solid #f0f0f0;font-weight:500}.card-body{padding:20px}.btn{display:inline-block;padding:8px 16px;font-size:14px;font-weight:400;line-height:1.5;text-align:center;text-decoration:none;vertical-align:middle;cursor:pointer;border:1px solid transparent;border-radius:4px;transition:all .3s;user-select:none}.btn:hover{opacity:.8}.btn:disabled{opacity:.6;cursor:not-allowed}.btn-primary{color:#fff;background-color:#409eff;border-color:#409eff}.btn-primary:hover{background-color:#66b1ff;border-color:#66b1ff}.btn-success{color:#fff;background-color:#67c23a;border-color:#67c23a}.btn-warning{color:#fff;background-color:#e6a23c;border-color:#e6a23c}.btn-danger{color:#fff;background-color:#f56c6c;border-color:#f56c6c}.btn-default{color:#606266;background-color:#fff;border-color:#dcdfe6}.btn-small{padding:5px 12px;font-size:12px}.btn-large{padding:12px 20px;font-size:16px}.form{margin:0}.form-item{margin-bottom:22px}.form-label{display:inline-block;margin-bottom:8px;font-weight:500;color:#606266}.form-input{width:100%;padding:8px 12px;font-size:14px;line-height:1.5;color:#606266;background-color:#fff;border:1px solid #dcdfe6;border-radius:4px;transition:border-color .3s}.form-input:focus{outline:none;border-color:#409eff;box-shadow:0 0 0 2px rgba(64,158,255,.2)}.form-input:disabled{background-color:#f5f7fa;color:#c0c4cc;cursor:not-allowed}.form-select{width:100%;padding:8px 12px;font-size:14px;line-height:1.5;color:#606266;background-color:#fff;border:1px solid #dcdfe6;border-radius:4px;cursor:pointer}.form-textarea{width:100%;padding:8px 12px;font-size:14px;line-height:1.5;color:#606266;background-color:#fff;border:1px solid #dcdfe6;border-radius:4px;resize:vertical;min-height:80px}.table{width:100%;border-collapse:collapse;background-color:#fff;border-radius:4px;overflow:hidden;box-shadow:0 2px 8px rgba(0,0,0,.1)}.table th,.table td{padding:12px 16px;text-align:left;border-bottom:1px solid #f0f0f0}.table th{background-color:#fafafa;font-weight:500;color:#909399}.table tbody tr:hover{background-color:#f5f7fa}.pagination{display:flex;align-items:center;justify-content:flex-end;margin-top:20px;gap:8px}.pagination-item{padding:6px 12px;border:1px solid #dcdfe6;border-radius:4px;cursor:pointer;transition:all .3s}.pagination-item:hover{color:#409eff;border-color:#409eff}.pagination-item.active{color:#fff;background-color:#409eff;border-color:#409eff}.pagination-item.disabled{color:#c0c4cc;cursor:not-allowed}.tag{display:inline-block;padding:2px 8px;font-size:12px;line-height:1.5;border-radius:4px;margin-right:8px}.tag-primary{color:#409eff;background-color:#ecf5ff;border:1px solid #d9ecff}.tag-success{color:#67c23a;background-color:#f0f9ff;border:1px solid #c2e7b0}.tag-warning{color:#e6a23c;background-color:#fdf6ec;border:1px solid #f5dab1}.tag-danger{color:#f56c6c;background-color:#fef0f0;border:1px solid #fbc4c4}.tag-info{color:#909399;background-color:#f4f4f5;border:1px solid #e9e9eb}.avatar{display:inline-block;width:40px;height:40px;border-radius:50%;background-color:#c0c4cc;color:#fff;text-align:center;line-height:40px;font-size:14px;overflow:hidden}.avatar-small{width:24px;height:24px;line-height:24px;font-size:12px}.avatar-large{width:64px;height:64px;line-height:64px;font-size:18px}.progress{width:100%;height:6px;background-color:#f5f7fa;border-radius:3px;overflow:hidden}.progress-bar{height:100%;background-color:#409eff;transition:width .3s}.link{color:#409eff;text-decoration:none;cursor:pointer;transition:color .3s}.link:hover{color:#66b1ff}.link-primary{color:#409eff}.link-success{color:#67c23a}.link-warning{color:#e6a23c}.link-danger{color:#f56c6c}.link-info{color:#909399}.divider{margin:24px 0;border:none;border-top:1px solid #e8e8e8}.divider-vertical{display:inline-block;width:1px;height:1em;background-color:#e8e8e8;vertical-align:middle;margin:0 8px}.menu{list-style:none;margin:0;padding:0;background-color:#263444;color:#fff}.menu-vertical{width:100%}.menu-item{position:relative;display:block;padding:12px 20px;color:#fff;text-decoration:none;cursor:pointer;transition:all .3s;border-bottom:1px solid rgba(255,255,255,.1)}.menu-item:hover{background-color:rgba(64,158,255,.08);color:#fff}.menu-item.active{background-color:#4d70ff;color:#fff}.menu-item.active:before{content:"";position:absolute;left:0;top:0;bottom:0;width:3px;background-color:#409eff}.menu-item-icon{display:inline-block;width:20px;margin-right:12px;text-align:center}.menu-item-title{display:inline-block;transition:all .3s}.menu.collapsed .menu-item{padding:12px 17px;text-align:center}.menu.collapsed .menu-item-title{display:none}.menu.collapsed .menu-item-icon{margin-right:0}.submenu{position:relative}.submenu-title{display:block;padding:12px 20px;color:#fff;text-decoration:none;cursor:pointer;transition:all .3s;border-bottom:1px solid rgba(255,255,255,.1)}.submenu-title:hover{background-color:rgba(64,158,255,.08);color:#fff}.submenu-title:after{content:"";position:absolute;right:20px;top:50%;transform:translateY(-50%) rotate(0);width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid #fff;transition:transform .3s}.submenu.open .submenu-title:after{transform:translateY(-50%) rotate(180deg)}.submenu-content{max-height:0;overflow:hidden;transition:max-height .3s;background-color:rgba(0,0,0,.2)}.submenu.open .submenu-content{max-height:500px}.submenu .menu-item{padding-left:40px;border-bottom:none}.submenu .menu-item:hover{background-color:rgba(64,158,255,.15)}.scrollbar{overflow-y:auto;overflow-x:hidden}.scrollbar::-webkit-scrollbar{width:6px}.scrollbar::-webkit-scrollbar-track{background:rgba(255,255,255,.1)}.scrollbar::-webkit-scrollbar-thumb{background:rgba(255,255,255,.3);border-radius:3px}.scrollbar::-webkit-scrollbar-thumb:hover{background:rgba(255,255,255,.5)}.carousel{position:relative;overflow:hidden;border-radius:4px}.carousel-container{display:flex;transition:transform .3s}.carousel-item{flex:0 0 100%;display:flex;align-items:center;justify-content:center}.carousel-indicators{position:absolute;bottom:10px;left:50%;transform:translate(-50%);display:flex;gap:8px}.carousel-indicator{width:8px;height:8px;border-radius:50%;background-color:rgba(255,255,255,.5);cursor:pointer;transition:background-color .3s}.carousel-indicator.active{background-color:#409eff}.dialog-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:1000}.dialog{background-color:#fff;border-radius:4px;box-shadow:0 4px 12px rgba(0,0,0,.15);max-width:90vw;max-height:90vh;overflow:hidden}.dialog-header{padding:20px 20px 10px;border-bottom:1px solid #f0f0f0;font-size:16px;font-weight:500}.dialog-body{padding:20px}.dialog-footer{padding:10px 20px 20px;text-align:right;border-top:1px solid #f0f0f0}.loading{display:inline-block;width:20px;height:20px;border:2px solid #f3f3f3;border-top:2px solid #409eff;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,.8);display:flex;align-items:center;justify-content:center;z-index:2000}.loading-text{margin-left:10px;color:#606266}.message{position:fixed;top:20px;left:50%;transform:translate(-50%);padding:12px 16px;border-radius:4px;box-shadow:0 4px 12px rgba(0,0,0,.15);z-index:3000;animation:messageSlideIn .3s ease-out}@keyframes messageSlideIn{0%{opacity:0;transform:translate(-50%) translateY(-20px)}to{opacity:1;transform:translate(-50%) translateY(0)}}.message-success{background-color:#f0f9ff;color:#67c23a;border:1px solid #c2e7b0}.message-warning{background-color:#fdf6ec;color:#e6a23c;border:1px solid #f5dab1}.message-error{background-color:#fef0f0;color:#f56c6c;border:1px solid #fbc4c4}.message-info{background-color:#f4f4f5;color:#909399;border:1px solid #e9e9eb}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.float-left{float:left}.float-right{float:right}.clearfix:after{content:"";display:table;clear:both}.hidden{display:none}.visible{display:block}.margin-0{margin:0}.margin-top-10{margin-top:10px}.margin-bottom-10{margin-bottom:10px}.margin-left-10{margin-left:10px}.margin-right-10{margin-right:10px}.padding-0{padding:0}.padding-10{padding:10px}.padding-20{padding:20px}.width-100{width:100%}.height-100{height:100%}.flex{display:flex}.flex-center{display:flex;align-items:center;justify-content:center}.flex-between{display:flex;align-items:center;justify-content:space-between}.flex-column{flex-direction:column}.flex-wrap{flex-wrap:wrap}.flex-1{flex:1}.btn-loading[data-v-f0b3f2fd]{pointer-events:none}.loading[data-v-f0b3f2fd]{margin-right:8px}.input-wrapper[data-v-47df032a]{position:relative;display:inline-block;width:100%}.base-input[data-v-47df032a]{width:100%;padding:8px 12px;border:1px solid #dcdfe6;border-radius:4px;font-size:14px;color:#606266;background-color:#fff;transition:border-color .2s,box-shadow .2s;outline:none;box-sizing:border-box}.base-input[data-v-47df032a]:hover{border-color:#c0c4cc}.base-input--focused[data-v-47df032a]{border-color:#409eff;box-shadow:0 0 0 2px rgba(64,158,255,.2)}.base-input--disabled[data-v-47df032a]{background-color:#f5f7fa;border-color:#e4e7ed;color:#c0c4cc;cursor:not-allowed}.base-input--small[data-v-47df032a]{padding:5px 8px;font-size:12px}.base-input--large[data-v-47df032a]{padding:12px 16px;font-size:16px}.form-inline[data-v-39ff5420]{display:flex;flex-wrap:wrap;align-items:center;gap:16px}.form-inline .form-item[data-v-39ff5420]{margin-bottom:0;margin-right:16px}.form-label-left .form-label[data-v-39ff5420]{text-align:left}.form-label-right .form-label[data-v-39ff5420]{text-align:right}.form-label-top .form-label[data-v-39ff5420]{text-align:left;margin-bottom:4px}.base-form-item[data-v-2592ce9c]{display:flex;margin-bottom:22px}.base-form-item__content[data-v-2592ce9c]{flex:1;position:relative}.base-form-item__error[data-v-2592ce9c]{color:#f56c6c;font-size:12px;line-height:1;margin-top:4px}.base-form-item--error[data-v-2592ce9c] .base-input{border-color:#f56c6c}.base-form-item--error[data-v-2592ce9c] .base-input:focus{border-color:#f56c6c;box-shadow:0 0 0 2px rgba(245,108,108,.2)}.base-form-item__label--required[data-v-2592ce9c]:before{content:"*";color:#f56c6c;margin-right:4px}.base-form-item__label[data-v-2592ce9c]{display:flex;align-items:center;margin-right:12px;margin-bottom:0;flex-shrink:0;font-size:14px;color:#606266}[data-v-2592ce9c] .base-form--label-top .base-form-item{flex-direction:column}[data-v-2592ce9c] .base-form--label-top .base-form-item__label{margin-right:0;margin-bottom:8px}[data-v-2592ce9c] .base-form--inline .base-form-item{display:inline-flex;margin-right:16px;margin-bottom:0;vertical-align:top}.container[data-v-264e6643]{display:flex;min-height:100vh}.aside[data-v-56fd2527]{background-color:#263444;transition:width .3s;overflow:hidden;flex-shrink:0}.main[data-v-173b46c7]{flex:1;display:flex;flex-direction:column;padding:20px;background-color:#f0f2f5;overflow:auto}.row[data-v-63d064ea]{display:flex;flex-wrap:wrap}.row-justify-end[data-v-63d064ea]{justify-content:flex-end}.row-justify-center[data-v-63d064ea]{justify-content:center}.row-justify-space-around[data-v-63d064ea]{justify-content:space-around}.row-justify-space-between[data-v-63d064ea]{justify-content:space-between}.row-align-middle[data-v-63d064ea]{align-items:center}.row-align-bottom[data-v-63d064ea]{align-items:flex-end}.col[data-v-6f4b390d]{position:relative;max-width:100%;min-height:1px}.col-1[data-v-6f4b390d]{flex:0 0 4.16667%;max-width:4.16667%}.col-2[data-v-6f4b390d]{flex:0 0 8.33333%;max-width:8.33333%}.col-3[data-v-6f4b390d]{flex:0 0 12.5%;max-width:12.5%}.col-4[data-v-6f4b390d]{flex:0 0 16.66667%;max-width:16.66667%}.col-5[data-v-6f4b390d]{flex:0 0 20.83333%;max-width:20.83333%}.col-6[data-v-6f4b390d]{flex:0 0 25%;max-width:25%}.col-7[data-v-6f4b390d]{flex:0 0 29.16667%;max-width:29.16667%}.col-8[data-v-6f4b390d]{flex:0 0 33.33333%;max-width:33.33333%}.col-9[data-v-6f4b390d]{flex:0 0 37.5%;max-width:37.5%}.col-10[data-v-6f4b390d]{flex:0 0 41.66667%;max-width:41.66667%}.col-11[data-v-6f4b390d]{flex:0 0 45.83333%;max-width:45.83333%}.col-12[data-v-6f4b390d]{flex:0 0 50%;max-width:50%}.col-13[data-v-6f4b390d]{flex:0 0 54.16667%;max-width:54.16667%}.col-14[data-v-6f4b390d]{flex:0 0 58.33333%;max-width:58.33333%}.col-15[data-v-6f4b390d]{flex:0 0 62.5%;max-width:62.5%}.col-16[data-v-6f4b390d]{flex:0 0 66.66667%;max-width:66.66667%}.col-17[data-v-6f4b390d]{flex:0 0 70.83333%;max-width:70.83333%}.col-18[data-v-6f4b390d]{flex:0 0 75%;max-width:75%}.col-19[data-v-6f4b390d]{flex:0 0 79.16667%;max-width:79.16667%}.col-20[data-v-6f4b390d]{flex:0 0 83.33333%;max-width:83.33333%}.col-21[data-v-6f4b390d]{flex:0 0 87.5%;max-width:87.5%}.col-22[data-v-6f4b390d]{flex:0 0 91.66667%;max-width:91.66667%}.col-23[data-v-6f4b390d]{flex:0 0 95.83333%;max-width:95.83333%}.col-24[data-v-6f4b390d]{flex:0 0 100%;max-width:100%}.col-offset-1[data-v-6f4b390d]{margin-left:4.16667%}.col-offset-2[data-v-6f4b390d]{margin-left:8.33333%}.col-offset-3[data-v-6f4b390d]{margin-left:12.5%}.col-offset-4[data-v-6f4b390d]{margin-left:16.66667%}.col-offset-5[data-v-6f4b390d]{margin-left:20.83333%}.col-offset-6[data-v-6f4b390d]{margin-left:25%}.col-offset-7[data-v-6f4b390d]{margin-left:29.16667%}.col-offset-8[data-v-6f4b390d]{margin-left:33.33333%}.col-offset-9[data-v-6f4b390d]{margin-left:37.5%}.col-offset-10[data-v-6f4b390d]{margin-left:41.66667%}.col-offset-11[data-v-6f4b390d]{margin-left:45.83333%}.col-offset-12[data-v-6f4b390d]{margin-left:50%}@media (max-width: 575px){.col-xs-1[data-v-6f4b390d]{flex:0 0 4.16667%;max-width:4.16667%}.col-xs-2[data-v-6f4b390d]{flex:0 0 8.33333%;max-width:8.33333%}.col-xs-6[data-v-6f4b390d]{flex:0 0 25%;max-width:25%}.col-xs-12[data-v-6f4b390d]{flex:0 0 50%;max-width:50%}.col-xs-24[data-v-6f4b390d]{flex:0 0 100%;max-width:100%}}@media (min-width: 576px){.col-sm-1[data-v-6f4b390d]{flex:0 0 4.16667%;max-width:4.16667%}.col-sm-2[data-v-6f4b390d]{flex:0 0 8.33333%;max-width:8.33333%}.col-sm-6[data-v-6f4b390d]{flex:0 0 25%;max-width:25%}.col-sm-12[data-v-6f4b390d]{flex:0 0 50%;max-width:50%}.col-sm-24[data-v-6f4b390d]{flex:0 0 100%;max-width:100%}}@media (min-width: 768px){.col-md-1[data-v-6f4b390d]{flex:0 0 4.16667%;max-width:4.16667%}.col-md-2[data-v-6f4b390d]{flex:0 0 8.33333%;max-width:8.33333%}.col-md-6[data-v-6f4b390d]{flex:0 0 25%;max-width:25%}.col-md-12[data-v-6f4b390d]{flex:0 0 50%;max-width:50%}.col-md-24[data-v-6f4b390d]{flex:0 0 100%;max-width:100%}}@media (min-width: 992px){.col-lg-1[data-v-6f4b390d]{flex:0 0 4.16667%;max-width:4.16667%}.col-lg-2[data-v-6f4b390d]{flex:0 0 8.33333%;max-width:8.33333%}.col-lg-6[data-v-6f4b390d]{flex:0 0 25%;max-width:25%}.col-lg-12[data-v-6f4b390d]{flex:0 0 50%;max-width:50%}.col-lg-24[data-v-6f4b390d]{flex:0 0 100%;max-width:100%}}@media (min-width: 1200px){.col-xl-1[data-v-6f4b390d]{flex:0 0 4.16667%;max-width:4.16667%}.col-xl-2[data-v-6f4b390d]{flex:0 0 8.33333%;max-width:8.33333%}.col-xl-6[data-v-6f4b390d]{flex:0 0 25%;max-width:25%}.col-xl-12[data-v-6f4b390d]{flex:0 0 50%;max-width:50%}.col-xl-24[data-v-6f4b390d]{flex:0 0 100%;max-width:100%}}.divider-horizontal[data-v-8fca3f99]{position:relative;margin:24px 0;border-top:1px solid #e8e8e8}.divider-horizontal .divider-content[data-v-8fca3f99]{position:absolute;top:50%;transform:translateY(-50%);background-color:#fff;padding:0 16px;color:#606266;font-size:14px}.divider-content-left[data-v-8fca3f99]{left:5%}.divider-content-center[data-v-8fca3f99]{left:50%;transform:translate(-50%) translateY(-50%)}.divider-content-right[data-v-8fca3f99]{right:5%}.divider-vertical[data-v-8fca3f99]{display:inline-block;width:1px;height:1em;background-color:#e8e8e8;vertical-align:middle;margin:0 8px}.avatar[data-v-b54355b9]{display:inline-block;width:40px;height:40px;border-radius:50%;background-color:#c0c4cc;color:#fff;text-align:center;line-height:40px;font-size:14px;overflow:hidden;position:relative}.avatar img[data-v-b54355b9]{width:100%;height:100%;object-fit:cover}.avatar-icon[data-v-b54355b9]{width:60%;height:60%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.avatar-text[data-v-b54355b9]{display:block;width:100%;height:100%}.avatar-small[data-v-b54355b9]{width:24px;height:24px;line-height:24px;font-size:12px}.avatar-large[data-v-b54355b9]{width:64px;height:64px;line-height:64px;font-size:18px}.avatar-square[data-v-b54355b9]{border-radius:4px}.carousel[data-v-b41008b0]{position:relative;overflow:hidden;border-radius:4px}.carousel-container[data-v-b41008b0]{display:flex;transition:transform .3s ease;height:100%}.carousel-indicators[data-v-b41008b0]{position:absolute;display:flex;gap:8px;z-index:10}.carousel-indicators-bottom[data-v-b41008b0]{bottom:10px;left:50%;transform:translate(-50%)}.carousel-indicators-top[data-v-b41008b0]{top:10px;left:50%;transform:translate(-50%)}.carousel-indicator[data-v-b41008b0]{width:8px;height:8px;border-radius:50%;background-color:rgba(255,255,255,.5);border:none;cursor:pointer;transition:background-color .3s}.carousel-indicator.active[data-v-b41008b0]{background-color:#409eff}.carousel-arrow[data-v-b41008b0]{position:absolute;top:50%;transform:translateY(-50%);width:40px;height:40px;background-color:rgba(0,0,0,.5);color:#fff;border:none;border-radius:50%;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;transition:background-color .3s;z-index:10}.carousel-arrow[data-v-b41008b0]:hover{background-color:rgba(0,0,0,.7)}.carousel-arrow-left[data-v-b41008b0]{left:10px}.carousel-arrow-right[data-v-b41008b0]{right:10px}.carousel[data-arrow=hover] .carousel-arrow[data-v-b41008b0]{opacity:0;transition:opacity .3s}.carousel[data-arrow=hover]:hover .carousel-arrow[data-v-b41008b0]{opacity:1}.carousel-item[data-v-d653f781]{flex:0 0 100%;height:100%;display:flex;align-items:center;justify-content:center}.base-card[data-v-663e3da6]{border-radius:4px;border:1px solid #ebeef5;background-color:#fff;overflow:hidden;color:#303133;transition:.3s}.base-card--shadow[data-v-663e3da6],.base-card[data-v-663e3da6]:hover{box-shadow:0 2px 12px rgba(0,0,0,.1)}.base-card__header[data-v-663e3da6]{padding:18px 20px;border-bottom:1px solid #ebeef5;box-sizing:border-box;font-weight:500;color:#303133}.base-card__body[data-v-663e3da6]{padding:20px}.base-timeline[data-v-d9f6b8e2]{margin:0;font-size:14px;list-style:none}.base-timeline-item[data-v-deb04d8a]{position:relative;padding-bottom:20px}.base-timeline-item__tail[data-v-deb04d8a]{position:absolute;left:4px;height:100%;border-left:2px solid #e4e7ed}.base-timeline-item:last-child .base-timeline-item__tail[data-v-deb04d8a]{display:none}.base-timeline-item__node[data-v-deb04d8a]{position:absolute;background-color:#fff;border-radius:50%;display:flex;justify-content:center;align-items:center}.base-timeline-item__node--normal[data-v-deb04d8a]{left:-1px;width:12px;height:12px}.base-timeline-item__node--large[data-v-deb04d8a]{left:-2px;width:14px;height:14px}.base-timeline-item__node-normal[data-v-deb04d8a]{width:10px;height:10px;border-radius:50%;background-color:#c0c4cc}.base-timeline-item__node--primary .base-timeline-item__node-normal[data-v-deb04d8a]{background-color:#409eff}.base-timeline-item__node--success .base-timeline-item__node-normal[data-v-deb04d8a]{background-color:#67c23a}.base-timeline-item__node--warning .base-timeline-item__node-normal[data-v-deb04d8a]{background-color:#e6a23c}.base-timeline-item__node--danger .base-timeline-item__node-normal[data-v-deb04d8a]{background-color:#f56c6c}.base-timeline-item__node--info .base-timeline-item__node-normal[data-v-deb04d8a]{background-color:#909399}.base-timeline-item__wrapper[data-v-deb04d8a]{position:relative;padding-left:28px;top:-3px}.base-timeline-item__timestamp[data-v-deb04d8a]{color:#909399;line-height:1;font-size:13px}.base-timeline-item__timestamp--top[data-v-deb04d8a]{margin-bottom:8px;padding-top:4px}.base-timeline-item__timestamp--bottom[data-v-deb04d8a]{margin-top:8px}.base-timeline-item__content[data-v-deb04d8a]{color:#303133}.base-select[data-v-7a185f90]{position:relative;display:inline-block;width:100%}.base-select__input[data-v-7a185f90]{position:relative;display:flex;align-items:center;justify-content:space-between;padding:8px 12px;border:1px solid #dcdfe6;border-radius:4px;background-color:#fff;cursor:pointer;transition:border-color .2s}.base-select__input[data-v-7a185f90]:hover{border-color:#c0c4cc}.base-select__input.is-focus[data-v-7a185f90]{border-color:#409eff}.base-select.is-disabled .base-select__input[data-v-7a185f90]{background-color:#f5f7fa;border-color:#e4e7ed;color:#c0c4cc;cursor:not-allowed}.base-select__selected[data-v-7a185f90]{color:#606266}.base-select__placeholder[data-v-7a185f90]{color:#c0c4cc}.base-select__arrow[data-v-7a185f90]{color:#c0c4cc;font-size:12px;transition:transform .3s}.base-select__arrow.is-reverse[data-v-7a185f90]{transform:rotate(180deg)}.base-select__dropdown[data-v-7a185f90]{position:absolute;top:100%;left:0;right:0;z-index:1000;background:#fff;border:1px solid #e4e7ed;border-radius:4px;box-shadow:0 2px 12px rgba(0,0,0,.1);margin-top:4px}.base-select__options[data-v-7a185f90]{max-height:200px;overflow-y:auto}.base-option[data-v-d95e9770]{padding:8px 12px;cursor:pointer;color:#606266;font-size:14px;line-height:1.5;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.base-option[data-v-d95e9770]:hover{background-color:#f5f7fa}.base-option.is-selected[data-v-d95e9770]{color:#409eff;background-color:#f0f9ff}.base-option.is-disabled[data-v-d95e9770]{color:#c0c4cc;cursor:not-allowed}.base-option.is-disabled[data-v-d95e9770]:hover{background-color:transparent}.base-checkbox[data-v-27e2b100]{color:#606266;font-weight:500;font-size:14px;position:relative;cursor:pointer;display:inline-flex;align-items:center;white-space:nowrap;user-select:none;margin-right:30px}.base-checkbox.is-disabled[data-v-27e2b100]{color:#c0c4cc;cursor:not-allowed}.base-checkbox__input[data-v-27e2b100]{white-space:nowrap;cursor:pointer;outline:none;display:inline-flex;position:relative}.base-checkbox__inner[data-v-27e2b100]{display:inline-block;position:relative;border:1px solid #dcdfe6;border-radius:2px;box-sizing:border-box;width:14px;height:14px;background-color:#fff;z-index:1;transition:border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46)}.base-checkbox__inner[data-v-27e2b100]:after{box-sizing:content-box;content:"";border:1px solid #fff;border-left:0;border-top:0;height:7px;left:4px;position:absolute;top:1px;transform:rotate(45deg) scaleY(0);width:3px;transition:transform .15s ease-in .05s;transform-origin:center}.base-checkbox.is-checked .base-checkbox__inner[data-v-27e2b100]{background-color:#409eff;border-color:#409eff}.base-checkbox.is-checked .base-checkbox__inner[data-v-27e2b100]:after{transform:rotate(45deg) scaleY(1)}.base-checkbox.is-disabled .base-checkbox__inner[data-v-27e2b100]{background-color:#edf2fc;border-color:#dcdfe6}.base-checkbox__original[data-v-27e2b100]{opacity:0;outline:none;position:absolute;margin:0;width:0;height:0;z-index:-1}.base-checkbox__label[data-v-27e2b100]{display:inline-block;padding-left:8px;line-height:19px;font-size:14px}.base-radio[data-v-c39e0420]{color:#606266;font-weight:500;font-size:14px;position:relative;cursor:pointer;display:inline-flex;align-items:center;white-space:nowrap;user-select:none;margin-right:30px}.base-radio.is-disabled[data-v-c39e0420]{color:#c0c4cc;cursor:not-allowed}.base-radio__input[data-v-c39e0420]{white-space:nowrap;cursor:pointer;outline:none;display:inline-flex;position:relative}.base-radio__inner[data-v-c39e0420]{border:1px solid #dcdfe6;border-radius:100%;width:14px;height:14px;background-color:#fff;position:relative;cursor:pointer;display:inline-block;box-sizing:border-box;transition:border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46)}.base-radio__inner[data-v-c39e0420]:after{width:4px;height:4px;border-radius:100%;background-color:#fff;content:"";position:absolute;left:50%;top:50%;transform:translate(-50%,-50%) scale(0);transition:transform .15s ease-in}.base-radio.is-checked .base-radio__inner[data-v-c39e0420]{border-color:#409eff;background:#409eff}.base-radio.is-checked .base-radio__inner[data-v-c39e0420]:after{transform:translate(-50%,-50%) scale(1)}.base-radio.is-disabled .base-radio__inner[data-v-c39e0420]{background-color:#f5f7fa;border-color:#e4e7ed}.base-radio__original[data-v-c39e0420]{opacity:0;outline:none;position:absolute;z-index:-1;top:0;left:0;right:0;bottom:0;margin:0}.base-radio__label[data-v-c39e0420]{display:inline-block;padding-left:8px;line-height:19px;font-size:14px}.base-radio-group[data-v-12a82aff]{display:inline-flex;align-items:center;flex-wrap:wrap;font-size:0}.base-icon[data-v-27fea9a9]{display:inline-flex;align-items:center;justify-content:center;vertical-align:middle}.base-icon svg[data-v-27fea9a9]{display:block}.svg-icon[data-v-dae6fe16]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}main{display:block}h1{font-size:2em;margin:.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace,monospace;font-size:1em}a{background-color:transparent}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}img{border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button}button::-moz-focus-inner,[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring{outline:1px dotted ButtonText}fieldset{padding:.35em .75em .625em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{vertical-align:baseline}textarea{overflow:auto}[type=checkbox],[type=radio]{box-sizing:border-box;padding:0}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details{display:block}summary{display:list-item}template{display:none}[hidden]{display:none}HTML,body,div,ul,ol,dl,li,dt,dd,p,blockquote,pre,form,fieldset,table,th,td{border:none;font-family:PingFang SC,HarmonyOS_Medium,Helvetica Neue,Microsoft YaHei,sans-serif;font-size:14px;margin:0;padding:0}html,body{height:100%;width:100%}address,caption,cite,code,th,var{font-style:normal;font-weight:400}a{text-decoration:none}input::-ms-clear{display:none}input::-ms-reveal{display:none}input{-webkit-appearance:none;margin:0;outline:none;padding:0}input::-webkit-input-placeholder{color:#ccc}input::-ms-input-placeholder{color:#ccc}input::-moz-placeholder{color:#ccc}input[type=submit],input[type=button]{cursor:pointer}button[disabled],input[disabled]{cursor:default}img{border:none}ul,ol,li{list-style-type:none}#app .pd-lr-15{padding:0 15px}#app .height-full{height:100%}#app .width-full{width:100%}#app .dp-flex{display:flex}#app .justify-content-center{justify-content:center}#app .align-items{align-items:center}#app .pd-0{padding:0}#app .el-container{position:relative;height:100%;width:100%}#app .el-container.mobile.openside{position:fixed;top:0}#app .gva-aside{-webkit-transition:width .2s;transition:width .2s;width:220px;height:100%;position:fixed;font-size:0;top:0;bottom:0;left:0;z-index:1001;overflow:hidden}#app .gva-aside .el-menu{border-right:none}#app .gva-aside .tilte{min-height:60px;text-align:center;transition:all .3s;display:flex;align-items:center;padding-left:23px}#app .gva-aside .tilte .logoimg{height:30px}#app .gva-aside .tilte .tit-text{text-align:left;display:inline-block;color:#fff;font-weight:700;font-size:14px;padding-left:5px}#app .gva-aside .tilte .introduction-text{opacity:70%;color:#fff;font-weight:400;font-size:14px;text-align:left;padding-left:5px}#app .gva-aside .footer{min-height:50px}#app .aside .el-menu--collapse>.el-menu-item{display:flex;justify-content:center}#app .aside .el-sub-menu .el-menu .is-active ul,#app .aside .el-sub-menu .el-menu .is-active.is-opened ul{border:none}#app .aside .el-sub-menu .el-menu--inline .gva-menu-item{margin-left:15px}#app .hideside .aside{width:54px}#app .mobile.hideside .gva-aside{-webkit-transition-duration:.2s;transition-duration:.2s;-webkit-transform:translate3d(-210px,0,0);transform:translate3d(-220px,0,0)}#app .mobile .gva-aside{-webkit-transition:-webkit-transform .28s;transition:-webkit-transform .28s;transition:transform .28s;transition:transform .28s,-webkit-transform .28s;width:210px}#app .main-cont.el-main{min-height:100%;margin-left:220px;position:relative}#app .hideside .main-cont.el-main{margin-left:54px}#app .mobile .main-cont.el-main{margin-left:0}#app .openside.mobile .shadowBg{background:#000;opacity:.3;width:100%;top:0;height:100%;position:absolute;z-index:999;left:0}.layout-cont .main-cont{position:relative}.layout-cont .main-cont.el-main{background-color:#f1f1f2;padding:0}.admin-box{min-height:calc(100vh - 200px);padding:12px;margin:44px 0 0}.admin-box .el-table--border{border-radius:4px;margin-bottom:14px}.admin-box .el-table thead{color:#262626}.admin-box .el-table th{padding:6px 0}.admin-box .el-table th .cell{color:rgba(0,0,0,.85);font-size:14px;line-height:40px;min-height:40px}.admin-box .el-table td{padding:6px 0}.admin-box .el-table td .cell{min-height:40px;line-height:40px;color:rgba(0,0,0,.65)}.admin-box .el-table td.is-leaf{border-bottom:1px solid #e8e8e8}.admin-box .el-table th.is-leaf{background:#F7FBFF;border-bottom:none}.admin-box .el-pagination{padding:20px 0 0}.admin-box .upload-demo,.admin-box .upload,.admin-box .edit_container,.admin-box .edit{padding:0}.admin-box .el-input .el-input__suffix{margin-top:-3px}.admin-box .el-input.is-disabled .el-input__suffix,.admin-box .el-cascader .el-input .el-input__suffix{margin-top:0}.admin-box .el-input__inner{border-color:rgba(0,0,0,.15);height:32px;border-radius:2px}.admin-box:after,.admin-box:before{content:"";display:block;clear:both}.button-box{background:#fff;border:none;padding:0 0 10px}.has-gutter tr th{background-color:#fafafa}.el-table--striped .el-table__body tr.el-table__row--striped td{background:#fff}.el-table th,.el-table tr{background-color:#fff}.el-pagination{padding:20px 0!important}.el-pagination .btn-prev,.el-pagination .btn-next{border:1px solid #ddd;border-radius:4px}.el-pagination .el-pager li{color:#666;font-size:12px;margin:0 5px;border:1px solid #ddd;border-radius:4px}.el-row{padding:10px 0}.el-row .el-col>label{line-height:30px;text-align:right;width:80%;padding-right:15px;display:inline-block}.el-row .line{line-height:30px;text-align:center}.edit_container{background-color:#fff;padding:15px}.edit_container .el-button{margin:15px 0}.edit{background-color:#fff}.edit .el-button{margin:15px 0}.el-container .tips{margin-top:10px;font-size:14px;font-weight:400;color:#606266}.el-container.layout-cont .main-cont.el-main{background-color:#f1f1f2}.el-container.layout-cont .main-cont.el-main .menu-total{cursor:pointer}.el-container.layout-cont .main-cont .router-history{background:#fff;border-top:1px solid #f4f4f4;padding:0}.el-container.layout-cont .main-cont .router-history .el-tabs__header{margin:0}.el-container.layout-cont .main-cont .router-history .el-tabs__header .el-tabs__item{height:40px;border:none;border-left:1px solid #f4f4f4;border-right:1px solid #f4f4f4}.el-container.layout-cont .main-cont .router-history .el-tabs__header .el-tabs__item+.el-tabs__item{border-left:0px solid #f4f4f4}.el-container.layout-cont .main-cont .router-history .el-tabs__header .el-tabs__item.is-active{background-color:rgba(64,158,255,.08)}.el-container.layout-cont .main-cont .router-history .el-tabs__header .el-tabs__nav{border:none}.el-table__row .el-button.el-button--text.el-button--small{position:relative}.el-table__row .cell button:last-child:after{content:""!important;position:absolute!important;width:0px!important}.clear:after,.clear:before{content:"";display:block;clear:both}.el-table--striped .el-table__body tr.el-table__row--level-1 td:first-child .cell .el-table__indent{border-right:1.5px solid #ccc;margin-left:6px}.el-table--striped .el-table__body tr.el-table__row--level-1 td:first-child .cell .el-table__placeholder{width:10px}.el-table--striped .el-table__body tr.el-table__row--level-2 td:first-child .cell .el-table__indent{border-right:1.5px solid #ccc;margin-left:6px}.el-table--striped .el-table__body tr.el-table__row--level-2 td:first-child .cell .el-table__placeholder{width:10px}.dropdown-group{min-width:100px}.topfix{position:fixed;top:0;box-sizing:border-box;z-index:999}.topfix>.el-row{padding:0}.topfix>.el-row .el-col-lg-14{height:44px}.layout-cont .right-box{padding-top:6px;display:flex;justify-content:flex-end;align-items:center}.layout-cont .right-box img{vertical-align:middle;border:1px solid #ccc;border-radius:6px}.layout-cont .header-cont{padding:0 16px;height:44px;background:#fff;box-shadow:0 2px 8px rgba(16,36,66,.1)}.layout-cont .main-cont{height:100vh!important;overflow:visible;position:relative}.layout-cont .main-cont .breadcrumb{height:44px;line-height:44px;display:inline-block;padding:0;margin-left:32px;font-size:16px}.layout-cont .main-cont .breadcrumb .el-breadcrumb__item .el-breadcrumb__inner,.layout-cont .main-cont .breadcrumb .el-breadcrumb__item .el-breadcrumb__separator{font-size:14px;opacity:.5;color:#252631}.layout-cont .main-cont .breadcrumb .el-breadcrumb__item:nth-last-child(1) .el-breadcrumb__inner{font-size:14px;opacity:1;font-weight:400;color:#252631}.layout-cont .main-cont.el-main{overflow:auto;background:#fff}.layout-cont .main-cont .menu-total{cursor:pointer;float:left;opacity:.7;margin-left:32px;margin-top:18px}.layout-cont .main-cont .aside{overflow:auto;height:calc(100% - 110px);border-bottom:1px #505A68 solid}.layout-cont .main-cont .aside::-webkit-scrollbar{display:none}.layout-cont .main-cont .aside .el-footer{--el-menu-bg-color: #273444;--el-menu-hover-bg-color: rgb(31, 42, 54)}.layout-cont .main-cont .el-menu-vertical{height:calc(100vh - 110px)!important;visibility:auto}.layout-cont .main-cont .el-menu-vertical:not(.el-menu--collapse){width:220px}.layout-cont .main-cont .el-menu--collapse{width:54px}.layout-cont .main-cont .el-menu--collapse li .el-tooltip,.layout-cont .main-cont .el-menu--collapse li .el-sub-menu__title{padding:0 15px!important}.layout-cont .main-cont::-webkit-scrollbar{display:none}.layout-cont .main-cont.main-left{width:auto!important}.layout-cont .main-cont.main-right .admin-title{float:left;font-size:16px;vertical-align:middle;margin-left:20px}.layout-cont .main-cont.main-right .admin-title img{vertical-align:middle}.layout-cont .main-cont.main-right .admin-title.collapse{width:53px}.header-avatar{display:flex;justify-content:center;align-items:center}.search-component{display:inline-flex;overflow:hidden;text-align:center}.search-component .el-input__inner{border:none;border-bottom:1px solid #606266}.search-component .el-dropdown-link{cursor:pointer}.search-component .search-icon{font-size:18px;display:inline-block;vertical-align:middle;box-sizing:border-box;color:rgba(0,0,0,.65)}.search-component .dropdown-group{min-width:100px}.search-component .user-box{cursor:pointer;margin-right:24px;color:rgba(0,0,0,.65)}.transition-box{overflow:hidden;width:120px;margin-right:32px;text-align:center;margin-top:-12px}.screenfull{overflow:hidden;color:rgba(0,0,0,.65)}.el-dropdown{overflow:hidden}.card{background-color:#fff;padding:20px;border-radius:4px;overflow:hidden}.card .car-left,.card .car-right{height:68px}.card .car-right .flow,.card .car-right .user-number,.card .car-right .feedback{width:24px;height:24px;display:inline-block;border-radius:50%;line-height:24px;text-align:center;font-size:13px;margin-right:5px}.card .car-right .flow{background-color:#fff7e8;border-color:#feefd0;color:#faad14}.card .car-right .user-number{background-color:#ecf5ff;border-color:#d9ecff;color:#409eff}.card .car-right .feedback{background-color:#eef9e8;border-color:#dcf3d1;color:#52c41a}.card .car-right .card-item{padding-right:20px;text-align:right;margin-top:12px}.card .car-right .card-item b{margin-top:6px;display:block}.card .card-img{width:68px;height:68px;display:inline-block;float:left;overflow:hidden}.card .card-img img{width:100%;height:100%;border-radius:50%}.card .text{height:68px;margin-left:10px;float:left;margin-top:14px}.card .text h4{font-size:20px;color:#262626;font-weight:500;white-space:nowrap;word-break:break-all;text-overflow:ellipsis}.card .text .tips-text{color:#8c8c8c;margin-top:8px}.card .text .tips-text .el-icon{margin-right:8px;display:inline-block}.shadow{margin:4px 0}.shadow .grid-content{background-color:#fff;border-radius:4px;text-align:center;padding:10px 0;cursor:pointer}.shadow .grid-content .el-icon{width:30px;height:30px;font-size:30px;margin-bottom:8px}.gva-btn-list{margin-bottom:12px;display:flex}.gva-btn-list .el-button+.el-button{margin-left:12px}.justify-content-flex-end{justify-content:flex-end}.clearfix:after{content:"";display:block;height:0;visibility:hidden;clear:both}.fl-left{float:left}.fl-right{float:right}.mg{margin:10px!important}.left-mg-xs{margin-left:6px!important}.left-mg-sm{margin-left:10px!important}.left-mg-md{margin-left:14px!important}.top-mg-lg{margin-top:20px!important}.tb-mg-lg{margin:20px 0!important}.bottom-mg-lg{margin-bottom:20px!important}.left-mg-lg{margin-left:18px!important}.title-1{text-align:center;font-size:32px}.title-3{text-align:center}.keyword{width:220px;margin:0 0 0 30px}#nprogress .bar{background:#4D70FF!important}@media screen and (min-width: 320px) and (max-width: 750px){.el-header,.layout-cont .main-cont .breadcrumb{padding:0 5px}.layout-cont .right-box{margin-right:5px}.el-main .admin-box{margin-left:0;margin-right:0}.el-main .big.admin-box{padding:0}.el-main .big .bottom .chart-player{height:auto!important;margin-bottom:15px}.el-main .big .bottom .todoapp{background-color:#fff;padding-bottom:10px}.card .car-left,.card .car-right{width:100%;height:100%}.card{padding-left:5px;padding-right:5px}.card .text{width:100%}.card .text h4{white-space:break-spaces}.shadow{margin-left:4px;margin-right:4px}.shadow .grid-content{margin-bottom:10px;padding:0}.el-dialog{width:90%}.el-transfer .el-transfer-panel{width:40%;display:inline-block}.el-transfer .el-transfer__buttons{padding:0 5px;display:inline-block}}#app{background:#eee;height:100vh;overflow:hidden;font-weight:400!important}.el-button{font-weight:400!important}.el-tabs__header{margin:0!important}.demo-tabs .el-tabs__header,.demo-tabs .el-tabs__header *{height:35px!important}.demo-tabs .el-tabs__nav{border-bottom:1px solid var(--el-border-color-light)!important}.el-table__header *{font-family:Microsoft YaHei}.organize-search{width:200px!important;float:right;height:32px!important;color:#aaa}.organize-search input{font-size:12px;color:#252631}.custom-dialog .el-dialog__title{font-size:16px!important;font-weight:700!important}.custom-dialog .el-form-item__label,.custom-dialog .el-form-item__content *,.custom-dialog .el-form-item__content * .el-radio__label{font-size:12px}.custom-dialog .el-radio__input.is-checked .el-radio__inner{border-color:#1890ff;background:#1890FF}.custom-dialog .el-tabs__active-bar{background-color:#3791cf}.custom-dialog .el-tabs__item.is-active{color:#189cff}.custom-dialog .el-switch.is-checked .el-switch__core{background-color:#1890ff;--el-switch-on-color: #1890FF}.custom-dialog .el-switch__core{background:#C0C0C0}.custom-dialog .dialog-footer button{height:28px;width:93px;border-radius:5px!important;font-size:12px}.custom-dialog .el-checkbox__input.is-checked .el-checkbox__inner{background:#1890FF;border-color:#1890ff}.header button{height:32px;width:77px;border-radius:4px!important;font-size:12px;color:#2972c8;--el-button-bg-color: #ffffff !important;--el-button-border-color: #E4E4E4 !important;font-family:PingFangSC-Regular,PingFang SC}.header .icon-shuaxin:before{margin-right:5px}.header .el-input .el-input__icon{font-size:16px}.table-row-style th.is-leaf{background:#FAFAFA!important}.risk-pagination{float:right;height:28px}.risk-pagination .el-pagination__total,.risk-pagination .el-input__inner,.risk-pagination .el-pagination__jump{color:#252631;opacity:.5}.risk-pagination .el-pager li.is-active+li{border-left:1px solid #ddd!important;border-radius:4px;color:#252631;opacity:.5}.risk-pagination *{height:26px;line-height:28px}.risk-pagination .el-pager{height:28px}.risk-pagination .el-pager li{height:28px;background-color:#fff!important}.risk-pagination .el-pager .is-active{height:28px;border:1px solid #2972C8!important;border-radius:4px!important;color:#2972c8!important}.risk-pagination .btn-prev,.risk-pagination .btn-next{height:28px;background-color:#fff!important}.terminal .table-row-style th.is-leaf{background:#FFFFFF}.terminal .table-row-style .app-table-style td{background-color:#fff!important}.organize .header button{height:28px;width:90px;border-radius:5px!important;font-size:12px}.organize .table-row-style th.is-leaf{background:#FFFFFF}.organize .table-row-style .app-table-style td{background-color:#fff!important}.organize .dialog-footer button{height:28px;width:93px;border-radius:5px!important;font-size:12px}.role .header button{height:28px;width:90px;border-radius:5px!important;font-size:12px}.role .table-row-style th.is-leaf{background:#FFFFFF}.role .table-row-style .app-table-style td{background-color:#fff!important}.role .dialog-footer button{height:28px;width:93px;border-radius:5px!important;font-size:12px}.application .header button{height:28px;width:90px;border-radius:5px!important;font-size:12px}.application .table-row-style th.is-leaf{background:#FFFFFF}.application .table-row-style .app-table-style td{background-color:#fff!important}.application .dialog-footer button{height:28px;width:93px;border-radius:5px!important;font-size:12px}.policy-tree div{font-size:12px}.custom-tree-type{font-size:6px;margin-left:10px;background:#0d84ff;color:#fff}#app .el-radio__input.is-checked .el-radio__inner:after{content:"";width:8px;height:3px;border:2px solid white;border-top:transparent;border-right:transparent;text-align:center;display:block;position:absolute;top:2px;left:1px;vertical-align:middle;transform:rotate(-45deg);border-radius:0;background-color:#2972c8!important;background:#2972C8!important}#app .el-radio__input.is-checked .el-radio__inner{background-color:#2972c8!important;background:#2972C8!important}#app .el-radio__input.is-checked+.el-radio__label{color:#252631!important}#app .el-radio,#app .el-form-item__label{color:#252631!important}#app .el-checkbox__input.is-indeterminate .el-checkbox__inner{background-color:#2972c8!important}#app .el-checkbox__input.is-checked .el-checkbox__inner{background-color:#2972c8!important;background:#2972C8!important}#app .el-checkbox.el-checkbox--large .el-checkbox__inner{border-radius:7px}#nprogress{pointer-events:none}#nprogress .bar{background:#29d;position:fixed;z-index:1031;top:0;left:0;width:100%;height:2px}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;box-shadow:0 0 10px #29d,0 0 5px #29d;opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translateY(-4px)}#nprogress .spinner{display:block;position:fixed;z-index:1031;top:15px;right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:solid 2px transparent;border-top-color:#29d;border-left-color:#29d;border-radius:50%;-webkit-animation:nprogress-spinner .4s linear infinite;animation:nprogress-spinner .4s linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .spinner,.nprogress-custom-parent #nprogress .bar{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n',document.head.appendChild(o),{execute:function(){var o;
/**
            * @vue/shared v3.5.16
            * (c) 2018-present Yuxi (Evan) You and Vue contributors
            * @license MIT
            **/
/*! #__NO_SIDE_EFFECTS__ */
function l(e){var t,n=Object.create(null),r=y(e.split(","));try{for(r.s();!(t=r.n()).done;){var o=t.value;n[o]=1}}catch(a){r.e(a)}finally{r.f()}return function(e){return e in n}}t({A:function(e){return I(e)?hr(fr,e,!1)||e:e||vr},C:ae,D:St,I:ee,J:function(e){var t=na();if(!t)return;var n=t.ut=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e(t.proxy);Array.from(document.querySelectorAll('[data-v-owner="'.concat(t.uid,'"]'))).forEach((function(e){return Wa(e,n)}))},r=function(){var r=e(t.proxy);t.ce?Wa(t.ce,r):Ha(t.subTree,r),n(r)};rr((function(){fn(r)})),nr((function(){fo(r,h,{flush:"post"});var e=new MutationObserver(r);e.observe(t.subTree.el.parentNode,{childList:!0}),ir((function(){return e.disconnect()}))}))},N:function(e){return hr(dr,e)},O:xn,P:br,U:cn,a:function(){return qr(hu)},d:Po,e:Uo,f:Mo,g:Ho,h:pr,i:gr,k:$o,l:qr,m:Vt,o:To,p:Ur,r:Ft,u:function(e){return qr(mu)},w:yn,y:/*! #__NO_SIDE_EFFECTS__ */
function(e){T(e)&&(e={loader:e});var t,n=e,r=n.loader,o=n.loadingComponent,a=n.errorComponent,i=n.delay,l=void 0===i?200:i,c=n.hydrate,u=n.timeout,s=n.suspensible,f=void 0===s||s,d=n.onError,p=null,v=0,h=function(){return v++,p=null,m()},m=function(){var e;return p||(e=p=r().catch((function(e){if(e=e instanceof Error?e:new Error(String(e)),d)return new Promise((function(t,n){d(e,(function(){return t(h())}),(function(){return n(e)}),v+1)}));throw e})).then((function(n){return e!==p&&p?p:(n&&(n.__esModule||"Module"===n[Symbol.toStringTag])&&(n=n.default),t=n,n)})))};return Nn({name:"AsyncComponentWrapper",__asyncLoader:m,__asyncHydrate:function(e,n,r){var o=c?function(){var t=c((function(){r()}),(function(t){return function(e,t){if(Un(e)&&"["===e.data)for(var n=1,r=e.nextSibling;r;){if(1===r.nodeType){if(!1===t(r))break}else if(Un(r))if("]"===r.data){if(0===--n)break}else"["===r.data&&n++;r=r.nextSibling}else t(e)}(e,t)}));t&&(n.bum||(n.bum=[])).push(t),(n.u||(n.u=[])).push((function(){return!0}))}:r;t?o():m().then((function(){return!n.isUnmounted&&o()}))},get __asyncResolved(){return t},setup:function(){var e=ta;if(Bn(e),t)return function(){return Dn(t,e)};var n=function(t){p=null,Qt(t,e,13,!a)};if(f&&e.suspense||ua)return m().then((function(t){return function(){return Dn(t,e)}})).catch((function(e){return n(e),function(){return a?qo(a,{error:e}):null}}));var r=Ft(!1),i=Ft(),c=Ft(!!l);return l&&setTimeout((function(){c.value=!1}),l),null!=u&&setTimeout((function(){if(!r.value&&!i.value){var e=new Error("Async component timed out after ".concat(u,"ms."));n(e),i.value=e}}),u),m().then((function(){r.value=!0,e.parent&&$n(e.parent.vnode)&&e.parent.update()})).catch((function(e){n(e),i.value=e})),function(){return r.value&&t?Dn(t,e):i.value&&a?qo(a,{error:i.value}):o&&!c.value?qo(o):void 0}}})},z:fo});var c,s={},d=[],h=function(){},x=function(){return!1},w=function(e){return 111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97)},_=function(e){return e.startsWith("onUpdate:")},k=Object.assign,S=function(e,t){var n=e.indexOf(t);n>-1&&e.splice(n,1)},C=Object.prototype.hasOwnProperty,j=function(e,t){return C.call(e,t)},O=Array.isArray,z=function(e){return"[object Map]"===F(e)},A=function(e){return"[object Set]"===F(e)},E=function(e){return"[object Date]"===F(e)},T=function(e){return"function"==typeof e},I=function(e){return"string"==typeof e},L=function(e){return"symbol"===b(e)},R=function(e){return null!==e&&"object"===b(e)},P=function(e){return(R(e)||T(e))&&T(e.then)&&T(e.catch)},M=Object.prototype.toString,F=function(e){return M.call(e)},N=function(e){return F(e).slice(8,-1)},B=function(e){return"[object Object]"===F(e)},V=function(e){return I(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e},U=l(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),q=function(e){var t=Object.create(null);return function(n){return t[n]||(t[n]=e(n))}},D=/-(\w)/g,$=q((function(e){return e.replace(D,(function(e,t){return t?t.toUpperCase():""}))})),H=/\B([A-Z])/g,W=q((function(e){return e.replace(H,"-$1").toLowerCase()})),G=q((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),K=q((function(e){return e?"on".concat(G(e)):""})),J=function(e,t){return!Object.is(e,t)},X=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var o=0;o<e.length;o++)e[o].apply(e,n)},Y=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Z=function(e){var t=parseFloat(e);return isNaN(t)?e:t},Q=function(){return c||(c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})};function ee(e){if(O(e)){for(var t={},n=0;n<e.length;n++){var r=e[n],o=I(r)?oe(r):ee(r);if(o)for(var a in o)t[a]=o[a]}return t}if(I(e)||R(e))return e}var te=/;(?![^(]*\))/g,ne=/:([^]+)/,re=/\/\*[^]*?\*\//g;function oe(e){var t={};return e.replace(re,"").split(te).forEach((function(e){if(e){var n=e.split(ne);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function ae(e){var t="";if(I(e))t=e;else if(O(e))for(var n=0;n<e.length;n++){var r=ae(e[n]);r&&(t+=r+" ")}else if(R(e))for(var o in e)e[o]&&(t+=o+" ");return t.trim()}var ie=l("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function le(e){return!!e||""===e}function ce(e,t){if(e===t)return!0;var n=E(e),r=E(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=L(e),r=L(t),n||r)return e===t;if(n=O(e),r=O(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;for(var n=!0,r=0;n&&r<e.length;r++)n=ce(e[r],t[r]);return n}(e,t);if(n=R(e),r=R(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var o in e){var a=e.hasOwnProperty(o),i=t.hasOwnProperty(o);if(a&&!i||!a&&i||!ce(e[o],t[o]))return!1}}return String(e)===String(t)}function ue(e,t){return e.findIndex((function(e){return ce(e,t)}))}var se,fe,de=function(e){return!(!e||!0!==e.__v_isRef)},pe=t("t",(function(e){return I(e)?e:null==e?"":O(e)||R(e)&&(e.toString===M||!T(e.toString))?de(e)?pe(e.value):JSON.stringify(e,ve,2):String(e)})),ve=function(e,t){return de(t)?ve(e,t.value):z(t)?v({},"Map(".concat(t.size,")"),g(t.entries()).reduce((function(e,t,n){var r=m(t,2),o=r[0],a=r[1];return e[he(o,n)+" =>"]=a,e}),{})):A(t)?v({},"Set(".concat(t.size,")"),g(t.values()).map((function(e){return he(e)}))):L(t)?he(t):!R(t)||O(t)||B(t)?t:String(t)},he=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return L(e)?"Symbol(".concat(null!=(t=e.description)?t:n,")"):e},me=function(){return p((function e(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];f(this,e),this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=se,!t&&se&&(this.index=(se.scopes||(se.scopes=[])).push(this)-1)}),[{key:"active",get:function(){return this._active}},{key:"pause",value:function(){if(this._active){var e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}},{key:"resume",value:function(){if(this._active&&this._isPaused){var e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}},{key:"run",value:function(e){if(this._active){var t=se;try{return se=this,e()}finally{se=t}}}},{key:"on",value:function(){1===++this._on&&(this.prevScope=se,se=this)}},{key:"off",value:function(){this._on>0&&0===--this._on&&(se=this.prevScope,this.prevScope=void 0)}},{key:"stop",value:function(e){if(this._active){var t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}])}();function ge(e){return new me(e)}function be(){return se}var ye,xe,we=new WeakSet,_e=function(){return p((function e(t){f(this,e),this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,se&&se.active&&se.effects.push(this)}),[{key:"pause",value:function(){this.flags|=64}},{key:"resume",value:function(){64&this.flags&&(this.flags&=-65,we.has(this)&&(we.delete(this),this.trigger()))}},{key:"notify",value:function(){2&this.flags&&!(32&this.flags)||8&this.flags||Se(this)}},{key:"run",value:function(){if(!(1&this.flags))return this.fn();this.flags|=2,Fe(this),Oe(this);var e=fe,t=Le;fe=this,Le=!0;try{return this.fn()}finally{ze(this),fe=e,Le=t,this.flags&=-3}}},{key:"stop",value:function(){if(1&this.flags){for(var e=this.deps;e;e=e.nextDep)Te(e);this.deps=this.depsTail=void 0,Fe(this),this.onStop&&this.onStop(),this.flags&=-2}}},{key:"trigger",value:function(){64&this.flags?we.add(this):this.scheduler?this.scheduler():this.runIfDirty()}},{key:"runIfDirty",value:function(){Ae(this)&&this.run()}},{key:"dirty",get:function(){return Ae(this)}}])}(),ke=0;function Se(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e.flags|=8,t)return e.next=xe,void(xe=e);e.next=ye,ye=e}function Ce(){ke++}function je(){if(!(--ke>0)){if(xe){var e=xe;for(xe=void 0;e;){var t=e.next;e.next=void 0,e.flags&=-9,e=t}}for(var n;ye;){var r=ye;for(ye=void 0;r;){var o=r.next;if(r.next=void 0,r.flags&=-9,1&r.flags)try{r.trigger()}catch(a){n||(n=a)}r=o}}if(n)throw n}}function Oe(e){for(var t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ze(e){for(var t,n=e.depsTail,r=n;r;){var o=r.prevDep;-1===r.version?(r===n&&(n=o),Te(r),Ie(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function Ae(e){for(var t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ee(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ee(e){if((!(4&e.flags)||16&e.flags)&&(e.flags&=-17,e.globalVersion!==Ne&&(e.globalVersion=Ne,e.isSSR||!(128&e.flags)||(e.deps||e._dirty)&&Ae(e)))){e.flags|=2;var t=e.dep,n=fe,r=Le;fe=e,Le=!0;try{Oe(e);var o=e.fn(e._value);(0===t.version||J(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(a){throw t.version++,a}finally{fe=n,Le=r,ze(e),e.flags&=-3}}}function Te(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.dep,r=e.prevSub,o=e.nextSub;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(var a=n.computed.deps;a;a=a.nextDep)Te(a,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function Ie(e){var t=e.prevDep,n=e.nextDep;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}var Le=!0,Re=[];function Pe(){Re.push(Le),Le=!1}function Me(){var e=Re.pop();Le=void 0===e||e}function Fe(e){var t=e.cleanup;if(e.cleanup=void 0,t){var n=fe;fe=void 0;try{t()}finally{fe=n}}}var Ne=0,Be=p((function e(t,n){f(this,e),this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0})),Ve=function(){return p((function e(t){f(this,e),this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}),[{key:"track",value:function(e){if(fe&&Le&&fe!==this.computed){var t=this.activeLink;if(void 0===t||t.sub!==fe)t=this.activeLink=new Be(fe,this),fe.deps?(t.prevDep=fe.depsTail,fe.depsTail.nextDep=t,fe.depsTail=t):fe.deps=fe.depsTail=t,Ue(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){var n=t.nextDep;n.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=n),t.prevDep=fe.depsTail,t.nextDep=void 0,fe.depsTail.nextDep=t,fe.depsTail=t,fe.deps===t&&(fe.deps=n)}return t}}},{key:"trigger",value:function(e){this.version++,Ne++,this.notify(e)}},{key:"notify",value:function(e){Ce();try{0;for(var t=this.subs;t;t=t.prevSub)t.sub.notify()&&t.sub.dep.notify()}finally{je()}}}])}();function Ue(e){if(e.dep.sc++,4&e.sub.flags){var t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(var n=t.deps;n;n=n.nextDep)Ue(n)}var r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}var qe=new WeakMap,De=Symbol(""),$e=Symbol(""),He=Symbol("");function We(e,t,n){if(Le&&fe){var r=qe.get(e);r||qe.set(e,r=new Map);var o=r.get(n);o||(r.set(n,o=new Ve),o.map=r,o.key=n),o.track()}}function Ge(e,t,n,r,o,a){var i=qe.get(e);if(i){var l=function(e){e&&e.trigger()};if(Ce(),"clear"===t)i.forEach(l);else{var c=O(e),u=c&&V(n);if(c&&"length"===n){var s=Number(r);i.forEach((function(e,t){("length"===t||t===He||!L(t)&&t>=s)&&l(e)}))}else switch((void 0!==n||i.has(void 0))&&l(i.get(n)),u&&l(i.get(He)),t){case"add":c?u&&l(i.get("length")):(l(i.get(De)),z(e)&&l(i.get($e)));break;case"delete":c||(l(i.get(De)),z(e)&&l(i.get($e)));break;case"set":z(e)&&l(i.get(De))}}je()}else Ne++}function Ke(e){var t=It(e);return t===e?t:(We(t,0,He),Et(e)?t:t.map(Rt))}function Je(e){return We(e=It(e),0,He),e}var Xe=(v(v(v(v(v(v(v(v(v(v(o={__proto__:null},Symbol.iterator,(function(){return Ye(this,Symbol.iterator,Rt)})),"concat",(function(){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=Ke(this)).concat.apply(e,g(n.map((function(e){return O(e)?Ke(e):e}))))})),"entries",(function(){return Ye(this,"entries",(function(e){return e[1]=Rt(e[1]),e}))})),"every",(function(e,t){return Qe(this,"every",e,t,void 0,arguments)})),"filter",(function(e,t){return Qe(this,"filter",e,t,(function(e){return e.map(Rt)}),arguments)})),"find",(function(e,t){return Qe(this,"find",e,t,Rt,arguments)})),"findIndex",(function(e,t){return Qe(this,"findIndex",e,t,void 0,arguments)})),"findLast",(function(e,t){return Qe(this,"findLast",e,t,Rt,arguments)})),"findLastIndex",(function(e,t){return Qe(this,"findLastIndex",e,t,void 0,arguments)})),"forEach",(function(e,t){return Qe(this,"forEach",e,t,void 0,arguments)})),v(v(v(v(v(v(v(v(v(v(o,"includes",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return tt(this,"includes",t)})),"indexOf",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return tt(this,"indexOf",t)})),"join",(function(e){return Ke(this).join(e)})),"lastIndexOf",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return tt(this,"lastIndexOf",t)})),"map",(function(e,t){return Qe(this,"map",e,t,void 0,arguments)})),"pop",(function(){return nt(this,"pop")})),"push",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return nt(this,"push",t)})),"reduce",(function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return et(this,"reduce",e,n)})),"reduceRight",(function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return et(this,"reduceRight",e,n)})),"shift",(function(){return nt(this,"shift")})),v(v(v(v(v(v(v(o,"some",(function(e,t){return Qe(this,"some",e,t,void 0,arguments)})),"splice",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return nt(this,"splice",t)})),"toReversed",(function(){return Ke(this).toReversed()})),"toSorted",(function(e){return Ke(this).toSorted(e)})),"toSpliced",(function(){var e;return(e=Ke(this)).toSpliced.apply(e,arguments)})),"unshift",(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return nt(this,"unshift",t)})),"values",(function(){return Ye(this,"values",Rt)})));function Ye(e,t,n){var r=Je(e),o=r[t]();return r===e||Et(e)||(o._next=o.next,o.next=function(){var e=o._next();return e.value&&(e.value=n(e.value)),e}),o}var Ze=Array.prototype;function Qe(e,t,n,r,o,a){var i=Je(e),l=i!==e&&!Et(e),c=i[t];if(c!==Ze[t]){var u=c.apply(e,a);return l?Rt(u):u}var s=n;i!==e&&(l?s=function(t,r){return n.call(this,Rt(t),r,e)}:n.length>2&&(s=function(t,r){return n.call(this,t,r,e)}));var f=c.call(i,s,r);return l&&o?o(f):f}function et(e,t,n,r){var o=Je(e),a=n;return o!==e&&(Et(e)?n.length>3&&(a=function(t,r,o){return n.call(this,t,r,o,e)}):a=function(t,r,o){return n.call(this,t,Rt(r),o,e)}),o[t].apply(o,[a].concat(g(r)))}function tt(e,t,n){var r=It(e);We(r,0,He);var o=r[t].apply(r,g(n));return-1!==o&&!1!==o||!Tt(n[0])?o:(n[0]=It(n[0]),r[t].apply(r,g(n)))}function nt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];Pe(),Ce();var r=It(e)[t].apply(e,n);return je(),Me(),r}var rt=l("__proto__,__v_isRef,__isVue"),ot=new Set(Object.getOwnPropertyNames(Symbol).filter((function(e){return"arguments"!==e&&"caller"!==e})).map((function(e){return Symbol[e]})).filter(L));function at(e){L(e)||(e=String(e));var t=It(this);return We(t,0,e),t.hasOwnProperty(e)}var it=function(){return p((function e(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];f(this,e),this._isReadonly=t,this._isShallow=n}),[{key:"get",value:function(e,t,n){if("__v_skip"===t)return e.__v_skip;var r=this._isReadonly,o=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return o;if("__v_raw"===t)return n===(r?o?kt:_t:o?wt:xt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;var a=O(e);if(!r){var i;if(a&&(i=Xe[t]))return i;if("hasOwnProperty"===t)return at}var l=Reflect.get(e,t,Mt(e)?e:n);return(L(t)?ot.has(t):rt(t))?l:(r||We(e,0,t),o?l:Mt(l)?a&&V(t)?l:l.value:R(l)?r?jt(l):St(l):l)}}])}(),lt=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return f(this,t),i(this,t,[!1,e])}return u(t,e),p(t,[{key:"set",value:function(e,t,n,r){var o=e[t];if(!this._isShallow){var a=At(o);if(Et(n)||At(n)||(o=It(o),n=It(n)),!O(e)&&Mt(o)&&!Mt(n))return!a&&(o.value=n,!0)}var i=O(e)&&V(t)?Number(t)<e.length:j(e,t),l=Reflect.set(e,t,n,Mt(e)?e:r);return e===It(r)&&(i?J(n,o)&&Ge(e,"set",t,n):Ge(e,"add",t,n)),l}},{key:"deleteProperty",value:function(e,t){var n=j(e,t);e[t];var r=Reflect.deleteProperty(e,t);return r&&n&&Ge(e,"delete",t,void 0),r}},{key:"has",value:function(e,t){var n=Reflect.has(e,t);return L(t)&&ot.has(t)||We(e,0,t),n}},{key:"ownKeys",value:function(e){return We(e,0,O(e)?"length":De),Reflect.ownKeys(e)}}])}(it),ct=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return f(this,t),i(this,t,[!0,e])}return u(t,e),p(t,[{key:"set",value:function(e,t){return!0}},{key:"deleteProperty",value:function(e,t){return!0}}])}(it),ut=new lt,st=new ct,ft=new lt(!0),dt=function(e){return e},pt=function(e){return Reflect.getPrototypeOf(e)};function vt(e){return function(){return"delete"!==e&&("clear"===e?void 0:this)}}function ht(e,t){var n={get:function(n){var r=this.__v_raw,o=It(r),a=It(n);e||(J(n,a)&&We(o,0,n),We(o,0,a));var i=pt(o).has,l=t?dt:e?Pt:Rt;return i.call(o,n)?l(r.get(n)):i.call(o,a)?l(r.get(a)):void(r!==o&&r.get(n))},get size(){var t=this.__v_raw;return!e&&We(It(t),0,De),Reflect.get(t,"size",t)},has:function(t){var n=this.__v_raw,r=It(n),o=It(t);return e||(J(t,o)&&We(r,0,t),We(r,0,o)),t===o?n.has(t):n.has(t)||n.has(o)},forEach:function(n,r){var o=this,a=o.__v_raw,i=It(a),l=t?dt:e?Pt:Rt;return!e&&We(i,0,De),a.forEach((function(e,t){return n.call(r,l(e),l(t),o)}))}};return k(n,e?{add:vt("add"),set:vt("set"),delete:vt("delete"),clear:vt("clear")}:{add:function(e){t||Et(e)||At(e)||(e=It(e));var n=It(this);return pt(n).has.call(n,e)||(n.add(e),Ge(n,"add",e,e)),this},set:function(e,n){t||Et(n)||At(n)||(n=It(n));var r=It(this),o=pt(r),a=o.has,i=o.get,l=a.call(r,e);l||(e=It(e),l=a.call(r,e));var c=i.call(r,e);return r.set(e,n),l?J(n,c)&&Ge(r,"set",e,n):Ge(r,"add",e,n),this},delete:function(e){var t=It(this),n=pt(t),r=n.has,o=n.get,a=r.call(t,e);a||(e=It(e),a=r.call(t,e)),o&&o.call(t,e);var i=t.delete(e);return a&&Ge(t,"delete",e,void 0),i},clear:function(){var e=It(this),t=0!==e.size,n=e.clear();return t&&Ge(e,"clear",void 0,void 0),n}}),["keys","values","entries",Symbol.iterator].forEach((function(r){n[r]=function(e,t,n){return function(){var r=this.__v_raw,o=It(r),a=z(o),i="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=r[e].apply(r,arguments),u=n?dt:t?Pt:Rt;return!t&&We(o,0,l?$e:De),v({next:function(){var e=c.next(),t=e.value,n=e.done;return n?{value:t,done:n}:{value:i?[u(t[0]),u(t[1])]:u(t),done:n}}},Symbol.iterator,(function(){return this}))}}(r,e,t)})),n}function mt(e,t){var n=ht(e,t);return function(t,r,o){return"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(j(n,r)&&r in t?n:t,r,o)}}var gt={get:mt(!1,!1)},bt={get:mt(!1,!0)},yt={get:mt(!0,!1)},xt=new WeakMap,wt=new WeakMap,_t=new WeakMap,kt=new WeakMap;function St(e){return At(e)?e:Ot(e,!1,ut,gt,xt)}function Ct(e){return Ot(e,!1,ft,bt,wt)}function jt(e){return Ot(e,!0,st,yt,_t)}function Ot(e,t,n,r,o){if(!R(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;var a,i=(a=e).__v_skip||!Object.isExtensible(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(N(a));if(0===i)return e;var l=o.get(e);if(l)return l;var c=new Proxy(e,2===i?r:n);return o.set(e,c),c}function zt(e){return At(e)?zt(e.__v_raw):!(!e||!e.__v_isReactive)}function At(e){return!(!e||!e.__v_isReadonly)}function Et(e){return!(!e||!e.__v_isShallow)}function Tt(e){return!!e&&!!e.__v_raw}function It(e){var t=e&&e.__v_raw;return t?It(t):e}function Lt(e){return!j(e,"__v_skip")&&Object.isExtensible(e)&&Y(e,"__v_skip",!0),e}var Rt=function(e){return R(e)?St(e):e},Pt=function(e){return R(e)?jt(e):e};function Mt(e){return!!e&&!0===e.__v_isRef}function Ft(e){return Nt(e,!1)}function Nt(e,t){return Mt(e)?e:new Bt(e,t)}var Bt=function(){return p((function e(t,n){f(this,e),this.dep=new Ve,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:It(t),this._value=n?t:Rt(t),this.__v_isShallow=n}),[{key:"value",get:function(){return this.dep.track(),this._value},set:function(e){var t=this._rawValue,n=this.__v_isShallow||Et(e)||At(e);e=n?e:It(e),J(e,t)&&(this._rawValue=e,this._value=n?e:Rt(e),this.dep.trigger())}}])}();function Vt(e){return Mt(e)?e.value:e}var Ut={get:function(e,t,n){return"__v_raw"===t?e:Vt(Reflect.get(e,t,n))},set:function(e,t,n,r){var o=e[t];return Mt(o)&&!Mt(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function qt(e){return zt(e)?e:new Proxy(e,Ut)}var Dt=function(){return p((function e(t,n,r){f(this,e),this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}),[{key:"value",get:function(){var e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e},set:function(e){this._object[this._key]=e}},{key:"dep",get:function(){return e=It(this._object),t=this._key,(n=qe.get(e))&&n.get(t);var e,t,n}}])}();function $t(e,t,n){var r=e[t];return Mt(r)?r:new Dt(e,t,n)}var Ht=function(){return p((function e(t,n,r){f(this,e),this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ve(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ne-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}),[{key:"notify",value:function(){if(this.flags|=16,!(8&this.flags)&&fe!==this)return Se(this,!0),!0}},{key:"value",get:function(){var e=this.dep.track();return Ee(this),e&&(e.version=this.dep.version),this._value},set:function(e){this.setter&&this.setter(e)}}])}();var Wt={},Gt=new WeakMap,Kt=void 0;function Jt(e,t){var n,r,o,a,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,l=i.immediate,c=i.deep,u=i.once,f=i.scheduler,d=i.augmentJob,p=i.call,v=function(e){return c?e:Et(e)||!1===c||0===c?Xt(e,1):Xt(e)},m=!1,g=!1;if(Mt(e)?(r=function(){return e.value},m=Et(e)):zt(e)?(r=function(){return v(e)},m=!0):O(e)?(g=!0,m=e.some((function(e){return zt(e)||Et(e)})),r=function(){return e.map((function(e){return Mt(e)?e.value:zt(e)?v(e):T(e)?p?p(e,2):e():void 0}))}):r=T(e)?t?p?function(){return p(e,2)}:e:function(){if(o){Pe();try{o()}finally{Me()}}var t=Kt;Kt=n;try{return p?p(e,3,[a]):e(a)}finally{Kt=t}}:h,t&&c){var b=r,x=!0===c?1/0:c;r=function(){return Xt(b(),x)}}var w=be(),_=function(){n.stop(),w&&w.active&&S(w.effects,n)};if(u&&t){var k=t;t=function(){k.apply(void 0,arguments),_()}}var C=g?new Array(e.length).fill(Wt):Wt,j=function(e){if(1&n.flags&&(n.dirty||e))if(t){var r=n.run();if(c||m||(g?r.some((function(e,t){return J(e,C[t])})):J(r,C))){o&&o();var i=Kt;Kt=n;try{var l=[r,C===Wt?void 0:g&&C[0]===Wt?[]:C,a];C=r,p?p(t,3,l):t.apply(void 0,l)}finally{Kt=i}}}else n.run()};return d&&d(j),(n=new _e(r)).scheduler=f?function(){return f(j,!1)}:j,a=function(e){return function(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Kt;if(t){var n=Gt.get(t);n||Gt.set(t,n=[]),n.push(e)}}(e,!1,n)},o=n.onStop=function(){var e=Gt.get(n);if(e){if(p)p(e,4);else{var t,r=y(e);try{for(r.s();!(t=r.n()).done;){(0,t.value)()}}catch(o){r.e(o)}finally{r.f()}}Gt.delete(n)}},t?l?j(!0):C=n.run():f?f(j.bind(null,!0),!0):n.run(),_.pause=n.pause.bind(n),_.resume=n.resume.bind(n),_.stop=_,_}function Xt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0,n=arguments.length>2?arguments[2]:void 0;if(t<=0||!R(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,Mt(e))Xt(e.value,t,n);else if(O(e))for(var r=0;r<e.length;r++)Xt(e[r],t,n);else if(A(e)||z(e))e.forEach((function(e){Xt(e,t,n)}));else if(B(e)){for(var o in e)Xt(e[o],t,n);var a,i=y(Object.getOwnPropertySymbols(e));try{for(i.s();!(a=i.n()).done;){var l=a.value;Object.prototype.propertyIsEnumerable.call(e,l)&&Xt(e[l],t,n)}}catch(c){i.e(c)}finally{i.f()}}return e}
/**
            * @vue/runtime-core v3.5.16
            * (c) 2018-present Yuxi (Evan) You and Vue contributors
            * @license MIT
            **/function Yt(e,t,n,r){try{return r?e.apply(void 0,g(r)):e()}catch(o){Qt(o,t,n)}}function Zt(e,t,n,r){if(T(e)){var o=Yt(e,t,n,r);return o&&P(o)&&o.catch((function(e){Qt(e,t,n)})),o}if(O(e)){for(var a=[],i=0;i<e.length;i++)a.push(Zt(e[i],t,n,r));return a}}function Qt(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=t?t.vnode:null,a=t&&t.appContext.config||s,i=a.errorHandler,l=a.throwUnhandledErrorInProduction;if(t){for(var c=t.parent,u=t.proxy,f="https://vuejs.org/error-reference/#runtime-".concat(n);c;){var d=c.ec;if(d)for(var p=0;p<d.length;p++)if(!1===d[p](e,u,f))return;c=c.parent}if(i)return Pe(),Yt(i,null,10,[e,u,f]),void Me()}!function(e,t,n){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(r)throw e;console.error(e)}(e,n,o,r,l)}var en=[],tn=-1,nn=[],rn=null,on=0,an=Promise.resolve(),ln=null;function cn(e){var t=ln||an;return e?t.then(this?e.bind(this):e):t}function un(e){if(!(1&e.flags)){var t=vn(e),n=en[en.length-1];!n||!(2&e.flags)&&t>=vn(n)?en.push(e):en.splice(function(e){for(var t=tn+1,n=en.length;t<n;){var r=t+n>>>1,o=en[r],a=vn(o);a<e||a===e&&2&o.flags?t=r+1:n=r}return t}(t),0,e),e.flags|=1,sn()}}function sn(){ln||(ln=an.then(hn))}function fn(e){O(e)?nn.push.apply(nn,g(e)):rn&&-1===e.id?rn.splice(on+1,0,e):1&e.flags||(nn.push(e),e.flags|=1),sn()}function dn(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:tn+1;n<en.length;n++){var r=en[n];if(r&&2&r.flags){if(e&&r.id!==e.uid)continue;en.splice(n,1),n--,4&r.flags&&(r.flags&=-2),r(),4&r.flags||(r.flags&=-2)}}}function pn(e){if(nn.length){var t,n=g(new Set(nn)).sort((function(e,t){return vn(e)-vn(t)}));if(nn.length=0,rn)return void(t=rn).push.apply(t,g(n));for(rn=n,on=0;on<rn.length;on++){var r=rn[on];4&r.flags&&(r.flags&=-2),8&r.flags||r(),r.flags&=-2}rn=null,on=0}}var vn=function(e){return null==e.id?2&e.flags?-1:1/0:e.id};function hn(e){try{for(tn=0;tn<en.length;tn++){var t=en[tn];!t||8&t.flags||(4&t.flags&&(t.flags&=-2),Yt(t,t.i,t.i?15:14),4&t.flags||(t.flags&=-2))}}finally{for(;tn<en.length;tn++){var n=en[tn];n&&(n.flags&=-2)}tn=-1,en.length=0,pn(),ln=null,(en.length||nn.length)&&hn()}}var mn=null,gn=null;function bn(e){var t=mn;return mn=e,gn=e&&e.type.__scopeId||null,t}function yn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:mn;if(!t)return e;if(e._n)return e;var n=function(){n._d&&Lo(-1);var r,o=bn(t);try{r=e.apply(void 0,arguments)}finally{bn(o),n._d&&Lo(1)}return r};return n._n=!0,n._c=!0,n._d=!0,n}function xn(e,t){if(null===mn)return e;for(var n=pa(mn),r=e.dirs||(e.dirs=[]),o=0;o<t.length;o++){var a=m(t[o],4),i=a[0],l=a[1],c=a[2],u=a[3],f=void 0===u?s:u;i&&(T(i)&&(i={mounted:i,updated:i}),i.deep&&Xt(l),r.push({dir:i,instance:n,value:l,oldValue:void 0,arg:c,modifiers:f}))}return e}function wn(e,t,n,r){for(var o=e.dirs,a=t&&t.dirs,i=0;i<o.length;i++){var l=o[i];a&&(l.oldValue=a[i].value);var c=l.dir[r];c&&(Pe(),Zt(c,n,8,[e.el,l,e,t]),Me())}}var _n=Symbol("_vte"),kn=function(e){return e.__isTeleport},Sn=Symbol("_leaveCb"),Cn=Symbol("_enterCb");var jn=[Function,Array],On={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:jn,onEnter:jn,onAfterEnter:jn,onEnterCancelled:jn,onBeforeLeave:jn,onLeave:jn,onAfterLeave:jn,onLeaveCancelled:jn,onBeforeAppear:jn,onAppear:jn,onAfterAppear:jn,onAppearCancelled:jn},zn=function(e){var t=e.subTree;return t.component?zn(t.component):t},An={name:"BaseTransition",props:On,setup:function(e,t){var n=t.slots,r=na(),o=function(){var e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return nr((function(){e.isMounted=!0})),ar((function(){e.isUnmounting=!0})),e}();return function(){var t=n.default&&Fn(n.default(),!0);if(t&&t.length){var a=En(t),i=It(e),l=i.mode;if(o.isLeaving)return Rn(a);var c=Pn(a);if(!c)return Rn(a);var u=Ln(c,i,o,r,(function(e){return u=e}));c.type!==Oo&&Mn(c,u);var s=r.subTree&&Pn(r.subTree);if(s&&s.type!==Oo&&!No(c,s)&&zn(r).type!==Oo){var f=Ln(s,i,o,r);if(Mn(s,f),"out-in"===l&&c.type!==Oo)return o.isLeaving=!0,f.afterLeave=function(){o.isLeaving=!1,8&r.job.flags||r.update(),delete f.afterLeave,s=void 0},Rn(a);"in-out"===l&&c.type!==Oo?f.delayLeave=function(e,t,n){In(o,s)[String(s.key)]=s,e[Sn]=function(){t(),e[Sn]=void 0,delete u.delayedLeave,s=void 0},u.delayedLeave=function(){n(),delete u.delayedLeave,s=void 0}}:s=void 0}else s&&(s=void 0);return a}}}};function En(e){var t=e[0];if(e.length>1){var n,r=y(e);try{for(r.s();!(n=r.n()).done;){var o=n.value;if(o.type!==Oo){t=o;break}}}catch(a){r.e(a)}finally{r.f()}}return t}var Tn=An;function In(e,t){var n=e.leavingVNodes,r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Ln(e,t,n,r,o){var a=t.appear,i=t.mode,l=t.persisted,c=void 0!==l&&l,u=t.onBeforeEnter,s=t.onEnter,f=t.onAfterEnter,d=t.onEnterCancelled,p=t.onBeforeLeave,v=t.onLeave,h=t.onAfterLeave,m=t.onLeaveCancelled,g=t.onBeforeAppear,b=t.onAppear,y=t.onAfterAppear,x=t.onAppearCancelled,w=String(e.key),_=In(n,e),k=function(e,t){e&&Zt(e,r,9,t)},S=function(e,t){var n=t[1];k(e,t),O(e)?e.every((function(e){return e.length<=1}))&&n():e.length<=1&&n()},C={mode:i,persisted:c,beforeEnter:function(t){var r=u;if(!n.isMounted){if(!a)return;r=g||u}t[Sn]&&t[Sn](!0);var o=_[w];o&&No(e,o)&&o.el[Sn]&&o.el[Sn](),k(r,[t])},enter:function(e){var t=s,r=f,o=d;if(!n.isMounted){if(!a)return;t=b||s,r=y||f,o=x||d}var i=!1,l=e[Cn]=function(t){i||(i=!0,k(t?o:r,[e]),C.delayedLeave&&C.delayedLeave(),e[Cn]=void 0)};t?S(t,[e,l]):l()},leave:function(t,r){var o=String(e.key);if(t[Cn]&&t[Cn](!0),n.isUnmounting)return r();k(p,[t]);var a=!1,i=t[Sn]=function(n){a||(a=!0,r(),k(n?m:h,[t]),t[Sn]=void 0,_[o]===e&&delete _[o])};_[o]=e,v?S(v,[t,i]):i()},clone:function(e){var a=Ln(e,t,n,r,o);return o&&o(a),a}};return C}function Rn(e){if($n(e))return(e=Do(e)).children=null,e}function Pn(e){if(!$n(e))return kn(e.type)&&e.children?En(e.children):e;if(e.component)return e.component.subTree;var t=e.shapeFlag,n=e.children;if(n){if(16&t)return n[0];if(32&t&&T(n.default))return n.default()}}function Mn(e,t){6&e.shapeFlag&&e.component?(e.transition=t,Mn(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Fn(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2?arguments[2]:void 0,r=[],o=0,a=0;a<e.length;a++){var i=e[a],l=null==n?i.key:String(n)+String(null!=i.key?i.key:a);i.type===Co?(128&i.patchFlag&&o++,r=r.concat(Fn(i.children,t,l))):(t||i.type!==Oo)&&r.push(null!=l?Do(i,{key:l}):i)}if(o>1)for(var c=0;c<r.length;c++)r[c].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Nn(e,t){return T(e)?function(){return k({name:e.name},t,{setup:e})}():e}function Bn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Vn(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(O(e))e.forEach((function(e,a){return Vn(e,t&&(O(t)?t[a]:t),n,r,o)}));else if(!qn(r)||o){var a=4&r.shapeFlag?pa(r.component):r.el,i=o?null:a,l=e.i,c=e.r,u=t&&t.r,f=l.refs===s?l.refs={}:l.refs,d=l.setupState,p=It(d),v=d===s?function(){return!1}:function(e){return j(p,e)};if(null!=u&&u!==c&&(I(u)?(f[u]=null,v(u)&&(d[u]=null)):Mt(u)&&(u.value=null)),T(c))Yt(c,l,12,[i,f]);else{var h=I(c),m=Mt(c);if(h||m){var g=function(){if(e.f){var t=h?v(c)?d[c]:f[c]:c.value;o?O(t)&&S(t,a):O(t)?t.includes(a)||t.push(a):h?(f[c]=[a],v(c)&&(d[c]=f[c])):(c.value=[a],e.k&&(f[e.k]=c.value))}else h?(f[c]=i,v(c)&&(d[c]=i)):m&&(c.value=i,e.k&&(f[e.k]=i))};i?(g.id=-1,no(g,n)):g()}}}else 512&r.shapeFlag&&r.type.__asyncResolved&&r.component.subTree.component&&Vn(e,t,n,r.component.subTree)}var Un=function(e){return 8===e.nodeType};Q().requestIdleCallback,Q().cancelIdleCallback;var qn=function(e){return!!e.type.__asyncLoader};function Dn(e,t){var n=t.vnode,r=n.ref,o=n.props,a=n.children,i=n.ce,l=qo(e,o,a);return l.ref=r,l.ce=i,delete t.vnode.ce,l}var $n=function(e){return e.type.__isKeepAlive},Hn={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup:function(e,t){var n=t.slots,r=na(),o=r.ctx;if(!o.renderer)return function(){var e=n.default&&n.default();return e&&1===e.length?e[0]:e};var a=new Map,i=new Set,l=null,c=r.suspense,u=o.renderer,s=u.p,f=u.m,d=u.um,p=(0,u.o.createElement)("div");function v(e){Yn(e),d(e,r,c,!0)}function h(e){a.forEach((function(t,n){var r=va(t.type);r&&!e(r)&&g(n)}))}function g(e){var t=a.get(e);!t||l&&No(t,l)?l&&Yn(l):v(t),a.delete(e),i.delete(e)}o.activate=function(e,t,n,r,o){var a=e.component;f(e,t,n,0,c),s(a.vnode,e,t,n,a,c,r,e.slotScopeIds,o),no((function(){a.isDeactivated=!1,a.a&&X(a.a);var t=e.props&&e.props.onVnodeMounted;t&&Xo(t,a.parent,e)}),c)},o.deactivate=function(e){var t=e.component;co(t.m),co(t.a),f(e,p,null,1,c),no((function(){t.da&&X(t.da);var n=e.props&&e.props.onVnodeUnmounted;n&&Xo(n,t.parent,e),t.isDeactivated=!0}),c)},fo((function(){return[e.include,e.exclude]}),(function(e){var t=m(e,2),n=t[0],r=t[1];n&&h((function(e){return Wn(n,e)})),r&&h((function(e){return!Wn(r,e)}))}),{flush:"post",deep:!0});var b=null,y=function(){null!=b&&(So(r.subTree.type)?no((function(){a.set(b,Zn(r.subTree))}),r.subTree.suspense):a.set(b,Zn(r.subTree)))};return nr(y),or(y),ar((function(){a.forEach((function(e){var t=r.subTree,n=r.suspense,o=Zn(t);if(e.type!==o.type||e.key!==o.key)v(e);else{Yn(o);var a=o.component.da;a&&no(a,n)}}))})),function(){if(b=null,!n.default)return l=null;var t=n.default(),r=t[0];if(t.length>1)return l=null,t;if(!(Fo(r)&&(4&r.shapeFlag||128&r.shapeFlag)))return l=null,r;var o=Zn(r);if(o.type===Oo)return l=null,o;var c=o.type,u=va(qn(o)?o.type.__asyncResolved||{}:c),s=e.include,f=e.exclude,d=e.max;if(s&&(!u||!Wn(s,u))||f&&u&&Wn(f,u))return o.shapeFlag&=-257,l=o,r;var p=null==o.key?c:o.key,v=a.get(p);return o.el&&(o=Do(o),128&r.shapeFlag&&(r.ssContent=o)),b=p,v?(o.el=v.el,o.component=v.component,o.transition&&Mn(o,o.transition),o.shapeFlag|=512,i.delete(p),i.add(p)):(i.add(p),d&&i.size>parseInt(d,10)&&g(i.values().next().value)),o.shapeFlag|=256,l=o,So(r.type)?r:o}}};t("W",Hn);function Wn(e,t){return O(e)?e.some((function(e){return Wn(e,t)})):I(e)?e.split(",").includes(t):"[object RegExp]"===F(e)&&(e.lastIndex=0,e.test(t))}function Gn(e,t){Jn(e,"a",t)}function Kn(e,t){Jn(e,"da",t)}function Jn(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ta,r=e.__wdc||(e.__wdc=function(){for(var t=n;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(Qn(t,r,n),n)for(var o=n.parent;o&&o.parent;)$n(o.parent.vnode)&&Xn(r,t,n,o),o=o.parent}function Xn(e,t,n,r){var o=Qn(t,e,r,!0);ir((function(){S(r[t],o)}),n)}function Yn(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Zn(e){return 128&e.shapeFlag?e.ssContent:e}function Qn(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ta,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(n){var o=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=function(){Pe();for(var r=aa(n),o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];var l=Zt(t,n,e,a);return r(),Me(),l});return r?o.unshift(a):o.push(a),a}}var er=function(e){return function(t){ua&&"sp"!==e||Qn(e,(function(){return t.apply(void 0,arguments)}),arguments.length>1&&void 0!==arguments[1]?arguments[1]:ta)}},tr=er("bm"),nr=t("G",er("m")),rr=er("bu"),or=er("u"),ar=er("bum"),ir=t("E",er("um")),lr=er("sp"),cr=er("rtg"),ur=er("rtc");function sr(e){Qn("ec",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:ta)}var fr="components",dr="directives";function pr(e,t){return hr(fr,e,!0,t)||e}var vr=Symbol.for("v-ndc");function hr(e,t){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=mn||ta;if(r){var o=r.type;if(e===fr){var a=va(o,!1);if(a&&(a===t||a===$(t)||a===G($(t))))return o}var i=mr(r[e]||o[e],t)||mr(r.appContext[e],t);return!i&&n?o:i}}function mr(e,t){return e&&(e[t]||e[$(t)]||e[G($(t))])}function gr(e,t,n,r){var o,a=n&&n[r],i=O(e);if(i||I(e)){var l=!1,c=!1;i&&zt(e)&&(l=!Et(e),c=At(e),e=Je(e)),o=new Array(e.length);for(var u=0,s=e.length;u<s;u++)o[u]=t(l?c?Pt(Rt(e[u])):Rt(e[u]):e[u],u,void 0,a&&a[u])}else if("number"==typeof e){o=new Array(e);for(var f=0;f<e;f++)o[f]=t(f+1,f,void 0,a&&a[f])}else if(R(e))if(e[Symbol.iterator])o=Array.from(e,(function(e,n){return t(e,n,void 0,a&&a[n])}));else{var d=Object.keys(e);o=new Array(d.length);for(var p=0,v=d.length;p<v;p++){var h=d[p];o[p]=t(e[h],h,p,a&&a[p])}}else o=[];return n&&(n[r]=o),o}function br(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(mn.ce||mn.parent&&qn(mn.parent)&&mn.parent.ce)return"default"!==t&&(n.name=t),To(),Mo(Co,null,[qo("slot",n,r&&r())],64);var a=e[t];a&&a._c&&(a._d=!1),To();var i=a&&yr(a(n)),l=n.key||i&&i.key,c=Mo(Co,{key:(l&&!L(l)?l:"_".concat(t))+(!i&&r?"_fb":"")},i||(r?r():[]),i&&1===e._?64:-2);return!o&&c.scopeId&&(c.slotScopeIds=[c.scopeId+"-s"]),a&&a._c&&(a._d=!0),c}function yr(e){return e.some((function(e){return!Fo(e)||e.type!==Oo&&!(e.type===Co&&!yr(e.children))}))?e:null}var xr=function(e){return e?la(e)?pa(e):xr(e.parent):null},wr=k(Object.create(null),{$:function(e){return e},$el:function(e){return e.vnode.el},$data:function(e){return e.data},$props:function(e){return e.props},$attrs:function(e){return e.attrs},$slots:function(e){return e.slots},$refs:function(e){return e.refs},$parent:function(e){return xr(e.parent)},$root:function(e){return xr(e.root)},$host:function(e){return e.ce},$emit:function(e){return e.emit},$options:function(e){return Ar(e)},$forceUpdate:function(e){return e.f||(e.f=function(){un(e.update)})},$nextTick:function(e){return e.n||(e.n=cn.bind(e.proxy))},$watch:function(e){return vo.bind(e)}}),_r=function(e,t){return e!==s&&!e.__isScriptSetup&&j(e,t)},kr={get:function(e,t){var n=e._;if("__v_skip"===t)return!0;var r,o=n.ctx,a=n.setupState,i=n.data,l=n.props,c=n.accessCache,u=n.type,f=n.appContext;if("$"!==t[0]){var d=c[t];if(void 0!==d)switch(d){case 1:return a[t];case 2:return i[t];case 4:return o[t];case 3:return l[t]}else{if(_r(a,t))return c[t]=1,a[t];if(i!==s&&j(i,t))return c[t]=2,i[t];if((r=n.propsOptions[0])&&j(r,t))return c[t]=3,l[t];if(o!==s&&j(o,t))return c[t]=4,o[t];Cr&&(c[t]=0)}}var p,v,h=wr[t];return h?("$attrs"===t&&We(n.attrs,0,""),h(n)):(p=u.__cssModules)&&(p=p[t])?p:o!==s&&j(o,t)?(c[t]=4,o[t]):(v=f.config.globalProperties,j(v,t)?v[t]:void 0)},set:function(e,t,n){var r=e._,o=r.data,a=r.setupState,i=r.ctx;return _r(a,t)?(a[t]=n,!0):o!==s&&j(o,t)?(o[t]=n,!0):!j(r.props,t)&&(("$"!==t[0]||!(t.slice(1)in r))&&(i[t]=n,!0))},has:function(e,t){var n,r=e._,o=r.data,a=r.setupState,i=r.accessCache,l=r.ctx,c=r.appContext,u=r.propsOptions;return!!i[t]||o!==s&&j(o,t)||_r(a,t)||(n=u[0])&&j(n,t)||j(l,t)||j(wr,t)||j(c.config.globalProperties,t)},defineProperty:function(e,t,n){return null!=n.get?e._.accessCache[t]=0:j(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Sr(e){return O(e)?e.reduce((function(e,t){return e[t]=null,e}),{}):e}var Cr=!0;function jr(e){var t=Ar(e),n=e.proxy,r=e.ctx;Cr=!1,t.beforeCreate&&Or(t.beforeCreate,e,"bc");var o=t.data,a=t.computed,i=t.methods,l=t.watch,c=t.provide,u=t.inject,s=t.created,f=t.beforeMount,d=t.mounted,p=t.beforeUpdate,v=t.updated,m=t.activated,g=t.deactivated,b=(t.beforeDestroy,t.beforeUnmount),y=(t.destroyed,t.unmounted),x=t.render,w=t.renderTracked,_=t.renderTriggered,k=t.errorCaptured,S=t.serverPrefetch,C=t.expose,j=t.inheritAttrs,z=t.components,A=t.directives;t.filters;if(u&&function(e,t){O(e)&&(e=Lr(e));var n=function(){var n,o=e[r];Mt(n=R(o)?"default"in o?qr(o.from||r,o.default,!0):qr(o.from||r):qr(o))?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:function(){return n.value},set:function(e){return n.value=e}}):t[r]=n};for(var r in e)n()}(u,r,null),i)for(var E in i){var I=i[E];T(I)&&(r[E]=I.bind(n))}if(o){var L=o.call(n,n);R(L)&&(e.data=St(L))}if(Cr=!0,a){var P=function(){var e=a[M],t=T(e)?e.bind(n,n):T(e.get)?e.get.bind(n,n):h,o=!T(e)&&T(e.set)?e.set.bind(n):h,i=ha({get:t,set:o});Object.defineProperty(r,M,{enumerable:!0,configurable:!0,get:function(){return i.value},set:function(e){return i.value=e}})};for(var M in a)P()}if(l)for(var F in l)zr(l[F],r,n,F);if(c){var N=T(c)?c.call(n):c;Reflect.ownKeys(N).forEach((function(e){Ur(e,N[e])}))}function B(e,t){O(t)?t.forEach((function(t){return e(t.bind(n))})):t&&e(t.bind(n))}if(s&&Or(s,e,"c"),B(tr,f),B(nr,d),B(rr,p),B(or,v),B(Gn,m),B(Kn,g),B(sr,k),B(ur,w),B(cr,_),B(ar,b),B(ir,y),B(lr,S),O(C))if(C.length){var V=e.exposed||(e.exposed={});C.forEach((function(e){Object.defineProperty(V,e,{get:function(){return n[e]},set:function(t){return n[e]=t}})}))}else e.exposed||(e.exposed={});x&&e.render===h&&(e.render=x),null!=j&&(e.inheritAttrs=j),z&&(e.components=z),A&&(e.directives=A),S&&Bn(e)}function Or(e,t,n){Zt(O(e)?e.map((function(e){return e.bind(t.proxy)})):e.bind(t.proxy),t,n)}function zr(e,t,n,r){var o=r.includes(".")?ho(n,r):function(){return n[r]};if(I(e)){var a=t[e];T(a)&&fo(o,a)}else if(T(e))fo(o,e.bind(n));else if(R(e))if(O(e))e.forEach((function(e){return zr(e,t,n,r)}));else{var i=T(e.handler)?e.handler.bind(n):t[e.handler];T(i)&&fo(o,i,e)}}function Ar(e){var t,n=e.type,r=n.mixins,o=n.extends,a=e.appContext,i=a.mixins,l=a.optionsCache,c=a.config.optionMergeStrategies,u=l.get(n);return u?t=u:i.length||r||o?(t={},i.length&&i.forEach((function(e){return Er(t,e,c,!0)})),Er(t,n,c)):t=n,R(n)&&l.set(n,t),t}function Er(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=t.mixins,a=t.extends;for(var i in a&&Er(e,a,n,!0),o&&o.forEach((function(t){return Er(e,t,n,!0)})),t)if(r&&"expose"===i);else{var l=Tr[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}var Tr={data:Ir,props:Mr,emits:Mr,methods:Pr,computed:Pr,beforeCreate:Rr,created:Rr,beforeMount:Rr,mounted:Rr,beforeUpdate:Rr,updated:Rr,beforeDestroy:Rr,beforeUnmount:Rr,destroyed:Rr,unmounted:Rr,activated:Rr,deactivated:Rr,errorCaptured:Rr,serverPrefetch:Rr,components:Pr,directives:Pr,watch:function(e,t){if(!e)return t;if(!t)return e;var n=k(Object.create(null),e);for(var r in t)n[r]=Rr(e[r],t[r]);return n},provide:Ir,inject:function(e,t){return Pr(Lr(e),Lr(t))}};function Ir(e,t){return t?e?function(){return k(T(e)?e.call(this,this):e,T(t)?t.call(this,this):t)}:t:e}function Lr(e){if(O(e)){for(var t={},n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Rr(e,t){return e?g(new Set([].concat(e,t))):t}function Pr(e,t){return e?k(Object.create(null),e,t):t}function Mr(e,t){return e?O(e)&&O(t)?g(new Set([].concat(g(e),g(t)))):k(Object.create(null),Sr(e),Sr(null!=t?t:{})):t}function Fr(){return{app:null,config:{isNativeTag:x,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}var Nr=0;function Br(e,t){return function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;T(n)||(n=k({},n)),null==r||R(r)||(r=null);var o=Fr(),a=new WeakSet,i=[],l=!1,c=o.app={_uid:Nr++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:ga,get config(){return o.config},set config(e){},use:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return a.has(e)||(e&&T(e.install)?(a.add(e),e.install.apply(e,[c].concat(n))):T(e)&&(a.add(e),e.apply(void 0,[c].concat(n)))),c},mixin:function(e){return o.mixins.includes(e)||o.mixins.push(e),c},component:function(e,t){return t?(o.components[e]=t,c):o.components[e]},directive:function(e,t){return t?(o.directives[e]=t,c):o.directives[e]},mount:function(a,i,u){if(!l){var s=c._ceVNode||qo(n,r);return s.appContext=o,!0===u?u="svg":!1===u&&(u=void 0),i&&t?t(s,a):e(s,a,u),l=!0,c._container=a,a.__vue_app__=c,pa(s.component)}},onUnmount:function(e){i.push(e)},unmount:function(){l&&(Zt(i,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide:function(e,t){return o.provides[e]=t,c},runWithContext:function(e){var t=Vr;Vr=c;try{return e()}finally{Vr=t}}};return c}}var Vr=null;function Ur(e,t){if(ta){var n=ta.provides,r=ta.parent&&ta.parent.provides;r===n&&(n=ta.provides=Object.create(r)),n[e]=t}else;}function qr(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=ta||mn;if(r||Vr){var o=Vr?Vr._context.provides:r?null==r.parent||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&T(t)?t.call(r&&r.proxy):t}}var Dr={},$r=function(){return Object.create(Dr)},Hr=function(e){return Object.getPrototypeOf(e)===Dr};function Wr(e,t,n,r){var o,a=m(e.propsOptions,2),i=a[0],l=a[1],c=!1;if(t)for(var u in t)if(!U(u)){var f=t[u],d=void 0;i&&j(i,d=$(u))?l&&l.includes(d)?(o||(o={}))[d]=f:n[d]=f:yo(e.emitsOptions,u)||u in r&&f===r[u]||(r[u]=f,c=!0)}if(l)for(var p=It(n),v=o||s,h=0;h<l.length;h++){var g=l[h];n[g]=Gr(i,p,g,v[g],e,!j(v,g))}return c}function Gr(e,t,n,r,o,a){var i=e[n];if(null!=i){var l=j(i,"default");if(l&&void 0===r){var c=i.default;if(i.type!==Function&&!i.skipFactory&&T(c)){var u=o.propsDefaults;if(n in u)r=u[n];else{var s=aa(o);r=u[n]=c.call(null,t),s()}}else r=c;o.ce&&o.ce._setProp(n,r)}i[0]&&(a&&!l?r=!1:!i[1]||""!==r&&r!==W(n)||(r=!0))}return r}var Kr=new WeakMap;function Jr(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=n?Kr:t.propsCache,o=r.get(e);if(o)return o;var a=e.props,i={},l=[],c=!1;if(!T(e)){var u=function(e){c=!0;var n=m(Jr(e,t,!0),2),r=n[0],o=n[1];k(i,r),o&&l.push.apply(l,g(o))};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!a&&!c)return R(e)&&r.set(e,d),d;if(O(a))for(var f=0;f<a.length;f++){var p=$(a[f]);Xr(p)&&(i[p]=s)}else if(a)for(var v in a){var h=$(v);if(Xr(h)){var b=a[v],y=i[h]=O(b)||T(b)?{type:b}:k({},b),x=y.type,w=!1,_=!0;if(O(x))for(var S=0;S<x.length;++S){var C=x[S],z=T(C)&&C.name;if("Boolean"===z){w=!0;break}"String"===z&&(_=!1)}else w=T(x)&&"Boolean"===x.name;y[0]=w,y[1]=_,(w||j(y,"default"))&&l.push(h)}}var A=[i,l];return R(e)&&r.set(e,A),A}function Xr(e){return"$"!==e[0]&&!U(e)}var Yr=function(e){return"_"===e[0]||"$stable"===e},Zr=function(e){return O(e)?e.map(Wo):[Wo(e)]},Qr=function(e,t,n){var r=e._ctx,o=function(){if(Yr(a))return 1;var n=e[a];if(T(n))t[a]=function(e,t,n){if(t._n)return t;var r=yn((function(){return Zr(t.apply(void 0,arguments))}),n);return r._c=!1,r}(0,n,r);else if(null!=n){var o=Zr(n);t[a]=function(){return o}}};for(var a in e)o()},eo=function(e,t){var n=Zr(t);e.slots.default=function(){return n}},to=function(e,t,n){for(var r in t)!n&&Yr(r)||(e[r]=t[r])};var no=function(e,t){if(t&&t.pendingBranch){var n;if(O(e))(n=t.effects).push.apply(n,g(e));else t.effects.push(e)}else fn(e)};function ro(e){return function(e,t){"boolean"!=typeof __VUE_PROD_HYDRATION_MISMATCH_DETAILS__&&(Q().__VUE_PROD_HYDRATION_MISMATCH_DETAILS__=!1),Q().__VUE__=!0;var n,r,o=e.insert,a=e.remove,i=e.patchProp,l=e.createElement,c=e.createText,u=e.createComment,f=e.setText,p=e.setElementText,v=e.parentNode,g=e.nextSibling,b=e.setScopeId,y=void 0===b?h:b,x=e.insertStaticContent,w=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:void 0,l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:!!t.dynamicChildren;if(e!==t){e&&!No(e,t)&&(r=oe(e),Z(e,o,a,!0),e=null),-2===t.patchFlag&&(c=!1,t.dynamicChildren=null);var u=t.type,s=t.ref,f=t.shapeFlag;switch(u){case jo:_(e,t,n,r);break;case Oo:k(e,t,n,r);break;case zo:null==e&&S(t,n,r,i);break;case Co:F(e,t,n,r,o,a,i,l,c);break;default:1&f?A(e,t,n,r,o,a,i,l,c):6&f?N(e,t,n,r,o,a,i,l,c):(64&f||128&f)&&u.process(e,t,n,r,o,a,i,l,c,le)}null!=s&&o&&Vn(s,e&&e.ref,a,t||e,!t)}},_=function(e,t,n,r){if(null==e)o(t.el=c(t.children),n,r);else{var a=t.el=e.el;t.children!==e.children&&f(a,t.children)}},k=function(e,t,n,r){null==e?o(t.el=u(t.children||""),n,r):t.el=e.el},S=function(e,t,n,r){var o=m(x(e.children,t,n,r,e.el,e.anchor),2);e.el=o[0],e.anchor=o[1]},C=function(e,t,n){for(var r,a=e.el,i=e.anchor;a&&a!==i;)r=g(a),o(a,t,n),a=r;o(i,t,n)},z=function(e){for(var t,n=e.el,r=e.anchor;n&&n!==r;)t=g(n),a(n),n=t;a(r)},A=function(e,t,n,r,o,a,i,l,c){"svg"===t.type?i="svg":"math"===t.type&&(i="mathml"),null==e?E(t,n,r,o,a,i,l,c):L(e,t,o,a,i,l,c)},E=function(e,t,n,r,a,c,u,s){var f,d,v=e.props,h=e.shapeFlag,m=e.transition,g=e.dirs;if(f=e.el=l(e.type,c,v&&v.is,v),8&h?p(f,e.children):16&h&&I(e.children,f,null,r,a,oo(e,c),u,s),g&&wn(e,null,r,"created"),T(f,e,e.scopeId,u,r),v){for(var b in v)"value"===b||U(b)||i(f,b,null,v[b],c,r);"value"in v&&i(f,"value",null,v.value,c),(d=v.onVnodeBeforeMount)&&Xo(d,r,e)}g&&wn(e,null,r,"beforeMount");var y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(a,m);y&&m.beforeEnter(f),o(f,t,n),((d=v&&v.onVnodeMounted)||y||g)&&no((function(){d&&Xo(d,r,e),y&&m.enter(f),g&&wn(e,null,r,"mounted")}),a)},T=function(e,t,n,r,o){if(n&&y(e,n),r)for(var a=0;a<r.length;a++)y(e,r[a]);if(o){var i=o.subTree;if(t===i||So(i.type)&&(i.ssContent===t||i.ssFallback===t)){var l=o.vnode;T(e,l,l.scopeId,l.slotScopeIds,o.parent)}}},I=function(e,t,n,r,o,a,i,l){for(var c=arguments.length>8&&void 0!==arguments[8]?arguments[8]:0;c<e.length;c++){var u=e[c]=l?Go(e[c]):Wo(e[c]);w(null,u,t,n,r,o,a,i,l)}},L=function(e,t,n,r,o,a,l){var c=t.el=e.el,u=t.patchFlag,f=t.dynamicChildren,d=t.dirs;u|=16&e.patchFlag;var v,h=e.props||s,m=t.props||s;if(n&&ao(n,!1),(v=m.onVnodeBeforeUpdate)&&Xo(v,n,t,e),d&&wn(t,e,n,"beforeUpdate"),n&&ao(n,!0),(h.innerHTML&&null==m.innerHTML||h.textContent&&null==m.textContent)&&p(c,""),f?R(e.dynamicChildren,f,c,n,r,oo(t,o),a):l||H(e,t,c,null,n,r,oo(t,o),a,!1),u>0){if(16&u)M(c,h,m,n,o);else if(2&u&&h.class!==m.class&&i(c,"class",null,m.class,o),4&u&&i(c,"style",h.style,m.style,o),8&u)for(var g=t.dynamicProps,b=0;b<g.length;b++){var y=g[b],x=h[y],w=m[y];w===x&&"value"!==y||i(c,y,x,w,o,n)}1&u&&e.children!==t.children&&p(c,t.children)}else l||null!=f||M(c,h,m,n,o);((v=m.onVnodeUpdated)||d)&&no((function(){v&&Xo(v,n,t,e),d&&wn(t,e,n,"updated")}),r)},R=function(e,t,n,r,o,a,i){for(var l=0;l<t.length;l++){var c=e[l],u=t[l],s=c.el&&(c.type===Co||!No(c,u)||198&c.shapeFlag)?v(c.el):n;w(c,u,s,null,r,o,a,i,!0)}},M=function(e,t,n,r,o){if(t!==n){if(t!==s)for(var a in t)U(a)||a in n||i(e,a,t[a],null,o,r);for(var l in n)if(!U(l)){var c=n[l],u=t[l];c!==u&&"value"!==l&&i(e,l,u,c,o,r)}"value"in n&&i(e,"value",t.value,n.value,o)}},F=function(e,t,n,r,a,i,l,u,s){var f=t.el=e?e.el:c(""),d=t.anchor=e?e.anchor:c(""),p=t.patchFlag,v=t.dynamicChildren,h=t.slotScopeIds;h&&(u=u?u.concat(h):h),null==e?(o(f,n,r),o(d,n,r),I(t.children||[],n,d,a,i,l,u,s)):p>0&&64&p&&v&&e.dynamicChildren?(R(e.dynamicChildren,v,n,a,i,l,u),(null!=t.key||a&&t===a.subTree)&&io(e,t,!0)):H(e,t,n,d,a,i,l,u,s)},N=function(e,t,n,r,o,a,i,l,c){t.slotScopeIds=l,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,i,c):B(t,n,r,o,a,i,c):V(e,t,c)},B=function(e,t,n,r,o,a,i){var l=e.component=function(e,t,n){var r=e.type,o=(t?t.appContext:e.appContext)||Yo,a={uid:Zo++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new me(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Jr(r,o),emitsOptions:bo(r,o),emit:null,emitted:null,propsDefaults:s,inheritAttrs:r.inheritAttrs,ctx:s,data:s,props:s,attrs:s,slots:s,refs:s,setupState:s,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=go.bind(null,a),e.ce&&e.ce(a);return a}(e,r,o);if($n(e)&&(l.ctx.renderer=le),function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];t&&ea(t);var r=e.vnode,o=r.props,a=r.children,i=la(e);(function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o={},a=$r();for(var i in e.propsDefaults=Object.create(null),Wr(e,t,o,a),e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=r?o:Ct(o):e.type.props?e.props=o:e.props=a,e.attrs=a})(e,o,i,t),function(e,t,n){var r=e.slots=$r();if(32&e.vnode.shapeFlag){var o=t._;o?(to(r,t,n),n&&Y(r,"_",o,!0)):Qr(t,r)}else t&&eo(e,t)}(e,a,n||t);var l=i?function(e,t){var n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,kr);var r=n.setup;if(r){Pe();var o=e.setupContext=r.length>1?function(e){var t=function(t){e.exposed=t||{}};return{attrs:new Proxy(e.attrs,da),slots:e.slots,emit:e.emit,expose:t}}(e):null,a=aa(e),i=Yt(r,e,0,[e.props,o]),l=P(i);if(Me(),a(),!l&&!e.sp||qn(e)||Bn(e),l){if(i.then(ia,ia),t)return i.then((function(n){sa(e,n,t)})).catch((function(t){Qt(t,e,0)}));e.asyncDep=i}else sa(e,i,t)}else fa(e,t)}(e,t):void 0;t&&ea(!1)}(l,!1,i),l.asyncDep){if(o&&o.registerDep(l,q,i),!e.el){var c=l.subTree=qo(Oo);k(null,c,t,n)}}else q(l,e,t,n,o,a,i)},V=function(e,t,n){var r=t.component=e.component;if(function(e,t,n){var r=e.props,o=e.children,a=e.component,i=t.props,l=t.children,c=t.patchFlag,u=a.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&c>=0))return!(!o&&!l||l&&l.$stable)||r!==i&&(r?!i||ko(r,i,u):!!i);if(1024&c)return!0;if(16&c)return r?ko(r,i,u):!!i;if(8&c)for(var s=t.dynamicProps,f=0;f<s.length;f++){var d=s[f];if(i[d]!==r[d]&&!yo(u,d))return!0}return!1}(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void D(r,t,n);r.next=t,r.update()}else t.el=e.el,r.vnode=t},q=function(e,t,n,o,a,i,l){var c=function(){if(e.isMounted){var u=e.next,s=e.bu,f=e.u,d=e.parent,p=e.vnode,h=lo(e);if(h)return u&&(u.el=p.el,D(e,u,l)),void h.asyncDep.then((function(){e.isUnmounted||c()}));var m,g=u;ao(e,!1),u?(u.el=p.el,D(e,u,l)):u=p,s&&X(s),(m=u.props&&u.props.onVnodeBeforeUpdate)&&Xo(m,d,u,p),ao(e,!0);var b=xo(e),y=e.subTree;e.subTree=b,w(y,b,v(y.el),oe(y),e,a,i),u.el=b.el,null===g&&function(e,t){var n=e.vnode,r=e.parent;for(;r;){var o=r.subTree;if(o.suspense&&o.suspense.activeBranch===n&&(o.el=n.el),o!==n)break;(n=r.vnode).el=t,r=r.parent}}(e,b.el),f&&no(f,a),(m=u.props&&u.props.onVnodeUpdated)&&no((function(){return Xo(m,d,u,p)}),a)}else{var x,_=t,k=_.el,S=_.props,C=e.bm,j=e.m,O=e.parent,z=e.root,A=e.type,E=qn(t);if(ao(e,!1),C&&X(C),!E&&(x=S&&S.onVnodeBeforeMount)&&Xo(x,O,t),ao(e,!0),k&&r){var T=function(){e.subTree=xo(e),r(k,e.subTree,e,a,null)};E&&A.__asyncHydrate?A.__asyncHydrate(k,e,T):T()}else{z.ce&&z.ce._injectChildStyle(A);var I=e.subTree=xo(e);w(null,I,n,o,e,a,i),t.el=I.el}if(j&&no(j,a),!E&&(x=S&&S.onVnodeMounted)){var L=t;no((function(){return Xo(x,O,L)}),a)}(256&t.shapeFlag||O&&qn(O.vnode)&&256&O.vnode.shapeFlag)&&e.a&&no(e.a,a),e.isMounted=!0,t=n=o=null}};e.scope.on();var u=e.effect=new _e(c);e.scope.off();var s=e.update=u.run.bind(u),f=e.job=u.runIfDirty.bind(u);f.i=e,f.id=e.uid,u.scheduler=function(){return un(f)},ao(e,!0),s()},D=function(e,t,n){t.component=e;var r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){var o=e.props,a=e.attrs,i=e.vnode.patchFlag,l=It(o),c=m(e.propsOptions,1)[0],u=!1;if(!(r||i>0)||16&i){var s;for(var f in Wr(e,t,o,a)&&(u=!0),l)t&&(j(t,f)||(s=W(f))!==f&&j(t,s))||(c?!n||void 0===n[f]&&void 0===n[s]||(o[f]=Gr(c,l,f,void 0,e,!0)):delete o[f]);if(a!==l)for(var d in a)t&&j(t,d)||(delete a[d],u=!0)}else if(8&i)for(var p=e.vnode.dynamicProps,v=0;v<p.length;v++){var h=p[v];if(!yo(e.emitsOptions,h)){var g=t[h];if(c)if(j(a,h))g!==a[h]&&(a[h]=g,u=!0);else{var b=$(h);o[b]=Gr(c,l,b,g,e,!1)}else g!==a[h]&&(a[h]=g,u=!0)}}u&&Ge(e.attrs,"set","")}(e,t.props,r,n),function(e,t,n){var r=e.vnode,o=e.slots,a=!0,i=s;if(32&r.shapeFlag){var l=t._;l?n&&1===l?a=!1:to(o,t,n):(a=!t.$stable,Qr(t,o)),i=t}else t&&(eo(e,t),i={default:1});if(a)for(var c in o)Yr(c)||null!=i[c]||delete o[c]}(e,t.children,n),Pe(),dn(e),Me()},H=function(e,t,n,r,o,a,i,l){var c=arguments.length>8&&void 0!==arguments[8]&&arguments[8],u=e&&e.children,s=e?e.shapeFlag:0,f=t.children,d=t.patchFlag,v=t.shapeFlag;if(d>0){if(128&d)return void K(u,f,n,r,o,a,i,l,c);if(256&d)return void G(u,f,n,r,o,a,i,l,c)}8&v?(16&s&&re(u,o,a),f!==u&&p(n,f)):16&s?16&v?K(u,f,n,r,o,a,i,l,c):re(u,o,a,!0):(8&s&&p(n,""),16&v&&I(f,n,r,o,a,i,l,c))},G=function(e,t,n,r,o,a,i,l,c){t=t||d;var u,s=(e=e||d).length,f=t.length,p=Math.min(s,f);for(u=0;u<p;u++){var v=t[u]=c?Go(t[u]):Wo(t[u]);w(e[u],v,n,null,o,a,i,l,c)}s>f?re(e,o,a,!0,!1,p):I(t,n,r,o,a,i,l,c,p)},K=function(e,t,n,r,o,a,i,l,c){for(var u=0,s=t.length,f=e.length-1,p=s-1;u<=f&&u<=p;){var v=e[u],h=t[u]=c?Go(t[u]):Wo(t[u]);if(!No(v,h))break;w(v,h,n,null,o,a,i,l,c),u++}for(;u<=f&&u<=p;){var m=e[f],g=t[p]=c?Go(t[p]):Wo(t[p]);if(!No(m,g))break;w(m,g,n,null,o,a,i,l,c),f--,p--}if(u>f){if(u<=p)for(var b=p+1,y=b<s?t[b].el:r;u<=p;)w(null,t[u]=c?Go(t[u]):Wo(t[u]),n,y,o,a,i,l,c),u++}else if(u>p)for(;u<=f;)Z(e[u],o,a,!0),u++;else{var x,_=u,k=u,S=new Map;for(u=k;u<=p;u++){var C=t[u]=c?Go(t[u]):Wo(t[u]);null!=C.key&&S.set(C.key,u)}var j=0,O=p-k+1,z=!1,A=0,E=new Array(O);for(u=0;u<O;u++)E[u]=0;for(u=_;u<=f;u++){var T=e[u];if(j>=O)Z(T,o,a,!0);else{var I=void 0;if(null!=T.key)I=S.get(T.key);else for(x=k;x<=p;x++)if(0===E[x-k]&&No(T,t[x])){I=x;break}void 0===I?Z(T,o,a,!0):(E[I-k]=u+1,I>=A?A=I:z=!0,w(T,t[I],n,null,o,a,i,l,c),j++)}}var L=z?function(e){var t,n,r,o,a,i=e.slice(),l=[0],c=e.length;for(t=0;t<c;t++){var u=e[t];if(0!==u){if(e[n=l[l.length-1]]<u){i[t]=n,l.push(t);continue}for(r=0,o=l.length-1;r<o;)e[l[a=r+o>>1]]<u?r=a+1:o=a;u<e[l[r]]&&(r>0&&(i[t]=l[r-1]),l[r]=t)}}r=l.length,o=l[r-1];for(;r-- >0;)l[r]=o,o=i[o];return l}(E):d;for(x=L.length-1,u=O-1;u>=0;u--){var R=k+u,P=t[R],M=R+1<s?t[R+1].el:r;0===E[u]?w(null,P,n,M,o,a,i,l,c):z&&(x<0||u!==L[x]?J(P,n,M,2):x--)}}},J=function(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,l=e.el,c=e.type,u=e.transition,s=e.children,f=e.shapeFlag;if(6&f)J(e.component.subTree,t,n,r);else if(128&f)e.suspense.move(t,n,r);else if(64&f)c.move(e,t,n,le);else if(c!==Co){if(c!==zo)if(2!==r&&1&f&&u)if(0===r)u.beforeEnter(l),o(l,t,n),no((function(){return u.enter(l)}),i);else{var d=u.leave,p=u.delayLeave,v=u.afterLeave,h=function(){e.ctx.isUnmounted?a(l):o(l,t,n)},m=function(){d(l,(function(){h(),v&&v()}))};p?p(l,h,m):m()}else o(l,t,n);else C(e,t,n)}else{o(l,t,n);for(var g=0;g<s.length;g++)J(s[g],t,n,r);o(e.anchor,t,n)}},Z=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=e.type,i=e.props,l=e.ref,c=e.children,u=e.dynamicChildren,s=e.shapeFlag,f=e.patchFlag,d=e.dirs,p=e.cacheIndex;if(-2===f&&(o=!1),null!=l&&(Pe(),Vn(l,null,n,e,!0),Me()),null!=p&&(t.renderCache[p]=void 0),256&s)t.ctx.deactivate(e);else{var v,h=1&s&&d,m=!qn(e);if(m&&(v=i&&i.onVnodeBeforeUnmount)&&Xo(v,t,e),6&s)ne(e.component,n,r);else{if(128&s)return void e.suspense.unmount(n,r);h&&wn(e,null,t,"beforeUnmount"),64&s?e.type.remove(e,t,n,le,r):u&&!u.hasOnce&&(a!==Co||f>0&&64&f)?re(u,t,n,!1,!0):(a===Co&&384&f||!o&&16&s)&&re(c,t,n),r&&ee(e)}(m&&(v=i&&i.onVnodeUnmounted)||h)&&no((function(){v&&Xo(v,t,e),h&&wn(e,null,t,"unmounted")}),n)}},ee=function(e){var t=e.type,n=e.el,r=e.anchor,o=e.transition;if(t!==Co)if(t!==zo){var i=function(){a(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){var l=o.leave,c=o.delayLeave,u=function(){return l(n,i)};c?c(e.el,i,u):u()}else i()}else z(e);else te(n,r)},te=function(e,t){for(var n;e!==t;)n=g(e),a(e),e=n;a(t)},ne=function(e,t,n){var r=e.bum,o=e.scope,a=e.job,i=e.subTree,l=e.um,c=e.m,u=e.a,s=e.parent,f=e.slots.__;co(c),co(u),r&&X(r),s&&O(f)&&f.forEach((function(e){s.renderCache[e]=void 0})),o.stop(),a&&(a.flags|=8,Z(i,e,t,n)),l&&no(l,t),no((function(){e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},re=function(e,t,n){for(var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;a<e.length;a++)Z(e[a],t,n,r,o)},oe=function(e){if(6&e.shapeFlag)return oe(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();var t=g(e.anchor||e.el),n=t&&t[_n];return n?g(n):t},ae=!1,ie=function(e,t,n){null==e?t._vnode&&Z(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),t._vnode=e,ae||(ae=!0,dn(),pn(),ae=!1)},le={p:w,um:Z,m:J,r:ee,mt:B,mc:I,pc:H,pbc:R,n:oe,o:e};if(t){var ce=m(t(le),2);n=ce[0],r=ce[1]}return{render:ie,hydrate:n,createApp:Br(ie,n)}}(e)}function oo(e,t){var n=e.type,r=e.props;return"svg"===t&&"foreignObject"===n||"mathml"===t&&"annotation-xml"===n&&r&&r.encoding&&r.encoding.includes("html")?void 0:t}function ao(e,t){var n=e.effect,r=e.job;t?(n.flags|=32,r.flags|=4):(n.flags&=-33,r.flags&=-5)}function io(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.children,o=t.children;if(O(r)&&O(o))for(var a=0;a<r.length;a++){var i=r[a],l=o[a];1&l.shapeFlag&&!l.dynamicChildren&&((l.patchFlag<=0||32===l.patchFlag)&&((l=o[a]=Go(o[a])).el=i.el),n||-2===l.patchFlag||io(i,l)),l.type===jo&&(l.el=i.el),l.type!==Oo||l.el||(l.el=i.el)}}function lo(e){var t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:lo(t)}function co(e){if(e)for(var t=0;t<e.length;t++)e[t].flags|=8}var uo=Symbol.for("v-scx"),so=function(){return qr(uo)};function fo(e,t,n){return po(e,t,n)}function po(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,o=r.immediate,a=(r.deep,r.flush),i=(r.once,k({},r)),l=t&&o||!t&&"post"!==a;if(ua)if("sync"===a){var c=so();n=c.__watcherHandles||(c.__watcherHandles=[])}else if(!l){var u=function(){};return u.stop=h,u.resume=h,u.pause=h,u}var f=ta;i.call=function(e,t,n){return Zt(e,f,t,n)};var d=!1;"post"===a?i.scheduler=function(e){no(e,f&&f.suspense)}:"sync"!==a&&(d=!0,i.scheduler=function(e,t){t?e():un(e)}),i.augmentJob=function(e){t&&(e.flags|=4),d&&(e.flags|=2,f&&(e.id=f.uid,e.i=f))};var p=Jt(e,t,i);return ua&&(n?n.push(p):l&&p()),p}function vo(e,t,n){var r,o=this.proxy,a=I(e)?e.includes(".")?ho(o,e):function(){return o[e]}:e.bind(o,o);T(t)?r=t:(r=t.handler,n=t);var i=aa(this),l=po(a,r.bind(o),n);return i(),l}function ho(e,t){var n=t.split(".");return function(){for(var t=e,r=0;r<n.length&&t;r++)t=t[n[r]];return t}}var mo=function(e,t){return"modelValue"===t||"model-value"===t?e.modelModifiers:e["".concat(t,"Modifiers")]||e["".concat($(t),"Modifiers")]||e["".concat(W(t),"Modifiers")]};function go(e,t){if(!e.isUnmounted){for(var n=e.vnode.props||s,r=arguments.length,o=new Array(r>2?r-2:0),a=2;a<r;a++)o[a-2]=arguments[a];var i,l=o,c=t.startsWith("update:"),u=c&&mo(n,t.slice(7));u&&(u.trim&&(l=o.map((function(e){return I(e)?e.trim():e}))),u.number&&(l=o.map(Z)));var f=n[i=K(t)]||n[i=K($(t))];!f&&c&&(f=n[i=K(W(t))]),f&&Zt(f,e,6,l);var d=n[i+"Once"];if(d){if(e.emitted){if(e.emitted[i])return}else e.emitted={};e.emitted[i]=!0,Zt(d,e,6,l)}}}function bo(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;var a=e.emits,i={},l=!1;if(!T(e)){var c=function(e){var n=bo(e,t,!0);n&&(l=!0,k(i,n))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return a||l?(O(a)?a.forEach((function(e){return i[e]=null})):k(i,a),R(e)&&r.set(e,i),i):(R(e)&&r.set(e,null),null)}function yo(e,t){return!(!e||!w(t))&&(t=t.slice(2).replace(/Once$/,""),j(e,t[0].toLowerCase()+t.slice(1))||j(e,W(t))||j(e,t))}function xo(e){var t,n,r=e.type,o=e.vnode,a=e.proxy,i=e.withProxy,l=m(e.propsOptions,1)[0],c=e.slots,u=e.attrs,s=e.emit,f=e.render,d=e.renderCache,p=e.props,v=e.data,h=e.setupState,g=e.ctx,b=e.inheritAttrs,y=bn(e);try{if(4&o.shapeFlag){var x=i||a,w=x;t=Wo(f.call(w,x,d,p,h,v,g)),n=u}else{var k=r;0,t=Wo(k.length>1?k(p,{attrs:u,slots:c,emit:s}):k(p,null)),n=r.props?u:wo(u)}}catch(O){Ao.length=0,Qt(O,e,1),t=qo(Oo)}var S=t;if(n&&!1!==b){var C=Object.keys(n),j=S.shapeFlag;C.length&&7&j&&(l&&C.some(_)&&(n=_o(n,l)),S=Do(S,n,!1,!0))}return o.dirs&&((S=Do(S,null,!1,!0)).dirs=S.dirs?S.dirs.concat(o.dirs):o.dirs),o.transition&&Mn(S,o.transition),t=S,bn(y),t}var wo=function(e){var t;for(var n in e)("class"===n||"style"===n||w(n))&&((t||(t={}))[n]=e[n]);return t},_o=function(e,t){var n={};for(var r in e)_(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function ko(e,t,n){var r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(var o=0;o<r.length;o++){var a=r[o];if(t[a]!==e[a]&&!yo(n,a))return!0}return!1}var So=function(e){return e.__isSuspense};var Co=t("F",Symbol.for("v-fgt")),jo=Symbol.for("v-txt"),Oo=Symbol.for("v-cmt"),zo=Symbol.for("v-stc"),Ao=[],Eo=null;function To(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];Ao.push(Eo=e?null:[])}var Io=1;function Lo(e){Io+=e,e<0&&Eo&&(arguments.length>1&&void 0!==arguments[1]&&arguments[1])&&(Eo.hasOnce=!0)}function Ro(e){return e.dynamicChildren=Io>0?Eo||d:null,Ao.pop(),Eo=Ao[Ao.length-1]||null,Io>0&&Eo&&Eo.push(e),e}function Po(e,t,n,r,o,a){return Ro(Uo(e,t,n,r,o,a,!0))}function Mo(e,t,n,r,o){return Ro(qo(e,t,n,r,o,!0))}function Fo(e){return!!e&&!0===e.__v_isVNode}function No(e,t){return e.type===t.type&&e.key===t.key}var Bo=function(e){var t=e.key;return null!=t?t:null},Vo=function(e){var t=e.ref,n=e.ref_key,r=e.ref_for;return"number"==typeof t&&(t=""+t),null!=t?I(t)||Mt(t)||T(t)?{i:mn,r:t,k:n,f:!!r}:t:null};function Uo(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e===Co?0:1,i=arguments.length>6&&void 0!==arguments[6]&&arguments[6],l=arguments.length>7&&void 0!==arguments[7]&&arguments[7],c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Bo(t),ref:t&&Vo(t),scopeId:gn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:mn};return l?(Ko(c,n),128&a&&e.normalize(c)):n&&(c.shapeFlag|=I(n)?8:16),Io>0&&!i&&Eo&&(c.patchFlag>0||6&a)&&32!==c.patchFlag&&Eo.push(c),c}var qo=t("j",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];e&&e!==vr||(e=Oo);if(Fo(e)){var i=Do(e,t,!0);return n&&Ko(i,n),Io>0&&!a&&Eo&&(6&i.shapeFlag?Eo[Eo.indexOf(e)]=i:Eo.push(i)),i.patchFlag=-2,i}l=e,T(l)&&"__vccOpts"in l&&(e=e.__vccOpts);var l;if(t){var c=t=function(e){return e?Tt(e)||Hr(e)?k({},e):e:null}(t),u=c.class,s=c.style;u&&!I(u)&&(t.class=ae(u)),R(s)&&(Tt(s)&&!O(s)&&(s=k({},s)),t.style=ee(s))}var f=I(e)?1:So(e)?128:kn(e)?64:R(e)?4:T(e)?2:0;return Uo(e,t,n,r,o,f,a,!0)}));function Do(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=e.props,a=e.ref,i=e.patchFlag,l=e.children,c=e.transition,u=t?Jo(o||{},t):o,s={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Bo(u),ref:t&&t.ref?n&&a?O(a)?a.concat(Vo(t)):[a,Vo(t)]:Vo(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Co?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Do(e.ssContent),ssFallback:e.ssFallback&&Do(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Mn(s,c.clone(s)),s}function $o(){return qo(jo,null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)}function Ho(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(To(),Mo(Oo,null,e)):qo(Oo,null,e)}function Wo(e){return null==e||"boolean"==typeof e?qo(Oo):O(e)?qo(Co,null,e.slice()):Fo(e)?Go(e):qo(jo,null,String(e))}function Go(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Do(e)}function Ko(e,t){var n=0,r=e.shapeFlag;if(null==t)t=null;else if(O(t))n=16;else if("object"===b(t)){if(65&r){var o=t.default;return void(o&&(o._c&&(o._d=!1),Ko(e,o()),o._c&&(o._d=!0)))}n=32;var a=t._;a||Hr(t)?3===a&&mn&&(1===mn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=mn}else T(t)?(t={default:t,_ctx:mn},n=32):(t=String(t),64&r?(n=16,t=[$o(t)]):n=8);e.children=t,e.shapeFlag|=n}function Jo(){for(var e={},t=0;t<arguments.length;t++){var n=t<0||arguments.length<=t?void 0:arguments[t];for(var r in n)if("class"===r)e.class!==n.class&&(e.class=ae([e.class,n.class]));else if("style"===r)e.style=ee([e.style,n.style]);else if(w(r)){var o=e[r],a=n[r];!a||o===a||O(o)&&o.includes(a)||(e[r]=o?[].concat(o,a):a)}else""!==r&&(e[r]=n[r])}return e}function Xo(e,t,n){Zt(e,t,7,[n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:null])}var Yo=Fr(),Zo=0;var Qo,ea,ta=null,na=function(){return ta||mn},ra=Q(),oa=function(e,t){var n;return(n=ra[e])||(n=ra[e]=[]),n.push(t),function(e){n.length>1?n.forEach((function(t){return t(e)})):n[0](e)}};Qo=oa("__VUE_INSTANCE_SETTERS__",(function(e){return ta=e})),ea=oa("__VUE_SSR_SETTERS__",(function(e){return ua=e}));var aa=function(e){var t=ta;return Qo(e),e.scope.on(),function(){e.scope.off(),Qo(t)}},ia=function(){ta&&ta.scope.off(),Qo(null)};function la(e){return 4&e.vnode.shapeFlag}var ca,ua=!1;function sa(e,t,n){T(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:R(t)&&(e.setupState=qt(t)),fa(e,n)}function fa(e,t,n){var r=e.type;if(!e.render){if(!t&&ca&&!r.render){var o=r.template||Ar(e).template;if(o){var a=e.appContext.config,i=a.isCustomElement,l=a.compilerOptions,c=r.delimiters,u=r.compilerOptions,s=k(k({isCustomElement:i,delimiters:c},l),u);r.render=ca(o,s)}}e.render=r.render||h}var f=aa(e);Pe();try{jr(e)}finally{Me(),f()}}var da={get:function(e,t){return We(e,0,""),e[t]}};function pa(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(qt(Lt(e.exposed)),{get:function(t,n){return n in t?t[n]:n in wr?wr[n](e):void 0},has:function(e,t){return t in e||t in wr}})):e.proxy}function va(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return T(e)?e.displayName||e.name:e.name||t&&e.__name}var ha=t("c",(function(e,t){var n=function(e,t){var n,r,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return T(e)?n=e:(n=e.get,r=e.set),new Ht(n,r,o)}(e,t,ua);return n}));function ma(e,t,n){var r=arguments.length;return 2===r?R(t)&&!O(t)?Fo(t)?qo(e,null,[t]):qo(e,t):qo(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&Fo(n)&&(n=[n]),qo(e,t,n))}var ga="3.5.16",ba=void 0,ya="undefined"!=typeof window&&window.trustedTypes;
/**
            * @vue/runtime-dom v3.5.16
            * (c) 2018-present Yuxi (Evan) You and Vue contributors
            * @license MIT
            **/if(ya)try{ba=ya.createPolicy("vue",{createHTML:function(e){return e}})}catch(rp){}var xa=ba?function(e){return ba.createHTML(e)}:function(e){return e},wa="undefined"!=typeof document?document:null,_a=wa&&wa.createElement("template"),ka={insert:function(e,t,n){t.insertBefore(e,n||null)},remove:function(e){var t=e.parentNode;t&&t.removeChild(e)},createElement:function(e,t,n,r){var o="svg"===t?wa.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?wa.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?wa.createElement(e,{is:n}):wa.createElement(e);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:function(e){return wa.createTextNode(e)},createComment:function(e){return wa.createComment(e)},setText:function(e,t){e.nodeValue=t},setElementText:function(e,t){e.textContent=t},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},querySelector:function(e){return wa.querySelector(e)},setScopeId:function(e,t){e.setAttribute(t,"")},insertStaticContent:function(e,t,n,r,o,a){var i=n?n.previousSibling:t.lastChild;if(o&&(o===a||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==a&&(o=o.nextSibling););else{_a.innerHTML=xa("svg"===r?"<svg>".concat(e,"</svg>"):"mathml"===r?"<math>".concat(e,"</math>"):e);var l=_a.content;if("svg"===r||"mathml"===r){for(var c=l.firstChild;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Sa="transition",Ca="animation",ja=Symbol("_vtc"),Oa={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},za=k({},On,Oa),Aa=(t("T",function(e){return e.displayName="Transition",e.props=za,e}((function(e,t){var n=t.slots;return ma(Tn,function(e){var t={};for(var n in e)n in Oa||(t[n]=e[n]);if(!1===e.css)return t;var r=e.name,o=void 0===r?"v":r,a=e.type,i=e.duration,l=e.enterFromClass,c=void 0===l?"".concat(o,"-enter-from"):l,u=e.enterActiveClass,s=void 0===u?"".concat(o,"-enter-active"):u,f=e.enterToClass,d=void 0===f?"".concat(o,"-enter-to"):f,p=e.appearFromClass,v=void 0===p?c:p,h=e.appearActiveClass,m=void 0===h?s:h,g=e.appearToClass,b=void 0===g?d:g,y=e.leaveFromClass,x=void 0===y?"".concat(o,"-leave-from"):y,w=e.leaveActiveClass,_=void 0===w?"".concat(o,"-leave-active"):w,S=e.leaveToClass,C=void 0===S?"".concat(o,"-leave-to"):S,j=function(e){if(null==e)return null;if(R(e))return[Ta(e.enter),Ta(e.leave)];var t=Ta(e);return[t,t]}(i),O=j&&j[0],z=j&&j[1],A=t.onBeforeEnter,E=t.onEnter,T=t.onEnterCancelled,I=t.onLeave,L=t.onLeaveCancelled,P=t.onBeforeAppear,M=void 0===P?A:P,F=t.onAppear,N=void 0===F?E:F,B=t.onAppearCancelled,V=void 0===B?T:B,U=function(e,t,n,r){e._enterCancelled=r,La(e,t?b:d),La(e,t?m:s),n&&n()},q=function(e,t){e._isLeaving=!1,La(e,x),La(e,C),La(e,_),t&&t()},D=function(e){return function(t,n){var r=e?N:E,o=function(){return U(t,e,n)};Aa(r,[t,o]),Ra((function(){La(t,e?v:c),Ia(t,e?b:d),Ea(r)||Ma(t,a,O,o)}))}};return k(t,{onBeforeEnter:function(e){Aa(A,[e]),Ia(e,c),Ia(e,s)},onBeforeAppear:function(e){Aa(M,[e]),Ia(e,v),Ia(e,m)},onEnter:D(!1),onAppear:D(!0),onLeave:function(e,t){e._isLeaving=!0;var n=function(){return q(e,t)};Ia(e,x),e._enterCancelled?(Ia(e,_),Ba()):(Ba(),Ia(e,_)),Ra((function(){e._isLeaving&&(La(e,x),Ia(e,C),Ea(I)||Ma(e,a,z,n))})),Aa(I,[e,n])},onEnterCancelled:function(e){U(e,!1,void 0,!0),Aa(T,[e])},onAppearCancelled:function(e){U(e,!0,void 0,!0),Aa(V,[e])},onLeaveCancelled:function(e){q(e),Aa(L,[e])}})}(e),n)}))),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];O(e)?e.forEach((function(e){return e.apply(void 0,g(t))})):e&&e.apply(void 0,g(t))}),Ea=function(e){return!!e&&(O(e)?e.some((function(e){return e.length>1})):e.length>1)};function Ta(e){var t=function(e){var t=I(e)?Number(e):NaN;return isNaN(t)?e:t}(e);return t}function Ia(e,t){t.split(/\s+/).forEach((function(t){return t&&e.classList.add(t)})),(e[ja]||(e[ja]=new Set)).add(t)}function La(e,t){t.split(/\s+/).forEach((function(t){return t&&e.classList.remove(t)}));var n=e[ja];n&&(n.delete(t),n.size||(e[ja]=void 0))}function Ra(e){requestAnimationFrame((function(){requestAnimationFrame(e)}))}var Pa=0;function Ma(e,t,n,r){var o=e._endId=++Pa,a=function(){o===e._endId&&r()};if(null!=n)return setTimeout(a,n);var i=function(e,t){var n=window.getComputedStyle(e),r=function(e){return(n[e]||"").split(", ")},o=r("".concat(Sa,"Delay")),a=r("".concat(Sa,"Duration")),i=Fa(o,a),l=r("".concat(Ca,"Delay")),c=r("".concat(Ca,"Duration")),u=Fa(l,c),s=null,f=0,d=0;t===Sa?i>0&&(s=Sa,f=i,d=a.length):t===Ca?u>0&&(s=Ca,f=u,d=c.length):d=(s=(f=Math.max(i,u))>0?i>u?Sa:Ca:null)?s===Sa?a.length:c.length:0;var p=s===Sa&&/\b(transform|all)(,|$)/.test(r("".concat(Sa,"Property")).toString());return{type:s,timeout:f,propCount:d,hasTransform:p}}(e,t),l=i.type,c=i.timeout,u=i.propCount;if(!l)return r();var s=l+"end",f=0,d=function(){e.removeEventListener(s,p),a()},p=function(t){t.target===e&&++f>=u&&d()};setTimeout((function(){f<u&&d()}),c+1),e.addEventListener(s,p)}function Fa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max.apply(Math,g(t.map((function(t,n){return Na(t)+Na(e[n])}))))}function Na(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function Ba(){return document.body.offsetHeight}var Va=Symbol("_vod"),Ua=Symbol("_vsh"),qa=t("R",{beforeMount:function(e,t,n){var r=t.value,o=n.transition;e[Va]="none"===e.style.display?"":e.style.display,o&&r?o.beforeEnter(e):Da(e,r)},mounted:function(e,t,n){var r=t.value,o=n.transition;o&&r&&o.enter(e)},updated:function(e,t,n){var r=t.value,o=t.oldValue,a=n.transition;!r!=!o&&(a?r?(a.beforeEnter(e),Da(e,!0),a.enter(e)):a.leave(e,(function(){Da(e,!1)})):Da(e,r))},beforeUnmount:function(e,t){Da(e,t.value)}});function Da(e,t){e.style.display=t?e[Va]:"none",e[Ua]=!t}var $a=Symbol("");function Ha(e,t){if(128&e.shapeFlag){var n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((function(){Ha(n.activeBranch,t)}))}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)Wa(e.el,t);else if(e.type===Co)e.children.forEach((function(e){return Ha(e,t)}));else if(e.type===zo)for(var r=e,o=r.el,a=r.anchor;o&&(Wa(o,t),o!==a);)o=o.nextSibling}function Wa(e,t){if(1===e.nodeType){var n=e.style,r="";for(var o in t)n.setProperty("--".concat(o),t[o]),r+="--".concat(o,": ").concat(t[o],";");n[$a]=r}}var Ga=/(^|;)\s*display\s*:/;var Ka=/\s*!important$/;function Ja(e,t,n){if(O(n))n.forEach((function(n){return Ja(e,t,n)}));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{var r=function(e,t){var n=Ya[t];if(n)return n;var r=$(t);if("filter"!==r&&r in e)return Ya[t]=r;r=G(r);for(var o=0;o<Xa.length;o++){var a=Xa[o]+r;if(a in e)return Ya[t]=a}return t}(e,t);Ka.test(n)?e.setProperty(W(r),n.replace(Ka,""),"important"):e[r]=n}}var Xa=["Webkit","Moz","ms"],Ya={};var Za="http://www.w3.org/1999/xlink";function Qa(e,t,n,r,o){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:ie(t);r&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(Za,t.slice(6,t.length)):e.setAttributeNS(Za,t,n):null==n||a&&!le(n)?e.removeAttribute(t):e.setAttribute(t,a?"":L(n)?String(n):n)}function ei(e,t,n,r,o){if("innerHTML"!==t&&"textContent"!==t){var a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){var i="OPTION"===a?e.getAttribute("value")||"":e.value,l=null==n?"checkbox"===e.type?"on":"":String(n);return i===l&&"_value"in e||(e.value=l),null==n&&e.removeAttribute(t),void(e._value=n)}var c=!1;if(""===n||null==n){var u=b(e[t]);"boolean"===u?n=le(n):null==n&&"string"===u?(n="",c=!0):"number"===u&&(n=0,c=!0)}try{e[t]=n}catch(rp){}c&&e.removeAttribute(o||t)}else null!=n&&(e[t]="innerHTML"===t?xa(n):n)}function ti(e,t,n,r){e.addEventListener(t,n,r)}var ni=Symbol("_vei");function ri(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=e[ni]||(e[ni]={}),i=a[t];if(r&&i)i.value=r;else{var l=function(e){var t;if(oi.test(e)){var n;for(t={};n=e.match(oi);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}var r=":"===e[2]?e.slice(3):W(e.slice(2));return[r,t]}(t),c=m(l,2),u=c[0],s=c[1];if(r){var f=a[t]=function(e,t){var n=function(e){if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Zt(function(e,t){if(O(t)){var n=e.stopImmediatePropagation;return e.stopImmediatePropagation=function(){n.call(e),e._stopped=!0},t.map((function(e){return function(t){return!t._stopped&&e&&e(t)}}))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=li(),n}(r,o);ti(e,u,f,s)}else i&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,u,i,s),a[t]=void 0)}}var oi=/(?:Once|Passive|Capture)$/;var ai=0,ii=Promise.resolve(),li=function(){return ai||(ii.then((function(){return ai=0})),ai=Date.now())};var ci=function(e){return 111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123};var ui=function(e){var t=e.props["onUpdate:modelValue"]||!1;return O(t)?function(e){return X(t,e)}:t},si=Symbol("_assign"),fi={deep:!0,created:function(e,t,n){e[si]=ui(n),ti(e,"change",(function(){var t=e._modelValue,n=hi(e),r=e.checked,o=e[si];if(O(t)){var a=ue(t,n),i=-1!==a;if(r&&!i)o(t.concat(n));else if(!r&&i){var l=g(t);l.splice(a,1),o(l)}}else if(A(t)){var c=new Set(t);r?c.add(n):c.delete(n),o(c)}else o(mi(e,r))}))},mounted:di,beforeUpdate:function(e,t,n){e[si]=ui(n),di(e,t,n)}};function di(e,t,n){var r,o=t.value,a=t.oldValue;if(e._modelValue=o,O(o))r=ue(o,n.props.value)>-1;else if(A(o))r=o.has(n.props.value);else{if(o===a)return;r=ce(o,mi(e,!0))}e.checked!==r&&(e.checked=r)}var pi={created:function(e,t,n){var r=t.value;e.checked=ce(r,n.props.value),e[si]=ui(n),ti(e,"change",(function(){e[si](hi(e))}))},beforeUpdate:function(e,t,n){var r=t.value,o=t.oldValue;e[si]=ui(n),r!==o&&(e.checked=ce(r,n.props.value))}};t("V",{deep:!0,created:function(e,t,n){var r=t.value,o=t.modifiers.number,a=A(r);ti(e,"change",(function(){var t=Array.prototype.filter.call(e.options,(function(e){return e.selected})).map((function(e){return o?Z(hi(e)):hi(e)}));e[si](e.multiple?a?new Set(t):t:t[0]),e._assigning=!0,cn((function(){e._assigning=!1}))})),e[si]=ui(n)},mounted:function(e,t){vi(e,t.value)},beforeUpdate:function(e,t,n){e[si]=ui(n)},updated:function(e,t){var n=t.value;e._assigning||vi(e,n)}});function vi(e,t){var n=e.multiple,r=O(t);if(!n||r||A(t)){for(var o,a=function(){var o=e.options[i],a=hi(o);if(n)if(r){var l=b(a);o.selected="string"===l||"number"===l?t.some((function(e){return String(e)===String(a)})):ue(t,a)>-1}else o.selected=t.has(a);else if(ce(hi(o),t))return e.selectedIndex!==i&&(e.selectedIndex=i),{v:void 0}},i=0,l=e.options.length;i<l;i++)if(o=a())return o.v;n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function hi(e){return"_value"in e?e._value:e.value}function mi(e,t){var n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}var gi,bi=["ctrl","shift","alt","meta"],yi={stop:function(e){return e.stopPropagation()},prevent:function(e){return e.preventDefault()},self:function(e){return e.target!==e.currentTarget},ctrl:function(e){return!e.ctrlKey},shift:function(e){return!e.shiftKey},alt:function(e){return!e.altKey},meta:function(e){return!e.metaKey},left:function(e){return"button"in e&&0!==e.button},middle:function(e){return"button"in e&&1!==e.button},right:function(e){return"button"in e&&2!==e.button},exact:function(e,t){return bi.some((function(n){return e["".concat(n,"Key")]&&!t.includes(n)}))}},xi=t("H",(function(e,t){var n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=function(n){for(var r=0;r<t.length;r++){var o=yi[t[r]];if(o&&o(n,t))return}for(var a=arguments.length,i=new Array(a>1?a-1:0),l=1;l<a;l++)i[l-1]=arguments[l];return e.apply(void 0,[n].concat(i))})})),wi={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},_i=(t("Z",(function(e,t){var n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=function(n){if("key"in n){var r=W(n.key);return t.some((function(e){return e===r||wi[e]===r}))?e(n):void 0}})})),k({patchProp:function(e,t,n,r,o,a){var i="svg"===o;"class"===t?function(e,t,n){var r=e[ja];r&&(t=(t?[t].concat(g(r)):g(r)).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,i):"style"===t?function(e,t,n){var r=e.style,o=I(n),a=!1;if(n&&!o){if(t)if(I(t)){var i,l=y(t.split(";"));try{for(l.s();!(i=l.n()).done;){var c=i.value,u=c.slice(0,c.indexOf(":")).trim();null==n[u]&&Ja(r,u,"")}}catch(p){l.e(p)}finally{l.f()}}else for(var s in t)null==n[s]&&Ja(r,s,"");for(var f in n)"display"===f&&(a=!0),Ja(r,f,n[f])}else if(o){if(t!==n){var d=r[$a];d&&(n+=";"+d),r.cssText=n,a=Ga.test(n)}}else t&&e.removeAttribute("style");Va in e&&(e[Va]=a?r.display:"",e[Ua]&&(r.display="none"))}(e,n,r):w(t)?_(t)||ri(e,t,n,r,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&ci(t)&&T(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){var o=e.tagName;if("IMG"===o||"VIDEO"===o||"CANVAS"===o||"SOURCE"===o)return!1}if(ci(t)&&I(n))return!1;return t in e}(e,t,r,i))?(ei(e,t,r),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||Qa(e,t,r,i,a,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&I(r)?("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),Qa(e,t,r,i)):ei(e,$(t),r,0,t)}},ka));var ki=function(){var e,t=(e=gi||(gi=ro(_i))).createApp.apply(e,arguments),n=t.mount;return t.mount=function(e){var r=function(e){if(I(e)){return document.querySelector(e)}return e}(e);if(r){var o=t._component;T(o)||o.render||o.template||(o.template=r.innerHTML),1===r.nodeType&&(r.textContent="");var a=n(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),a}},t};var Si=t("_",(function(e,t){var n,r=e.__vccOpts||e,o=y(t);try{for(o.s();!(n=o.n()).done;){var a=m(n.value,2),i=a[0],l=a[1];r[i]=l}}catch(c){o.e(c)}finally{o.f()}return r})),Ci=["disabled","type"],ji={key:0,class:"loading"},Oi={__name:"Button",props:{type:{type:String,default:"default",validator:function(e){return["default","primary","success","warning","danger"].includes(e)}},size:{type:String,default:"default",validator:function(e){return["small","default","large"].includes(e)}},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},nativeType:{type:String,default:"button",validator:function(e){return["button","submit","reset"].includes(e)}}},emits:["click"],setup:function(e,t){var n=t.emit,r=e,o=n,a=ha((function(){var e=["btn"];return"default"!==r.type?e.push("btn-".concat(r.type)):e.push("btn-default"),"default"!==r.size&&e.push("btn-".concat(r.size)),r.loading&&e.push("btn-loading"),e.join(" ")})),i=function(e){r.disabled||r.loading||o("click",e)};return function(t,n){return To(),Po("button",{class:ae(a.value),disabled:e.disabled,type:e.nativeType,onClick:i},[e.loading?(To(),Po("span",ji)):Ho("",!0),br(t.$slots,"default",{},void 0,!0)],10,Ci)}}},zi=Si(Oi,[["__scopeId","data-v-f0b3f2fd"]]),Ai={class:"input-wrapper"},Ei=["type","value","placeholder","disabled","readonly","maxlength"],Ti={__name:"Input",props:{modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},placeholder:{type:String,default:""},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:void 0},size:{type:String,default:"default",validator:function(e){return["small","default","large"].includes(e)}}},emits:["update:modelValue","input","change","focus","blur"],setup:function(e,t){var n=t.expose,r=t.emit,o=e,a=r,i=Ft(null),l=Ft(!1),c=ha((function(){var e=["base-input"];return"default"!==o.size&&e.push("base-input--".concat(o.size)),l.value&&e.push("base-input--focused"),o.disabled&&e.push("base-input--disabled"),e.join(" ")})),u=function(e){var t=e.target.value;a("update:modelValue",t),a("input",t,e)},s=function(e){a("change",e.target.value,e)},f=function(e){l.value=!0,a("focus",e)},d=function(e){l.value=!1,a("blur",e)};return n({focus:function(){var e;return null===(e=i.value)||void 0===e?void 0:e.focus()},blur:function(){var e;return null===(e=i.value)||void 0===e?void 0:e.blur()}}),function(t,n){return To(),Po("div",Ai,[Uo("input",{ref_key:"inputRef",ref:i,class:ae(c.value),type:e.type,value:e.modelValue,placeholder:e.placeholder,disabled:e.disabled,readonly:e.readonly,maxlength:e.maxlength,onInput:u,onChange:s,onFocus:f,onBlur:d},null,42,Ei)])}}},Ii=Si(Ti,[["__scopeId","data-v-47df032a"]]),Li={__name:"Form",props:{model:{type:Object,default:function(){return{}}},rules:{type:Object,default:function(){return{}}},labelPosition:{type:String,default:"right",validator:function(e){return["left","right","top"].includes(e)}},labelWidth:{type:String,default:"100px"},inline:{type:Boolean,default:!1}},emits:["submit","validate"],setup:function(e,t){var n=t.expose,r=t.emit,o=e,a=r,i=Ft([]),l=ha((function(){var e=["base-form"];return o.inline&&e.push("base-form--inline"),e.push("base-form--label-".concat(o.labelPosition)),e.join(" ")})),c=function(e){a("submit",e)};return n({validate:function(e){return new Promise((function(t,n){var r=!0,o=0,a=[];if(0===i.value.length)return e&&e(!0),void t(!0);i.value.forEach((function(l){l.validate("",(function(l){o++,l&&(r=!1,a.push(l)),o===i.value.length&&(e&&e(r,a),r?t(!0):n(a))}))}))}))},validateField:function(e,t){var n=Array.isArray(e)?e:[e],r=i.value.filter((function(e){return n.includes(e.prop)}));if(0!==r.length){var o=!0,a=0;r.forEach((function(e){e.validate("",(function(e){a++,e&&(o=!1),a===r.length&&t&&t(o)}))}))}else t&&t()},resetFields:function(){i.value.forEach((function(e){e.resetField()}))},clearValidate:function(e){if(e){var t=Array.isArray(e)?e:[e];i.value.forEach((function(e){t.includes(e.prop)&&e.clearValidate()}))}else i.value.forEach((function(e){e.clearValidate()}))}}),Ur("baseForm",{model:o.model,rules:o.rules,labelPosition:o.labelPosition,labelWidth:o.labelWidth,addFormItem:function(e){i.value.push(e)},removeFormItem:function(e){var t=i.value.indexOf(e);t>-1&&i.value.splice(t,1)}}),function(e,t){return To(),Po("form",{class:ae(l.value),onSubmit:xi(c,["prevent"])},[br(e.$slots,"default",{},void 0,!0)],34)}}},Ri=Si(Li,[["__scopeId","data-v-39ff5420"]]),Pi={class:"base-form-item__content"},Mi={key:0,class:"base-form-item__error"},Fi={__name:"FormItem",props:{label:{type:String,default:""},prop:{type:String,default:""},rules:{type:[Object,Array],default:function(){return[]}},required:{type:Boolean,default:!1},labelWidth:{type:String,default:""}},setup:function(e,t){var n=t.expose,r=e,o=qr("baseForm",{}),a=Ft(""),i=Ft(null),l=ha((function(){var e=["base-form-item"];return a.value&&e.push("base-form-item--error"),(r.required||s.value)&&e.push("base-form-item--required"),e.join(" ")})),c=ha((function(){var e=["base-form-item__label"];return(r.required||s.value)&&e.push("base-form-item__label--required"),e.join(" ")})),u=ha((function(){var e=r.labelWidth||o.labelWidth;return e&&"top"!==o.labelPosition?{width:e,minWidth:e}:{}})),s=ha((function(){return f().some((function(e){return e.required}))})),f=function(){var e,t=(null===(e=o.rules)||void 0===e?void 0:e[r.prop])||[],n=r.rules||[];return[].concat(t,n)},d=function(e,t){if(!r.prop||!o.model)return t&&t(),!0;var n=o.model[r.prop],i=f();if(0===i.length)return t&&t(),!0;var l,c=y(i);try{for(c.s();!(l=c.n()).done;){var u=l.value;if(!e||!u.trigger||u.trigger===e){if(u.required&&(null==n||""===n)){var s=u.message||"".concat(r.label,"是必填项");return a.value=s,t&&t(s),!1}if(null!=n&&""!==n){if(u.min&&String(n).length<u.min){var d=u.message||"".concat(r.label,"长度不能少于").concat(u.min,"个字符");return a.value=d,t&&t(d),!1}if(u.max&&String(n).length>u.max){var p=u.message||"".concat(r.label,"长度不能超过").concat(u.max,"个字符");return a.value=p,t&&t(p),!1}if(u.pattern&&!u.pattern.test(String(n))){var v=u.message||"".concat(r.label,"格式不正确");return a.value=v,t&&t(v),!1}if(u.validator&&"function"==typeof u.validator)try{if(!1===u.validator(u,n,(function(e){e?(a.value=e.message||e,t&&t(e.message||e)):(a.value="",t&&t())}))){var h=u.message||"".concat(r.label,"验证失败");return a.value=h,t&&t(h),!1}}catch(g){var m=u.message||g.message||"".concat(r.label,"验证失败");return a.value=m,t&&t(m),!1}}}}}catch(b){c.e(b)}finally{c.f()}return a.value="",t&&t(),!0},p=function(){r.prop&&o.model&&void 0!==i.value&&(o.model[r.prop]=i.value),a.value=""},v=function(){a.value=""};return r.prop&&o.model&&fo((function(){return o.model[r.prop]}),(function(){a.value&&d("change")})),nr((function(){r.prop&&o.model&&(i.value=o.model[r.prop]),o.addFormItem&&o.addFormItem({prop:r.prop,validate:d,resetField:p,clearValidate:v})})),ir((function(){o.removeFormItem&&o.removeFormItem({prop:r.prop,validate:d,resetField:p,clearValidate:v})})),n({validate:d,resetField:p,clearValidate:v,prop:r.prop}),function(t,n){return To(),Po("div",{class:ae(l.value)},[e.label?(To(),Po("label",{key:0,class:ae(c.value),style:ee(u.value)},pe(e.label),7)):Ho("",!0),Uo("div",Pi,[br(t.$slots,"default",{},void 0,!0),a.value?(To(),Po("div",Mi,pe(a.value),1)):Ho("",!0)])],2)}}},Ni=Si(Fi,[["__scopeId","data-v-2592ce9c"]]),Bi={class:"container"},Vi=Si({__name:"Container",setup:function(e){return function(e,t){return To(),Po("div",Bi,[br(e.$slots,"default",{},void 0,!0)])}}},[["__scopeId","data-v-264e6643"]]),Ui=Si({__name:"Aside",props:{width:{type:String,default:"220px"},collapsed:{type:Boolean,default:!1},collapsedWidth:{type:String,default:"54px"}},setup:function(e){var t=e,n=ha((function(){var e=["aside"];return t.collapsed&&e.push("collapsed"),e.join(" ")})),r=ha((function(){return{width:t.collapsed?t.collapsedWidth:t.width}}));return function(e,t){return To(),Po("aside",{class:ae(n.value),style:ee(r.value)},[br(e.$slots,"default",{},void 0,!0)],6)}}},[["__scopeId","data-v-56fd2527"]]),qi={class:"main"},Di=Si({__name:"Main",setup:function(e){return function(e,t){return To(),Po("main",qi,[br(e.$slots,"default",{},void 0,!0)])}}},[["__scopeId","data-v-173b46c7"]]),$i=Si({__name:"Row",props:{gutter:{type:Number,default:0},justify:{type:String,default:"start",validator:function(e){return["start","end","center","space-around","space-between"].includes(e)}},align:{type:String,default:"top",validator:function(e){return["top","middle","bottom"].includes(e)}}},setup:function(e){var t=e,n=ha((function(){var e=["row"];return"start"!==t.justify&&e.push("row-justify-".concat(t.justify)),"top"!==t.align&&e.push("row-align-".concat(t.align)),e.join(" ")})),r=ha((function(){var e={};return t.gutter>0&&(e.marginLeft="-".concat(t.gutter/2,"px"),e.marginRight="-".concat(t.gutter/2,"px")),e}));return provide("row",{gutter:t.gutter}),function(e,t){return To(),Po("div",{class:ae(n.value),style:ee(r.value)},[br(e.$slots,"default",{},void 0,!0)],6)}}},[["__scopeId","data-v-63d064ea"]]),Hi=Si({__name:"Col",props:{span:{type:Number,default:24},offset:{type:Number,default:0},push:{type:Number,default:0},pull:{type:Number,default:0},xs:{type:[Number,Object],default:void 0},sm:{type:[Number,Object],default:void 0},md:{type:[Number,Object],default:void 0},lg:{type:[Number,Object],default:void 0},xl:{type:[Number,Object],default:void 0}},setup:function(e){var t=e,n=qr("row",{gutter:0}),r=ha((function(){var e=["col"];24!==t.span&&e.push("col-".concat(t.span)),t.offset>0&&e.push("col-offset-".concat(t.offset)),t.push>0&&e.push("col-push-".concat(t.push)),t.pull>0&&e.push("col-pull-".concat(t.pull));return["xs","sm","md","lg","xl"].forEach((function(n){var r=t[n];void 0!==r&&("number"==typeof r?e.push("col-".concat(n,"-").concat(r)):"object"===b(r)&&(void 0!==r.span&&e.push("col-".concat(n,"-").concat(r.span)),void 0!==r.offset&&e.push("col-".concat(n,"-offset-").concat(r.offset)),void 0!==r.push&&e.push("col-".concat(n,"-push-").concat(r.push)),void 0!==r.pull&&e.push("col-".concat(n,"-pull-").concat(r.pull))))})),e.join(" ")})),o=ha((function(){var e={};return n.gutter>0&&(e.paddingLeft="".concat(n.gutter/2,"px"),e.paddingRight="".concat(n.gutter/2,"px")),e}));return function(e,t){return To(),Po("div",{class:ae(r.value),style:ee(o.value)},[br(e.$slots,"default",{},void 0,!0)],6)}}},[["__scopeId","data-v-6f4b390d"]]),Wi=Si({__name:"Divider",props:{direction:{type:String,default:"horizontal",validator:function(e){return["horizontal","vertical"].includes(e)}},contentPosition:{type:String,default:"center",validator:function(e){return["left","center","right"].includes(e)}}},setup:function(e){var t=e,n=ha((function(){var e=["divider"];return"vertical"===t.direction?e.push("divider-vertical"):e.push("divider-horizontal"),e.join(" ")})),r=ha((function(){var e=["divider-content"];return"horizontal"===t.direction&&e.push("divider-content-".concat(t.contentPosition)),e.join(" ")}));return function(e,t){return To(),Po("div",{class:ae(n.value)},[e.$slots.default?(To(),Po("span",{key:0,class:ae(r.value)},[br(e.$slots,"default",{},void 0,!0)],2)):Ho("",!0)],2)}}},[["__scopeId","data-v-8fca3f99"]]),Gi=["src","alt"],Ki={key:1,class:"avatar-icon","aria-hidden":"true"},Ji=["xlink:href"],Xi={key:2,class:"avatar-text"},Yi={__name:"Avatar",props:{size:{type:[Number,String],default:40,validator:function(e){return"string"==typeof e?["small","default","large"].includes(e):"number"==typeof e&&e>0}},shape:{type:String,default:"circle",validator:function(e){return["circle","square"].includes(e)}},src:{type:String,default:""},alt:{type:String,default:""},icon:{type:String,default:""},text:{type:String,default:""}},emits:["error"],setup:function(e,t){var n=t.emit,r=e,o=n,a=Ft(!1),i=ha((function(){var e=["avatar"];return"string"==typeof r.size&&e.push("avatar-".concat(r.size)),"square"===r.shape&&e.push("avatar-square"),e.join(" ")})),l=ha((function(){var e={};return"number"==typeof r.size&&(e.width="".concat(r.size,"px"),e.height="".concat(r.size,"px"),e.lineHeight="".concat(r.size,"px"),e.fontSize="".concat(Math.floor(.35*r.size),"px")),e})),c=function(e){a.value=!0,o("error",e)};return function(t,n){return To(),Po("div",{class:ae(i.value),style:ee(l.value)},[e.src?(To(),Po("img",{key:0,src:e.src,alt:e.alt,onError:c},null,40,Gi)):e.icon?(To(),Po("svg",Ki,[Uo("use",{"xlink:href":"#".concat(e.icon)},null,8,Ji)])):(To(),Po("span",Xi,[br(t.$slots,"default",{},(function(){return[$o(pe(e.text),1)]}),!0)]))],6)}}},Zi=Si(Yi,[["__scopeId","data-v-b54355b9"]]),Qi=["onClick"],el={__name:"Carousel",props:{height:{type:String,default:"300px"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,default:"bottom",validator:function(e){return["bottom","top","none"].includes(e)}},arrow:{type:String,default:"hover",validator:function(e){return["always","hover","never"].includes(e)}}},emits:["change"],setup:function(e,t){var n=t.expose,r=t.emit,o=e,a=r,i=Ft(0),l=Ft(0),c=null,u=ha((function(){return{transform:"translateX(-".concat(100*i.value,"%)")}})),s=ha((function(){var e=["carousel-indicators"];return e.push("carousel-indicators-".concat(o.indicatorPosition)),e.join(" ")})),f=function(e){e!==i.value&&(i.value=e,a("change",e))},d=function(){var e=(i.value+1)%l.value;f(e)},p=function(){var e=(i.value-1+l.value)%l.value;f(e)};return Ur("carousel",{addItem:function(){l.value++},removeItem:function(){l.value--}}),nr((function(){o.autoplay&&l.value>1&&(c=setInterval(d,o.interval))})),ir((function(){c&&(clearInterval(c),c=null)})),n({next:d,prev:p,setCurrentIndex:f}),function(t,n){return To(),Po("div",{class:"carousel",style:ee({height:e.height})},[Uo("div",{class:"carousel-container",style:ee(u.value)},[br(t.$slots,"default",{},void 0,!0)],4),"none"!==e.indicatorPosition?(To(),Po("div",{key:0,class:ae(s.value)},[(To(!0),Po(Co,null,gr(l.value,(function(e,t){return To(),Po("button",{key:t,class:ae(["carousel-indicator",{active:t===i.value}]),onClick:function(e){return f(t)}},null,10,Qi)})),128))],2)):Ho("",!0),"never"!==e.arrow?(To(),Po("button",{key:1,class:"carousel-arrow carousel-arrow-left",onClick:p}," ‹ ")):Ho("",!0),"never"!==e.arrow?(To(),Po("button",{key:2,class:"carousel-arrow carousel-arrow-right",onClick:d}," › ")):Ho("",!0)],4)}}},tl=Si(el,[["__scopeId","data-v-b41008b0"]]),nl={class:"carousel-item"},rl=Si({__name:"CarouselItem",setup:function(e){var t=qr("carousel",null);return nr((function(){null==t||t.addItem()})),ir((function(){null==t||t.removeItem()})),function(e,t){return To(),Po("div",nl,[br(e.$slots,"default",{},void 0,!0)])}}},[["__scopeId","data-v-d653f781"]]),ol={key:0,class:"base-card__header"};var al=Si({name:"BaseCard",props:{shadow:{type:String,default:"always",validator:function(e){return["always","hover","never"].includes(e)}},bodyStyle:{type:Object,default:function(){return{}}}}},[["render",function(e,t,n,r,o,a){return To(),Po("div",{class:ae(["base-card",{"base-card--shadow":n.shadow}])},[e.$slots.header?(To(),Po("div",ol,[br(e.$slots,"header",{},void 0,!0)])):Ho("",!0),Uo("div",{class:"base-card__body",style:ee(n.bodyStyle)},[br(e.$slots,"default",{},void 0,!0)],4)],2)}],["__scopeId","data-v-663e3da6"]]),il={class:"base-timeline"};var ll=Si({name:"BaseTimeline"},[["render",function(e,t,n,r,o,a){return To(),Po("div",il,[br(e.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-d9f6b8e2"]]),cl={name:"BaseTimelineItem",props:{timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},placement:{type:String,default:"bottom",validator:function(e){return["top","bottom"].includes(e)}},type:{type:String,default:"",validator:function(e){return["primary","success","warning","danger","info",""].includes(e)}},color:{type:String,default:""},size:{type:String,default:"normal",validator:function(e){return["normal","large"].includes(e)}},icon:{type:String,default:""}},computed:{nodeClass:function(){var e=["base-timeline-item__node--".concat(this.size)];return this.type&&e.push("base-timeline-item__node--".concat(this.type)),e},nodeStyle:function(){var e={};return this.color&&(e.backgroundColor=this.color,e.borderColor=this.color),e},timestampClass:function(){return["base-timeline-item__timestamp--".concat(this.placement)]}}},ul={class:"base-timeline-item"},sl={class:"base-timeline-item__wrapper"},fl={class:"base-timeline-item__content"};var dl=Si(cl,[["render",function(e,t,n,r,o,a){return To(),Po("div",ul,[t[1]||(t[1]=Uo("div",{class:"base-timeline-item__tail"},null,-1)),Uo("div",{class:ae(["base-timeline-item__node",a.nodeClass]),style:ee(a.nodeStyle)},[br(e.$slots,"dot",{},(function(){return[t[0]||(t[0]=Uo("div",{class:"base-timeline-item__node-normal"},null,-1))]}),!0)],6),Uo("div",sl,[n.timestamp?(To(),Po("div",{key:0,class:ae(["base-timeline-item__timestamp",a.timestampClass])},pe(n.timestamp),3)):Ho("",!0),Uo("div",fl,[br(e.$slots,"default",{},void 0,!0)])])])}],["__scopeId","data-v-deb04d8a"]]),pl={name:"BaseSelect",props:{modelValue:{type:[String,Number,Boolean],default:""},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:function(e){return["large","default","small"].includes(e)}}},emits:["update:modelValue","change"],data:function(){return{visible:!1,selectedLabel:""}},mounted:function(){this.updateSelectedLabel(),document.addEventListener("click",this.handleDocumentClick)},beforeUnmount:function(){document.removeEventListener("click",this.handleDocumentClick)},watch:{modelValue:function(){this.updateSelectedLabel()}},methods:{toggleDropdown:function(){this.disabled||(this.visible=!this.visible)},handleDocumentClick:function(e){this.$el.contains(e.target)||(this.visible=!1)},handleOptionClick:function(e,t){this.$emit("update:modelValue",e),this.$emit("change",e),this.selectedLabel=t,this.visible=!1},updateSelectedLabel:function(){var e=this;this.$nextTick((function(){var t,n=null===(t=e.$el)||void 0===t?void 0:t.querySelectorAll(".base-option");n&&n.forEach((function(t){var n,r;(null===(n=t.__vue__)||void 0===n?void 0:n.value)===e.modelValue&&(e.selectedLabel=(null===(r=t.__vue__)||void 0===r?void 0:r.label)||t.textContent)}))}))}},provide:function(){return{select:this}}},vl={key:0,class:"base-select__selected"},hl={key:1,class:"base-select__placeholder"},ml={class:"base-select__dropdown"},gl={class:"base-select__options"};var bl=Si(pl,[["render",function(e,t,n,r,o,a){return To(),Po("div",{class:ae(["base-select",{"is-disabled":n.disabled}])},[Uo("div",{class:ae(["base-select__input",{"is-focus":o.visible}]),onClick:t[0]||(t[0]=function(){return a.toggleDropdown&&a.toggleDropdown.apply(a,arguments)})},[o.selectedLabel?(To(),Po("span",vl,pe(o.selectedLabel),1)):(To(),Po("span",hl,pe(n.placeholder),1)),Uo("i",{class:ae(["base-select__arrow",{"is-reverse":o.visible}])},"▼",2)],2),xn(Uo("div",ml,[Uo("div",gl,[br(e.$slots,"default",{},void 0,!0)])],512),[[qa,o.visible]])],2)}],["__scopeId","data-v-7a185f90"]]);var yl=Si({name:"BaseOption",props:{value:{type:[String,Number,Boolean],required:!0},label:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1}},inject:["select"],computed:{isSelected:function(){return this.select.modelValue===this.value}},methods:{handleClick:function(){this.disabled||this.select.handleOptionClick(this.value,this.label||this.$el.textContent)}}},[["render",function(e,t,n,r,o,a){return To(),Po("div",{class:ae(["base-option",{"is-selected":a.isSelected,"is-disabled":n.disabled}]),onClick:t[0]||(t[0]=function(){return a.handleClick&&a.handleClick.apply(a,arguments)})},[br(e.$slots,"default",{},(function(){return[$o(pe(n.label),1)]}),!0)],2)}],["__scopeId","data-v-d95e9770"]]),xl={name:"BaseCheckbox",props:{modelValue:{type:[Boolean,String,Number,Array],default:!1},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:function(e){return["large","default","small"].includes(e)}}},emits:["update:modelValue","change"],computed:{model:{get:function(){return this.modelValue},set:function(e){this.$emit("update:modelValue",e)}},isChecked:function(){return Array.isArray(this.modelValue)?this.modelValue.includes(this.label):!0===this.modelValue}},methods:{handleChange:function(e){this.$emit("change",e.target.checked)}}},wl={class:"base-checkbox__input"},_l=["disabled","value"],kl={key:0,class:"base-checkbox__label"};var Sl=Si(xl,[["render",function(e,t,n,r,o,a){return To(),Po("label",{class:ae(["base-checkbox",{"is-disabled":n.disabled,"is-checked":a.isChecked}])},[Uo("span",wl,[t[2]||(t[2]=Uo("span",{class:"base-checkbox__inner"},null,-1)),xn(Uo("input",{type:"checkbox",class:"base-checkbox__original",disabled:n.disabled,value:n.label,"onUpdate:modelValue":t[0]||(t[0]=function(e){return a.model=e}),onChange:t[1]||(t[1]=function(){return a.handleChange&&a.handleChange.apply(a,arguments)})},null,40,_l),[[fi,a.model]])]),e.$slots.default||n.label?(To(),Po("span",kl,[br(e.$slots,"default",{},(function(){return[$o(pe(n.label),1)]}),!0)])):Ho("",!0)],2)}],["__scopeId","data-v-27e2b100"]]),Cl={name:"BaseRadio",props:{modelValue:{type:[String,Number,Boolean],default:""},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:function(e){return["large","default","small"].includes(e)}}},emits:["update:modelValue","change"],computed:{model:{get:function(){return this.modelValue},set:function(e){this.$emit("update:modelValue",e)}},isChecked:function(){return this.modelValue===this.label}},methods:{handleChange:function(e){this.$emit("change",e.target.value)}}},jl={class:"base-radio__input"},Ol=["disabled","value"],zl={key:0,class:"base-radio__label"};var Al=Si(Cl,[["render",function(e,t,n,r,o,a){return To(),Po("label",{class:ae(["base-radio",{"is-disabled":n.disabled,"is-checked":a.isChecked}])},[Uo("span",jl,[t[2]||(t[2]=Uo("span",{class:"base-radio__inner"},null,-1)),xn(Uo("input",{type:"radio",class:"base-radio__original",disabled:n.disabled,value:n.label,"onUpdate:modelValue":t[0]||(t[0]=function(e){return a.model=e}),onChange:t[1]||(t[1]=function(){return a.handleChange&&a.handleChange.apply(a,arguments)})},null,40,Ol),[[pi,a.model]])]),e.$slots.default||n.label?(To(),Po("span",zl,[br(e.$slots,"default",{},(function(){return[$o(pe(n.label),1)]}),!0)])):Ho("",!0)],2)}],["__scopeId","data-v-c39e0420"]]),El={name:"BaseRadioGroup",props:{modelValue:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:function(e){return["large","default","small"].includes(e)}},textColor:{type:String,default:""},fill:{type:String,default:""}},emits:["update:modelValue","change"],watch:{modelValue:function(e){this.$emit("change",e)}},provide:function(){return{radioGroup:this}}},Tl={class:"base-radio-group",role:"radiogroup"};var Il=Si(El,[["render",function(e,t,n,r,o,a){return To(),Po("div",Tl,[br(e.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-12a82aff"]]),Ll={key:0,viewBox:"0 0 1024 1024",width:"1em",height:"1em",fill:"currentColor"},Rl=["d"];var Pl=Si({name:"BaseIcon",props:{name:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconClass:function(){return v({},"base-icon--".concat(this.name),this.name)},iconStyle:function(){return{fontSize:"number"==typeof this.size?"".concat(this.size,"px"):this.size,color:this.color}},iconPath:function(){return{search:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116 65.6-158.4C296 211.3 352.2 188 412 188s116 23.3 158.4 65.6C612.7 296 636 352.2 636 412s-23.3 116-65.6 158.4z",plus:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z",warning:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z",document:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z",loading:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C637 83.6 579.4 72 520 72s-117 11.6-171.3 34.6a440.45 440.45 0 0 0-139.9 94.3 437.71 437.71 0 0 0-94.3 139.9C91.6 395 80 452.6 80 512s11.6 117 34.6 171.3a440.45 440.45 0 0 0 94.3 139.9 437.71 437.71 0 0 0 139.9 94.3C475 940.4 532.6 952 592 952c19.9 0 36 16.1 36 36s-16.1 36-36 36c-59.4 0-117-11.6-171.3-34.6a512.69 512.69 0 0 1-139.9-94.3c-40.8-35.4-73.4-76.3-94.3-139.9C163.6 709 152 651.4 152 592s11.6-117 34.6-171.3a512.69 512.69 0 0 1 94.3-139.9c35.4-40.8 76.3-73.4 139.9-94.3C467 163.6 524.6 152 584 152c19.9 0 36 16.1 36 36s-16.1 36-36 36z",jieru:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z",shezhi:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a449.4 449.4 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 28.9a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM512 701c-104.9 0-190-85.1-190-190s85.1-190 190-190 190 85.1 190 190-85.1 190-190 190z",windows:"M120.83 208.13l344.49-47.04v331.65H120.83V208.13zm0 607.78l344.49 47.04V531.3H120.83v284.61zm384.34 50.02L903.17 896V531.3H505.17v334.63zM505.17 128L903.17 96v435.3H505.17V128z",mac:"M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z",ios:"M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z",android:"M765.7 486.8L620.7 182.2c-3.4-7.2-11.1-11.7-19.5-11.7s-16.1 4.5-19.5 11.7L436.6 486.8a32.05 32.05 0 0 0 28.6 46.4c7.9 0 15.3-2.9 21.1-8.2l23.9-23.9h185.6l23.9 23.9c5.8 5.3 13.2 8.2 21.1 8.2 17.7 0 32-14.3 32-32 0-7.6-2.6-14.8-7.4-20.4zM334.1 548.9L149.9 757.1a32.06 32.06 0 0 0 0 45.3l208.2 208.2c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L195.2 757.1 403.4 548.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0z",xiazai:"M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z",expand:"M342 88H120c-17.7 0-32 14.3-32 32v222c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v174c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32zM342 856h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16H342V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v222c0 17.7 14.3 32 32 32h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0c17.7 0 32-14.3 32-32V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v174H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222z"}[this.name]||""}}},[["render",function(e,t,n,r,o,a){return To(),Po("i",{class:ae(["base-icon",a.iconClass]),style:ee(a.iconStyle)},[n.name?(To(),Po("svg",Ll,[Uo("path",{d:a.iconPath},null,8,Rl)])):br(e.$slots,"default",{key:1},void 0,!0)],6)}],["__scopeId","data-v-27fea9a9"]]),Ml=["xlink:href","href"];var Fl=Si({name:"SvgIcon",props:{iconClass:{type:String,required:!0},className:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconName:function(){return"#icon-".concat(this.iconClass)},svgClass:function(){return this.className?"svg-icon "+this.className:"svg-icon"},svgStyle:function(){return{fontSize:"number"==typeof this.size?"".concat(this.size,"px"):this.size,color:this.color,width:"1em",height:"1em"}}}},[["render",function(e,t,n,r,o,a){return To(),Po("svg",Jo({class:a.svgClass,style:a.svgStyle,"aria-hidden":"true"},function(e,t){var n={};for(var r in e)n[t&&/[A-Z]/.test(r)?"on:".concat(r):K(r)]=e[r];return n}(e.$listeners,!0)),[Uo("use",{"xlink:href":a.iconName,href:a.iconName},null,8,Ml)],16)}],["__scopeId","data-v-dae6fe16"]]),Nl={template:'\n    <div class="loading-overlay" v-if="visible">\n      <div class="loading-content">\n        <div class="loading"></div>\n        <div v-if="text" class="loading-text">{{ text }}</div>\n      </div>\n    </div>\n  ',data:function(){return{visible:!1,text:""}},methods:{show:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.visible=!0,this.text=e.text||""},hide:function(){this.visible=!1,this.text=""}}},Bl=function(){return p((function e(){f(this,e),this.instance=null,this.container=null}),[{key:"service",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.instance&&this.close(),this.container=document.createElement("div"),this.container.className="loading-service-container",!1!==t.fullscreen)document.body.appendChild(this.container);else if(t.target){var n="string"==typeof t.target?document.querySelector(t.target):t.target;n?(n.appendChild(this.container),n.style.position="relative"):document.body.appendChild(this.container)}else document.body.appendChild(this.container);return this.instance=ki(Nl),this.instance.mount(this.container).show(t),{close:function(){return e.close()}}}},{key:"close",value:function(){this.instance&&(this.instance.unmount(),this.instance=null),this.container&&this.container.parentNode&&(this.container.parentNode.removeChild(this.container),this.container=null)}}])}(),Vl=new Bl,Ul=t("L",{service:function(e){return Vl.service(e)}}),ql=v({name:"BaseMessage",props:{message:{type:String,default:""},type:{type:String,default:"info",validator:function(e){return["success","warning","info","error"].includes(e)}},showClose:{type:Boolean,default:!1},duration:{type:Number,default:3e3}},data:function(){return{visible:!0}},mounted:function(){var e=this;this.duration>0&&setTimeout((function(){e.close()}),this.duration)},methods:{close:function(){var e=this;this.visible=!1,setTimeout((function(){e.$el.remove()}),300)}},render:function(){return this.visible?ma("div",{class:["base-message","base-message--".concat(this.type),{"base-message--closable":this.showClose}],style:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",zIndex:9999,padding:"12px 16px",borderRadius:"4px",color:"#fff",fontSize:"14px",boxShadow:"0 2px 12px 0 rgba(0, 0, 0, 0.1)",transition:"all 0.3s",backgroundColor:this.getBackgroundColor()}},[ma("span",this.message),this.showClose&&ma("span",{style:{marginLeft:"8px",cursor:"pointer",fontSize:"16px"},onClick:this.close},"×")]):null}},"methods",{getBackgroundColor:function(){var e={success:"#67c23a",warning:"#e6a23c",error:"#f56c6c",info:"#909399"};return e[this.type]||e.info}}),Dl=t("M",(function(e){"string"==typeof e&&(e={message:e});var t=document.createElement("div");document.body.appendChild(t);var n=ki(ql,e);return n.mount(t),{close:function(){n.unmount(),document.body.removeChild(t)}}}));Dl.success=function(e){return Dl({message:e,type:"success"})},Dl.warning=function(e){return Dl({message:e,type:"warning"})},Dl.error=function(e){return Dl({message:e,type:"error"})},Dl.info=function(e){return Dl({message:e,type:"info"})};var $l={name:"BaseMessageBox",props:{title:{type:String,default:"提示"},message:{type:String,default:""},type:{type:String,default:"info",validator:function(e){return["success","warning","info","error"].includes(e)}},showCancelButton:{type:Boolean,default:!1},confirmButtonText:{type:String,default:"确定"},cancelButtonText:{type:String,default:"取消"}},data:function(){return{visible:!0}},methods:{handleConfirm:function(){this.$emit("confirm"),this.close()},handleCancel:function(){this.$emit("cancel"),this.close()},close:function(){var e=this;this.visible=!1,setTimeout((function(){e.$el.remove()}),300)}},render:function(){return this.visible?ma("div",{class:"base-message-box-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:9999,display:"flex",alignItems:"center",justifyContent:"center"}},[ma("div",{class:"base-message-box",style:{backgroundColor:"#fff",borderRadius:"4px",boxShadow:"0 2px 12px 0 rgba(0, 0, 0, 0.1)",minWidth:"300px",maxWidth:"500px",padding:"20px"}},[ma("div",{style:{fontSize:"16px",fontWeight:"bold",marginBottom:"10px",color:"#303133"}},this.title),ma("div",{style:{fontSize:"14px",color:"#606266",marginBottom:"20px",lineHeight:"1.5"}},this.message),ma("div",{style:{textAlign:"right"}},[this.showCancelButton&&ma("button",{style:{padding:"8px 16px",marginRight:"10px",border:"1px solid #dcdfe6",borderRadius:"4px",backgroundColor:"#fff",color:"#606266",cursor:"pointer"},onClick:this.handleCancel},this.cancelButtonText),ma("button",{style:{padding:"8px 16px",border:"none",borderRadius:"4px",backgroundColor:"#409eff",color:"#fff",cursor:"pointer"},onClick:this.handleConfirm},this.confirmButtonText)])])]):null}},Hl=function(e){return new Promise((function(t,n){var r=document.createElement("div");document.body.appendChild(r);var o=ki($l,a(a({},e),{},{onConfirm:function(){o.unmount(),document.body.removeChild(r),t("confirm")},onCancel:function(){o.unmount(),document.body.removeChild(r),n("cancel")}}));o.mount(r)}))};Hl.confirm=function(e){return Hl(a({message:e,title:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"确认",showCancelButton:!0},arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}))},Hl.alert=function(e){return Hl(a({message:e,title:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"提示",showCancelButton:!1},arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}))};var Wl={"base-button":zi,"base-input":Ii,"base-form":Ri,"base-form-item":Ni,"base-container":Vi,"base-aside":Ui,"base-main":Di,"base-row":$i,"base-col":Hi,"base-divider":Wi,"base-avatar":Zi,"base-carousel":tl,"base-carousel-item":rl,"base-card":al,"base-timeline":ll,"base-timeline-item":dl,"base-select":bl,"base-option":yl,"base-checkbox":Sl,"base-radio":Al,"base-radio-group":Il,"base-icon":Pl,"svg-icon":Fl},Gl={install:function(e){Object.keys(Wl).forEach((function(t){e.component(t,Wl[t])})),e.config.globalProperties.$loading=Ul,e.config.globalProperties.$message=Dl,e.config.globalProperties.$messageBox=Hl}},Kl={appName:"ASec安全平台",appLogo:"/src/assets/ASD.png",introduction:"ASec",showViteLogo:!1},Jl={install:function(e){!function(e){e.config.globalProperties.$GIN_VUE_ADMIN=Kl}(e)}},Xl=t("B",(function(e,t,n){return e()})),Yl="undefined"!=typeof document;function Zl(e){return"object"===b(e)||"displayName"in e||"props"in e||"__vccOpts"in e}var Ql=Object.assign;function ec(e,t){var n={};for(var r in t){var o=t[r];n[r]=nc(o)?o.map(e):e(o)}return n}var tc=function(){},nc=Array.isArray,rc=/#/g,oc=/&/g,ac=/\//g,ic=/=/g,lc=/\?/g,cc=/\+/g,uc=/%5B/g,sc=/%5D/g,fc=/%5E/g,dc=/%60/g,pc=/%7B/g,vc=/%7C/g,hc=/%7D/g,mc=/%20/g;function gc(e){return encodeURI(""+e).replace(vc,"|").replace(uc,"[").replace(sc,"]")}function bc(e){return gc(e).replace(cc,"%2B").replace(mc,"+").replace(rc,"%23").replace(oc,"%26").replace(dc,"`").replace(pc,"{").replace(hc,"}").replace(fc,"^")}function yc(e){return null==e?"":function(e){return gc(e).replace(rc,"%23").replace(lc,"%3F")}(e).replace(ac,"%2F")}function xc(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}var wc=/\/$/;function _c(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",o={},a="",i="",l=t.indexOf("#"),c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(n=t.slice(0,c),o=e(a=t.slice(c+1,l>-1?l:t.length))),l>-1&&(n=n||t.slice(0,l),i=t.slice(l,t.length)),{fullPath:(n=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;var n=t.split("/"),r=e.split("/"),o=r[r.length-1];".."!==o&&"."!==o||r.push("");var a,i,l=n.length-1;for(a=0;a<r.length;a++)if("."!==(i=r[a])){if(".."!==i)break;l>1&&l--}return n.slice(0,l).join("/")+"/"+r.slice(a).join("/")}(null!=n?n:t,r))+(a&&"?")+a+i,path:n,query:o,hash:xc(i)}}function kc(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Sc(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Cc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var n in e)if(!jc(e[n],t[n]))return!1;return!0}function jc(e,t){return nc(e)?Oc(e,t):nc(t)?Oc(t,e):e===t}function Oc(e,t){return nc(t)?e.length===t.length&&e.every((function(e,n){return e===t[n]})):1===e.length&&e[0]===t}var zc,Ac,Ec={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};function Tc(e){if(!e)if(Yl){var t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(wc,"")}!function(e){e.pop="pop",e.push="push"}(zc||(zc={})),function(e){e.back="back",e.forward="forward",e.unknown=""}(Ac||(Ac={}));var Ic=/^[^#]+#/;function Lc(e,t){return e.replace(Ic,"#")+t}var Rc=function(){return{left:window.scrollX,top:window.scrollY}};function Pc(e){var t;if("el"in e){var n=e.el,r="string"==typeof n&&n.startsWith("#"),o="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=function(e,t){var n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Mc(e,t){return(history.state?history.state.position-t:-1)+e}var Fc=new Map;var Nc=function(){return location.protocol+"//"+location.host};function Bc(e,t){var n=t.pathname,r=t.search,o=t.hash,a=e.indexOf("#");if(a>-1){var i=o.includes(e.slice(a))?e.slice(a).length:1,l=o.slice(i);return"/"!==l[0]&&(l="/"+l),kc(l,"")}return kc(n,e)+r+o}function Vc(e,t,n){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return{back:e,current:t,forward:n,replaced:arguments.length>3&&void 0!==arguments[3]&&arguments[3],position:window.history.length,scroll:r?Rc():null}}function Uc(e){var t=function(e){var t=window,n=t.history,r=t.location,o={value:Bc(e,r)},a={value:n.state};function i(t,o,i){var l=e.indexOf("#"),c=l>-1?(r.host&&document.querySelector("base")?e:e.slice(l))+t:Nc()+e+t;try{n[i?"replaceState":"pushState"](o,"",c),a.value=o}catch(u){console.error(u),r[i?"replace":"assign"](c)}}return a.value||i(o.value,{back:null,current:o.value,forward:null,position:n.length-1,replaced:!0,scroll:null},!0),{location:o,state:a,push:function(e,t){var r=Ql({},a.value,n.state,{forward:e,scroll:Rc()});i(r.current,r,!0),i(e,Ql({},Vc(o.value,e,null),{position:r.position+1},t),!1),o.value=e},replace:function(e,t){i(e,Ql({},n.state,Vc(a.value.back,e,a.value.forward,!0),t,{position:a.value.position}),!0),o.value=e}}}(e=Tc(e)),n=function(e,t,n,r){var o=[],a=[],i=null,l=function(a){var l=a.state,c=Bc(e,location),u=n.value,s=t.value,f=0;if(l){if(n.value=c,t.value=l,i&&i===u)return void(i=null);f=s?l.position-s.position:0}else r(c);o.forEach((function(e){e(n.value,u,{delta:f,type:zc.pop,direction:f?f>0?Ac.forward:Ac.back:Ac.unknown})}))};function c(){var e=window.history;e.state&&e.replaceState(Ql({},e.state,{scroll:Rc()}),"")}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:function(){i=n.value},listen:function(e){o.push(e);var t=function(){var t=o.indexOf(e);t>-1&&o.splice(t,1)};return a.push(t),t},destroy:function(){var e,t=y(a);try{for(t.s();!(e=t.n()).done;)(0,e.value)()}catch(n){t.e(n)}finally{t.f()}a=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}}}(e,t.state,t.location,t.replace);var r=Ql({location:"",base:e,go:function(e){!(arguments.length>1&&void 0!==arguments[1])||arguments[1]||n.pauseListeners(),history.go(e)},createHref:Lc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:function(){return t.location.value}}),Object.defineProperty(r,"state",{enumerable:!0,get:function(){return t.state.value}}),r}function qc(e){return"string"==typeof e||"symbol"===b(e)}var Dc,$c=Symbol("");function Hc(e,t){return Ql(new Error,v({type:e},$c,!0),t)}function Wc(e,t){return e instanceof Error&&$c in e&&(null==t||!!(e.type&t))}!function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"}(Dc||(Dc={}));var Gc="[^/]+?",Kc={sensitive:!1,strict:!1,start:!0,end:!0},Jc=/[.+*?^${}()[\]/\\]/g;function Xc(e,t){for(var n=0;n<e.length&&n<t.length;){var r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Yc(e,t){for(var n=0,r=e.score,o=t.score;n<r.length&&n<o.length;){var a=Xc(r[n],o[n]);if(a)return a;n++}if(1===Math.abs(o.length-r.length)){if(Zc(r))return 1;if(Zc(o))return-1}return o.length-r.length}function Zc(e){var t=e[e.length-1];return e.length>0&&t[t.length-1]<0}var Qc={type:0,value:""},eu=/[a-zA-Z0-9_]/;function tu(e,t,n){var r=function(e,t){var n,r=Ql({},Kc,t),o=[],a=r.start?"^":"",i=[],l=y(e);try{for(l.s();!(n=l.n()).done;){var c=n.value,u=c.length?[]:[90];r.strict&&!c.length&&(a+="/");for(var s=0;s<c.length;s++){var f=c[s],d=40+(r.sensitive?.25:0);if(0===f.type)s||(a+="/"),a+=f.value.replace(Jc,"\\$&"),d+=40;else if(1===f.type){var p=f.value,v=f.repeatable,h=f.optional,m=f.regexp;i.push({name:p,repeatable:v,optional:h});var g=m||Gc;if(g!==Gc){d+=10;try{new RegExp("(".concat(g,")"))}catch(_){throw new Error('Invalid custom RegExp for param "'.concat(p,'" (').concat(g,"): ")+_.message)}}var b=v?"((?:".concat(g,")(?:/(?:").concat(g,"))*)"):"(".concat(g,")");s||(b=h&&c.length<2?"(?:/".concat(b,")"):"/"+b),h&&(b+="?"),a+=b,d+=20,h&&(d+=-8),v&&(d+=-20),".*"===g&&(d+=-50)}u.push(d)}o.push(u)}}catch(_){l.e(_)}finally{l.f()}if(r.strict&&r.end){var x=o.length-1;o[x][o[x].length-1]+=.7000000000000001}r.strict||(a+="/?"),r.end?a+="$":r.strict&&!a.endsWith("/")&&(a+="(?:/|$)");var w=new RegExp(a,r.sensitive?"":"i");return{re:w,score:o,keys:i,parse:function(e){var t=e.match(w),n={};if(!t)return null;for(var r=1;r<t.length;r++){var o=t[r]||"",a=i[r-1];n[a.name]=o&&a.repeatable?o.split("/"):o}return n},stringify:function(t){var n,r="",o=!1,a=y(e);try{for(a.s();!(n=a.n()).done;){var i=n.value;o&&r.endsWith("/")||(r+="/"),o=!1;var l,c=y(i);try{for(c.s();!(l=c.n()).done;){var u=l.value;if(0===u.type)r+=u.value;else if(1===u.type){var s=u.value,f=u.repeatable,d=u.optional,p=s in t?t[s]:"";if(nc(p)&&!f)throw new Error('Provided param "'.concat(s,'" is an array but it is not repeatable (* or + modifiers)'));var v=nc(p)?p.join("/"):p;if(!v){if(!d)throw new Error('Missing required param "'.concat(s,'"'));i.length<2&&(r.endsWith("/")?r=r.slice(0,-1):o=!0)}r+=v}}}catch(_){c.e(_)}finally{c.f()}}}catch(_){a.e(_)}finally{a.f()}return r||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Qc]];if(!e.startsWith("/"))throw new Error('Invalid path "'.concat(e,'"'));function t(e){throw new Error("ERR (".concat(r,')/"').concat(u,'": ').concat(e))}var n,r=0,o=r,a=[];function i(){n&&a.push(n),n=[]}var l,c=0,u="",s="";function f(){u&&(0===r?n.push({type:0,value:u}):1===r||2===r||3===r?(n.length>1&&("*"===l||"+"===l)&&t("A repeatable param (".concat(u,") must be alone in its segment. eg: '/:ids+.")),n.push({type:1,value:u,regexp:s,repeatable:"*"===l||"+"===l,optional:"*"===l||"?"===l})):t("Invalid state to consume buffer"),u="")}function d(){u+=l}for(;c<e.length;)if("\\"!==(l=e[c++])||2===r)switch(r){case 0:"/"===l?(u&&f(),i()):":"===l?(f(),r=1):d();break;case 4:d(),r=o;break;case 1:"("===l?r=2:eu.test(l)?d():(f(),r=0,"*"!==l&&"?"!==l&&"+"!==l&&c--);break;case 2:")"===l?"\\"==s[s.length-1]?s=s.slice(0,-1)+l:r=3:s+=l;break;case 3:f(),r=0,"*"!==l&&"?"!==l&&"+"!==l&&c--,s="";break;default:t("Unknown state")}else o=r,r=4;return 2===r&&t('Unfinished custom RegExp for param "'.concat(u,'"')),f(),i(),a}(e.path),n),o=Ql(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function nu(e,t){var n=[],r=new Map;function o(e,n,r){var l=!r,c=ou(e);c.aliasOf=r&&r.record;var u,s,f=cu(t,e),d=[c];if("alias"in e){var p,v=y("string"==typeof e.alias?[e.alias]:e.alias);try{for(v.s();!(p=v.n()).done;){var h=p.value;d.push(ou(Ql({},c,{components:r?r.record.components:c.components,path:h,aliasOf:r?r.record:c})))}}catch(C){v.e(C)}finally{v.f()}}for(var m=0,g=d;m<g.length;m++){var b=g[m],x=b.path;if(n&&"/"!==x[0]){var w=n.record.path,_="/"===w[w.length-1]?"":"/";b.path=n.record.path+(x&&_+x)}if(u=tu(b,n,f),r?r.alias.push(u):((s=s||u)!==u&&s.alias.push(u),l&&e.name&&!iu(u)&&a(e.name)),uu(u)&&i(u),c.children)for(var k=c.children,S=0;S<k.length;S++)o(k[S],u,r&&r.children[S]);r=r||u}return s?function(){a(s)}:tc}function a(e){if(qc(e)){var t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(a),t.alias.forEach(a))}else{var o=n.indexOf(e);o>-1&&(n.splice(o,1),e.record.name&&r.delete(e.record.name),e.children.forEach(a),e.alias.forEach(a))}}function i(e){var t=function(e,t){var n=0,r=t.length;for(;n!==r;){var o=n+r>>1;Yc(e,t[o])<0?r=o:n=o+1}var a=function(e){var t=e;for(;t=t.parent;)if(uu(t)&&0===Yc(e,t))return t;return}(e);a&&(r=t.lastIndexOf(a,r-1));return r}(e,n);n.splice(t,0,e),e.record.name&&!iu(e)&&r.set(e.record.name,e)}return t=cu({strict:!1,end:!0,sensitive:!1},t),e.forEach((function(e){return o(e)})),{addRoute:o,resolve:function(e,t){var o,a,i,l={};if("name"in e&&e.name){if(!(o=r.get(e.name)))throw Hc(1,{location:e});i=o.record.name,l=Ql(ru(t.params,o.keys.filter((function(e){return!e.optional})).concat(o.parent?o.parent.keys.filter((function(e){return e.optional})):[]).map((function(e){return e.name}))),e.params&&ru(e.params,o.keys.map((function(e){return e.name})))),a=o.stringify(l)}else if(null!=e.path)a=e.path,(o=n.find((function(e){return e.re.test(a)})))&&(l=o.parse(a),i=o.record.name);else{if(!(o=t.name?r.get(t.name):n.find((function(e){return e.re.test(t.path)}))))throw Hc(1,{location:e,currentLocation:t});i=o.record.name,l=Ql({},t.params,e.params),a=o.stringify(l)}for(var c=[],u=o;u;)c.unshift(u.record),u=u.parent;return{name:i,path:a,params:l,matched:c,meta:lu(c)}},removeRoute:a,clearRoutes:function(){n.length=0,r.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function ru(e,t){var n,r={},o=y(t);try{for(o.s();!(n=o.n()).done;){var a=n.value;a in e&&(r[a]=e[a])}}catch(i){o.e(i)}finally{o.f()}return r}function ou(e){var t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:au(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function au(e){var t={},n=e.props||!1;if("component"in e)t.default=n;else for(var r in e.components)t[r]="object"===b(n)?n[r]:n;return t}function iu(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function lu(e){return e.reduce((function(e,t){return Ql(e,t.meta)}),{})}function cu(e,t){var n={};for(var r in e)n[r]=r in t?t[r]:e[r];return n}function uu(e){var t=e.record;return!!(t.name||t.components&&Object.keys(t.components).length||t.redirect)}function su(e){var t={};if(""===e||"?"===e)return t;for(var n=("?"===e[0]?e.slice(1):e).split("&"),r=0;r<n.length;++r){var o=n[r].replace(cc," "),a=o.indexOf("="),i=xc(a<0?o:o.slice(0,a)),l=a<0?null:xc(o.slice(a+1));if(i in t){var c=t[i];nc(c)||(c=t[i]=[c]),c.push(l)}else t[i]=l}return t}function fu(e){var t="",n=function(n){var r=e[n];if(n=bc(n).replace(ic,"%3D"),null==r)return void 0!==r&&(t+=(t.length?"&":"")+n),1;(nc(r)?r.map((function(e){return e&&bc(e)})):[r&&bc(r)]).forEach((function(e){void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))};for(var r in e)n(r);return t}function du(e){var t={};for(var n in e){var r=e[n];void 0!==r&&(t[n]=nc(r)?r.map((function(e){return null==e?null:""+e})):null==r?r:""+r)}return t}var pu=Symbol(""),vu=Symbol(""),hu=Symbol(""),mu=Symbol(""),gu=Symbol("");function bu(){var e=[];return{add:function(t){return e.push(t),function(){var n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:function(){return e.slice()},reset:function(){e=[]}}}function yu(e,t,n,r,o){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:function(e){return e()},i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return function(){return new Promise((function(l,c){var u=function(e){var a;!1===e?c(Hc(4,{from:n,to:t})):e instanceof Error?c(e):"string"==typeof(a=e)||a&&"object"===b(a)?c(Hc(2,{from:t,to:e})):(i&&r.enterCallbacks[o]===i&&"function"==typeof e&&i.push(e),l())},s=a((function(){return e.call(r&&r.instances[o],t,n,u)})),f=Promise.resolve(s);e.length<3&&(f=f.then(u)),f.catch((function(e){return c(e)}))}))}}function xu(e,t,n,r){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:function(e){return e()},i=[],l=y(e);try{var c=function(){var e=o.value,l=function(o){var l=e.components[o];if("beforeRouteEnter"!==t&&!e.instances[o])return 1;if(Zl(l)){var c=(l.__vccOpts||l)[t];c&&i.push(yu(c,n,r,e,o,a))}else{var u=l();i.push((function(){return u.then((function(i){if(!i)throw new Error("Couldn't resolve component \"".concat(o,'" at "').concat(e.path,'"'));var l,c=(l=i).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&Zl(l.default)?i.default:i;e.mods[o]=i,e.components[o]=c;var u=(c.__vccOpts||c)[t];return u&&yu(u,n,r,e,o,a)()}))}))}};for(var c in e.components)l(c)};for(l.s();!(o=l.n()).done;)c()}catch(u){l.e(u)}finally{l.f()}return i}function wu(e){var t=qr(hu),n=qr(mu),r=ha((function(){var n=Vt(e.to);return t.resolve(n)})),o=ha((function(){var e=r.value.matched,t=e.length,o=e[t-1],a=n.matched;if(!o||!a.length)return-1;var i=a.findIndex(Sc.bind(null,o));if(i>-1)return i;var l=ku(e[t-2]);return t>1&&ku(o)===l&&a[a.length-1].path!==l?a.findIndex(Sc.bind(null,e[t-2])):i})),a=ha((function(){return o.value>-1&&function(e,t){var n,r=function(){var n=t[o],r=e[o];if("string"==typeof n){if(n!==r)return{v:!1}}else if(!nc(r)||r.length!==n.length||n.some((function(e,t){return e!==r[t]})))return{v:!1}};for(var o in t)if(n=r())return n.v;return!0}(n.params,r.value.params)})),i=ha((function(){return o.value>-1&&o.value===n.matched.length-1&&Cc(n.params,r.value.params)}));return{route:r,href:ha((function(){return r.value.href})),isActive:a,isExactActive:i,navigate:function(){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){var t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})){var n=t[Vt(e.replace)?"replace":"push"](Vt(e.to)).catch(tc);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition((function(){return n})),n}return Promise.resolve()}}}var _u=Nn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:wu,setup:function(e,t){var n=t.slots,r=St(wu(e)),o=qr(hu).options,a=ha((function(){return v(v({},Su(e.activeClass,o.linkActiveClass,"router-link-active"),r.isActive),Su(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active"),r.isExactActive)}));return function(){var t,o=n.default&&(1===(t=n.default(r)).length?t[0]:t);return e.custom?o:ma("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:a.value},o)}}});function ku(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}var Su=function(e,t,n){return null!=e?e:null!=t?t:n};function Cu(e,t){if(!e)return null;var n=e(t);return 1===n.length?n[0]:n}var ju=Nn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup:function(e,t){var n=t.attrs,r=t.slots,o=qr(gu),a=ha((function(){return e.route||o.value})),i=qr(vu,0),l=ha((function(){for(var e,t=Vt(i),n=a.value.matched;(e=n[t])&&!e.components;)t++;return t})),c=ha((function(){return a.value.matched[l.value]}));Ur(vu,ha((function(){return l.value+1}))),Ur(pu,c),Ur(gu,a);var u=Ft();return fo((function(){return[u.value,c.value,e.name]}),(function(e,t){var n=m(e,3),r=n[0],o=n[1],a=n[2],i=m(t,3),l=i[0],c=i[1];i[2];o&&(o.instances[a]=r,c&&c!==o&&r&&r===l&&(o.leaveGuards.size||(o.leaveGuards=c.leaveGuards),o.updateGuards.size||(o.updateGuards=c.updateGuards))),!r||!o||c&&Sc(o,c)&&l||(o.enterCallbacks[a]||[]).forEach((function(e){return e(r)}))}),{flush:"post"}),function(){var t=a.value,o=e.name,i=c.value,l=i&&i.components[o];if(!l)return Cu(r.default,{Component:l,route:t});var s=i.props[o],f=s?!0===s?t.params:"function"==typeof s?s(t):s:null,d=ma(l,Ql({},f,n,{onVnodeUnmounted:function(e){e.component.isUnmounted&&(i.instances[o]=null)},ref:u}));return Cu(r.default,{Component:d,route:t})||d}}});var Ou=[{path:"/",redirect:"/login"},{path:"/status",name:"Status",component:function(){return Xl((function(){return n.import("./status-legacy.62e90d26.js")}),void 0,n.meta.url)}},{path:"/verify",name:"verify",component:function(){return Xl((function(){return n.import("./verify-legacy.ccee1121.js")}),void 0,n.meta.url)}},{path:"/appverify",name:"appverify",component:function(){return Xl((function(){return n.import("./appverify-legacy.6d884c0d.js")}),void 0,n.meta.url)}},{path:"/login",name:"Login",component:function(){return Xl((function(){return n.import("./index-legacy.a87e50ba.js")}),void 0,n.meta.url)}},{path:"/client",name:"Client",component:function(){return Xl((function(){return n.import("./index-legacy.7d930172.js")}),void 0,n.meta.url)},children:[{path:"/client/login",name:"ClientNewLogin",component:function(){return Xl((function(){return n.import("./login-legacy.17ab53e1.js")}),void 0,n.meta.url)}},{path:"/client/main",name:"ClientMain",component:function(){return Xl((function(){return n.import("./main-legacy.8df5e5bc.js")}),void 0,n.meta.url)}},{path:"/client/setting",name:"ClientSetting",component:function(){return Xl((function(){return n.import("./setting-legacy.6b8603bc.js")}),void 0,n.meta.url)}}]},{path:"/clientLogin",name:"ClientLogin",component:function(){return Xl((function(){return n.import("./clientLogin-legacy.8b5e6af3.js")}),void 0,n.meta.url)}},{path:"/downloadWin",name:"downloadWin",component:function(){return Xl((function(){return n.import("./downloadWin-legacy.562868f6.js")}),void 0,n.meta.url)}},{path:"/wx_oauth_callback",name:"WxOAuthCallback",component:function(){return Xl((function(){return n.import("./wx_oauth_callback-legacy.c08df0b1.js")}),void 0,n.meta.url)}},{path:"/oauth2_result",name:"OAuth2Result",component:function(){return Xl((function(){return n.import("./oauth2_result-legacy.e5a95024.js")}),void 0,n.meta.url)}},{path:"/oauth2_premises",name:"OAuth2Premises",component:function(){return Xl((function(){return n.import("./oauth2_premises-legacy.2c1bcd1f.js")}),void 0,n.meta.url)}}],zu=function(e){var t=nu(e.routes,e),n=e.parseQuery||su,r=e.stringifyQuery||fu,o=e.history,a=bu(),i=bu(),l=bu(),c=Nt(Ec,!0),u=Ec;Yl&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");var s,f=ec.bind(null,(function(e){return""+e})),d=ec.bind(null,yc),p=ec.bind(null,xc);function v(e,a){if(a=Ql({},a||c.value),"string"==typeof e){var i=_c(n,e,a.path),l=t.resolve({path:i.path},a),u=o.createHref(i.fullPath);return Ql(i,l,{params:p(l.params),hash:xc(i.hash),redirectedFrom:void 0,href:u})}var s;if(null!=e.path)s=Ql({},e,{path:_c(n,e.path,a.path).path});else{var v=Ql({},e.params);for(var h in v)null==v[h]&&delete v[h];s=Ql({},e,{params:d(v)}),a.params=d(a.params)}var m=t.resolve(s,a),g=e.hash||"";m.params=f(p(m.params));var b,y=function(e,t){var n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(r,Ql({},e,{hash:(b=g,gc(b).replace(pc,"{").replace(hc,"}").replace(fc,"^")),path:m.path})),x=o.createHref(y);return Ql({fullPath:y,hash:g,query:r===fu?du(e.query):e.query||{}},m,{redirectedFrom:void 0,href:x})}function h(e){return"string"==typeof e?_c(n,e,c.value.path):Ql({},e)}function g(e,t){if(u!==e)return Hc(8,{from:t,to:e})}function x(e){return _(e)}function w(e){var t=e.matched[e.matched.length-1];if(t&&t.redirect){var n=t.redirect,r="function"==typeof n?n(e):n;return"string"==typeof r&&((r=r.includes("?")||r.includes("#")?r=h(r):{path:r}).params={}),Ql({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function _(e,t){var n=u=v(e),o=c.value,a=e.state,i=e.force,l=!0===e.replace,s=w(n);if(s)return _(Ql(h(s),{state:"object"===b(s)?Ql({},a,s.state):a,force:i,replace:l}),t||n);var f,d=n;return d.redirectedFrom=t,!i&&function(e,t,n){var r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&Sc(t.matched[r],n.matched[o])&&Cc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(r,o,n)&&(f=Hc(16,{to:d,from:o}),R(o,o,!0,!1)),(f?Promise.resolve(f):C(d,o)).catch((function(e){return Wc(e)?Wc(e,2)?e:L(e):I(e,d,o)})).then((function(e){if(e){if(Wc(e,2))return _(Ql({replace:l},h(e.to),{state:"object"===b(e.to)?Ql({},a,e.to.state):a,force:i}),t||d)}else e=O(d,o,!0,l,a);return j(d,o,e),e}))}function k(e,t){var n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function S(e){var t=F.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function C(e,t){var n,r=function(e,t){for(var n=[],r=[],o=[],a=Math.max(t.matched.length,e.matched.length),i=function(){var a=t.matched[l];a&&(e.matched.find((function(e){return Sc(e,a)}))?r.push(a):n.push(a));var i=e.matched[l];i&&(t.matched.find((function(e){return Sc(e,i)}))||o.push(i))},l=0;l<a;l++)i();return[n,r,o]}(e,t),o=m(r,3),l=o[0],c=o[1],u=o[2];n=xu(l.reverse(),"beforeRouteLeave",e,t);var s,f=y(l);try{for(f.s();!(s=f.n()).done;){s.value.leaveGuards.forEach((function(r){n.push(yu(r,e,t))}))}}catch(p){f.e(p)}finally{f.f()}var d=k.bind(null,e,t);return n.push(d),B(n).then((function(){n=[];var r,o=y(a.list());try{for(o.s();!(r=o.n()).done;){var i=r.value;n.push(yu(i,e,t))}}catch(p){o.e(p)}finally{o.f()}return n.push(d),B(n)})).then((function(){n=xu(c,"beforeRouteUpdate",e,t);var r,o=y(c);try{for(o.s();!(r=o.n()).done;){r.value.updateGuards.forEach((function(r){n.push(yu(r,e,t))}))}}catch(p){o.e(p)}finally{o.f()}return n.push(d),B(n)})).then((function(){n=[];var r,o=y(u);try{for(o.s();!(r=o.n()).done;){var a=r.value;if(a.beforeEnter)if(nc(a.beforeEnter)){var i,l=y(a.beforeEnter);try{for(l.s();!(i=l.n()).done;){var c=i.value;n.push(yu(c,e,t))}}catch(p){l.e(p)}finally{l.f()}}else n.push(yu(a.beforeEnter,e,t))}}catch(p){o.e(p)}finally{o.f()}return n.push(d),B(n)})).then((function(){return e.matched.forEach((function(e){return e.enterCallbacks={}})),(n=xu(u,"beforeRouteEnter",e,t,S)).push(d),B(n)})).then((function(){n=[];var r,o=y(i.list());try{for(o.s();!(r=o.n()).done;){var a=r.value;n.push(yu(a,e,t))}}catch(p){o.e(p)}finally{o.f()}return n.push(d),B(n)})).catch((function(e){return Wc(e,8)?e:Promise.reject(e)}))}function j(e,t,n){l.list().forEach((function(r){return S((function(){return r(e,t,n)}))}))}function O(e,t,n,r,a){var i=g(e,t);if(i)return i;var l=t===Ec,u=Yl?history.state:{};n&&(r||l?o.replace(e.fullPath,Ql({scroll:l&&u&&u.scroll},a)):o.push(e.fullPath,a)),c.value=e,R(e,t,n,l),L()}function z(){s||(s=o.listen((function(e,t,n){if(N.listening){var r=v(e),a=w(r);if(a)_(Ql(a,{replace:!0,force:!0}),r).catch(tc);else{u=r;var i,l,s=c.value;Yl&&(i=Mc(s.fullPath,n.delta),l=Rc(),Fc.set(i,l)),C(r,s).catch((function(e){return Wc(e,12)?e:Wc(e,2)?(_(Ql(h(e.to),{force:!0}),r).then((function(e){Wc(e,20)&&!n.delta&&n.type===zc.pop&&o.go(-1,!1)})).catch(tc),Promise.reject()):(n.delta&&o.go(-n.delta,!1),I(e,r,s))})).then((function(e){(e=e||O(r,s,!1))&&(n.delta&&!Wc(e,8)?o.go(-n.delta,!1):n.type===zc.pop&&Wc(e,20)&&o.go(-1,!1)),j(r,s,e)})).catch(tc)}}})))}var A,E=bu(),T=bu();function I(e,t,n){L(e);var r=T.list();return r.length?r.forEach((function(r){return r(e,t,n)})):console.error(e),Promise.reject(e)}function L(e){return A||(A=!e,z(),E.list().forEach((function(t){var n=m(t,2),r=n[0],o=n[1];return e?o(e):r()})),E.reset()),e}function R(t,n,r,o){var a=e.scrollBehavior;if(!Yl||!a)return Promise.resolve();var i,l,c=!r&&(i=Mc(t.fullPath,0),l=Fc.get(i),Fc.delete(i),l)||(o||!r)&&history.state&&history.state.scroll||null;return cn().then((function(){return a(t,n,c)})).then((function(e){return e&&Pc(e)})).catch((function(e){return I(e,t,n)}))}var P,M=function(e){return o.go(e)},F=new Set,N={currentRoute:c,listening:!0,addRoute:function(e,n){var r,o;return qc(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)},removeRoute:function(e){var n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((function(e){return e.record}))},resolve:v,options:e,push:x,replace:function(e){return x(Ql(h(e),{replace:!0}))},go:M,back:function(){return M(-1)},forward:function(){return M(1)},beforeEach:a.add,beforeResolve:i.add,afterEach:l.add,onError:T.add,isReady:function(){return A&&c.value!==Ec?Promise.resolve():new Promise((function(e,t){E.add([e,t])}))},install:function(e){e.component("RouterLink",_u),e.component("RouterView",ju),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:function(){return Vt(c)}}),Yl&&!P&&c.value===Ec&&(P=!0,x(o.location).catch((function(e){})));var t={},n=function(e){Object.defineProperty(t,e,{get:function(){return c.value[e]},enumerable:!0})};for(var r in Ec)n(r);e.provide(hu,this),e.provide(mu,Ct(t)),e.provide(gu,c);var a=e.unmount;F.add(e),e.unmount=function(){F.delete(e),F.size<1&&(u=Ec,s&&s(),s=null,c.value=Ec,P=!1,A=!1),a()}}};function B(e){return e.reduce((function(e,t){return e.then((function(){return S(t)}))}),Promise.resolve())}return N}({history:function(e){return(e=location.host?e||location.pathname+location.search:"").includes("#")||(e+="#"),Uc(e)}(),routes:Ou});zu.beforeEach(function(){var t=r(e().m((function t(n,r,o){var a,i,l,c,u,s;return e().w((function(e){for(;;)switch(e.n){case 0:if(a=window.location.href,i=window.location.origin,logger.log("Router beforeEach Current URL:",a,"origin:",i),"file:"===document.location.protocol||a.startsWith(i+"/#/")){e.n=1;break}return console.log("Hash is not at the correct position"),-1===(l=a.indexOf("#"))?c="".concat(i,"/#").concat(a.substring(i.length)):(u=a.substring(i.length,l),s=a.substring(l),u=u.replace(/^\/\?/,"&"),console.log("beforeHash:",u),console.log("afterHash:",s),c="".concat(i,"/").concat(s).concat(u)),console.log("Final new URL:",c),window.location.replace(c),e.a(2);case 1:logger.log("Proceeding with normal navigation"),o();case 2:return e.a(2)}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}());var Au=t("Y","undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{});function Eu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Tu={exports:{}},Iu={exports:{}},Lu=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},Ru=Lu,Pu=Object.prototype.toString;function Mu(e){return"[object Array]"===Pu.call(e)}function Fu(e){return void 0===e}function Nu(e){return null!==e&&"object"===b(e)}function Bu(e){return"[object Function]"===Pu.call(e)}function Vu(e,t){if(null!=e)if("object"!==b(e)&&(e=[e]),Mu(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var Uu={isArray:Mu,isArrayBuffer:function(e){return"[object ArrayBuffer]"===Pu.call(e)},isBuffer:function(e){return null!==e&&!Fu(e)&&null!==e.constructor&&!Fu(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:Nu,isUndefined:Fu,isDate:function(e){return"[object Date]"===Pu.call(e)},isFile:function(e){return"[object File]"===Pu.call(e)},isBlob:function(e){return"[object Blob]"===Pu.call(e)},isFunction:Bu,isStream:function(e){return Nu(e)&&Bu(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:Vu,merge:function e(){var t={};function n(n,r){"object"===b(t[r])&&"object"===b(n)?t[r]=e(t[r],n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)Vu(arguments[r],n);return t},deepMerge:function e(){var t={};function n(n,r){"object"===b(t[r])&&"object"===b(n)?t[r]=e(t[r],n):"object"===b(n)?t[r]=e({},n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)Vu(arguments[r],n);return t},extend:function(e,t,n){return Vu(t,(function(t,r){e[r]=n&&"function"==typeof t?Ru(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}},qu=Uu;function Du(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var $u=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(qu.isURLSearchParams(t))r=t.toString();else{var o=[];qu.forEach(t,(function(e,t){null!=e&&(qu.isArray(e)?t+="[]":e=[e],qu.forEach(e,(function(e){qu.isDate(e)?e=e.toISOString():qu.isObject(e)&&(e=JSON.stringify(e)),o.push(Du(t)+"="+Du(e))})))})),r=o.join("&")}if(r){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e},Hu=Uu;function Wu(){this.handlers=[]}Wu.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Wu.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Wu.prototype.forEach=function(e){Hu.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var Gu,Ku,Ju=Wu,Xu=Uu;function Yu(){return Ku?Gu:(Ku=1,Gu=function(e){return!(!e||!e.__CANCEL__)})}var Zu,Qu,es,ts,ns,rs,os,as,is,ls,cs,us,ss,fs,ds,ps,vs,hs,ms,gs,bs=Uu;function ys(){return Qu||(Qu=1,Zu=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}),Zu}function xs(){if(ts)return es;ts=1;var e=ys();return es=function(t,n,r,o,a){var i=new Error(t);return e(i,n,r,o,a)},es}function ws(){if(rs)return ns;rs=1;var e=xs();return ns=function(t,n,r){var o=r.config.validateStatus;!o||o(r.status)?t(r):n(e("Request failed with status code "+r.status,r.config,null,r.request,r))},ns}function _s(){return as||(as=1,os=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}),os}function ks(){return ls||(ls=1,is=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}),is}function Ss(){if(us)return cs;us=1;var e=_s(),t=ks();return cs=function(n,r){return n&&!e(r)?t(n,r):r},cs}function Cs(){if(fs)return ss;fs=1;var e=Uu,t=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];return ss=function(n){var r,o,a,i={};return n?(e.forEach(n.split("\n"),(function(n){if(a=n.indexOf(":"),r=e.trim(n.substr(0,a)).toLowerCase(),o=e.trim(n.substr(a+1)),r){if(i[r]&&t.indexOf(r)>=0)return;i[r]="set-cookie"===r?(i[r]?i[r]:[]).concat([o]):i[r]?i[r]+", "+o:o}})),i):i}}function js(){if(ps)return ds;ps=1;var e=Uu;return ds=e.isStandardBrowserEnv()?function(){var t,n=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var t=e;return n&&(r.setAttribute("href",t),t=r.href),r.setAttribute("href",t),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return t=o(window.location.href),function(n){var r=e.isString(n)?o(n):n;return r.protocol===t.protocol&&r.host===t.host}}():function(){return!0},ds}function Os(){if(hs)return vs;hs=1;var e=Uu;return vs=e.isStandardBrowserEnv()?{write:function(t,n,r,o,a,i){var l=[];l.push(t+"="+encodeURIComponent(n)),e.isNumber(r)&&l.push("expires="+new Date(r).toGMTString()),e.isString(o)&&l.push("path="+o),e.isString(a)&&l.push("domain="+a),!0===i&&l.push("secure"),document.cookie=l.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}}function zs(){if(gs)return ms;gs=1;var e=Uu,t=ws(),n=$u,r=Ss(),o=Cs(),a=js(),i=xs();return ms=function(l){return new Promise((function(c,u){var s=l.data,f=l.headers;e.isFormData(s)&&delete f["Content-Type"];var d=new XMLHttpRequest;if(l.auth){var p=l.auth.username||"",v=l.auth.password||"";f.Authorization="Basic "+btoa(p+":"+v)}var h=r(l.baseURL,l.url);if(d.open(l.method.toUpperCase(),n(h,l.params,l.paramsSerializer),!0),d.timeout=l.timeout,d.onreadystatechange=function(){if(d&&4===d.readyState&&(0!==d.status||d.responseURL&&0===d.responseURL.indexOf("file:"))){var e="getAllResponseHeaders"in d?o(d.getAllResponseHeaders()):null,n={data:l.responseType&&"text"!==l.responseType?d.response:d.responseText,status:d.status,statusText:d.statusText,headers:e,config:l,request:d};t(c,u,n),d=null}},d.onabort=function(){d&&(u(i("Request aborted",l,"ECONNABORTED",d)),d=null)},d.onerror=function(){u(i("Network Error",l,null,d)),d=null},d.ontimeout=function(){var e="timeout of "+l.timeout+"ms exceeded";l.timeoutErrorMessage&&(e=l.timeoutErrorMessage),u(i(e,l,"ECONNABORTED",d)),d=null},e.isStandardBrowserEnv()){var m=Os(),g=(l.withCredentials||a(h))&&l.xsrfCookieName?m.read(l.xsrfCookieName):void 0;g&&(f[l.xsrfHeaderName]=g)}if("setRequestHeader"in d&&e.forEach(f,(function(e,t){void 0===s&&"content-type"===t.toLowerCase()?delete f[t]:d.setRequestHeader(t,e)})),e.isUndefined(l.withCredentials)||(d.withCredentials=!!l.withCredentials),l.responseType)try{d.responseType=l.responseType}catch(rp){if("json"!==l.responseType)throw rp}"function"==typeof l.onDownloadProgress&&d.addEventListener("progress",l.onDownloadProgress),"function"==typeof l.onUploadProgress&&d.upload&&d.upload.addEventListener("progress",l.onUploadProgress),l.cancelToken&&l.cancelToken.promise.then((function(e){d&&(d.abort(),u(e),d=null)})),void 0===s&&(s=null),d.send(s)}))},ms}var As=Uu,Es=function(e,t){bs.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},Ts={"Content-Type":"application/x-www-form-urlencoded"};function Is(e,t){!As.isUndefined(e)&&As.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var Ls,Rs={adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(Ls=zs()),Ls),transformRequest:[function(e,t){return Es(t,"Accept"),Es(t,"Content-Type"),As.isFormData(e)||As.isArrayBuffer(e)||As.isBuffer(e)||As.isStream(e)||As.isFile(e)||As.isBlob(e)?e:As.isArrayBufferView(e)?e.buffer:As.isURLSearchParams(e)?(Is(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):As.isObject(e)?(Is(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(rp){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};Rs.headers={common:{Accept:"application/json, text/plain, */*"}},As.forEach(["delete","get","head"],(function(e){Rs.headers[e]={}})),As.forEach(["post","put","patch"],(function(e){Rs.headers[e]=As.merge(Ts)}));var Ps=Rs,Ms=Uu,Fs=function(e,t,n){return Xu.forEach(n,(function(n){e=n(e,t)})),e},Ns=Yu(),Bs=Ps;function Vs(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var Us,qs,Ds,$s,Hs,Ws,Gs=Uu,Ks=function(e,t){t=t||{};var n={},r=["url","method","params","data"],o=["headers","auth","proxy"],a=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];Gs.forEach(r,(function(e){void 0!==t[e]&&(n[e]=t[e])})),Gs.forEach(o,(function(r){Gs.isObject(t[r])?n[r]=Gs.deepMerge(e[r],t[r]):void 0!==t[r]?n[r]=t[r]:Gs.isObject(e[r])?n[r]=Gs.deepMerge(e[r]):void 0!==e[r]&&(n[r]=e[r])})),Gs.forEach(a,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])}));var i=r.concat(o).concat(a),l=Object.keys(t).filter((function(e){return-1===i.indexOf(e)}));return Gs.forEach(l,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])})),n},Js=Uu,Xs=$u,Ys=Ju,Zs=function(e){return Vs(e),e.headers=e.headers||{},e.data=Fs(e.data,e.headers,e.transformRequest),e.headers=Ms.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),Ms.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||Bs.adapter)(e).then((function(t){return Vs(e),t.data=Fs(t.data,t.headers,e.transformResponse),t}),(function(t){return Ns(t)||(Vs(e),t&&t.response&&(t.response.data=Fs(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},Qs=Ks;function ef(e){this.defaults=e,this.interceptors={request:new Ys,response:new Ys}}function tf(){if(qs)return Us;function e(e){this.message=e}return qs=1,e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,Us=e}ef.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=Qs(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[Zs,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},ef.prototype.getUri=function(e){return e=Qs(this.defaults,e),Xs(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},Js.forEach(["delete","get","head","options"],(function(e){ef.prototype[e]=function(t,n){return this.request(Js.merge(n||{},{method:e,url:t}))}})),Js.forEach(["post","put","patch"],(function(e){ef.prototype[e]=function(t,n,r){return this.request(Js.merge(r||{},{method:e,url:t,data:n}))}}));var nf=Uu,rf=Lu,of=ef,af=Ks;function lf(e){var t=new of(e),n=rf(of.prototype.request,t);return nf.extend(n,of.prototype,t),nf.extend(n,t),n}var cf=lf(Ps);cf.Axios=of,cf.create=function(e){return lf(af(cf.defaults,e))},cf.Cancel=tf(),cf.CancelToken=function(){if($s)return Ds;$s=1;var e=tf();function t(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var n;this.promise=new Promise((function(e){n=e}));var r=this;t((function(t){r.reason||(r.reason=new e(t),n(r.reason))}))}return t.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},t.source=function(){var e;return{token:new t((function(t){e=t})),cancel:e}},Ds=t}(),cf.isCancel=Yu(),cf.all=function(e){return Promise.all(e)},cf.spread=Ws?Hs:(Ws=1,Hs=function(e){return function(t){return e.apply(null,t)}}),Iu.exports=cf,Iu.exports.default=cf,function(e){e.exports=Iu.exports}(Tu);var uf=t("q",Eu(Tu.exports));var sf,ff,df=t("K",{all:sf=sf||new Map,on:function(e,t){var n=sf.get(e);n?n.push(t):sf.set(e,[t])},off:function(e,t){var n=sf.get(e);n&&(t?n.splice(n.indexOf(t)>>>0,1):sf.set(e,[]))},emit:function(e,t){var n=sf.get(e);n&&n.slice().map((function(e){e(t)})),(n=sf.get("*"))&&n.slice().map((function(n){n(e,t)}))}});ff=document.location.protocol+"//"+document.location.host;var pf,vf=t("x",uf.create({baseURL:ff,timeout:99999})),hf=0,mf=function(){--hf<=0&&(clearTimeout(pf),df.emit("closeLoading"))};vf.interceptors.request.use((function(e){var t=Ed();return e.donNotShowLoading||(hf++,pf&&clearTimeout(pf),pf=setTimeout((function(){hf>0&&df.emit("showLoading")}),400)),e.url.match(/(\w+\/){0}\w+/)[0],e.headers=a({"Content-Type":"application/json"},e.headers),t.token.accessToken&&(e.url.includes("refresh_token")?e.headers.Authorization="".concat(t.token.tokenType," ").concat(t.token.refreshToken):e.headers.Authorization="".concat(t.token.tokenType," ").concat(t.token.accessToken)),e}),(function(e){return mf(),Dl({showClose:!0,message:e,type:"error"}),e})),vf.interceptors.response.use((function(e){var t=Ed();return mf(),e.headers["new-token"]&&t.setToken(e.headers["new-token"]),logger.log("请求：",{request_url:e.config.url,response:e}),200===e.status||204===e.status||201===e.status||"true"===e.headers.success?e:(Dl({showClose:!0,message:e.data.msg||decodeURI(e.headers.msg),type:"error"}),e.data.data&&e.data.data.reload&&(t.token="",localStorage.clear(),zu.push({name:"Login",replace:!0})),e.data.msg?e.data:e)}),(function(e){var t=Ed();if(mf(),e.response){switch(e.response.status){case 500:Hl.confirm("\n        <p>检测到接口错误".concat(e,'</p>\n        <p>错误码<span style="color:red"> 500 </span>：此类错误内容常见于后台panic，请先查看后台日志，如果影响您正常使用可强制登出清理缓存</p>\n        '),"接口报错",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"清理缓存",cancelButtonText:"取消"}).then((function(){Ed().token="",localStorage.clear(),zu.push({name:"Login",replace:!0})}));break;case 404:Dl({showClose:!0,message:e.response.data.error,type:"error"});break;case 401:t.authFailureLoginOut();var n=window.localStorage.getItem("refresh_times")||0;window.localStorage.setItem("refresh_times",Number(n)+1);break;default:console.log(e.response),Dl({showClose:!0,message:e.response.data.errorMessage||e.response.data.error,type:"error"})}return e}Hl.confirm("\n        <p>检测到请求错误</p>\n        <p>".concat(e,"</p>\n        "),"请求报错",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"稍后重试",cancelButtonText:"取消"})}));var gf="default";try{if("file:"!==document.location.protocol){var bf=new XMLHttpRequest;bf.open("GET",document.location,!1),bf.send(null),gf=bf.getResponseHeader("X-Corp-ID")||"default"}}catch(op){console.warn("无法获取 X-Corp-ID header，使用默认值:",op),gf="default"}var yf,xf,wf=function(e){return vf({url:"/auth/login/v1/user",method:"post",data:JSON.stringify(e)})},_f=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/users/").concat(e),method:"delete"})},kf=function(e){return vf({url:"/user/setSelfInfo",method:"put",data:e})},Sf=function(e){var t=e.id;return delete e.id,vf({url:"/auth/admin/realms/".concat(gf,"/users/").concat(t),method:"put",data:e})},Cf=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/roles"),method:"get",data:e})},jf=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/users/").concat(e,"/groups"),method:"get"})},Of=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/groups"),method:"get",params:e})},zf=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/groups/count"),method:"get",params:e})},Af=function(e,t){return vf({url:"/auth/admin/realms/".concat(gf,"/groups/").concat(e,"/members"),method:"get",params:t})},Ef=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/groups/").concat(e),method:"delete"})},Tf=function(e){return vf({url:"/auth/admin/realms/".concat(gf,"/users"),method:"post",data:e})},If=function(e){return yf=e},Lf=Symbol();function Rf(e){return e&&"object"===b(e)&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}!function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"}(xf||(xf={}));var Pf=function(){};function Mf(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Pf;e.push(t);var o,a=function(){var n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};return!n&&be()&&(o=a,se&&se.cleanups.push(o)),a}function Ff(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];e.slice().forEach((function(e){e.apply(void 0,n)}))}var Nf=function(e){return e()},Bf=Symbol(),Vf=Symbol();function Uf(e,t){for(var n in e instanceof Map&&t instanceof Map?t.forEach((function(t,n){return e.set(n,t)})):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e),t)if(t.hasOwnProperty(n)){var r=t[n],o=e[n];Rf(o)&&Rf(r)&&e.hasOwnProperty(n)&&!Mt(r)&&!zt(r)?e[n]=Uf(o,r):e[n]=r}return e}var qf=Symbol();var Df=Object.assign;function $f(e,t,n,r){var o=t.state,a=t.actions,i=t.getters,l=n.state.value[e];return Hf(e,(function(){l||(n.state.value[e]=o?o():{});var t=function(e){var t=O(e)?new Array(e.length):{};for(var n in e)t[n]=$t(e,n);return t}(n.state.value[e]);return Df(t,a,Object.keys(i||{}).reduce((function(t,r){return t[r]=Lt(ha((function(){If(n);var t=n._s.get(e);return i[r].call(t,t)}))),t}),{}))}),t,n,r,!0)}function Hf(e,t){var n,r,o,a,i,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},c=arguments.length>3?arguments[3]:void 0,u=arguments.length>5?arguments[5]:void 0,s=Df({actions:{}},l),f={deep:!0},d=[],p=[],v=c.state.value[e];function h(t){var n;r=o=!1,"function"==typeof t?(t(c.state.value[e]),n={type:xf.patchFunction,storeId:e,events:a}):(Uf(c.state.value[e],t),n={type:xf.patchObject,payload:t,storeId:e,events:a});var l=i=Symbol();cn().then((function(){i===l&&(r=!0)})),o=!0,Ff(d,n,c.state.value[e])}u||v||(c.state.value[e]={}),Ft({});var m=u?function(){var e=l.state,t=e?e():{};this.$patch((function(e){Df(e,t)}))}:Pf;var g=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(Bf in t)return t[Vf]=n,t;var r=function(){If(c);var n,o=Array.from(arguments),a=[],i=[];Ff(p,{args:o,name:r[Vf],store:y,after:function(e){a.push(e)},onError:function(e){i.push(e)}});try{n=t.apply(this&&this.$id===e?this:y,o)}catch(op){throw Ff(i,op),op}return n instanceof Promise?n.then((function(e){return Ff(a,e),e})).catch((function(e){return Ff(i,e),Promise.reject(e)})):(Ff(a,n),n)};return r[Bf]=!0,r[Vf]=n,r},b={_p:c,$id:e,$onAction:Mf.bind(null,p),$patch:h,$reset:m,$subscribe:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=Mf(d,t,i.detached,(function(){return u()})),u=n.run((function(){return fo((function(){return c.state.value[e]}),(function(n){("sync"===i.flush?o:r)&&t({storeId:e,type:xf.direct,events:a},n)}),Df({},f,i))}));return l},$dispose:function(){n.stop(),d=[],p=[],c._s.delete(e)}},y=St(b);c._s.set(e,y);var x,w,_=(c._a&&c._a.runWithContext||Nf)((function(){return c._e.run((function(){return(n=ge()).run((function(){return t({action:g})}))}))}));for(var k in _){var S=_[k];if(Mt(S)&&(!Mt(w=S)||!w.effect)||zt(S))u||(!v||Rf(x=S)&&x.hasOwnProperty(qf)||(Mt(S)?S.value=v[k]:Uf(S,v[k])),c.state.value[e][k]=S);else if("function"==typeof S){var C=g(S,k);_[k]=C,s.actions[k]=S}}return Df(y,_),Df(It(y),_),Object.defineProperty(y,"$state",{get:function(){return c.state.value[e]},set:function(e){h((function(t){Df(t,e)}))}}),c._p.forEach((function(e){Df(y,n.run((function(){return e({store:y,app:c._a,pinia:c,options:s})})))})),v&&u&&l.hydrate&&l.hydrate(y.$state,v),r=!0,o=!0,y}
/*! #__NO_SIDE_EFFECTS__ */function Wf(e,t,n){var r,o,a="function"==typeof t;function i(e,n){return(e=e||(!!(ta||mn||Vr)?qr(Lf,null):null))&&If(e),(e=yf)._s.has(r)||(a?Hf(r,t,o,e):$f(r,o,e)),e._s.get(r)}return"string"==typeof e?(r=e,o=a?n:t):(o=e,r=e.id),i.$id=r,i}var Gf=Object.assign({"../view/app/index.vue":function(){return Xl((function(){return n.import("./index-legacy.7191d780.js")}),void 0,n.meta.url)},"../view/client/download.vue":function(){return Xl((function(){return n.import("./download-legacy.10480f59.js")}),void 0,n.meta.url)},"../view/client/header.vue":function(){return Xl((function(){return n.import("./header-legacy.306ae93b.js")}),void 0,n.meta.url)},"../view/client/index.vue":function(){return Xl((function(){return n.import("./index-legacy.7d930172.js")}),void 0,n.meta.url)},"../view/client/login.vue":function(){return Xl((function(){return n.import("./login-legacy.17ab53e1.js")}),void 0,n.meta.url)},"../view/client/main.vue":function(){return Xl((function(){return n.import("./main-legacy.8df5e5bc.js")}),void 0,n.meta.url)},"../view/client/menu.vue":function(){return Xl((function(){return n.import("./menu-legacy.c2e930e8.js")}),void 0,n.meta.url)},"../view/client/setting.vue":function(){return Xl((function(){return n.import("./setting-legacy.6b8603bc.js")}),void 0,n.meta.url)},"../view/error/index.vue":function(){return Xl((function(){return n.import("./index-legacy.0663c08d.js")}),void 0,n.meta.url)},"../view/error/reload.vue":function(){return Xl((function(){return n.import("./reload-legacy.18e2e98d.js")}),void 0,n.meta.url)},"../view/layout/aside/asideComponent/asyncSubmenu.vue":function(){return Xl((function(){return n.import("./asyncSubmenu-legacy.c048302e.js")}),void 0,n.meta.url)},"../view/layout/aside/asideComponent/index.vue":function(){return Xl((function(){return n.import("./index-legacy.b21accac.js")}),void 0,n.meta.url)},"../view/layout/aside/asideComponent/menuItem.vue":function(){return Xl((function(){return n.import("./menuItem-legacy.56ba1b2b.js")}),void 0,n.meta.url)},"../view/layout/aside/historyComponent/history.vue":function(){return Xl((function(){return n.import("./history-legacy.f711b78e.js")}),void 0,n.meta.url)},"../view/layout/aside/index.vue":function(){return Xl((function(){return n.import("./index-legacy.02aa8fce.js")}),void 0,n.meta.url)},"../view/layout/bottomInfo/bottomInfo.vue":function(){return Xl((function(){return n.import("./bottomInfo-legacy.7c1c30d1.js")}),void 0,n.meta.url)},"../view/layout/index.vue":function(){return Xl((function(){return n.import("./index-legacy.71f83ddc.js")}),void 0,n.meta.url)},"../view/layout/screenfull/index.vue":function(){return Xl((function(){return n.import("./index-legacy.115218cc.js")}),void 0,n.meta.url)},"../view/layout/search/search.vue":function(){return Xl((function(){return n.import("./search-legacy.d0b65db9.js")}),void 0,n.meta.url)},"../view/layout/setting/index.vue":function(){return Xl((function(){return n.import("./index-legacy.60b84ec8.js")}),void 0,n.meta.url)},"../view/login/clientLogin.vue":function(){return Xl((function(){return n.import("./clientLogin-legacy.8b5e6af3.js")}),void 0,n.meta.url)},"../view/login/dingtalk/dingtalk.vue":function(){return Xl((function(){return n.import("./dingtalk-legacy.f9340dd6.js")}),void 0,n.meta.url)},"../view/login/downloadWin.vue":function(){return Xl((function(){return n.import("./downloadWin-legacy.562868f6.js")}),void 0,n.meta.url)},"../view/login/feishu/feishu.vue":function(){return Xl((function(){return n.import("./feishu-legacy.e088a96b.js")}),void 0,n.meta.url)},"../view/login/index.vue":function(){return Xl((function(){return n.import("./index-legacy.a87e50ba.js")}),void 0,n.meta.url)},"../view/login/localLogin/localLogin.vue":function(){return Xl((function(){return n.import("./localLogin-legacy.32a3220e.js")}),void 0,n.meta.url)},"../view/login/oauth2/oauth2.vue":function(){return Xl((function(){return n.import("./oauth2-legacy.ef052ac1.js")}),void 0,n.meta.url)},"../view/login/oauth2/oauth2_premises.vue":function(){return Xl((function(){return n.import("./oauth2_premises-legacy.2c1bcd1f.js")}),void 0,n.meta.url)},"../view/login/oauth2/oauth2_result.vue":function(){return Xl((function(){return n.import("./oauth2_result-legacy.e5a95024.js")}),void 0,n.meta.url)},"../view/login/secondaryAuth/secondaryAuth.vue":function(){return Xl((function(){return n.import("./secondaryAuth-legacy.2066388a.js")}),void 0,n.meta.url)},"../view/login/secondaryAuth/verifyCode.vue":function(){return Xl((function(){return n.import("./verifyCode-legacy.8e8d81ff.js")}),void 0,n.meta.url)},"../view/login/sms/sms.vue":function(){return Xl((function(){return n.import("./sms-legacy.10f365b2.js")}),void 0,n.meta.url)},"../view/login/verify.vue":function(){return Xl((function(){return n.import("./verify-legacy.ccee1121.js")}),void 0,n.meta.url)},"../view/login/wx/status.vue":function(){return Xl((function(){return n.import("./status-legacy.62e90d26.js")}),void 0,n.meta.url)},"../view/login/wx/wechat.vue":function(){return Xl((function(){return n.import("./wechat-legacy.6a493c96.js")}),void 0,n.meta.url)},"../view/login/wx/wx_oauth_callback.vue":function(){return Xl((function(){return n.import("./wx_oauth_callback-legacy.c08df0b1.js")}),void 0,n.meta.url)},"../view/resource/appverify.vue":function(){return Xl((function(){return n.import("./appverify-legacy.6d884c0d.js")}),void 0,n.meta.url)},"../view/routerHolder.vue":function(){return Xl((function(){return n.import("./routerHolder-legacy.9fe805e4.js")}),void 0,n.meta.url)}}),Kf=Object.assign({}),Jf=function(e){e.forEach((function(e){e.component?"view"===e.component.split("/")[0]?e.component=Xf(Gf,e.component):"plugin"===e.component.split("/")[0]&&(e.component=Xf(Kf,e.component)):delete e.component,e.children&&Jf(e.children)}))};function Xf(e,t){return e[Object.keys(e).filter((function(e){return e.replace("../","")===t}))[0]]}var Yf=[],Zf=[],Qf=[],ed={},td=function(e,t){e&&e.forEach((function(e){e.children&&!e.children.every((function(e){return e.hidden}))||"404"===e.name||e.hidden||Yf.push({label:e.meta.title,value:e.name}),e.meta.btns=e.btns,e.meta.hidden=e.hidden,!0===e.meta.defaultMenu?Zf.push(a(a({},e),{},{path:"/".concat(e.path)})):(t[e.name]=e,e.children&&e.children.length>0&&td(e.children,t))}))},nd=function(e){e&&e.forEach((function(e){(e.children&&e.children.some((function(e){return e.meta.keepAlive}))||e.meta.keepAlive)&&e.component&&e.component().then((function(t){Qf.push(t.default.name),ed[e.name]=t.default.name})),e.children&&e.children.length>0&&nd(e.children)}))},rd=t("S",Wf("router",(function(){var t=Ft([]);df.on("setKeepAlive",(function(e){var n=[];e.forEach((function(e){ed[e.name]&&n.push(ed[e.name])})),t.value=Array.from(new Set(n))}));var n=Ft([]),o=Ft(Yf),a={},i=function(){var t=r(e().m((function t(){var r,i,l;return e().w((function(e){for(;;)switch(e.n){case 0:return r=[{path:"/layout",name:"layout",component:"view/layout/index.vue",meta:{title:"底层layout"},children:[]},{path:"/appverify",name:"appverify",component:"view/resource/appverify.vue",meta:{title:"appverify"},children:[]}],e.n=1,new Promise((function(e,t){e({code:0,data:{menus:[{ID:9,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"clientLogin",name:"clientLogin",hidden:!0,component:"view/login/clientLogin.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"客户端登陆",topTitle:"客户端登陆",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"9",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"dashboard",name:"dashboard",hidden:!1,component:"view/app/index.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"应用门户",topTitle:"",icon:"icon-zuhu-yingyongliebiao",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"download",name:"download",hidden:!1,component:"view/client/download.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"客户端下载",topTitle:"客户端下载",icon:"icon-zuhu-kehuduanxiazai",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:8,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"person",name:"person",hidden:!0,component:"view/person/person.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"个人信息",topTitle:"个人信息",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"8",children:null,parameters:[],btns:null}]},msg:"获取成功"})}));case 1:return i=e.v,(l=i.data.menus)&&l.push({path:"404",name:"404",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/index.vue"},{path:"reload",name:"Reload",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/reload.vue"}),td(l,a),r[0].children=l,0!==Zf.length&&r.push.apply(r,Zf),r.push({path:"/:catchAll(.*)",redirect:"/layout/404"}),Jf(r),nd(l),n.value=r,o.value=Yf,logger.log({asyncRouters:n.value}),logger.log({routerList:o.value}),e.a(2,!0)}}),t)})));return function(){return t.apply(this,arguments)}}();return{asyncRouters:n,routerList:o,keepAliveRouters:t,SetAsyncRouter:i,routeMap:a}}))),od={},ad=Object.prototype.hasOwnProperty;function id(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(rp){return null}}function ld(e){try{return encodeURIComponent(e)}catch(rp){return null}}od.stringify=function(e,t){t=t||"";var n,r,o=[];for(r in"string"!=typeof t&&(t="?"),e)if(ad.call(e,r)){if((n=e[r])||null!=n&&!isNaN(n)||(n=""),r=ld(r),n=ld(n),null===r||null===n)continue;o.push(r+"="+n)}return o.length?t+o.join("&"):""},od.parse=function(e){for(var t,n=/([^=?#&]+)=?([^&]*)/g,r={};t=n.exec(e);){var o=id(t[1]),a=id(t[2]);null===o||null===a||o in r||(r[o]=a)}return r};var cd=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e},ud=od,sd=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,fd=/[\n\r\t]/g,dd=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,pd=/:\d+$/,vd=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,hd=/^[a-zA-Z]:/;function md(e){return(e||"").toString().replace(sd,"")}var gd=[["#","hash"],["?","query"],function(e,t){return xd(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],bd={hash:1,query:1};function yd(e){var t,n=("undefined"!=typeof window?window:void 0!==Au?Au:"undefined"!=typeof self?self:{}).location||{},r={},o=b(e=e||n);if("blob:"===e.protocol)r=new _d(unescape(e.pathname),{});else if("string"===o)for(t in r=new _d(e,{}),bd)delete r[t];else if("object"===o){for(t in e)t in bd||(r[t]=e[t]);void 0===r.slashes&&(r.slashes=dd.test(e.href))}return r}function xd(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function wd(e,t){e=(e=md(e)).replace(fd,""),t=t||{};var n,r=vd.exec(e),o=r[1]?r[1].toLowerCase():"",a=!!r[2],i=!!r[3],l=0;return a?i?(n=r[2]+r[3]+r[4],l=r[2].length+r[3].length):(n=r[2]+r[4],l=r[2].length):i?(n=r[3]+r[4],l=r[3].length):n=r[4],"file:"===o?l>=2&&(n=n.slice(2)):xd(o)?n=r[4]:o?a&&(n=n.slice(2)):l>=2&&xd(t.protocol)&&(n=r[4]),{protocol:o,slashes:a||xd(o),slashesCount:l,rest:n}}function _d(e,t,n){if(e=(e=md(e)).replace(fd,""),!(this instanceof _d))return new _d(e,t,n);var r,o,a,i,l,c,u=gd.slice(),s=b(t),f=this,d=0;for("object"!==s&&"string"!==s&&(n=t,t=null),n&&"function"!=typeof n&&(n=ud.parse),r=!(o=wd(e||"",t=yd(t))).protocol&&!o.slashes,f.slashes=o.slashes||r&&t.slashes,f.protocol=o.protocol||t.protocol||"",e=o.rest,("file:"===o.protocol&&(2!==o.slashesCount||hd.test(e))||!o.slashes&&(o.protocol||o.slashesCount<2||!xd(f.protocol)))&&(u[3]=[/(.*)/,"pathname"]);d<u.length;d++)"function"!=typeof(i=u[d])?(a=i[0],c=i[1],a!=a?f[c]=e:"string"==typeof a?~(l="@"===a?e.lastIndexOf(a):e.indexOf(a))&&("number"==typeof i[2]?(f[c]=e.slice(0,l),e=e.slice(l+i[2])):(f[c]=e.slice(l),e=e.slice(0,l))):(l=a.exec(e))&&(f[c]=l[1],e=e.slice(0,l.index)),f[c]=f[c]||r&&i[3]&&t[c]||"",i[4]&&(f[c]=f[c].toLowerCase())):e=i(e,f);n&&(f.query=n(f.query)),r&&t.slashes&&"/"!==f.pathname.charAt(0)&&(""!==f.pathname||""!==t.pathname)&&(f.pathname=function(e,t){if(""===e)return t;for(var n=(t||"/").split("/").slice(0,-1).concat(e.split("/")),r=n.length,o=n[r-1],a=!1,i=0;r--;)"."===n[r]?n.splice(r,1):".."===n[r]?(n.splice(r,1),i++):i&&(0===r&&(a=!0),n.splice(r,1),i--);return a&&n.unshift(""),"."!==o&&".."!==o||n.push(""),n.join("/")}(f.pathname,t.pathname)),"/"!==f.pathname.charAt(0)&&xd(f.protocol)&&(f.pathname="/"+f.pathname),cd(f.port,f.protocol)||(f.host=f.hostname,f.port=""),f.username=f.password="",f.auth&&(~(l=f.auth.indexOf(":"))?(f.username=f.auth.slice(0,l),f.username=encodeURIComponent(decodeURIComponent(f.username)),f.password=f.auth.slice(l+1),f.password=encodeURIComponent(decodeURIComponent(f.password))):f.username=encodeURIComponent(decodeURIComponent(f.auth)),f.auth=f.password?f.username+":"+f.password:f.username),f.origin="file:"!==f.protocol&&xd(f.protocol)&&f.host?f.protocol+"//"+f.host:"null",f.href=f.toString()}_d.prototype={set:function(e,t,n){var r=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(n||ud.parse)(t)),r[e]=t;break;case"port":r[e]=t,cd(t,r.protocol)?t&&(r.host=r.hostname+":"+t):(r.host=r.hostname,r[e]="");break;case"hostname":r[e]=t,r.port&&(t+=":"+r.port),r.host=t;break;case"host":r[e]=t,pd.test(t)?(t=t.split(":"),r.port=t.pop(),r.hostname=t.join(":")):(r.hostname=t,r.port="");break;case"protocol":r.protocol=t.toLowerCase(),r.slashes=!n;break;case"pathname":case"hash":if(t){var o="pathname"===e?"/":"#";r[e]=t.charAt(0)!==o?o+t:t}else r[e]=t;break;case"username":case"password":r[e]=encodeURIComponent(t);break;case"auth":var a=t.indexOf(":");~a?(r.username=t.slice(0,a),r.username=encodeURIComponent(decodeURIComponent(r.username)),r.password=t.slice(a+1),r.password=encodeURIComponent(decodeURIComponent(r.password))):r.username=encodeURIComponent(decodeURIComponent(t))}for(var i=0;i<gd.length;i++){var l=gd[i];l[4]&&(r[l[1]]=r[l[1]].toLowerCase())}return r.auth=r.password?r.username+":"+r.password:r.username,r.origin="file:"!==r.protocol&&xd(r.protocol)&&r.host?r.protocol+"//"+r.host:"null",r.href=r.toString(),r},toString:function(e){e&&"function"==typeof e||(e=ud.stringify);var t,n=this,r=n.host,o=n.protocol;o&&":"!==o.charAt(o.length-1)&&(o+=":");var a=o+(n.protocol&&n.slashes||xd(n.protocol)?"//":"");return n.username?(a+=n.username,n.password&&(a+=":"+n.password),a+="@"):n.password?(a+=":"+n.password,a+="@"):"file:"!==n.protocol&&xd(n.protocol)&&!r&&"/"!==n.pathname&&(a+="@"),(":"===r[r.length-1]||pd.test(n.hostname)&&!n.port)&&(r+=":"),a+=r+n.pathname,(t="object"===b(n.query)?e(n.query):n.query)&&(a+="?"!==t.charAt(0)?"?"+t:t),n.hash&&(a+=n.hash),a}},_d.extractProtocol=wd,_d.location=yd,_d.trimLeft=md,_d.qs=ud;var kd=_d,Sd=(t("X",(function(e){return vf({url:"/auth/login/v1/cache",method:"post",data:e})})),function(e){return vf({url:"/auth/login/v1/user/third",method:"post",data:e})}),Cd=function(e,t,n){return vf({url:"/auth/login/v1/callback/".concat(e),method:"get",params:{code:t,state:n}})},jd=function(){return vf({url:"/auth/authz/v1/user/refresh_token",method:"get",donNotShowLoading:!0})},Od=!1;function zd(e,t){setInterval((function(){Od||(Od=!0,jd().then((function(n){console.log("---refreshToken--"),200===n.status?-1===n.data.code?(console.log("刷新token失败，退出至登录"),e()):(console.log("刷新token成功，保存token"),t(n.data)):(console.log("刷新token失败，退出至登录"),e())})).catch((function(){console.log("---refreshToken err--"),e()})).finally((function(){Od=!1})))}),6e5)}t("n",(function(e){return vf({url:"/auth/login/v1/send_sms",method:"post",data:e})}));var Ad=t("v",(function(e){return vf({url:"/auth/login/v1/sms_verify",method:"post",data:e})})),Ed=(t("s",(function(e){return vf({url:"/auth/login/v1/sms_key",method:"post",data:e})})),t("b",Wf("user",(function(){var t=Ft(null),n=Ft({id:"",name:"",groupId:"",groupName:"",corpId:"",sourceId:"",phone:"",email:"",avatar:"",roles:[],sideMode:"dark",activeColor:"#4D70FF",baseColor:"#fff"}),o=Ft(window.localStorage.getItem("token")||""),i=Ft(window.localStorage.getItem("loginType")||"");try{o.value=o.value?JSON.parse(o.value):""}catch(rp){console.log("---清理localStorage中的token---"),window.localStorage.removeItem("token"),o.value=""}var l=function(e){o.value=e},c=function(e){i.value=e},u=function(){var t=r(e().m((function t(r){var o;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,vf({url:"/auth/user/v1/login_user",method:"get"});case 1:return 200===(o=e.v).status&&(t=o.data.userInfo,n.value=t),e.a(2,o)}var t}),t)})));return function(e){return t.apply(this,arguments)}}(),s=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Sf(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),f=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,_f(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),d=function(){var n=r(e().m((function n(r,o,a){var i,s,f,d,p,h,m,g,b,y,x,w,_,k,S,C,j,O,z,A,E,T,I,L,R,P,M;return e().w((function(e){for(;;)switch(e.n){case 0:t.value=Ul.service({fullscreen:!0,text:"登录中，请稍候..."}),e.p=1,i="",M=o,e.n="qiyewx"===M||"qiyewx_oauth"===M||"feishu"===M||"dingtalk"===M||"oauth2"===M||"cas"===M||"msad"===M||"ldap"===M?2:"accessory"===M?4:6;break;case 2:return e.n=3,Sd(r);case 3:return i=e.v,c(a),e.a(3,8);case 4:return e.n=5,Ad(r);case 5:return i=e.v,e.a(3,8);case 6:return e.n=7,wf(r);case 7:return i=e.v,c(a),e.a(3,8);case 8:if(s=i.data.msg,200!==i.status){e.n=20;break}if(-1!==i.data.code&&1!==(null===(f=i.data)||void 0===f||null===(f=f.data)||void 0===f?void 0:f.status)){e.n=9;break}return Dl({showClose:!0,message:s,type:"error"}),t.value.close(),e.a(2,{code:-1});case 9:if(!i.data.data){e.n=11;break}if(!i.data.data.secondary){e.n=10;break}return t.value.close(),e.a(2,{isSecondary:!0,secondary:i.data.data.secondary,uniqKey:i.data.data.uniqKey,contactType:i.data.data.contactType,hasContactInfo:i.data.data.hasContactInfo,secondaryType:i.data.secondaryType,userName:i.data.data.userName,user_id:i.data.data.userID});case 10:l(i.data.data);case 11:return e.n=12,u();case 12:return zd(v,l),h=rd(),e.n=13,h.SetAsyncRouter();case 13:h.asyncRouters.forEach((function(e){zu.addRoute(e)})),m=window.location.href.replace(/#/g,"&"),g=kd(m,!0),b={},y=null,x=null;try{(w=localStorage.getItem("client_params"))&&(_=JSON.parse(w),y=_.type,x=_.wp)}catch(rp){console.warn("LoginIn: 获取localStorage参数失败:",rp)}if(k=window.location.search,S=new URLSearchParams(k),S.get("type"),!(null!==(d=g.query)&&void 0!==d&&d.redirect||null!==(p=g.query)&&void 0!==p&&p.redirect_url)){e.n=16;break}if(O="",null!==(C=g.query)&&void 0!==C&&C.redirect?O=(null===(z=g.query)||void 0===z?void 0:z.redirect.indexOf("?"))>-1?null===(A=g.query)||void 0===A?void 0:A.redirect.substring((null===(E=g.query)||void 0===E?void 0:E.redirect.indexOf("?"))+1):"":null!==(j=g.query)&&void 0!==j&&j.redirect_url&&(O=(null===(T=g.query)||void 0===T?void 0:T.redirect_url.indexOf("?"))>-1?null===(I=g.query)||void 0===I?void 0:I.redirect_url.substring((null===(L=g.query)||void 0===L?void 0:L.redirect_url.indexOf("?"))+1):""),O.split("&").forEach((function(e){var t=e.split("=");b[t[0]]=t[1]})),y&&(b.type=y),x&&(b.wp=x),t.value.close(),window.localStorage.setItem("refresh_times",0),"qiyewx_oauth"!==o){e.n=14;break}return e.a(2,!0);case 14:return window.location.href=(null===(R=g.query)||void 0===R?void 0:R.redirect)||(null===(P=g.query)||void 0===P?void 0:P.redirect_url),e.a(2,!0);case 15:e.n=17;break;case 16:b={type:y||g.query.type},(x||g.query.wp)&&(b.wp=x||g.query.wp);case 17:return g.query.wp&&(b.wp=g.query.wp),e.n=18,zu.push({name:"dashboard",query:b});case 18:return t.value.close(),e.a(2,!0);case 19:e.n=21;break;case 20:Dl({showClose:!0,message:s,type:"error"}),t.value.close();case 21:e.n=23;break;case 22:e.p=22,e.v,Dl({showClose:!0,message:"服务异常，请联系管理员！",type:"error"}),t.value.close();case 23:return e.a(2)}}),n,null,[[1,22]])})));return function(e,t,r){return n.apply(this,arguments)}}(),p=function(){var n=r(e().m((function n(r,o,a){var i,c,s;return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,t.value=Ul.service({fullscreen:!0,text:"处理登录中..."}),e.n=1,Cd(r,o,a);case 1:if(200!==(i=e.v).status||!i.data){e.n=4;break}if(!(c=i.data).needSecondary){e.n=2;break}return t.value.close(),e.a(2,{isSecondary:!0,uniqKey:c.uniqKey});case 2:if(!c.token){e.n=4;break}return l({accessToken:c.token,refreshToken:c.refresh_token,expireIn:c.expires_in,tokenType:c.token_type||"Bearer"}),e.n=3,u();case 3:return t.value.close(),e.a(2,!0);case 4:return t.value.close(),e.a(2,!1);case 5:return e.p=5,s=e.v,console.error("OAuth2登录处理失败:",s),t.value.close(),Dl({showClose:!0,message:s.message||"登录失败，请重试",type:"error"}),e.a(2,!1)}}),n,null,[[0,5]])})));return function(e,t,r){return n.apply(this,arguments)}}(),v=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return zd(),e.n=1,vf({url:"/auth/user/v1/logout",method:"post",data:""});case 1:n=e.v,console.log("登出res",n),200===n.status?-1===n.data.code?Dl({showClose:!0,message:n.data.msg,type:"error"}):n.data.redirectUrl?(console.log("检测到OAuth2登出URL，正在重定向:",n.data.redirectUrl),m(),window.location.href=n.data.redirectUrl):(zu.push({name:"Login",replace:!0}),m()):Dl({showClose:!0,message:"服务异常，请联系管理员！",type:"error"});case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),h=function(){var t=r(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:zd(),m(),zu.push({name:"Login",replace:!0}),window.location.reload();case 1:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),m=function(){var t=r(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:sessionStorage.clear(),window.localStorage.removeItem("userInfo"),window.localStorage.removeItem("token"),o.value="";case 1:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),g=function(){var t=r(e().m((function t(r){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,kf({sideMode:r});case 1:0===e.v.code&&(n.value.sideMode=r,Dl({type:"success",message:"设置成功"}));case 2:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),b=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Cf(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),y=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,jf(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),x=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,getUserRole(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),w=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Of(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=r(e().m((function t(){var n;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,vf({url:"/console/v1/user/director_types",method:"get",params:void 0});case 1:if(0!==(n=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,n)}}),t)})));return function(){return t.apply(this,arguments)}}(),k=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,zf(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),S=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t=n,vf({url:"/auth/admin/realms/".concat(gf,"/groups/").concat(t),method:"get"});case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}var t}),t)})));return function(e){return t.apply(this,arguments)}}(),C=function(){var t=r(e().m((function t(n,r){var o;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Af(n,r);case 1:if(0!==(o=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,o)}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),j=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o=void 0,o=(t=n).id,delete t.id,vf({url:"/auth/admin/realms/".concat(gf,"/groups/").concat(o,"/children"),method:"post",data:t});case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}var t,o}),t)})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,delete(t=n).id,vf({url:"/auth/admin/realms/".concat(gf,"/groups"),method:"post",data:t});case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}var t}),t)})));return function(e){return t.apply(this,arguments)}}(),z=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,o=void 0,o=(t=n).id,delete t.id,vf({url:"/auth/admin/realms/".concat(gf,"/groups/").concat(o),method:"put",data:t});case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}var t,o}),t)})));return function(e){return t.apply(this,arguments)}}(),A=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Ef(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),E=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return delete n.id,e.n=1,Tf(n);case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}}),t)})));return function(e){return t.apply(this,arguments)}}(),T=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t=n,vf({url:"/auth/admin/realms/".concat(gf,"/users"),method:"get",params:t});case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}var t}),t)})));return function(e){return t.apply(this,arguments)}}(),I=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t=n,vf({url:"/auth/admin/realms/".concat(gf,"/users/count"),method:"get",params:t});case 1:if(0!==(r=e.v).code){e.n=2;break}return e.a(2,"");case 2:return e.a(2,r)}var t}),t)})));return function(e){return t.apply(this,arguments)}}();return fo((function(){return o.value}),(function(){window.localStorage.setItem("token",JSON.stringify(o.value))})),fo((function(){return i.value}),(function(){window.localStorage.setItem("loginType",i.value)})),{userInfo:n,token:o,loginType:i,NeedInit:function(){o.value="",window.localStorage.removeItem("token"),zu.push({name:"Init",replace:!0})},ResetUserInfo:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};n.value=a(a({},n.value),e)},GetUserInfo:u,LoginIn:d,LoginOut:v,authFailureLoginOut:h,changeSideMode:g,mode:"dark",sideMode:"#273444",setToken:l,baseColor:"#fff",activeColor:"#4D70FF",loadingInstance:t,ClearStorage:m,GetOrganize:w,GetOrganizeDetails:S,UpdateOrganize:z,CreateOrganize:O,DelOrganize:A,AddSubgroup:j,CreateUser:E,GetUserList:T,GetUserListCount:I,UpdateUser:s,DeleteUser:f,GetRoles:b,GetGroupMembers:C,GetOrganizeCount:k,GetUserOrigin:_,GetUserGroups:y,GetUserRole:x,handleOAuth2Login:p}})))),Td=Wf("app",{state:function(){return{isClient:!1,clientType:"windows"}},actions:{setIsClient:function(){var e=/QtWebEngine/.test(navigator.userAgent);e||urlHashParams&&urlHashParams.get("asec_client")&&(e=!0),this.isClient=e}}}),Id=t("Q",(function(e,t){var n=/\$\{(.+?)\}/,r=e.match(/\$\{(.+?)\}/g);return r&&r.forEach((function(r){var o=r.match(n)[1],a=t.params[o]||t.query[o];e=e.replace(r,a)})),e}));function Ld(e,t){if(e){var n=Id(e,t);return"".concat(n," - ").concat(Kl.appName)}return"".concat(Kl.appName)}var Rd={exports:{}};
/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
             * @license MIT */!function(e){e.exports=function(){var e,t,n={version:"0.2.0"},r=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function o(e,t,n){return e<t?t:e>n?n:e}function a(e){return 100*(-1+e)}function i(e,t,n){var o;return(o="translate3d"===r.positionUsing?{transform:"translate3d("+a(e)+"%,0,0)"}:"translate"===r.positionUsing?{transform:"translate("+a(e)+"%,0)"}:{"margin-left":a(e)+"%"}).transition="all "+t+"ms "+n,o}n.configure=function(e){var t,n;for(t in e)void 0!==(n=e[t])&&e.hasOwnProperty(t)&&(r[t]=n);return this},n.status=null,n.set=function(e){var t=n.isStarted();e=o(e,r.minimum,1),n.status=1===e?null:e;var a=n.render(!t),u=a.querySelector(r.barSelector),s=r.speed,f=r.easing;return a.offsetWidth,l((function(t){""===r.positionUsing&&(r.positionUsing=n.getPositioningCSS()),c(u,i(e,s,f)),1===e?(c(a,{transition:"none",opacity:1}),a.offsetWidth,setTimeout((function(){c(a,{transition:"all "+s+"ms linear",opacity:0}),setTimeout((function(){n.remove(),t()}),s)}),s)):setTimeout(t,s)})),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var e=function(){setTimeout((function(){n.status&&(n.trickle(),e())}),r.trickleSpeed)};return r.trickle&&e(),this},n.done=function(e){return e||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(e){var t=n.status;return t?("number"!=typeof e&&(e=(1-t)*o(Math.random()*t,.1,.95)),t=o(t+e,0,.994),n.set(t)):n.start()},n.trickle=function(){return n.inc(Math.random()*r.trickleRate)},e=0,t=0,n.promise=function(r){return r&&"resolved"!==r.state()?(0===t&&n.start(),e++,t++,r.always((function(){0===--t?(e=0,n.done()):n.set((e-t)/e)})),this):this},n.render=function(e){if(n.isRendered())return document.getElementById("nprogress");s(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=r.template;var o,i=t.querySelector(r.barSelector),l=e?"-100":a(n.status||0),u=document.querySelector(r.parent);return c(i,{transition:"all 0 linear",transform:"translate3d("+l+"%,0,0)"}),r.showSpinner||(o=t.querySelector(r.spinnerSelector))&&p(o),u!=document.body&&s(u,"nprogress-custom-parent"),u.appendChild(t),t},n.remove=function(){f(document.documentElement,"nprogress-busy"),f(document.querySelector(r.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&p(e)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective"in e?"translate3d":t+"Transform"in e?"translate":"margin"};var l=function(){var e=[];function t(){var n=e.shift();n&&n(t)}return function(n){e.push(n),1==e.length&&t()}}(),c=function(){var e=["Webkit","O","Moz","ms"],t={};function n(e){return e.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(function(e,t){return t.toUpperCase()}))}function r(t){var n=document.body.style;if(t in n)return t;for(var r,o=e.length,a=t.charAt(0).toUpperCase()+t.slice(1);o--;)if((r=e[o]+a)in n)return r;return t}function o(e){return e=n(e),t[e]||(t[e]=r(e))}function a(e,t,n){t=o(t),e.style[t]=n}return function(e,t){var n,r,o=arguments;if(2==o.length)for(n in t)void 0!==(r=t[n])&&t.hasOwnProperty(n)&&a(e,n,r);else a(e,o[1],o[2])}}();function u(e,t){return("string"==typeof e?e:d(e)).indexOf(" "+t+" ")>=0}function s(e,t){var n=d(e),r=n+t;u(n,t)||(e.className=r.substring(1))}function f(e,t){var n,r=d(e);u(e,t)&&(n=r.replace(" "+t+" "," "),e.className=n.substring(1,n.length-1))}function d(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function p(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return n}()}(Rd);var Pd=Rd.exports,Md=function(e,t){return["/client","/client/login","/client/setting"].includes(e.path)?(logger.log("客户端直接返回"),!0):(logger.log("客户端查询登录状态:",e.path),{name:"ClientNewLogin",query:{redirect:e.href,asec_debug:logger.debug}})},Fd=0,Nd=["Login","Init","ClientLogin","Status","downloadWin","WxOAuthCallback","OAuth2Result","OAuth2Premises"],Bd=function(){var t=r(e().m((function t(n){var r;return e().w((function(e){for(;;)switch(e.n){case 0:return logger.log("----getRouter---"),r=rd(),e.n=1,r.SetAsyncRouter();case 1:return e.n=2,n.GetUserInfo();case 2:r.asyncRouters.forEach((function(e){zu.addRoute(e)}));case 3:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}();function Vd(e){return Ud.apply(this,arguments)}function Ud(){return(Ud=r(e().m((function t(n){var r,o;return e().w((function(e){for(;;)switch(e.n){case 0:if(!n.matched.some((function(e){return e.meta.keepAlive}))){e.n=5;break}if(!(n.matched&&n.matched.length>2)){e.n=5;break}r=1;case 1:if(!(r<n.matched.length)){e.n=5;break}if("layout"!==(o=n.matched[r-1]).name){e.n=2;break}return n.matched.splice(r,1),e.n=2,Vd(n);case 2:if("function"!=typeof o.components.default){e.n=4;break}return e.n=3,o.components.default();case 3:return e.n=4,Vd(n);case 4:r++,e.n=1;break;case 5:return e.a(2)}}),t)})))).apply(this,arguments)}var qd=function(t){return logger.log("socket连接开始"),new Promise((function(n,o){var a={action:2,msg:"",platform:document.location.hostname},i=Ft({}),l=Ft("ws://127.0.0.1:50001"),c=navigator.platform;0!==c.indexOf("Mac")&&"MacIntel"!==c||(l.value="wss://127.0.0.1:50001");var u=function(){var o=r(e().m((function o(){var c,u;return e().w((function(o){for(;;)switch(o.n){case 0:i.value=new WebSocket(l.value),u=function(){c=setTimeout((function(){console.log("WebSocket连接超时"),f(),n()}),2e3)},i.value.onopen=function(){logger.log("socket连接成功"),u(),s(JSON.stringify(a))},i.value.onmessage=function(){var o=r(e().m((function r(o){var a,i,l,u,s;return e().w((function(e){for(;;)switch(e.n){case 0:if(logger.log("-------e--------"),logger.log(JSON.parse(o.data)),clearTimeout(c),null==o||!o.data){e.n=11;break}if(e.p=1,(a=JSON.parse(o.data)).msg.token){e.n=2;break}return n(),e.a(2);case 2:return i={accessToken:a.msg.token,expireIn:3600,refreshToken:a.msg.refreshToken,refreshExpireIn:604800,tokenType:"Bearer"},e.n=3,t.setToken(i);case 3:return e.n=4,jd();case 4:if(200!==(l=e.v).status){e.n=8;break}if(!(null!=l&&null!==(u=l.data)&&void 0!==u&&u.code||-1!==(null==l||null===(s=l.data)||void 0===s?void 0:s.code))){e.n=7;break}return e.n=5,t.setToken(l.data);case 5:return e.n=6,t.GetUserInfo();case 6:n();case 7:n();case 8:n(),e.n=11;break;case 9:return e.p=9,e.v,e.n=10,f();case 10:n();case 11:return e.n=12,f();case 12:n();case 13:return e.a(2)}}),r,null,[[1,9]])})));return function(e){return o.apply(this,arguments)}}(),i.value.onerror=function(){console.log("socket连接错误"),clearTimeout(c),n()};case 1:return o.a(2)}}),o)})));return function(){return o.apply(this,arguments)}}(),s=function(e){i.value.send(e)},f=function(){logger.log("socket断开链接"),i.value.close()};logger.log("asecagent://?web=".concat(JSON.stringify(a))),u()}))};zu.beforeEach(function(){var t=r(e().m((function t(n,r){var o,i,l;return e().w((function(e){for(;;)switch(e.n){case 0:if(Pd.start(),!Td().isClient){e.n=1;break}return e.a(2,Md(n));case 1:return o=Ed(),n.meta.matched=g(n.matched),e.n=2,Vd(n);case 2:if(i=o.token,document.title=Ld(n.meta.title,n),"WxOAuthCallback"==n.name||"verify"==n.name?document.title="":document.title=Ld(n.meta.title,n),logger.log("路由参数：",{whiteList:Nd,to:n,from:r}),l=window.localStorage.getItem("refresh_times")||0,i&&'""'!==i||!(Number(l)<5)||"Login"===n.name){e.n=4;break}return e.n=3,qd(o);case 3:i=o.token;case 4:if(!Nd.includes(n.name)){e.n=12;break}if(!i||["downloadWin","Login","WxOAuthCallback","OAuth2Callback"].includes(n.name)){e.n=10;break}if(Fd||!(Nd.indexOf(r.name)<0)){e.n=6;break}return Fd++,e.n=5,Bd(o);case 5:logger.log("getRouter");case 6:if(!o.userInfo){e.n=7;break}return logger.log("dashboard"),e.a(2,{name:"dashboard"});case 7:return zd(),e.n=8,o.ClearStorage();case 8:return logger.log("强制退出账号"),e.a(2,{name:"Login",query:{redirect:document.location.hash}});case 9:e.n=11;break;case 10:return logger.log("直接返回"),e.a(2,!0);case 11:e.n=20;break;case 12:if(logger.log("不在白名单中:",i),!i){e.n=19;break}if(Fd||!(Nd.indexOf(r.name)<0)){e.n=16;break}return Fd++,e.n=13,Bd(o);case 13:if(logger.log("初始化动态路由:",o.token),!o.token){e.n=14;break}return logger.log("返回to"),e.a(2,a(a({},n),{},{replace:!1}));case 14:return logger.log("返回login"),e.a(2,{name:"Login",query:{redirect:n.href}});case 15:e.n=18;break;case 16:if(!n.matched.length){e.n=17;break}return zd(o.LoginOut,o.setToken),logger.log("返回refresh"),e.a(2,!0);case 17:return console.log("404:",n.matched),e.a(2,{path:"/layout/404"});case 18:e.n=20;break;case 19:return logger.log("不在白名单中并且未登录的时候"),e.a(2,{name:"Login",query:{redirect:document.location.hash}});case 20:return e.a(2)}}),t)})));return function(e,n){return t.apply(this,arguments)}}()),zu.afterEach((function(){Pd.done()})),zu.onError((function(){Pd.remove()}));var Dd,$d,Hd,Wd,Gd,Kd={install:function(e){var t=Ed();e.directive("auth",{mounted:function(e,n){var r=t.userInfo,o="";switch(Object.prototype.toString.call(n.value)){case"[object Array]":o="Array";break;case"[object String]":o="String";break;case"[object Number]":o="Number";break;default:o=""}if(""!==o){var a=n.value.toString().split(",").some((function(e){return Number(e)===r.id}));n.modifiers.not&&(a=!a),a||e.parentNode.removeChild(e)}else e.parentNode.removeChild(e)}})}},Jd=(Dd=ge(!0),$d=Dd.run((function(){return Ft({})})),Wd=[],Gd=Lt({install:function(e){If(Gd),Gd._a=e,e.provide(Lf,Gd),e.config.globalProperties.$pinia=Gd,Wd.forEach((function(e){return Hd.push(e)})),Wd=[]},use:function(e){return this._a?Hd.push(e):Wd.push(e),this},_p:Hd=[],_a:null,_e:Dd,_s:new Map,state:$d}),Gd),Xd={id:"app"};var Yd=Si({name:"App",created:function(){var e=qr("$keycloak");logger.log("App created: ",e)}},[["render",function(e,t,n,r,o,a){var i=pr("router-view");return To(),Po("div",Xd,[qo(i)])}]]);if(logger.log(navigator.userAgent),logger.log(document.location.href),Pd.configure({showSpinner:!1,ease:"ease",speed:500}),Pd.start(),/msie|trident/i.test(navigator.userAgent)){alert("\n    对不起，您正在使用的浏览器版本过低。\n    本网站不支持IE浏览器，请使用现代浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。\n  ")}var Zd=ki(Yd);Zd.config.productionTip=!1;var Qd=document.location.protocol+"//"+document.location.host;try{if("file:"!==document.location.protocol){var ep=new XMLHttpRequest;ep.open("GET",document.location,!1),ep.send(null),ep.getResponseHeader("X-Corp-ID")}}catch(op){console.warn("无法获取 X-Corp-ID header，使用默认值:",op)}var tp;tp=Qd+"/auth",logger.log("url:".concat(tp)),function(){if("undefined"!=typeof document){var e=document.createElement("div");e.innerHTML='\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="display: none;">\n  \x3c!-- 基础图标 --\x3e\n  <symbol id="icon-search" viewBox="0 0 1024 1024">\n    <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116 65.6-158.4C296 211.3 352.2 188 412 188s116 23.3 158.4 65.6C612.7 296 636 352.2 636 412s-23.3 116-65.6 158.4z"/>\n  </symbol>\n  \n  <symbol id="icon-plus" viewBox="0 0 1024 1024">\n    <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"/>\n  </symbol>\n  \n  <symbol id="icon-warning" viewBox="0 0 1024 1024">\n    <path d="M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"/>\n  </symbol>\n  \n  <symbol id="icon-document" viewBox="0 0 1024 1024">\n    <path d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z"/>\n  </symbol>\n  \n  \x3c!-- 项目特定图标 --\x3e\n  <symbol id="icon-jieru" viewBox="0 0 1024 1024">\n    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z"/>\n  </symbol>\n  \n  <symbol id="icon-shezhi" viewBox="0 0 1024 1024">\n    <path d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a449.4 449.4 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 28.9a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM512 701c-104.9 0-190-85.1-190-190s85.1-190 190-190 190 85.1 190 190-85.1 190-190 190z"/>\n  </symbol>\n  \n  <symbol id="icon-windows" viewBox="0 0 1024 1024">\n    <path d="M120.83 208.13l344.49-47.04v331.65H120.83V208.13zm0 607.78l344.49 47.04V531.3H120.83v284.61zm384.34 50.02L903.17 896V531.3H505.17v334.63zM505.17 128L903.17 96v435.3H505.17V128z"/>\n  </symbol>\n  \n  <symbol id="icon-mac" viewBox="0 0 1024 1024">\n    <path d="M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z"/>\n  </symbol>\n  \n  <symbol id="icon-ios" viewBox="0 0 1024 1024">\n    <path d="M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z"/>\n  </symbol>\n  \n  <symbol id="icon-android" viewBox="0 0 1024 1024">\n    <path d="M765.7 486.8L620.7 182.2c-3.4-7.2-11.1-11.7-19.5-11.7s-16.1 4.5-19.5 11.7L436.6 486.8a32.05 32.05 0 0 0 28.6 46.4c7.9 0 15.3-2.9 21.1-8.2l23.9-23.9h185.6l23.9 23.9c5.8 5.3 13.2 8.2 21.1 8.2 17.7 0 32-14.3 32-32 0-7.6-2.6-14.8-7.4-20.4zM334.1 548.9L149.9 757.1a32.06 32.06 0 0 0 0 45.3l208.2 208.2c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L195.2 757.1 403.4 548.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0z"/>\n  </symbol>\n  \n  <symbol id="icon-xiazai" viewBox="0 0 1024 1024">\n    <path d="M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"/>\n  </symbol>\n  \n  <symbol id="icon-expand" viewBox="0 0 1024 1024">\n    <path d="M342 88H120c-17.7 0-32 14.3-32 32v222c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v174c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32zM342 856h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16H342V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v222c0 17.7 14.3 32 32 32h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0c17.7 0 32-14.3 32-32V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v174H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222z"/>\n  </symbol>\n</svg>\n',e.style.display="none",document.body.insertBefore(e,document.body.firstChild)}}(),Zd.use(Jl).use(Jd).use(Kd).use(zu).use(Gl).mount("#app");var np=Td();np.setIsClient(),logger.log("是否是客户端:",np.isClient,"客户端类型:",np.clientType)}}}))}();
