/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as Xi,u as er,r as V,G as nr,h as tr,o as de,d as pe,e as ee,k as De,t as fe,j as or,w as ir,g as rr}from"./index.d0594432.js";const On="3.7.7",ar=On,$=typeof Buffer=="function",Ne=typeof TextDecoder=="function"?new TextDecoder:void 0,_e=typeof TextEncoder=="function"?new TextEncoder:void 0,sr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Y=Array.prototype.slice.call(sr),ne=(e=>{let n={};return e.forEach((i,r)=>n[i]=r),n})(Y),cr=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,S=String.fromCharCode.bind(String),Be=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),En=e=>e.replace(/=/g,"").replace(/[+\/]/g,n=>n=="+"?"-":"_"),Dn=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),Nn=e=>{let n,i,r,o,s="";const c=e.length%3;for(let a=0;a<e.length;){if((i=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");n=i<<16|r<<8|o,s+=Y[n>>18&63]+Y[n>>12&63]+Y[n>>6&63]+Y[n&63]}return c?s.slice(0,c-3)+"===".substring(c):s},ke=typeof btoa=="function"?e=>btoa(e):$?e=>Buffer.from(e,"binary").toString("base64"):Nn,ve=$?e=>Buffer.from(e).toString("base64"):e=>{let i=[];for(let r=0,o=e.length;r<o;r+=4096)i.push(S.apply(null,e.subarray(r,r+4096)));return ke(i.join(""))},oe=(e,n=!1)=>n?En(ve(e)):ve(e),lr=e=>{if(e.length<2){var n=e.charCodeAt(0);return n<128?e:n<2048?S(192|n>>>6)+S(128|n&63):S(224|n>>>12&15)+S(128|n>>>6&63)+S(128|n&63)}else{var n=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return S(240|n>>>18&7)+S(128|n>>>12&63)+S(128|n>>>6&63)+S(128|n&63)}},ur=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,_n=e=>e.replace(ur,lr),Me=$?e=>Buffer.from(e,"utf8").toString("base64"):_e?e=>ve(_e.encode(e)):e=>ke(_n(e)),G=(e,n=!1)=>n?En(Me(e)):Me(e),Le=e=>G(e,!0),dr=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,pr=e=>{switch(e.length){case 4:var n=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),i=n-65536;return S((i>>>10)+55296)+S((i&1023)+56320);case 3:return S((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return S((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},Bn=e=>e.replace(dr,pr),Mn=e=>{if(e=e.replace(/\s+/g,""),!cr.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let n,i="",r,o;for(let s=0;s<e.length;)n=ne[e.charAt(s++)]<<18|ne[e.charAt(s++)]<<12|(r=ne[e.charAt(s++)])<<6|(o=ne[e.charAt(s++)]),i+=r===64?S(n>>16&255):o===64?S(n>>16&255,n>>8&255):S(n>>16&255,n>>8&255,n&255);return i},be=typeof atob=="function"?e=>atob(Dn(e)):$?e=>Buffer.from(e,"base64").toString("binary"):Mn,Ln=$?e=>Be(Buffer.from(e,"base64")):e=>Be(be(e).split("").map(n=>n.charCodeAt(0))),Fn=e=>Ln(jn(e)),fr=$?e=>Buffer.from(e,"base64").toString("utf8"):Ne?e=>Ne.decode(Ln(e)):e=>Bn(be(e)),jn=e=>Dn(e.replace(/[-_]/g,n=>n=="-"?"+":"/")),me=e=>fr(jn(e)),gr=e=>{if(typeof e!="string")return!1;const n=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(n)||!/[^\s0-9a-zA-Z\-_]/.test(n)},Wn=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),Hn=function(){const e=(n,i)=>Object.defineProperty(String.prototype,n,Wn(i));e("fromBase64",function(){return me(this)}),e("toBase64",function(n){return G(this,n)}),e("toBase64URI",function(){return G(this,!0)}),e("toBase64URL",function(){return G(this,!0)}),e("toUint8Array",function(){return Fn(this)})},Un=function(){const e=(n,i)=>Object.defineProperty(Uint8Array.prototype,n,Wn(i));e("toBase64",function(n){return oe(this,n)}),e("toBase64URI",function(){return oe(this,!0)}),e("toBase64URL",function(){return oe(this,!0)})},vr=()=>{Hn(),Un()},mr={version:On,VERSION:ar,atob:be,atobPolyfill:Mn,btoa:ke,btoaPolyfill:Nn,fromBase64:me,toBase64:G,encode:G,encodeURI:Le,encodeURL:Le,utob:_n,btou:Bn,decode:me,isValid:gr,fromUint8Array:oe,toUint8Array:Fn,extendString:Hn,extendUint8Array:Un,extendBuiltins:vr};function w(){return w=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var r in i)({}).hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e},w.apply(null,arguments)}function Fe(e,n,i,r,o,s,c){try{var a=e[s](c),l=a.value}catch(d){return void i(d)}a.done?n(l):Promise.resolve(l).then(r,o)}function q(e){return function(){var n=this,i=arguments;return new Promise(function(r,o){var s=e.apply(n,i);function c(l){Fe(s,r,o,c,a,"next",l)}function a(l){Fe(s,r,o,c,a,"throw",l)}c(void 0)})}}var Vn={exports:{}},Pe={exports:{}};(function(e){function n(i,r){this.v=i,this.k=r}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Pe);var we={exports:{}},ye={exports:{}};(function(e){function n(i,r,o,s){var c=Object.defineProperty;try{c({},"",{})}catch{c=0}e.exports=n=function(l,d,u,p){if(d)c?c(l,d,{value:u,enumerable:!p,configurable:!p,writable:!p}):l[d]=u;else{var f=function(m,y){n(l,m,function(I){return this._invoke(m,y,I)})};f("next",0),f("throw",1),f("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,n(i,r,o,s)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(ye);(function(e){var n=ye.exports;function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var r,o,s=typeof Symbol=="function"?Symbol:{},c=s.iterator||"@@iterator",a=s.toStringTag||"@@toStringTag";function l(I,A,k,E){var T=A&&A.prototype instanceof u?A:u,N=Object.create(T.prototype);return n(N,"_invoke",function(K,Qi,Zi){var L,b,x,Q=0,Ee=Zi||[],U=!1,_={p:0,n:0,v:r,a:Z,f:Z.bind(r,4),d:function(R,B){return L=R,b=0,x=r,_.n=B,d}};function Z(D,R){for(b=D,x=R,o=0;!U&&Q&&!B&&o<Ee.length;o++){var B,O=Ee[o],ue=_.p,X=O[2];D>3?(B=X===R)&&(x=O[(b=O[4])?5:(b=3,3)],O[4]=O[5]=r):O[0]<=ue&&((B=D<2&&ue<O[1])?(b=0,_.v=R,_.n=O[1]):ue<X&&(B=D<3||O[0]>R||R>X)&&(O[4]=D,O[5]=R,_.n=X,b=0))}if(B||D>1)return d;throw U=!0,R}return function(D,R,B){if(Q>1)throw TypeError("Generator is already running");for(U&&R===1&&Z(R,B),b=R,x=B;(o=b<2?r:x)||!U;){L||(b?b<3?(b>1&&(_.n=-1),Z(b,x)):_.n=x:_.v=x);try{if(Q=2,L){if(b||(D="next"),o=L[D]){if(!(o=o.call(L,x)))throw TypeError("iterator result is not an object");if(!o.done)return o;x=o.value,b<2&&(b=0)}else b===1&&(o=L.return)&&o.call(L),b<2&&(x=TypeError("The iterator does not provide a '"+D+"' method"),b=1);L=r}else if((o=(U=_.n<0)?x:K.call(Qi,_))!==d)break}catch(O){L=r,b=1,x=O}finally{Q=1}}return{value:o,done:U}}}(I,k,E),!0),N}var d={};function u(){}function p(){}function f(){}o=Object.getPrototypeOf;var v=[][c]?o(o([][c]())):(n(o={},c,function(){return this}),o),m=f.prototype=u.prototype=Object.create(v);function y(I){return Object.setPrototypeOf?Object.setPrototypeOf(I,f):(I.__proto__=f,n(I,a,"GeneratorFunction")),I.prototype=Object.create(m),I}return p.prototype=f,n(m,"constructor",f),n(f,"constructor",p),p.displayName="GeneratorFunction",n(f,a,"GeneratorFunction"),n(m),n(m,a,"Generator"),n(m,c,function(){return this}),n(m,"toString",function(){return"[object Generator]"}),(e.exports=i=function(){return{w:l,m:y}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports})(we);var zn={exports:{}},Se={exports:{}},Ce={exports:{}};(function(e){var n=Pe.exports,i=ye.exports;function r(o,s){function c(l,d,u,p){try{var f=o[l](d),v=f.value;return v instanceof n?s.resolve(v.v).then(function(m){c("next",m,u,p)},function(m){c("throw",m,u,p)}):s.resolve(v).then(function(m){f.value=m,u(f)},function(m){return c("throw",m,u,p)})}catch(m){p(m)}}var a;this.next||(i(r.prototype),i(r.prototype,typeof Symbol=="function"&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),i(this,"_invoke",function(l,d,u){function p(){return new s(function(f,v){c(l,u,f,v)})}return a=a?a.then(p,p):p()},!0)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Ce);(function(e){var n=we.exports,i=Ce.exports;function r(o,s,c,a,l){return new i(n().w(o,s,c,a),l||Promise)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Se);(function(e){var n=Se.exports;function i(r,o,s,c,a){var l=n(r,o,s,c,a);return l.next().then(function(d){return d.done?d.value:l.next()})}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports})(zn);var Gn={exports:{}};(function(e){function n(i){var r=Object(i),o=[];for(var s in r)o.unshift(s);return function c(){for(;o.length;)if((s=o.pop())in r)return c.value=s,c.done=!1,c;return c.done=!0,c}}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Gn);var Jn={exports:{}},$n={exports:{}};(function(e){function n(i){return e.exports=n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,n(i)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})($n);(function(e){var n=$n.exports.default;function i(r){if(r!=null){var o=r[typeof Symbol=="function"&&Symbol.iterator||"@@iterator"],s=0;if(o)return o.call(r);if(typeof r.next=="function")return r;if(!isNaN(r.length))return{next:function(){return r&&s>=r.length&&(r=void 0),{value:r&&r[s++],done:!r}}}}throw new TypeError(n(r)+" is not iterable")}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports})(Jn);(function(e){var n=Pe.exports,i=we.exports,r=zn.exports,o=Se.exports,s=Ce.exports,c=Gn.exports,a=Jn.exports;function l(){var d=i(),u=d.m(l),p=(Object.getPrototypeOf?Object.getPrototypeOf(u):u.__proto__).constructor;function f(y){var I=typeof y=="function"&&y.constructor;return!!I&&(I===p||(I.displayName||I.name)==="GeneratorFunction")}var v={throw:1,return:2,break:3,continue:3};function m(y){var I,A;return function(k){I||(I={stop:function(){return A(k.a,2)},catch:function(){return k.v},abrupt:function(T,N){return A(k.a,v[T],N)},delegateYield:function(T,N,K){return I.resultName=N,A(k.d,a(T),K)},finish:function(T){return A(k.f,T)}},A=function(T,N,K){k.p=I.prev,k.n=I.next;try{return T(N,K)}finally{I.next=k.n}}),I.resultName&&(I[I.resultName]=k.v,I.resultName=void 0),I.sent=k.v,I.next=k.n;try{return y.call(this,I)}finally{k.p=I.prev,k.n=I.next}}}return(e.exports=l=function(){return{wrap:function(A,k,E,T){return d.w(m(A),k,E,T&&T.reverse())},isGeneratorFunction:f,mark:d.m,awrap:function(A,k){return new n(A,k)},AsyncIterator:s,async:function(A,k,E,T,N){return(f(k)?o:r)(m(A),k,E,T,N)},keys:c,values:a}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=l,e.exports.__esModule=!0,e.exports.default=e.exports})(Vn);var ie=Vn.exports(),M=ie;try{regeneratorRuntime=ie}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=ie:Function("r","regeneratorRuntime = r")(ie)}function re(e){return e==="undefined"}function Te(){return!re(typeof my)&&my!==null&&!re(typeof my.alert)}var C;(function(e){e.CANCEL="-1",e.SUCCESS="0",e.API_UNDEFINED="1",e.INVALID_PARAMS="2",e.UNKNOWN_ERROR="3",e.UNAUTHORIZED_CALL="4",e.WRONG_CORP_ID="5",e.CREATE_CHAT_FAILED="6",e.UNAUTHORIZED_API="7",e.INVALID_CORP_ID="8",e.SERVER_RESPONSE_ERROR="9",e.WRONG_DEVICE_INFO="10",e.UPLOAD_FAIL="11",e.PROCESS_FAIL="12",e.DUPLICATED_CALL="13",e.TOO_LARGE_PIC="14",e.REQUEST_REJECT_OR_INSECURE_REQUEST="15",e.PC_NOT_ALLOWED_TO_OPEN_SIDE_PANE_OR_MODAL="21",e.PC_CLOSE_SIDE_PANE_OR_MODAL="22",e.UNAUTHORIZED_PARAMS="23",e.GESTURE_PASSWORD_DOES_NOT_EXIST="24",e.NETWORK_ERROR="25"})(C||(C={}));var h;(function(e){e.MOBILE="mobile",e.PC="pc",e.MINI_APP="mini",e.UNKNOWN="unknown"})(h||(h={}));var g;(function(e){e.ANDROID="android",e.IOS="ios",e.UNKNOW="unknow"})(g||(g={}));var J;(function(e){e.UPDATE_NETWORK_STATUS="DINGGOV_ON_NETWORK_TYPE_CHANGED",e.UPDATE_LOCATION="DINGGOV_GEO_LOCATION_UPDATE",e.UPDATE_TRACE="DINGGOV_TRACE_UPDATE",e.ON_SHAKE="onShake"})(J||(J={}));var P;(function(e){e.isDingTalk="DingTalk",e.isMpaas="mPaaS",e.isUnknow="unknow"})(P||(P={}));var j=navigator&&(navigator.swuserAgent||navigator.userAgent)||"",Kn=function(){function e(){this.readyFnStack=[],this.generalEventCallbackStack={},this.apiList={},this.continuousCallbackStack={},this.isH5Mobile=null,this.appType=null,this.platformType=null,this.aliBridge=window&&window.navigator&&window.AlipayJSBridge,this.isReady=!1,this.init(),console.warn("\u8BF7\u5C06 gdt-jsapi \u7248\u672C\u8BF7\u5347\u7EA7\u5230 1.9.24 \u7248\u672C\u4EE5\u4E0A\u7684\u6700\u65B0\u7248\u672C\uFF0C\u8C22\u8C22")}var n=e.prototype;return n.h5AndroidbridgeInit=function(){var i=this;this.h5BridgeReadyPromise=new Promise(function(r,o){var s=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),i.execReadyFn()}catch{}};window.nuva&&(window.nuva.isReady===void 0||window.nuva.isReady)?s():(document.addEventListener("runtimeready",function(){s()},!1),document.addEventListener("runtimefailed",function(c){var a=c&&c.detail||{errorCode:C.INVALID_PARAMS,errorMessage:"unknown nuvajs bootstrap error"};i.handleBridgeResponse(a,r,o)},!1))})},n.h5IosBridgeInit=function(){var i=this;this.h5BridgeReadyPromise=new Promise(function(r,o){if(typeof WebViewJavascriptBridge<"u")try{WebViewJavascriptBridge.init(function(s,c){}),i.execReadyFn()}catch{}else document.addEventListener("WebViewJavascriptBridgeReady",function(){try{WebViewJavascriptBridge&&WebViewJavascriptBridge.init(function(s,c){}),i.execReadyFn()}catch{}},!1)})},n.init=function(){var i=this,r=this.getAppType(),o=this.getContainerType();if(r===h.PC&&window.dingtalk&&!window.dingtalk.isRegister&&(window.dingtalk.isRegister=!0,window.dingtalk.callbackStack={},window.dingtalk.event.register(function(c,a){if(i.continuousCallbackStack[c])i.continuousCallbackStack[c](a);else if(a){var l=""+a.msgId;c==="openapi.event.emit"?(console.log("dingtalk receive event:",a,"identifer is",l),window.dingtalk.callbackStack[l]&&(window.dingtalk.callbackStack[l](a),delete window.dingtalk.callbackStack[l])):c==="im.fileTask.addNewTask"||c==="im.fileTask.updateTask"?(a.msgId||a.taskId)&&typeof i.continuousCallbackStack[a.msgId||a.taskId]=="function"&&i.continuousCallbackStack[a.msgId||a.taskId](c,a):i.generalEventCallbackStack[c]&&i.generalEventCallbackStack[c].forEach(function(d){d.call(i,a)})}})),r===h.MOBILE){if(o===P.isDingTalk)this.platformType===g.ANDROID?!this.h5BridgeReadyPromise&&this.h5AndroidbridgeInit():this.platformType===g.IOS&&!this.h5BridgeReadyPromise&&this.h5IosBridgeInit();else if(o===P.isMpaas&&r===h.MOBILE)if(window.AlipayJSBridge)this.execReadyFn();else{var s=setTimeout(function(){console.warn("window.AlipayJSBridge \u672A\u521D\u59CB\u5316\u5B8C\u6BD5\uFF0C\u8D70\u5230\u515C\u5E95\u903B\u8F91",i.isReady,window.AlipayJSBridge),i.isReady||i.execReadyFn.call(i)},5200);document.addEventListener("AlipayJSBridgeReady",function(){i.isReady||(clearTimeout(s),i.execReadyFn.call(i))},!1)}}else setTimeout(function(){i.execReadyFn()})},n.execReadyFn=function(){this.isReady=!0;for(var i=this.readyFnStack.shift();i;)i&&i(this),i=this.readyFnStack.shift()},n.onReady=function(i){this.isReady?i&&i(this):this.readyFnStack.push(i)},n.setCurrentInvoker=function(i){this.currentInvoker=i},n.getCurrentInvoker=function(){return this.currentInvoker},n.getBridge=function(){return this.aliBridge},n.getContainerType=function(){return/TaurusApp/g.test(j)?/DingTalk/g.test(j)?P.isDingTalk:P.isMpaas:/DingTalk/g.test(j)?P.isDingTalk:/mPaaSClient/g.test(j)||/Nebula/g.test(j)?P.isMpaas:P.isUnknow},n.getAppType=function(){return this.appType||(this.isMobile()?this.appType=h.MOBILE:window&&window.navigator&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("dingtalk-win")>=0&&window.navigator.userAgent.indexOf("TaurusApp")>=0?this.appType=h.PC:Te()?this.appType=h.MINI_APP:(console.warn("\u68C0\u6D4B\u5230\u9875\u9762\u5728\u975E\u4E13\u6709\u9489\u9489\u5BA2\u6237\u7AEF\u4E2D\u6253\u5F00\uFF0CJSAPI \u8C03\u7528\u53EF\u80FD\u4E0D\u4F1A\u751F\u6548\uFF01"),this.appType=h.UNKNOWN)),this.appType},n.isMobile=function(){var i=/iPhone|iPad|iPod|iOS/i.test(j),r=/Android/i.test(j),o=window&&window.navigator&&window.navigator.userAgent||"";return this.isH5Mobile!==null?this.isH5Mobile:o&&o.indexOf("dingtalk-win")>=0?(this.isH5Mobile=!1,!1):!(!o||!(o.includes("mPaaSClient")||o.includes("Nebula")||o.includes("DingTalk")))&&(this.isH5Mobile=!0,this.platformType=i?g.IOS:r?g.ANDROID:g.UNKNOW,!0)},n.registerEvent=function(i,r){var o=this;if(typeof r=="function")return this.getAppType()===h.PC?(this.generalEventCallbackStack[i]||(this.generalEventCallbackStack[i]=[]),this.generalEventCallbackStack[i].push(r),function(){var s=o.generalEventCallbackStack[i].findIndex(function(c){return c===r});o.generalEventCallbackStack[i].splice(s,1)}):this.getAppType()===h.MOBILE?(document.addEventListener(i,r,!1),function(){document.removeEventListener(i,r)}):void 0;console.error("callback \u53C2\u6570\u5E94\u8BE5\u4E3A\u51FD\u6570")},n.registerClientAPI=function(i,r){this.apiList[i]=r},n.registerAPI=function(i,r){if(this.isMobile(),typeof r=="object"){var o=r,s=this.getAppType();this.registerClientAPI(i,o[s])}else this.registerClientAPI(i,r)},n.invokeMiniApp=function(){var i=q(M.mark(function r(o,s){var c=this;return M.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return s===void 0&&(s={}),a.abrupt("return",new Promise(function(l,d){s=w({_apiName:o},s);var u=c.apiList[o],p=c.getContainerType();if(!u)return console.warn("API: "+o+"\uFF0C\u672A\u6CE8\u518C"),d("API: "+o+"\uFF0C\u672A\u6CE8\u518C");if(p===P.isMpaas){if(typeof u=="function")return void u.call(null,s,{context:my,resolve:l,reject:d,methodName:o});my.call(o,s,function(f){c.handleBridgeResponse(f,l,d)})}else if(p===P.isDingTalk){if(typeof u=="function")return void u.call(null,s,{context:dd.dtBridge,resolve:l,reject:d,methodName:o,containerType:p,appType:h.MINI_APP});dd.dtBridge({m:"taurus.common."+o,args:s,onSuccess:function(f){c.handleBridgeResponse(f,l,d)},onFail:function(f){c.handleBridgeResponse(f,l,d)}})}}));case 2:case"end":return a.stop()}},r)}));return function(r,o){return i.apply(this,arguments)}}(),n.invokeMobile=function(){var i=q(M.mark(function r(o,s,c){var a=this;return M.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return s===void 0&&(s={}),l.abrupt("return",new Promise(function(d,u){s=w({_apiName:o},s);var p=a.apiList[o],f=a.getContainerType();if(!p)return console.warn("API: "+o+"\uFF0C\u672A\u6CE8\u518C"),u("API: "+o+"\uFF0C\u672A\u6CE8\u518C");if(f===P.isDingTalk){if(a.platformType===g.IOS){var v=Object.assign({},s);if(v.watch===!0&&typeof WebViewJavascriptBridge<"u"&&WebViewJavascriptBridge.registerHandler(c!=null&&c.dingTalkAPIName?c?.dingTalkAPIName:"taurus.common."+o,function(A,k){typeof s.onSuccess=="function"&&s.onSuccess.call(null,A),k&&k({errorCode:"0",errorMessage:"success"})}),typeof p=="function")return void p.call(null,s,{context:window.WebViewJavascriptBridge,resolve:d,reject:u,methodName:o,containerType:f,appType:h.MOBILE,platformType:g.IOS,watch:v.watch});window.WebViewJavascriptBridge!==void 0&&window.WebViewJavascriptBridge.callHandler("taurus.common."+o,Object.assign({},v),function(A){!v.watch&&a.handleBridgeResponse(A||{},d,u)})}else if(a.platformType===g.ANDROID){var m=o.split("."),y=m.pop()||"",I=m.join(".")||"taurus.common";if(typeof p=="function")return void p.call(null,s,{context:window.WebViewJavascriptBridgeAndroid,resolve:d,reject:u,methodName:o,containerType:f,appType:h.MOBILE,platformType:g.ANDROID});typeof window.WebViewJavascriptBridgeAndroid=="function"&&window.WebViewJavascriptBridgeAndroid(function(A){a.handleBridgeResponse(A,d,u)},function(A){a.handleBridgeResponse(A,d,u)},I,y,s)}}else if(f===P.isMpaas){if(typeof p=="function")return void p.call(null,s,{context:AlipayJSBridge,resolve:d,reject:u,methodName:o});AlipayJSBridge.call(o,s,function(A){a.handleBridgeResponse(A,d,u)})}}));case 2:case"end":return l.stop()}},r)}));return function(r,o,s){return i.apply(this,arguments)}}(),n.findFitMsgId=function(i){var r,o;return(r=window.dingtalk)!==null&&r!==void 0&&(o=r.callbackStack)!==null&&o!==void 0&&o[i]?this.findFitMsgId(i+1):i},n.invokePC=function(){var i=q(M.mark(function r(o,s,c){var a=this;return M.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return s===void 0&&(s={}),c===void 0&&(c={msgId:1}),l.abrupt("return",new Promise(function(d,u){try{s=w({_apiName:o},s);var p=a.findFitMsgId(Date.now()),f=c.pcClientAPIName||o;if(c.msgId=p,!window.dingtalk)return Promise.reject(new Error("\u8BF7\u5728\u9489\u9489\u5BB9\u5668\u5185\u4F7F\u7528 JSAPI"));a.apiList[o]?a.apiList[o].call(null,s,c):(console.info("invoke bridge api:",f,p,s),window.dingtalk.platform.invokeAPI(p,f,s)),window.dingtalk&&window.dingtalk.isRegister&&!window.dingtalk.callbackStack&&(window.dingtalk.callbackStack={}),window.dingtalk.callbackStack[""+p]=function(v){var m=v;return m.body?d(m.body):d(m)}}catch(v){u(v)}}));case 3:case"end":return l.stop()}},r)}));return function(r,o,s){return i.apply(this,arguments)}}(),n.handleBridgeResponse=function(i,r,o){i&&i.errorCode?i.errorCode===C.SUCCESS?r(i.result):(console.warn("API \u8C03\u7528\u5931\u8D25",i),o(i)):i&&i.success==="false"?o(i):r(i)},n.invoke=function(){var i=q(M.mark(function r(o,s,c){var a;return M.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:if(s===void 0&&(s={}),(a=this.getAppType())!==h.MOBILE){l.next=8;break}if(this.isReady){l.next=5;break}return l.abrupt("return",Promise.reject("\u9519\u8BEF\uFF1A\u8BF7\u5728 dd.ready() \u56DE\u8C03\u4E2D\u4F7F\u7528 JSAPI\uFF0C\u5F53\u524D\u8C03\u7528\u51FD\u6570\uFF1A"+o));case 5:return l.abrupt("return",this.invokeMobile(o,s,c));case 8:if(a!==h.PC){l.next=12;break}return l.abrupt("return",this.invokePC(o,s,c));case 12:if(a!==h.MINI_APP){l.next=16;break}return l.abrupt("return",this.invokeMiniApp(o,s));case 16:return l.abrupt("return",Promise.reject("\u9519\u8BEF\uFF1A\u672A\u5728\u9489\u9489\u8FD0\u884C\u73AF\u5883\u4E0B\u8C03\u7528\u8BE5 API\uFF0C\u65E0\u6548\uFF0C\u8BF7\u68C0\u67E5\u8FD0\u884C\u73AF\u5883"));case 17:case"end":return l.stop()}},r,this)}));return function(r,o,s){return i.apply(this,arguments)}}(),n.existEventListener=function(i){return!!this.continuousCallbackStack[i]},n.registerContinuesEvent=function(i,r){this.continuousCallbackStack[i]=r},n.removeContinuesEvent=function(i){this.existEventListener(i)&&(this.continuousCallbackStack[i](),delete this.continuousCallbackStack[i])},e}();Te()||(window._invoker=window._invoker||new Kn);const t=Te()?new Kn:window._invoker;function hr(e,n){if(e)return function(i){return typeof i=="function"||n.includes("Sync")||n.startsWith("create")?e(i):new Promise(function(r,o){e(w({},i,{success:function(s){r(s)},fail:function(s){o(s)}}))})}}function je(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.alert",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"taurus.common","alert",e):a===g.IOS&&o.callHandler("taurus.common.alert",Object.assign({},e),function(u){i(u)})}else o&&o.call("alert",e,function(){i()})}t.registerAPI("alert",{mini:je,mobile:je}),Yn.version={android:"1.3.2",ios:"1.3.2"};function Yn(e){return t.invoke("alert",e)}t.registerAPI("authConfig",{mini:!0,mobile:!0}),qn.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"};function qn(e){return t.invoke("authConfig",e)}function We(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.bizContactDepartmentsPickerExternal",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"taurus.common","bizContactDepartmentsPickerExternal",e):a===g.IOS&&o.callHandler("taurus.common.bizContactDepartmentsPickerExternal",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("bizContactDepartmentsPickerExternal",e,function(u){t.handleBridgeResponse(u,i,r)})}t.registerAPI("bizContactDepartmentsPickerExternal",{mini:We,mobile:We,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPickerEx",e)}}),Qn.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"};function Qn(e){return t.invoke("bizContactDepartmentsPickerExternal",e)}function He(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.bizCustomContactChooseExternal",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"taurus.common","bizCustomContactChooseExternal",e):a===g.IOS&&o.callHandler("taurus.common.bizCustomContactChooseExternal",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("bizCustomContactChooseExternal",e,function(u){t.handleBridgeResponse(u,i,r)})}t.registerAPI("bizCustomContactChooseExternal",{mini:He,mobile:He,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.customContact.chooseEx",e)}}),Zn.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"};function Zn(e){return t.invoke("bizCustomContactChooseExternal",e)}function Ue(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.bizCustomContactMultipleChooseExternal",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"taurus.common","bizCustomContactMultipleChooseExternal",e):a===g.IOS&&o.callHandler("taurus.common.bizCustomContactMultipleChooseExternal",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("bizCustomContactMultipleChooseExternal",e,function(u){t.handleBridgeResponse(u,i,r)})}t.registerAPI("bizCustomContactMultipleChooseExternal",{mini:Ue,mobile:Ue,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.customContact.multipleChooseEx",e)}}),Xn.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"};function Xn(e){return t.invoke("bizCustomContactMultipleChooseExternal",e)}t.registerAPI("callPhone",{mini:!0,mobile:!0}),et.version={android:"1.1.0",ios:"1.1.0"};function et(e){return t.invoke("callPhone",e)}t.registerAPI("version",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"version",{})}});function ce(){return t.invoke("version")}var Ir=1,Ar=-1,nt=0;function kr(e,n){return typeof e!="number"&&(e=0),typeof n!="number"&&(n=0),e>n?Ir:e<n?Ar:nt}function xe(e,n){e===void 0&&(e=""),n===void 0&&(n="");var i=/^\d+(\.\d+){2,3}$/;if(!i.test(e)||!i.test(n))throw new Error("\u8BF7\u4F20\u5165\u6B63\u786E\u7684\u7248\u672C\u53F7\u683C\u5F0F");for(var r=(""+e).split(".").map(function(l){return parseInt(l,10)}),o=(""+n).split(".").map(function(l){return parseInt(l,10)}),s=Math.max(r.length,o.length),c=0,a=0;a<s&&(c=kr(r[a],o[a]))===nt;a++);return c}var he=navigator&&navigator.userAgent||"",br=function(){return he.indexOf("Android")>-1||he.indexOf("Adr")>-1},Pr=function(){return!!he.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)},wr=function(){return/(windows)/i.test(navigator.userAgent)};function yr(e){return tt.apply(this,arguments)}function tt(){return(tt=q(M.mark(function e(n){var i,r,o,s;return M.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(se[n]){c.next=2;break}return c.abrupt("return",!1);case 2:return c.next=4,ce();case 4:return i=c.sent,r=i.version,o=se[n].version,s=br()?"android":Pr()?"ios":wr()?"pc":"unknown",c.abrupt("return",!(!o||!o[s])&&xe(r,o[s])>0);case 9:case"end":return c.stop()}},e)}))).apply(this,arguments)}t.registerAPI("checkVPNAppInstalled",{mini:!0,mobile:!0}),ot.version={android:"1.6.0",ios:"1.6.0"};function ot(){return t.invoke("checkVPNAppInstalled")}t.registerAPI("checkVPNAppOnline",{mini:!0,mobile:!0}),it.version={android:"1.6.0",ios:"1.6.0"};function it(){return t.invoke("checkVPNAppOnline")}var Ve;(function(e){e[e.DEFAULT=1]="DEFAULT",e[e.NEW=2]="NEW"})(Ve||(Ve={}));var ze;(function(e){e[e.GLOBAL_ORG=1]="GLOBAL_ORG",e[e.FRIEND=2]="FRIEND",e[e.GROUP=4]="GROUP",e[e.RECOMMEND=5]="RECOMMEND",e[e.SPECIAL_ATTENTION=7]="SPECIAL_ATTENTION",e[e.LOAD_GROUP_PERSON=8]="LOAD_GROUP_PERSON",e[e.ORG=9]="ORG"})(ze||(ze={}));var Ge;(function(e){e.PHONE_HIDE="PHONE_HIDE",e.CHAT_INVALID="CHAT_INVALID",e.GROUP_CHAT_PULL_INVALID="GROUP_CHAT_PULL_INVALID",e.APP_DING_INVALID="APP_DING_INVALID",e.PHONE_DING_INVALID="PHONE_DING_INVALID",e.SMS_DING_INVALID="SMS_DING_INVALID",e.AUDIO_VIDEO_HIDE="AUDIO_VIDEO_HIDE"})(Ge||(Ge={})),t.registerAPI("chooseContact",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.choose",e)}}),rt.version={pc:"1.1.0"};function rt(e){return t.invoke("chooseContact",e)}function Je(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.chooseContactWithComplexPicker",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"taurus.common","chooseContactWithComplexPicker",e):a===g.IOS&&o.callHandler("taurus.common.chooseContactWithComplexPicker",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("chooseContactWithComplexPicker",e,function(u){u.error&&u.error.toString()===C.API_UNDEFINED?o.call("complexPicker",e,function(p){t.handleBridgeResponse(p,i,r)}):t.handleBridgeResponse(u,i,r)})}t.registerAPI("chooseContactWithComplexPicker",{mini:Je,mobile:Je,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPicker",e)}}),at.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"};function at(e){return t.invoke("chooseContactWithComplexPicker",e)}t.registerAPI("chooseDateRangeWithCalendar",{mini:!0,mobile:!0}),st.version={android:"1.3.10",ios:"1.3.10"};function st(e){return t.invoke("chooseDateRangeWithCalendar",e)}t.registerAPI("chooseDayWithCalendar",{mini:!0,mobile:!0}),ct.version={android:"1.3.10",ios:"1.3.10"};function ct(e){return t.invoke("chooseDayWithCalendar",e)}t.registerAPI("chooseDepartments",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPicker",e)}}),lt.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"};function lt(e){return t.invoke("chooseDepartments",e)}t.registerAPI("chooseFile",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseFile",e)}}),ut.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"};function ut(e){return t.invoke("chooseFile",e)}t.registerAPI("chooseHalfDayWithCalendar",{mini:!0,mobile:!0}),dt.version={android:"1.3.10",ios:"1.3.10"};function dt(e){return t.invoke("chooseHalfDayWithCalendar",e)}var $e;(function(e){e[e.image=0]="image",e[e.video=1]="video"})($e||($e={})),t.registerAPI("dgChooseImage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgChooseImage",e)}}),pt.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"};function pt(e){return t.invoke("dgChooseImage",w({},e,{_apiName:"chooseImage"}))}t.registerAPI("chooseInterconnectionChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"chooseInterconnectionChat",e)}}),ft.version={pc:"2.9.0",ios:"2.9.0",android:"2.9.0"};function ft(e){return t.invoke("chooseInterconnectionChat",e)}t.registerAPI("chooseImage",{mini:!0}),gt.version={android:"1.6.2",ios:"1.6.2"};function gt(e){return new Promise(function(n,i){my.chooseImage(w({},e,{success:function(r){n(r)},fail:function(r){i(r)}}))})}t.registerAPI("chooseSpaceDir",{mini:!0,mobile:!0,pc:function(e,n){e===void 0&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseSpaceDir",e)}}),vt.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"};function vt(){return t.invoke("chooseSpaceDir")}t.registerAPI("chooseTimeWithCalendar",{mini:!0,mobile:!0}),mt.version={android:"1.3.10",ios:"1.3.10"};function mt(e){return t.invoke("chooseTimeWithCalendar",e)}t.registerAPI("chooseVideo",{mini:!0,mobile:!0}),ht.version={android:"1.6.2",ios:"1.6.2"};function ht(e){return t.invoke("chooseVideo",e)}function Ke(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"biz.navigation.close",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"biz.navigation","close",e):a===g.IOS&&o.callHandler("biz.navigation.close",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("popWindow",e,function(u){t.handleBridgeResponse(u,i,r)})}t.registerAPI("closePage",{mini:Ke,mobile:Ke,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.quit",e)}}),It.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"};function It(e){return t.invoke("closePage",w({},e,{_apiName:"closePage"}))}var Ye;(function(e){e.CODE="code",e.ACCOUNTID="accountId"})(Ye||(Ye={}));var qe;(function(e){e.CODE="code",e.id="id"})(qe||(qe={})),t.registerAPI("complexPickerAdmin",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPickerAdmin",e)}}),At.version={pc:"2.8.0"};function At(e){return t.invoke("complexPickerAdmin",e)}t.registerAPI("confirm",{mini:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};s===P.isDingTalk?o({m:"taurus.common.confirm",args:c,onSuccess:function(a){var l={errorCode:C.SUCCESS,result:{buttonIndex:a.ok?0:1}};t.handleBridgeResponse(l,i,r)},onFail:function(a){t.handleBridgeResponse(a,i,r)}}):o&&o.call("confirm",c,function(a){var l={errorCode:C.SUCCESS,result:{buttonIndex:a.ok?0:1}};t.handleBridgeResponse(l,i,r)})},mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType,a={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};s?c===g.ANDROID?o&&o(function(l){var d={errorCode:C.SUCCESS,result:{buttonIndex:l.ok?0:1}};t.handleBridgeResponse(d,i,r)},function(l){t.handleBridgeResponse(l,i,r)},"taurus.common","confirm",a):c===g.IOS&&o.callHandler("taurus.common.confirm",Object.assign({},a),function(l){t.handleBridgeResponse(l,i,r)}):o&&o.call("confirm",a,function(l){var d={errorCode:C.SUCCESS,result:{buttonIndex:l.ok?0:1}};t.handleBridgeResponse(d,i,r)})},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.confirm",e)}}),kt.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"};function kt(e){return t.invoke("confirm",e)}t.registerAPI("copyToClipboard",{mini:!0,mobile:!0}),bt.version={android:"1.3.2",ios:"1.3.2"};function bt(e){return t.invoke("copyToClipboard",e)}t.registerAPI("createChatGroup",{mini:!0,mobile:!0}),Pt.version={android:"1.3.0",ios:"1.3.0",pc:"1.3.0"};function Pt(e){return t.invoke("createChatGroup",e)}t.registerAPI("createDing",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.create",e)}}),wt.version={android:"1.3.9",ios:"1.3.9",pc:"1.3.9"};function wt(e){return t.invoke("createDing",e)}t.registerAPI("createDingV2",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.createV2",e)}}),yt.version={android:"2.7.0",ios:"2.7.0",pc:"2.7.0"};function yt(e){return t.invoke("createDingV2",e)}function Qe(e,n){e===void 0&&(e={});var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"biz.conference.createVideoConf",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"biz.conference","createVideoConf",e):a===g.IOS&&o.callHandler("biz.conference.createVideoConf",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("createVideoConf",e,function(){i()})}t.registerAPI("createVideoConf",{mini:Qe,mobile:Qe,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.createVideoConf",w({},e))}}),St.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"};function St(e){return t.invoke("createVideoConf",e)}t.registerAPI("createVideoMeeting",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.meeting.create",w({isVideoConference:!0},e))}}),Ct.version={android:"1.3.1.1",ios:"1.3.1.1",pc:"1.9.4"};function Ct(e){return t.invoke("createVideoMeeting",e)}t.registerAPI("dealWithBackAction",{mobile:!0}),Tt.version={android:"1.2.0.10"};function Tt(e){return t.invoke("dealWithBackAction",e)}t.registerAPI("disableClosePage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.disableClosePage",{})}}),xt.version={pc:"3.4.0"};function xt(){return t.invoke("disableClosePage")}t.registerAPI("disablePullToRefresh",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;s?c===g.ANDROID?o&&o(function(){i()},function(){r()},"ui.pullToRefresh","disable",{}):c===g.IOS&&o.callHandler("ui.pullToRefresh.disable",Object.assign({},{}),function(a){i(a)}):o&&o.call("pullRefresh",{pullRefresh:!1},function(){i()})}}),Rt.version={android:"1.3.0",ios:"1.3.0"};function Rt(){return t.invoke("disablePullToRefresh",{_apiName:"disablePullToRefresh"})}t.registerAPI("disableWebviewBounce",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;s?c===g.ANDROID?o&&o(function(){i()},function(){r()},"ui.webViewBounce","disable",{}):c===g.IOS&&o.callHandler("ui.webViewBounce.disable",Object.assign({},{}),function(a){i(a)}):o&&o.call("bounce",{enable:!1},function(a){i(a)})}}),Ot.version={ios:"1.3.0"};function Ot(){return t.invoke("disableWebviewBounce",{_apiName:"disableWebviewBounce"})}t.registerAPI("downloadAudio",{mini:!0,mobile:!0}),Et.version={android:"1.3.0",ios:"1.3.0"};function Et(e){return t.invoke("downloadAudio",e)}var Sr=1;function Dt(e){return t.invoke("downloadFile",e)}t.registerAPI("downloadFile",{mini:function(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.context;o===P.isDingTalk?s&&s({m:"taurus.common.downloadFile",args:e,onSuccess:function(c){t.handleBridgeResponse(c,i,r)},onFail:function(c){t.handleBridgeResponse(c,i,r)}}):s&&s.call("downloadFile",e,function(c){c.error?r(c):i(c)})},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.downloadFile",e),t.registerContinuesEvent(n.msgId,function(i,r){i==="im.fileTask.addNewTask"&&(t.removeContinuesEvent(n.msgId),t.registerContinuesEvent(r.taskId,function(o,s){if(o==="im.fileTask.updateTask"){var c=s.doneSize,a=s.fileName,l=s.filePath,d=s.fileSize,u=s.speed;e.onProgress({doneSize:c,fileName:a,filePath:l,fileSize:d,speed:u}),s.status===Sr&&t.removeContinuesEvent(s.taskId)}}))})}}),Dt.version={pc:"1.3.5"};t.registerAPI("enablePullToRefresh",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;s?c===g.ANDROID?o&&o(function(){i()},function(){r()},"ui.pullToRefresh","enable",{}):c===g.IOS&&o.callHandler("ui.pullToRefresh.enable",Object.assign({},{}),function(){i()}):o&&o.call("pullRefresh",{pullRefresh:!0},function(){i()})}}),Nt.version={android:"1.3.0",ios:"1.3.0"};function Nt(){return t.invoke("enablePullToRefresh",{_apiName:"enablePullToRefresh"})}t.registerAPI("enableVpn",{mini:!0,mobile:!0}),_t.version={android:"1.1.0",ios:"1.1.0"};function _t(){return t.invoke("enableVpn")}t.registerAPI("enableWebviewBounce",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;s?c===g.ANDROID?o&&o(function(){i()},function(){r()},"taurus.common","bounce",{enable:!0}):c===g.IOS&&o.callHandler("taurus.common.bounce",Object.assign({},{enable:!0}),function(a){i(a)}):o&&o.call("bounce",{enable:!0},function(a){i(a)})}}),Bt.version={ios:"1.3.0"};function Bt(){return t.invoke("enableWebviewBounce",{_apiName:"enableWebviewBounce"})}t.registerAPI("exclusiveInvoke",{mini:!0,mobile:!0}),Mt.version={ios:"1.9.5",android:"1.9.5"};function Mt(e){return t.invoke("exclusiveInvoke",e)}t.registerAPI("faceComparison",{mobile:!0,mini:!0}),Lt.version={android:"2.4.0",ios:"2.4.0"};function Lt(e){return t.invoke("faceComparison",e)}var Ze;(function(e){e.PNG="png",e.JPG="jpg"})(Ze||(Ze={})),t.registerAPI("faceRecognition",{mobile:!0,mini:!0}),Ft.version={android:"2.4.0",ios:"2.4.0"};function Ft(e){return t.invoke("faceRecognition",e)}t.registerAPI("getAppInstallStatus",{mini:!0,mobile:!0}),jt.version={android:"2.1.10",ios:"2.1.10"};function jt(e){return t.invoke("getAppInstallStatus",e)}t.registerAPI("getAuthCode",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"runtime.permission.requestAuthCode",e)},mobile:!0,mini:!0}),Wt.version={android:"1.0.0",ios:"1.0.0",pc:"1.0.0"};function Wt(e){return t.invoke("getAuthCode",e)}t.registerAPI("getConfig",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getConfig",e)}}),Ht.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"};function Ht(){return t.invoke("getConfig",{})}function Re(){return t.getContainerType()}t.registerAPI("getDeviceId",{mobile:!0,mini:!0}),Ut.version={android:"2.5.0",ios:"2.5.0"};function Ut(){return t.invoke("getDeviceId",{})}t.registerAPI("getFromClipboard",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"util.clipboardData.getData",e)}}),Vt.version={android:"2.3.1",ios:"2.3.1",pc:"2.6.10"};function Vt(){return t.invoke("getFromClipboard")}t.registerAPI("getGeolocation",{mini:!0,mobile:!0}),zt.version={android:"1.2.0",ios:"1.2.0"};function zt(e){return t.invoke("getGeolocation",e)}t.registerAPI("getGeolocationStatus",{mobile:!0,mini:!0}),Gt.version={android:"1.6.2",ios:"1.6.2"};function Gt(e){return t.invoke("getGeolocationStatus",e)}t.registerAPI("getHotspotInfo",{mobile:!0,mini:!0}),Jt.version={android:"1.3.5",ios:"1.3.5"};function Jt(){return t.invoke("getHotspotInfo")}t.registerAPI("getLanguageSetting",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getLanguageSetting",e)}}),$t.version={android:"1.4.0",ios:"1.4.0",pc:"1.4.0"};function $t(){return t.invoke("getLanguageSetting")}t.registerAPI("getLoginUser",{mobile:!0,mini:!0}),Kt.version={android:"1.1.0",ios:"1.1.0"};function Kt(){return t.invoke("getLoginUser")}t.registerAPI("getNetworkType",{mobile:!0,mini:!0}),Yt.version={android:"1.3.0",ios:"1.3.0"};function Yt(){return t.invoke("getNetworkType")}t.registerAPI("getPhoneInfo",{mini:!0,mobile:!0}),qt.version={android:"1.3.5",ios:"1.3.5"};function qt(){return t.invoke("getPhoneInfo")}var Xe;(function(e){e.SOCKS5="SOCKS5",e.HTTP="HTTP"})(Xe||(Xe={})),t.registerAPI("getProxyInfo",{pc:function(e,n){e===void 0&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"net.util.getProxyInfo",e)}}),Qt.version={pc:"2.10.0"};function Qt(){return t.invoke("getProxyInfo",{})}t.registerAPI("getStorageItem",{mobile:!0,mini:!0}),Zt.version={android:"1.3.1.1",ios:"1.3.1.1"};function Zt(e){return t.invoke("getStorageItem",e)}t.registerAPI("getTraceStatus",{mobile:!0}),Xt.version={android:"1.3.4",ios:"1.3.4"};function Xt(e){return t.invoke("getTraceStatus",e)}t.registerAPI("getUUID",{mobile:!0,mini:!0}),eo.version={android:"1.3.5",ios:"1.3.5"};function eo(){return t.invoke("getUUID")}var Cr=/TaurusApp\((\S*)\/(\S*)\)/;function Tr(){if(window&&window.navigator){var e=window.navigator.userAgent;if(e){var n=e.match(Cr);return Promise.resolve({group:"TaurusApp",name:n[1],version:n[2]})}return Promise.reject("\u8C03\u7528\u9519\u8BEF\uFF1A\u65E0\u6CD5\u68C0\u6D4B\u5230\u5F53\u4E0B\u73AF\u5883\u7684 userAgent\uFF0C\u8BF7\u786E\u4FDD\u5728\u653F\u52A1\u9489\u9489\u5BA2\u6237\u7AEF H5 \u5BB9\u5668\u4E0B\u8C03\u7528\u3002")}}t.registerAPI("getUserAgent",{mobile:!0,mini:!0,pc:!0}),no.version={android:"1.6.2",ios:"1.6.2",pc:"1.6.2"};function no(){var e=t.getAppType();return e===h.PC||e===h.MOBILE?Tr():e===h.MINI_APP?t.invoke("getUserAgent",{}):void 0}var en,nn,tn;(function(e){e.off="0",e.on="1"})(en||(en={})),function(e){e[e.off=0]="off",e[e.on=1]="on"}(nn||(nn={})),function(e){e[e.name=1]="name",e[e.id=2]="id",e[e.custom=3]="custom"}(tn||(tn={})),t.registerAPI("getWaterMarkConfig",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfig",e)},mini:!0,mobile:!0});function Ie(e){return t.invoke("getWaterMarkConfig",e)}var le,to="h5Page",xr="meetingDetail",Rr="docPreview",Or=[to,xr,Rr],F=!re(typeof my)&&my!==null&&!re(typeof my.alert);F&&(le=my.getSystemInfoSync());var on=F?le.platform:navigator.userAgent,Er=F?le.screenWidth:window.screen.width,te=(F?le.pixelRatio:window.devicePixelRatio)||2,ge=F?Promise.resolve(""):"",Dr=function(){function e(i){i===void 0&&(i={}),this.options=w({texts:[""],width:50,height:50,textRotate:-10,textColor:"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"normal",opacity:90,canvas:[],fontSize:14},i),this.options.width*=this.options.fontSize/12,this.options.height*=this.options.fontSize/12,this.options.deg=this.options.textRotate*Math.PI/180,this.options.cosDeg=Math.cos(this.options.deg),this.options.absSinDeg=Math.abs(Math.sin(this.options.deg))}var n=e.prototype;return n.init=function(){var i=this,r=null,o=null;F?o=my.createCanvasContext("canvasBg"):(r=this.createCanvas(),o=r.getContext("2d")),this.calcTextSize();var s=this.options,c=s.allItemsWidth,a=s.drawItems,l=s.height,d=s.containerComp,u=Math.ceil(Er/c),p=new Array(u).fill(a).reduce(function(m,y){return m.concat(y)},[]),f=function(){i.setCanvasStyle(o),i.drawText(o,p),o.translate(0,l),i.drawText(o,p.reverse(),!0)};if(F)return new Promise(function(m){d.setState({width:c*u,height:2*l},function(){setTimeout(function(){f(),o.draw(),m(o.toDataURL("image/png"))},0)})});r.width=c*u,r.height=2*l,r.style.display="none",f();var v=r.toDataURL("image/png");return this.destroy(),v},n.calcTextSize=function(){var i=0,r=0,o=this.options;o.drawItems=[].map.call(o.texts,function(c){var a,l,d,u;if(F){for(var p=0,f=0;f<c.length;f+=1)p+=/[\uff00-\uffff]/.test(c[f])?1:.5;a=1.1*o.fontSize*p,l=1.2*o.fontSize}else{var v=(d='<span style="font:'+o.fontSize+"px "+o.textFont+';visibility:hidden;">'+c+"</span>",(u=document.createElement("div")).innerHTML=d.trim(),u.firstChild);document.body.appendChild(v),a=v.offsetWidth,l=v.offsetHeight,document.body.removeChild(v)}return i=Math.max(i,a),o.fontHeight||(o.fontHeight=l),r+=Math.ceil(o.cosDeg*(o.width<a?a:o.width)),{txt:c,width:a,height:l}}),i>o.width&&(o.width=i);var s=i*o.absSinDeg+o.fontHeight*o.cosDeg;s>o.height&&(o.height=s),o.maxItemWidth=i,o.allItemsWidth=r},n.setCanvasStyle=function(i){var r=this.options,o=r.deg,s=r.absSinDeg,c=r.height,a=r.fontHeight,l=r.fontStyle,d=r.fontSize,u=r.textFont,p=r.textColor,f=r.opacity;i.rotate(o);var v=s*(c-a);i.translate(-v,0),i.font=l+" "+d+"px "+u,i.fillStyle=p,i.textAlign="left",i.textBaseline="bottom",i.globalAlpha=f},n.drawText=function(i,r,o){o===void 0&&(o=!1);var s=this.options,c=s.maxItemWidth,a=s.width,l=s.height,d=s.deg,u=s.cosDeg,p=s.absSinDeg;r.forEach(function(f,v){var m=u*(c-f.width)/2,y=a*u*v,I=Math.abs(y*Math.tan(d))+l;i.fillText(f.txt,y+(o?u*(a-f.width)/2:m),I+(o?p*(a-f.width)/2:0))})},n.createCanvas=function(){var i=document.createElement("canvas");return this.options.canvas.push(i),i},n.destroy=function(){this.options.canvas.forEach(function(i){i.remove(),i=null})},e}();function Nr(e,n){var i=JSON.parse(e),r=i.watermark||i;if(!r||String(r.watermarkStatus)==="0"||!Array.isArray(r.targetPages)||!r.targetPages.some(function(p){return p.name===n&&String(p.value)==="1"}))return ge;var o=[];if(Array.isArray(r.contentType)){var s="";r.contentType.includes(1)&&(s+=r.userName+" "),r.contentType.includes(2)&&(s+=(r.account||"").slice(-4)),s&&o.push(s),r.contentType.includes(0)&&r.contentCustom&&o.push(r.contentCustom)}if(!o.length)return ge;var c,a,l=/Android|Adr|SymbianOS|Windows\s*Phone|Mobile/.test(on),d=/iPhone|iPad|iPod|Mac\s*OS.*Mobile|iOS/.test(on),u=String(r.watermarkShowDensity)==="0";return d?u?(c=114,a=66):(c=86,a=45):l?u?(c=47*te,a=40*te):(c=25*te,a=25*te):u?(c=300,a=126):(c=194,a=106),new Dr({containerComp:this,texts:o,width:c,height:a,textRotate:-10,textColor:{0:"#FF0000",1:"#000000",2:"#0000FF"}[r.fontColor]||"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:String(r.fontStyle)==="0"?"normal":"bold",opacity:(120-parseInt(r.fontDiaphaneity,10))/100,fontSize:{0:12,1:16,2:28}[r.fontSize]||16}).init()}function Ae(e,n){if(e===void 0&&(e={}),n===void 0&&(n=to),!Or.includes(n))throw new Error("\u7B2C\u4E8C\u4E2A\u53EF\u9009\u53C2\u6570\uFF0C\u4EC5\u80FD\u4E3A\u201Ch5Page\u201D\u6216\u201CmeetingDetail\u201D");try{return Nr.call(this,JSON.stringify(e),n)}catch(i){throw i}}oo.version={android:"1.1.0",ios:"1.1.0",pc:"1.1.0"};function oo(e,n){return e===void 0&&(e=""),new Promise(function(i,r){Ie({pageInfo:e}).then(function(o){try{var s=Ae(o,n);i(s)}catch(c){r(c)}})})}var rn;(function(e){e[e.ENABLE=1]="ENABLE",e[e.DISABLE=0]="DISABLE"})(rn||(rn={})),t.registerAPI("getWaterMarkConfigV2",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfigV2",e)}}),Oe.version={android:"2.8.0",ios:"2.8.0",pc:"2.8.0"};function Oe(e){return t.invoke("getWaterMarkConfigV2",e)}var W;(function(e){e[e.DISABLE=0]="DISABLE",e[e.ENABLE=1]="ENABLE"})(W||(W={}));var ae;(function(e){e.IMSESSIONLIST="imSessionList",e.DOCPREVIEW="docPreview",e.H5PAGEOTHER="h5PageOther",e.MEETINGDETAIL="meetingDetail",e.H5PAGEBASIC="h5PageBasic",e.SELECTIONCOMPONENT="selectionComponent",e.CONTACTLIST="contactList",e.CONTACTDETAIL="contactDetail",e.CHAT="chat",e.SECRETCHAT="secretChat",e.CAMERA="camera"})(ae||(ae={}));var an,sn="",_r={1:"normal",2:"bold",3:"italic"};(function(e){e[e.LOOSE=0]="LOOSE",e[e.NORMAL=1]="NORMAL",e[e.DENSE=2]="DENSE"})(an||(an={}));var H;(function(e){e[e.RIGHT=0]="RIGHT",e[e.LEFT=1]="LEFT"})(H||(H={}));var Br=749,Mr=326,Lr=200,Fr=16,cn=1.3,jr=function(){function e(i){this.options=Object.assign({texts:"",width:50,height:50,tiltAngle:-15,fontColor:"#171A1D",textFont:"PingFangSC-Regular,system-ui,sans-serif",transparency:90,canvas:[],fontSize:13,tWidth:0,tHeight:0,deg:-15},i,{width:i.leftAndRightSpacing,height:i.upAndDownSpacing}),this.options.deg=this.options.tiltAngle*Math.PI/180}var n=e.prototype;return n.init=function(){var i,r,o,s,c,a,l,d,u,p=null;return u=(p=this.createCanvas()).getContext("2d"),p.width=((i=window)===null||i===void 0||(r=i.screen)===null||r===void 0?void 0:r.width)||((o=document)===null||o===void 0||(s=o.documentElement)===null||s===void 0?void 0:s.clientWidth)||Br,p.height=((c=window)===null||c===void 0||(a=c.screen)===null||a===void 0?void 0:a.height)||((l=document)===null||l===void 0||(d=l.documentElement)===null||d===void 0?void 0:d.clientHeight)||Mr,this.calcTextSize(),this.setCanvasStyle(u),this.drawText(u),p.toDataURL("image/png")},n.calcTextSize=function(){var i,r,o=this.options,s="exclusiveDingTalkWaterMarkCustomClass"+100*Math.random(),c=(i='<span id="'+s+'" style="font:'+o.fontSize+"px "+o.textFont+';visibility:hidden;display:inline-block;">'+o.texts+"</span>",(r=document.createElement("div")).innerHTML=i.trim(),r.firstChild);document.body.appendChild(c);var a=document.getElementById(s),l=Math.max(a.clientWidth,o.texts.length*o.fontSize*cn)||Lr,d=Math.min(a.clientHeight,o.fontSize*cn)||Fr;o.tWidth=l,o.tHeight=d,document.body.removeChild(c)},n.setCanvasStyle=function(i){var r=this.options,o=r.deg,s=r.fontStyle,c=r.fontSize,a=r.textFont,l=r.fontColor,d=r.transparency;i.rotate(o),i.font=s+" "+c+"px "+a,i.fillStyle=l,i.textAlign="left",i.textBaseline="bottom",i.globalAlpha=(100-d)/100},n.fillContent=function(i,r){for(var o=this.options,s=o.width,c=o.height,a=o.texts,l=o.tWidth,d=o.tHeight,u=0;u<40;u++)for(var p=u*c+d,f=0;f<40;f++){var v=void 0;v=u%2==0?i===H.RIGHT?(l+s)*f:(l+s)*f+l+s:i===H.RIGHT?(l+s)*f+s:(l+s)*f+l,r.fillText(a,i===H.RIGHT?v:-v,p)}},n.drawText=function(i){this.fillContent(H.RIGHT,i),this.fillContent(H.LEFT,i)},n.createCanvas=function(){var i=document.createElement("canvas");return this.options.canvas.push(i),i},e}();function Wr(e,n){var i,r,o,s,c,a,l,d;n===void 0&&(n=ae.H5PAGEOTHER);var u=null;try{u=JSON.parse(e)}catch{u={}}var p=(i=u)===null||i===void 0||(r=i.watermark)===null||r===void 0?void 0:r.ruleContent,f=(o=u)===null||o===void 0?void 0:o.userInfo;if(p?.enable===W.DISABLE||p?.enable===W.ENABLE&&(p==null||(s=p.effectPage)===null||s===void 0?void 0:s[n])!==W.ENABLE)return sn;var v,m="";return(p==null||(c=p.watermarkContent)===null||c===void 0?void 0:c.enableUsername)===W.ENABLE&&(m+=f?.userName),(p==null||(a=p.watermarkContent)===null||a===void 0?void 0:a.enablePhoneNumber)===W.ENABLE&&(m+=" "+f?.lastFourPhoneNo),p!=null&&(l=p.watermarkContent)!==null&&l!==void 0&&l.customCopy&&(m+=" "+(p==null||(v=p.watermarkContent)===null||v===void 0?void 0:v.customCopy)),m.length?new jr(Object.assign({texts:m,textFont:"PingFangSC-Regular,system-ui,sans-serif"},p?.watermarkStyle,{fontStyle:_r[p==null||(d=p.watermarkStyle)===null||d===void 0?void 0:d.fontStyle]})).init():sn}function Hr(e,n){n===void 0&&(n=ae.H5PAGEOTHER);try{return Wr.call(null,JSON.stringify(e),n)}catch{return""}}function Ur(e){return new Promise(function(n,i){ce().then(function(r){var o=r.version;xe(o,"2.8.0")!==-1?Oe({pageInfo:e}).then(function(s){try{var c=Hr(s,e);n(c)}catch(a){i(a)}}):Ie({pageInfo:e}).then(function(s){try{var c=Ae(s,e);n(c)}catch(a){i(a)}})}).catch(function(){Ie({pageInfo:e}).then(function(r){try{var o=Ae(r,e);n(o)}catch(s){i(s)}})})})}t.registerAPI("getWifiStatus",{mobile:!0,mini:!0}),io.version={android:"1.3.5",ios:"1.3.5"};function io(){return t.invoke("getWifiStatus")}t.registerAPI("getWorkbenchContext",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWorkbenchContext",e)}}),ro.version={android:"2.1.10",ios:"2.1.10"};function ro(){return t.invoke("getWorkbenchContext")}t.registerAPI("h5PageBack",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;s?c===g.ANDROID?o&&o(function(a){t.handleBridgeResponse(a,i,r)},function(a){t.handleBridgeResponse(a,i,r)},"biz.navigation","goBack",e):c===g.IOS&&o.callHandler("biz.navigation.goBack",Object.assign({},e),function(a){i(a)}):o&&o.call("h5PageBack",{_apiName:"goBack"},function(){i()})}}),ao.version={android:"1.3.0",ios:"1.3.9"};function ao(){return t.invoke("h5PageBack",{_apiName:"goBack"})}function ln(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"device.notification.hidePreloader",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"device.notification","hidePreloader",e):a===g.IOS&&o.callHandler("device.notification.hidePreloader",Object.assign({},e),function(u){i(u)})}else o&&o.call("hideLoading",e,function(){i()})}t.registerAPI("hideLoading",{mini:ln,mobile:ln}),so.version={android:"1.3.2",ios:"1.3.2"};function so(){return t.invoke("hideLoading")}function Vr(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=(n.appType,n.platformType);if(s){var a={show:!1,control:!0,text:""};c===g.ANDROID?o&&o(function(l){t.handleBridgeResponse(l,i,r)},function(l){t.handleBridgeResponse(l,i,r)},"biz.navigation","setRight",a):c===g.IOS&&o.callHandler("biz.navigation.setRight",Object.assign({},a),function(l){i(l)})}else o&&o.call("hideOptionMenu",e,function(){i()})}t.registerAPI("hideOptionMenu",{mobile:Vr}),co.version={android:"1.1.0",ios:"1.1.0"};function co(){return t.invoke("hideOptionMenu")}function un(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.appType,a=n.context,l=Object.assign(e,{hidden:!0});if(o){var d=function(){e.onSuccess&&e.onSuccess(),i()},u=function(){e.onFail&&e.onFail(),r()};c===h.MINI_APP?a&&a({m:"biz.navigation.hideBar",args:l,onSuccess:d,onFail:u}):s===g.ANDROID?a&&a(d,u,"biz.navigation","hideBar",l):s===g.IOS&&a.callHandler("biz.navigation.hideBar",Object.assign({},l),function(){i()})}else a&&a.call("hideTitlebar",l,function(){i()})}t.registerAPI("hideTitlebar",{mini:un,mobile:un}),lo.version={android:"2.1.0",ios:"2.1.0"};function lo(){return t.invoke("hideTitlebar")}t.registerAPI("isDownloadFileExist",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.isLocalFileExist",e)}}),uo.version={pc:"1.3.5"};function uo(e){return t.invoke("isDownloadFileExist",e)}function dn(e,n){e===void 0&&(e={});var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"biz.conference.joinScheduleConf",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"biz.conference","joinScheduleConf",e):a===g.IOS&&o.callHandler("biz.conference.joinScheduleConf",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("joinScheduleConf",e,function(){i()})}t.registerAPI("joinScheduleConf",{mini:dn,mobile:dn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinScheduleConf",w({},e))}}),po.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"};function po(e){return t.invoke("joinScheduleConf",e)}function pn(e,n){e===void 0&&(e={});var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"biz.conference.joinVideoConf",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"biz.conference","joinVideoConf",e):a===g.IOS&&o.callHandler("biz.conference.joinVideoConf",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("joinVideoConf",e,function(){i()})}t.registerAPI("joinVideoConf",{mini:pn,mobile:pn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinVideoConf",w({},e))}}),fo.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"};function fo(e){return t.invoke("joinVideoConf",e)}t.registerAPI("joinVideoMeeting",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinVideoMeeting",w({},e))}}),go.version={android:"3.9.0",ios:"3.9.0",pc:"3.9.0"};function go(e){return t.invoke("joinVideoMeeting",e)}t.registerAPI("locateOnMap",{mobile:!0,mini:!0}),vo.version={android:"1.3.0",ios:"1.3.0"};function vo(e){return t.invoke("locateOnMap",e)}function zr(e,n){return t.registerEvent(e,n)}t.registerAPI("onAudioPlayEnd",{mini:!0,mobile:!0}),mo.version={android:"1.6.2",ios:"1.6.2"};function mo(){return t.invoke("onAudioPlayEnd")}t.registerAPI("onRecordAudioEnd",{mini:!0,mobile:!0}),ho.version={android:"1.3.0",ios:"1.3.0"};function ho(e){return t.invoke("onRecordAudioEnd",e)}t.registerAPI("openApiInvoker",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"openApiInvoker",e)}}),Io.version={ios:"3.0.1",android:"3.0.1",pc:"3.0.1"};function Io(e){return t.invoke("openApiInvoker",e)}t.registerAPI("openApp",{mini:!0,mobile:!0}),Ao.version={android:"1.3.2",ios:"1.3.2"};function Ao(e){return t.invoke("openApp",e)}t.registerAPI("openBrowser",{mini:!0,mobile:!0}),ko.version={android:"1.2.3"};function ko(e){return t.invoke("openBrowser",e)}t.registerAPI("openChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"internal.chat.toConversation",{cid:e.chatId})}}),bo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"};function bo(e){return t.invoke("openChat",e)}t.registerAPI("openDownloadFile",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLocalFile",e)}}),Po.version={pc:"1.3.5"};function Po(e){return t.invoke("openDownloadFile",e)}function fn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;ce().then(function(l){var d=l.version,u=xe(d,"1.6.2")!==-1;if(s){var p=function(v){t.handleBridgeResponse(v,i,r)},f=function(v){t.handleBridgeResponse(v,i,r)};c===h.MINI_APP?o&&o({m:u?"taurus.common.openLink":"taurus.common.pushWindow",args:e,onSuccess:p,onFail:f}):a===g.ANDROID?o&&o(p,f,"taurus.common",u?"openLink":"pushWindow",e):a===g.IOS&&o.callHandler(u?"taurus.common.openLink":"taurus.common.pushWindow",Object.assign({},e),function(v){t.handleBridgeResponse(v,i,r)})}else o&&o.call(u?"openLink":"pushWindow",e,function(v){t.handleBridgeResponse(v,i,r)})})}t.registerAPI("openLink",{mini:fn,mobile:fn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLink",e)}}),wo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"};function wo(e){return t.invoke("openLink",e)}t.registerAPI("openPage",{mini:!0,mobile:!0}),yo.version={android:"1.1.0",ios:"1.1.0"};function yo(e){return t.invoke("openPage",e)}t.registerAPI("dgOpenApp",{mobile:!0,mini:!0}),So.version={android:"1.3.1.1",ios:"1.3.1.1"};function So(e){return t.invoke("dgOpenApp",w({},e,{_apiName:"openSchemeUrl"}))}t.registerAPI("openSlidePanel",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openSlidePanel",e)}}),Co.version={pc:"1.3.5"};function Co(e){return t.invoke("openSlidePanel",e)}t.registerAPI("openWatermarkCamera",{mobile:!0,mini:!0}),To.version={android:"1.3.7",ios:"1.3.7"};function To(){return t.invoke("openWatermarkCamera")}t.registerAPI("pauseAudio",{mini:!0,mobile:!0}),xo.version={android:"1.3.0",ios:"1.3.0"};function xo(e){return t.invoke("pauseAudio",e)}t.registerAPI("pickChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.chat.pickConversation",e)}}),Ro.version={android:"1.2.0",ios:"1.2.0",pc:"2.9.0"};function Ro(e){return t.invoke("pickChat",e)}t.registerAPI("pickChatByCorpId",{mini:!0,mobile:!0});function Gr(e){return t.invoke("pickChatByCorpId",e)}t.registerAPI("pickGroupChat",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.pickGroupChat",e)}}),Oo.version={pc:"2.10.30"};function Oo(e){return t.invoke("pickGroupChat",e)}function gn(e,n){e===void 0&&(e={});var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:a===g.ANDROID?"taurus.common.pickGroupConversation":"internal.chat.pickGroupConversation",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"taurus.common","pickGroupConversation",e):a===g.IOS&&o.callHandler("internal.chat.pickGroupConversation",Object.assign({},e),function(u){i(u)})}else o&&o.call("pickGroupConversation",e,function(){i()})}t.registerAPI("pickGroupConversation",{mini:gn,mobile:gn}),Eo.version={android:"2.8.0",ios:"2.8.0"};function Eo(e){return e===void 0&&(e={owner:!1}),t.invoke("pickGroupConversation",e)}t.registerAPI("playAudio",{mini:!0,mobile:!0}),Do.version={android:"1.3.0",ios:"1.3.0"};function Do(e){return t.invoke("playAudio",e)}t.registerAPI("previewDoc",{mini:!0,mobile:!0}),No.version={android:"1.1.0",ios:"1.1.0"};function No(e){return t.invoke("previewDoc",e)}t.registerAPI("previewImage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.previewImage",e)}}),_o.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"};function _o(e){return t.invoke("previewImage",e)}t.registerAPI("printFile",{mini:!0,mobile:!0}),Bo.version={android:"2.2.10"};function Bo(e){return t.invoke("printFile",e)}t.registerAPI("printNativeLog",{mini:!0,mobile:!0}),Mo.version={android:"1.9.4",ios:"1.9.4"};function Mo(e){return t.invoke("printNativeLog",e)}function vn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType,l={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};if(s){var d=function(p){t.handleBridgeResponse(p,i,r)},u=function(p){t.handleBridgeResponse(p,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.prompt",args:l,onSuccess:d,onFail:u}):a===g.ANDROID?o&&o(d,u,"taurus.common","prompt",l):a===g.IOS&&o.callHandler("taurus.common.prompt",Object.assign({},l),function(p){t.handleBridgeResponse(p,i,r)})}else o&&o.call("prompt",l,function(p){var f={errorCode:C.SUCCESS,result:{buttonIndex:p.ok?0:1,value:p.inputValue}};t.handleBridgeResponse(f,i,r)})}t.registerAPI("prompt",{mini:vn,mobile:vn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.prompt",e)}}),Lo.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"};function Lo(e){return t.invoke("prompt",e)}t.registerAPI("pushWindow",{mini:!0,mobile:!0}),Fo.version={android:"2.9.7",ios:"2.9.7"};function Fo(e){return t.invoke("pushWindow",e)}t.registerAPI("readImageToBase64",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"readImageToBase64",e)}}),jo.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"};function jo(e){return t.invoke("readImageToBase64",e)}function Jr(e){typeof e=="function"?t.onReady(e):console.error("dd.ready's param must be function! ")}var mn;(function(e){e[e.ADJUST_BY_NET=0]="ADJUST_BY_NET",e[e.LOW_QUALITY=1]="LOW_QUALITY",e[e.MID_QUALITY=2]="MID_QUALITY",e[e.HIGH_QUALITY=3]="HIGH_QUALITY",e[e.NOT_COMPRESSED=4]="NOT_COMPRESSED",e[e.CUSTOM=5]="CUSTOM"})(mn||(mn={})),t.registerAPI("reduceImageSize",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"reduceImageSize",e)}}),Wo.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"};function Wo(e){return t.invoke("reduceImageSize",e)}t.registerAPI("removeStorageItem",{mobile:!0,mini:!0}),Ho.version={android:"1.3.1.1",ios:"1.3.1.1"};function Ho(e){return t.invoke("removeStorageItem",e)}function hn(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.appType,a=n.context;if(o){var l=function(){e.onSuccess&&e.onSuccess(),i()},d=function(){e.onFail&&e.onFail(),r()};c===h.MINI_APP?a&&a({m:"biz.navigation.replace",args:e,onSuccess:l,onFail:d}):s===g.ANDROID?a&&a(l,d,"biz.navigation","replace",e):s===g.IOS&&a.callHandler("taurus.common.replacePage",Object.assign({},e),function(){i()})}else a&&a.call("replacePage",e,function(){i()})}t.registerAPI("replacePage",{mini:hn,mobile:hn}),Uo.version={android:"1.3.2",ios:"1.3.2"};function Uo(e){return t.invoke("replacePage",e)}t.registerAPI("resetView",{mini:!0,mobile:!0}),Vo.version={android:"1.3.0",ios:"1.3.0"};function Vo(){return t.invoke("resetView")}t.registerAPI("resumeAudio",{mini:!0,mobile:!0}),zo.version={android:"1.3.0",ios:"1.3.0"};function zo(e){return t.invoke("resumeAudio",e)}t.registerAPI("rotateView",{mini:!0,mobile:!0}),Go.version={android:"1.3.0",ios:"1.3.0"};function Go(e){return t.invoke("rotateView",e)}t.registerAPI("scan",{mini:!0,mobile:!0}),Jo.version={android:"1.3.2",ios:"1.3.2"};function Jo(e){return t.invoke("scan",e)}t.registerAPI("searchOnMap",{mini:!0,mobile:!0}),$o.version={android:"1.3.2",ios:"1.3.2"};function $o(e){return t.invoke("searchOnMap",e)}function $r(e){return w({},e,{actionId:"",actionType:"0"})}function In(e,n){var i=n.resolve,r=n.context;r&&r.call("sendOutData",$r(e),function(){i()})}t.registerAPI("cardSendOutData",{mini:In,mobile:In}),Ko.version={android:"2.5.0",ios:"2.5.0"};function Ko(e){return t.invoke("cardSendOutData",e)}var An;(function(e){e.DEFAULT="0",e.DISABLEALL="1",e.ENABLEALL="2"})(An||(An={})),t.registerAPI("setLocalScreenShotPolicy",{mini:!0,mobile:!0}),Yo.version={android:"2.12.12",ios:"2.12.12"};function Yo(e){return t.invoke("setLocalScreenShotPolicy",e)}function Kr(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.context;o?s===g.ANDROID?c&&c(function(a){e.onSuccess&&e.onSuccess(),i()},function(a){r()},"biz.navigation","setIcon",e):s===g.IOS&&c.callHandler("biz.navigation.setIcon",Object.assign({},e),function(a){i()}):c&&c.call("setNavIcon",e,function(a){i()})}t.registerAPI("setNavIcon",{mobile:Kr}),qo.version={android:"1.3.0",ios:"1.3.0"};function qo(e){return t.invoke("setNavIcon",e)}function kn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType,l=n.watch;if(s){var d=function(p){e.onSuccess&&e.onSuccess(),t.handleBridgeResponse(p,i,r)},u=function(p){t.handleBridgeResponse(p,i,r)};c===h.MINI_APP?o&&o({m:"biz.navigation.setLeft",args:e,onSuccess:d,onFail:u}):a===g.ANDROID?o&&o(d,u,"biz.navigation","setLeft",e):a===g.IOS&&o.callHandler("biz.navigation.setLeft",Object.assign({},e),function(p){!l&&i(p)})}else o&&o.call("setNavLeftText",e,function(){i()})}t.registerAPI("setNavLeftText",{mini:kn,mobile:kn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setLeft",e)}}),Qo.version={ios:"1.2.0",pc:"1.2.0"};function Qo(e){var n=Re();return t.invoke("setNavLeftText",n===P.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,{dingTalkAPIName:n===P.isDingTalk?"biz.navigation.setLeft":null})}t.registerAPI("setOptionMenu",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;if(s){var a,l,d={text:e.title,show:e.show===void 0||e.show,control:e.control===void 0||e.control};if(c===g.ANDROID)o&&o(function(f){e.onSuccess&&e.onSuccess(f),i(f)},function f(v){e.onFail&&e.onFail(v),r(f)},"biz.navigation",(e==null||(a=e.menus)===null||a===void 0?void 0:a.length)>1?"setMenu":"setRight",(e==null||(l=e.menus)===null||l===void 0?void 0:l.length)>1?e:d);else if(c===g.IOS){var u,p;o.callHandler((e==null||(u=e.menus)===null||u===void 0?void 0:u.length)>1?"biz.navigation.setMenu":"biz.navigation.setRight",Object.assign({},(e==null||(p=e.menus)===null||p===void 0?void 0:p.length)>1?e:d),function(){i()})}}else o&&o.call("setOptionMenu",e,function(){i()})}}),Zo.version={android:"1.1.0",ios:"1.1.0"};function Zo(e){var n=Re();return t.invoke("setOptionMenu",n===P.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,n===P.isDingTalk?{dingTalkAPIName:"biz.navigation.setRight"}:null)}t.registerAPI("setProxyInfo",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"net.util.setProxyInfo",e)}}),Xo.version={pc:"2.10.0"};function Xo(e){return t.invoke("setProxyInfo",e)}t.registerAPI("setStorageItem",{mobile:!0,mini:!0}),ei.version={android:"1.3.1.1",ios:"1.3.1.1"};function ei(e){return t.invoke("setStorageItem",e)}var bn;function Pn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"biz.navigation.setTitle",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"biz.navigation","setTitle",e):a===g.IOS&&o.callHandler("biz.navigation.setTitle",Object.assign({},e),function(u){t.handleBridgeResponse(u,i,r)})}else o&&o.call("setTitle",e,function(){i()})}(function(e){e.TRUE="true",e.FALSE="false"})(bn||(bn={})),t.registerAPI("setTitle",{mini:Pn,mobile:Pn,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setTitle",e)}}),ni.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"};function ni(e){return t.invoke("setTitle",e)}t.registerAPI("shareFileToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareFileToMessage",e)}}),ti.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"};function ti(e){return t.invoke("shareFileToMessage",e)}t.registerAPI("shareImageToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareImageToMessage",e)}}),oi.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"};function oi(e){return t.invoke("shareImageToMessage",e)}t.registerAPI("shareToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.share",e)}}),ii.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"};function ii(e){return t.invoke("shareToMessage",e)}t.registerAPI("shootVideo",{mini:!0,mobile:!0}),ri.version={android:"1.3.5",ios:"1.3.5"};function ri(){return t.invoke("shootVideo")}t.registerAPI("showActionSheet",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.actionSheet",e)}}),ai.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"};function ai(e){return t.invoke("showActionSheet",e)}t.registerAPI("showCallMenu",{mini:!0,mobile:!0}),si.version={android:"1.3.9",ios:"1.3.9"};function si(e){return t.invoke("showCallMenu",e)}t.registerAPI("showDatePicker",{mobile:!0,mini:!0}),ci.version={android:"1.3.0",ios:"1.3.0"};function ci(e){return t.invoke("showDatePicker",e)}t.registerAPI("showDateTimePicker",{mini:!0,mobile:!0}),li.version={android:"1.3.10",ios:"1.3.10"};function li(e){return t.invoke("showDateTimePicker",e)}t.registerAPI("showExtendModal",{mini:!0,mobile:!0}),ui.version={android:"1.3.5",ios:"1.3.5"};function ui(e){return t.invoke("showExtendModal",e)}t.registerAPI("showHomeBottomTab",{mobile:!0}),di.version={android:"1.3.0",ios:"1.3.0"};function di(e){return t.invoke("showHomeBottomTab",e)}function wn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType;if(s){var l=function(u){t.handleBridgeResponse(u,i,r)},d=function(u){t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?o&&o({m:"device.notification.showPreloader",args:e,onSuccess:l,onFail:d}):a===g.ANDROID?o&&o(l,d,"device.notification","showPreloader",e):a===g.IOS&&o.callHandler("device.notification.showPreloader",Object.assign({},e),function(u){i(u)})}else o&&o.call("showLoading",e,function(){i()})}t.registerAPI("showLoading",{mini:wn,mobile:wn}),pi.version={android:"1.3.2",ios:"1.3.2"};function pi(e){return t.invoke("showLoading",e)}t.registerAPI("showModal",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openModal",e)}}),fi.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"};function fi(e){return t.invoke("showModal",e)}t.registerAPI("showMultiSelect",{mini:!0,mobile:!0}),gi.version={android:"1.3.10",ios:"1.3.10"};function gi(e){return t.invoke("showMultiSelect",e)}t.registerAPI("showOnMap",{mini:!0,mobile:!0}),vi.version={android:"1.3.2",ios:"1.3.2"};function vi(e){return t.invoke("showOnMap",e)}t.registerAPI("showOptionMenu",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.platformType;n.containerType?s===g.ANDROID?o&&o(function(){i()},function(){r()},"taurus.common","showOptionMenu",e):s===g.IOS&&o.callHandler("taurus.common.showOptionMenu",Object.assign({},e),function(){i()}):o&&o.call("showOptionMenu",e,function(){i()})}}),mi.version={android:"1.1.0",ios:"1.1.0"};function mi(){return t.invoke("showOptionMenu")}t.registerAPI("showPlainInputUponKeyboard",{mobile:!0,mini:!0}),hi.version={android:"1.3.0",ios:"1.3.0"};function hi(e){return t.invoke("showPlainInputUponKeyboard",e)}t.registerAPI("showQuickCallMenu",{mini:!0,mobile:!0}),Ii.version={android:"1.6.2",ios:"1.6.2"};function Ii(e){return t.invoke("showQuickCallMenu",e)}t.registerAPI("showSelect",{mini:!0,mobile:!0}),Ai.version={android:"1.3.2",ios:"1.3.2"};function Ai(e){return t.invoke("showSelect",e)}t.registerAPI("showSignature",{mobile:!0}),ki.version={android:"1.3.4"};function ki(e){return t.invoke("showSignature",e)}t.registerAPI("showSocialShare",{mini:!0,mobile:!0}),bi.version={android:"1.2.0.10",ios:"1.2.0.10"};function bi(e){return t.invoke("showSocialShare",e)}t.registerAPI("showTimePicker",{mobile:!0,mini:!0}),Pi.version={android:"1.3.0",ios:"1.3.0"};function Pi(e){return t.invoke("showTimePicker",e)}function yn(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.appType,a=n.context,l=Object.assign(e,{hidden:!1});if(o){var d=function(){e.onSuccess&&e.onSuccess(),i()},u=function(){e.onFail&&e.onFail(),r()};c===h.MINI_APP?a&&a({m:"biz.navigation.hideBar",args:l,onSuccess:d,onFail:u}):s===g.ANDROID?a&&a(d,u,"biz.navigation","hideBar",l):s===g.IOS&&a.callHandler("biz.navigation.hideBar",Object.assign({},l),function(){i()})}else a&&a.call("showTitlebar",l,function(){i()})}t.registerAPI("showTitlebar",{mini:yn,mobile:yn}),wi.version={android:"2.1.0",ios:"2.1.0"};function wi(){return t.invoke("showTitlebar")}t.registerAPI("startFaceRecognition",{mini:!0,mobile:!0}),yi.version={android:"1.8.2",ios:"1.8.2"};function yi(e){return t.invoke("startFaceRecognition",e)}function Sn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.platformType,c=n.containerType,a=n.appType,l=t.registerEvent(J.UPDATE_LOCATION,function(p){var f=p.data;f.errorCode!==C.SUCCESS?e.onFail&&e.onFail(f):e.onSuccess&&e.onSuccess(f.result)});if(c){var d=function(p){t.registerContinuesEvent(e.sceneId,l),t.handleBridgeResponse(p,i,r)},u=function(p){t.registerContinuesEvent(e.sceneId,l),t.handleBridgeResponse(p,i,r)};a===h.MINI_APP?(console.log("taurus.common.startGeolocation",e),o&&o({m:"taurus.common.startGeolocation",args:e,onSuccess:d,onFail:u})):s===g.ANDROID?o&&o(d,u,"taurus.common","startGeolocation",e):s===g.IOS&&o.callHandler("taurus.common.startGeolocation",Object.assign({},e),function(p){t.registerContinuesEvent(e.sceneId,l),t.handleBridgeResponse(p,i,r)})}else o&&o.call("startGeolocation",e,function(p){t.registerContinuesEvent(e.sceneId,l),t.handleBridgeResponse(p,i,r)})}t.registerAPI("startGeolocation",{mobile:Sn,mini:Sn}),Si.version={android:"1.3.2",ios:"1.3.2"};function Si(e){return t.invoke("startGeolocation",e)}function Cn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.appType,a=n.platformType,l=t.registerEvent(J.UPDATE_NETWORK_STATUS,function(p){var f=p.data;f.errorCode!==C.SUCCESS?e.onFail&&e.onFail(f):e.onSuccess&&e.onSuccess(f.result)});if(s){var d=function(p){t.registerContinuesEvent(p.result.requestId,l),t.handleBridgeResponse(p,i,r)},u=function(p){t.registerContinuesEvent(p.result.requestId,l),t.handleBridgeResponse(p,i,r)};c===h.MINI_APP?o&&o({m:"taurus.common.startListenNetworkStatus",args:e,onSuccess:d,onFail:u}):a===g.ANDROID?o&&o(d,u,"taurus.common","startListenNetworkStatus",e):a===g.IOS&&o.callHandler("taurus.common.startListenNetworkStatus",Object.assign({},e),function(p){t.registerContinuesEvent(p.result.requestId,l),t.handleBridgeResponse(p,i,r)})}else o&&o.call("startListenNetworkStatus",e,function(p){t.registerContinuesEvent(p.result.requestId,l),t.handleBridgeResponse(p,i,r)})}t.registerAPI("startListenNetworkStatus",{mobile:Cn,mini:Cn}),Ci.version={android:"1.3.1.1",ios:"1.3.1.1"};function Ci(e){return t.invoke("startListenNetworkStatus",e)}t.registerAPI("startRecordAudio",{mini:!0,mobile:!0}),Ti.version={android:"1.3.0",ios:"1.3.0"};function Ti(e){return t.invoke("startRecordAudio",e)}function Yr(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType,a=n.appType,l=t.registerEvent(J.UPDATE_TRACE,function(p){var f=p.data;f.errorCode&&f.errorCode!==C.SUCCESS?e.onFail&&e.onFail(f):e.onSuccess&&e.onSuccess(f.result||f)});if(s){var d=function(p){t.registerContinuesEvent(e.traceId,l),t.handleBridgeResponse(p,i,r)},u=function(p){t.registerContinuesEvent(e.traceId,l),t.handleBridgeResponse(p,i,r)};a===h.MINI_APP?o&&o({m:"taurus.common.startTraceReport",args:e,onSuccess:d,onFail:u}):c===g.ANDROID?o&&o(d,u,"taurus.common","startTraceReport",e):c===g.IOS&&o.callHandler("taurus.common.startTraceReport",Object.assign({},e),function(p){t.registerContinuesEvent(e.traceId,l),t.handleBridgeResponse(p,i,r)})}else o&&o.call("startTraceReport",e,function(p){t.registerContinuesEvent(e.traceId,l),t.handleBridgeResponse(p,i,r)})}t.registerAPI("startTraceReport",{mobile:Yr,mini:!0}),xi.version={android:"1.3.4",ios:"1.3.4"};function xi(e){return t.invoke("startTraceReport",e)}t.registerAPI("startVPNApp",{mini:!0,mobile:!0}),Ri.version={android:"1.6.0",ios:"1.6.0"};function Ri(e){return t.invoke("startVPNApp",e)}function qr(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType,a=t.registerEvent(J.ON_SHAKE,function(){e.onSuccess&&e.onSuccess()});s?c===g.ANDROID?o&&o(function(l){e.onSuccess&&e.onSuccess(),i()},function(l){r()},"taurus.common","startWatchShake",e):c===g.IOS&&o.callHandler("taurus.common.startWatchShake",Object.assign({},e),function(l){i()}):o&&o.call("startWatchShake",e,function(l){t.registerContinuesEvent("shake",a),t.handleBridgeResponse(l,i,r)})}t.registerAPI("startWatchShake",{mobile:qr}),Oi.version={android:"1.6.2",ios:"1.6.2"};function Oi(e){return t.invoke("startWatchShake",e)}t.registerAPI("stopAudio",{mini:!0,mobile:!0}),Ei.version={android:"1.3.0",ios:"1.3.0"};function Ei(e){return t.invoke("stopAudio",e)}function Tn(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.appType,a=n.context;if(o){var l=function(u){t.removeContinuesEvent(e.sceneId),t.handleBridgeResponse(u,i,r)},d=function(u){t.removeContinuesEvent(e.sceneId),t.handleBridgeResponse(u,i,r)};c===h.MINI_APP?a&&a({m:"taurus.common.stopGeolocation",args:e,onSuccess:l,onFail:d}):s===g.ANDROID?a&&a(l,d,"taurus.common","stopGeolocation",e):s===g.IOS&&a.callHandler("taurus.common.stopGeolocation",Object.assign({},e),function(u){t.removeContinuesEvent(e.sceneId),t.handleBridgeResponse(u,i,r)})}else a&&a.call("stopGeolocation",e,function(u){t.removeContinuesEvent(e.sceneId),t.handleBridgeResponse(u,i,r)})}t.registerAPI("stopGeolocation",{mobile:Tn,mini:Tn}),Di.version={android:"1.3.2",ios:"1.3.2"};function Di(e){return t.invoke("stopGeolocation",e)}function xn(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.appType,c=n.platformType,a=n.context;if(o){var l=function(u){t.removeContinuesEvent(e.requestId),t.handleBridgeResponse(u,i,r)},d=function(u){t.removeContinuesEvent(e.requestId),t.handleBridgeResponse(u,i,r)};s===h.MINI_APP?a&&a({m:"taurus.common.stopListenNetworkStatus",args:e,onSuccess:l,onFail:d}):c===g.ANDROID?a&&a(l,d,"taurus.common","stopListenNetworkStatus",e):c===g.IOS&&a.callHandler("taurus.common.stopListenNetworkStatus",Object.assign({},e),function(u){t.removeContinuesEvent(e.requestId),t.handleBridgeResponse(u,i,r)})}else a&&a.call("stopListenNetworkStatus",e,function(u){t.removeContinuesEvent(e.requestId),t.handleBridgeResponse(u,i,r)})}t.registerAPI("stopListenNetworkStatus",{mini:xn,mobile:xn}),Ni.version={android:"1.3.1.1",ios:"1.3.1.1"};function Ni(e){return t.invoke("stopListenNetworkStatus",e)}t.registerAPI("stopPullToRefresh",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType;s?c===g.ANDROID?o&&o(function(){i()},function(){r()},"ui.pullToRefresh","stop",e):c===g.IOS&&o.callHandler("ui.pullToRefresh.stop",Object.assign({},e),function(){i()}):o&&o.call("restorePullToRefresh",e,function(){i()})}}),_i.version={android:"1.3.0",ios:"1.3.0"};function _i(){return t.invoke("stopPullToRefresh",{_apiName:"stopPullToRefresh"})}t.registerAPI("stopRecordAudio",{mini:!0,mobile:!0}),Bi.version={android:"1.3.0",ios:"1.3.0"};function Bi(e){return t.invoke("stopRecordAudio",e)}function Qr(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.context;o?s===g.ANDROID?c&&c(function(a){t.removeContinuesEvent(e.traceId),t.handleBridgeResponse(a,i,r)},function(a){t.removeContinuesEvent(e.traceId),t.handleBridgeResponse(a,i,r)},"taurus.common","stopTraceReport",e):s===g.IOS&&c.callHandler("taurus.common.stopTraceReport",Object.assign({},e),function(a){t.removeContinuesEvent(e.traceId),t.handleBridgeResponse(a,i,r)}):c&&c.call("stopTraceReport",e,function(a){t.removeContinuesEvent(e.traceId),t.handleBridgeResponse(a,i,r)})}t.registerAPI("stopTraceReport",{mobile:Qr}),Mi.version={android:"1.3.4",ios:"1.3.4"};function Mi(e){return t.invoke("stopTraceReport",e)}t.registerAPI("stopVPNApp",{mini:!0,mobile:!0}),Li.version={android:"1.6.0",ios:"1.6.0"};function Li(e){return t.invoke("stopVPNApp",e)}function Zr(e,n){var i=n.resolve,r=n.reject,o=n.containerType,s=n.platformType,c=n.context;o?s===g.ANDROID?c&&c(function(a){e.onSuccess&&e.onSuccess(),i()},function(a){r()},"taurus.common","stopWatchShake",e):s===g.IOS&&c.callHandler("taurus.common.stopWatchShake",Object.assign({},e),function(a){i()}):c&&c.call("stopWatchShake",e,function(a){t.removeContinuesEvent("shake"),t.handleBridgeResponse(a,i,r)})}t.registerAPI("stopWatchShake",{mobile:Zr}),Fi.version={android:"1.6.2",ios:"1.6.2"};function Fi(){return t.invoke("stopWatchShake")}t.registerAPI("subscribe",{mobile:function(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType,a=!1;s?c===g.ANDROID?o&&o(function(l){a?(e.onSuccess||e.onFail)&&(l.errorCode!=="0"?e.onFail&&e.onFail(l):e.onSuccess&&e.onSuccess(l.result)):(a=!0,t.handleBridgeResponse(l,i,r))},function(l){a?e.onFail&&e.onFail(l):(a=!0,t.handleBridgeResponse(l,i,r))},"taurus.common","subscribe",e):c===g.IOS&&o.callHandler("taurus.common.subscribe",Object.assign({},e),function(l){a?(e.onSuccess||e.onFail)&&(l.errorCode!=="0"?e.onFail&&e.onFail(l):e.onSuccess&&e.onSuccess(l.result)):(a=!0,t.handleBridgeResponse(l,i,r))}):o&&o.call("subscribe",e,function(l){a?(e.onSuccess||e.onFail)&&(l.errorCode!=="0"?e.onFail&&e.onFail(l):e.onSuccess&&e.onSuccess(l.result)):(a=!0,t.handleBridgeResponse(l,i,r))})}}),ji.version={android:"1.6.0",ios:"1.6.0"};function ji(e){return t.invoke("subscribe",e)}t.registerAPI("takePhoto",{mini:!0,mobile:!0}),Wi.version={android:"1.3.5",ios:"1.3.5"};function Wi(){return t.invoke("takePhoto")}t.registerAPI("testProxy",{pc:function(e,n){e===void 0&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"net.util.testProxy",e)}}),Hi.version={pc:"2.10.0"};function Hi(){return t.invoke("testProxy",{})}function Rn(e,n){var i=n.resolve,r=n.reject,o=n.context,s=n.containerType,c=n.platformType,a=n.appType,l={type:e.icon==="error"?"fail":e.icon==="success"?"success":"none",content:e.text,duration:1e3*e.duration,taurusToastStyle:e.taurusToastStyle};if(s){var d=function(){t.handleBridgeResponse({errorCode:C.SUCCESS,result:{}},i,r)},u=function(p){t.handleBridgeResponse(p,i,r)};a===h.MINI_APP?o&&o({m:"taurus.common.toast",args:l,onSuccess:d,onFail:u}):c===g.ANDROID?o&&o(d,u,"taurus.common","toast",l):c===g.IOS&&o.callHandler("taurus.common.toast",Object.assign({},l),function(){t.handleBridgeResponse({errorCode:C.SUCCESS,result:{}},i,r)})}else o&&o.call("toast",l,function(){t.handleBridgeResponse({errorCode:C.SUCCESS,result:{}},i,r)})}t.registerAPI("toast",{mobile:Rn,mini:Rn,pc:function(e,n){var i=e.icon,r=e.text,o=e.duration,s=e.delay;window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.toast",{type:i,text:r,duration:o,delay:s})}}),Ui.version={android:"1.3.2",ios:"1.3.2"};function Ui(e){return t.invoke("toast",e)}t.registerAPI("unlockWithSecurityVerification",{mini:!0,mobile:!0}),Vi.version={android:"1.3.1.1",ios:"1.3.1.1"};function Vi(){return t.invoke("unlockWithSecurityVerification")}t.registerAPI("unsubscribe",{mobile:!0}),zi.version={android:"1.6.0",ios:"1.6.0"};function zi(e){return t.invoke("unsubscribe",e)}t.registerAPI("dgUploadFile",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgUploadFile",e)}}),Gi.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"};function Gi(e){return t.invoke("dgUploadFile",w({},e,{_apiName:"uploadFile"}))}t.registerAPI("uploadFileByType",{mini:!0,mobile:!0}),Ji.version={android:"1.3.0",ios:"1.3.0"};function Ji(e){return t.invoke("uploadFileByType",e)}t.registerAPI("uploadFile",{mini:!0}),$i.version={android:"1.6.2",ios:"1.6.2"};function $i(e){return new Promise(function(n,i){my.uploadFile(w({},e,{success:function(r){n(r)},fail:function(r){i(r)}}))})}t.registerAPI("uploadRemoteFileToDisk",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.uploadRemoteFileToDisk",e)}}),Ki.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"};function Ki(e){return t.invoke("uploadRemoteFileToDisk",e)}t.registerAPI("ut",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.ut",e)}}),Yi.version={pc:"1.3.10"};function Yi(e){return t.invoke("ut",e)}t.registerAPI("vibrate",{mini:!0,mobile:!0}),qi.version={android:"1.3.1",ios:"1.3.1"};function qi(e){return t.invoke("vibrate",e)}var z={alert:Yn,authConfig:qn,bizContactDepartmentsPickerExternal:Qn,bizCustomContactChooseExternal:Zn,bizCustomContactMultipleChooseExternal:Xn,callPhone:et,canIUse:yr,checkVPNAppInstalled:ot,checkVPNAppOnline:it,chooseContact:rt,chooseContactWithComplexPicker:at,chooseDateRangeWithCalendar:st,chooseDayWithCalendar:ct,chooseDepartments:lt,chooseFile:ut,chooseHalfDayWithCalendar:dt,chooseImage:pt,chooseInterconnectionChat:ft,chooseLocalImage:gt,chooseSpaceDir:vt,chooseTimeWithCalendar:mt,chooseVideo:ht,closePage:It,complexPickerAdmin:At,confirm:kt,copyToClipboard:bt,createChatGroup:Pt,createDing:wt,createDingV2:yt,createVideoConf:St,createVideoMeeting:Ct,dealWithBackAction:Tt,disableClosePage:xt,disablePullToRefresh:Rt,disableWebviewBounce:Ot,downloadAudio:Et,downloadFile:Dt,enablePullToRefresh:Nt,enableVpn:_t,enableWebviewBounce:Bt,exclusiveInvoke:Mt,faceComparison:Lt,faceRecognition:Ft,getAppInstallStatus:jt,getAuthCode:Wt,getConfig:Ht,getContainerType:Re,getDeviceId:Ut,getFromClipboard:Vt,getGeolocation:zt,getGeolocationStatus:Gt,getHotspotInfo:Jt,getLanguageSetting:$t,getLoginUser:Kt,getNetworkType:Yt,getPhoneInfo:qt,getProxyInfo:Qt,getStorageItem:Zt,getTraceStatus:Xt,getUUID:eo,getUserAgent:no,getWaterMark:oo,getWaterMarkConfigV2:Oe,getWaterMarkV2:Ur,getWifiStatus:io,getWorkbenchContext:ro,goBack:ao,hideLoading:so,hideOptionMenu:co,hideTitleBar:lo,isDownloadFileExist:uo,joinScheduleConf:po,joinVideoConf:fo,joinVideoMeeting:go,locateOnMap:vo,on:zr,onAudioPlayEnd:mo,onRecordAudioEnd:ho,openApiInvoker:Io,openApp:Ao,openBrowser:ko,openChat:bo,openDownloadFile:Po,openLink:wo,openPage:yo,openSchemeUrl:So,openSlidePanel:Co,openWatermarkCamera:To,pauseAudio:xo,pickChat:Ro,pickChatByCorpId:Gr,pickGroupChat:Oo,pickGroupConversation:Eo,playAudio:Do,previewDoc:No,previewImage:_o,printFile:Bo,printNativeLog:Mo,prompt:Lo,pushWindow:Fo,readImageToBase64:jo,ready:Jr,reduceImageSize:Wo,removeStorageItem:Ho,replacePage:Uo,resetView:Vo,resumeAudio:zo,rotateView:Go,scan:Jo,searchOnMap:$o,sendOutData:Ko,setLocalScreenShotPolicy:Yo,setNavIcon:qo,setNavLeftText:Qo,setOptionMenu:Zo,setProxyInfo:Xo,setStorageItem:ei,setTitle:ni,shareFileToMessage:ti,shareImageToMessage:oi,shareToMessage:ii,shootVideo:ri,showActionSheet:ai,showCallMenu:si,showDatePicker:ci,showDateTimePicker:li,showExtendModal:ui,showHomeBottomTab:di,showLoading:pi,showModal:fi,showMultiSelect:gi,showOnMap:vi,showOptionMenu:mi,showPlainInputUponKeyboard:hi,showQuickCallMenu:Ii,showSelect:Ai,showSignature:ki,showSocialShare:bi,showTimePicker:Pi,showTitleBar:wi,startFaceRecognition:yi,startGeolocation:Si,startListenNetworkStatus:Ci,startRecordAudio:Ti,startTraceReport:xi,startVPNApp:Ri,startWatchShake:Oi,stopAudio:Ei,stopGeolocation:Di,stopListenNetworkStatus:Ni,stopPullToRefresh:_i,stopRecordAudio:Bi,stopTraceReport:Mi,stopVPNApp:Li,stopWatchShake:Fi,subscribe:ji,takePhoto:Wi,testProxy:Hi,toast:Ui,unlockWithSecurityVerification:Vi,unsubscribe:zi,uploadFile:Gi,uploadFileByType:Ji,uploadLocalFile:$i,uploadRemoteFileToDisk:Ki,ut:Yi,version:ce,vibrate:qi};if(t.getAppType()===h.MINI_APP)z=new Proxy(z,{get:function(e,n,i){return n in z?Reflect.get(e,n,i):hr(Reflect.get(my,n,i),n)}});else{window.dd&&console.warn("\u5DF2\u7ECF\u5B58\u5728 window.dd \u53D8\u91CF\uFF0C\u5F15\u5165 gdt-jsapi \u4F1A\u4FEE\u6539 window.dd \u7684\u503C\u3002");try{Object.defineProperty(window,"dd",{value:z,writable:!0})}catch(e){console.error(e)}window.gdt&&console.warn("\u5DF2\u7ECF\u5B58\u5728 window.gdt \u53D8\u91CF\uFF0C\u5F15\u5165 gdt-jsapi \u4F1A\u4FEE\u6539 window.gdt \u7684\u503C\u3002");try{Object.defineProperty(window,"gdt",{value:z,writable:!0})}catch(e){console.error(e)}}const se=z;const Xr={name:"OAuth2Premises",setup(){const e=er(),n=V(""),i=V("\u4E13\u6709\u9489\u9489"),r=V(!1),o=V(!0),s=V(""),c=V({callback_url:"",state:""}),a=async()=>{o.value=window.self===window.top,r.value=!0;const f=await d();if(r.value=!1,e.query.state){const v=mr.decode(e.query.state);c.value=JSON.parse(v),c.value.state=e.query.state}f.errorCode===0?window.location.href=u(f.data):n.value=p()},l=()=>{if(console.log("isAuthState:",o.value,"url:",n.value),o.value||r.value)return;const f={event:"wakeup-app",params:{url:n.value,authKey:s.value}};console.log("peurl",n.value,c.value.state),window.parent.postMessage(f,window.location.origin)},d=()=>new Promise((f,v)=>{se.ready(function(){se.getAuthCode({corpId:e.query.corpId}).then(m=>{f({errorCode:0,data:m})}).catch(m=>{f({errorCode:1,data:m})})})}),u=f=>{const v=[];return f.state=e.query.state||"",Object.keys(f).forEach(m=>{v.push(`${m}=${f[m]}`)}),c.value.callback_url+"?"+v.join("&")},p=()=>{const f=c.value.callback_url+"?state="+c.value.state;return`taurus://taurusclient/action/open_app?type=1&offline=false&url=${encodeURIComponent(f)}`};return nr(()=>{a()}),{url:n,appName:i,loading:r,isAuthState:o,authKey:s,routeHandle:l}}},ea={class:"premises-page"},na={style:{"text-align":"center"}},ta={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},oa={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},ia={class:"submit-btn-wrapper"},ra={key:0,class:"premises-tip"};function aa(e,n,i,r,o,s){const c=tr("base-button");return de(),pe("div",ea,[ee("div",na,[ee("span",ta,[(de(),pe("svg",oa,n[0]||(n[0]=[ee("use",{"xlink:href":"#icon-auth-zhezhending"},null,-1)]))),De(" "+fe(r.appName),1)])]),ee("div",ia,[or(c,{type:"primary",size:"large",class:"login_submit_button","native-type":"submit",onClick:r.routeHandle},{default:ir(()=>[De(fe(r.isAuthState?"\u6B63\u5728\u83B7\u53D6\u6388\u6743\u4FE1\u606F":"\u6388\u6743\u767B\u5F55"),1)]),_:1},8,["onClick"])]),r.isAuthState?rr("",!0):(de(),pe("span",ra,"\u82E5\u6253\u5F00\u5931\u8D25\uFF0C\u8BF7\u5148\u5B89\u88C5"+fe(r.appName)+"App",1))])}const ca=Xi(Xr,[["render",aa]]);export{ca as default};
