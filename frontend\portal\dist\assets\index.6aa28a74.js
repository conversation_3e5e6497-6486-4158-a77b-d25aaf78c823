/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{x as e,_ as a,y as l,u as t,r as n,c as i,b as s,z as u,p as o,o as r,d as v,e as c,f as d,j as p,m,F as h,k as y,t as g,g as f,A as _,B as x,i as w,C as k,L as C,D as b}from"./index.2a422357.js";const L={class:"login-page"},T={class:"content"},P={class:"right-panel"},O={key:0},E={key:0,class:"title"},I={key:1,class:"title"},j={style:{"text-align":"center"}},q={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},R={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},S=["xlink:href"],A={key:2,class:"login_panel_form"},D={key:3,class:"auth-switcher"},V={class:"auth-switcher-container"},U=["disabled"],$={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},K={class:"auth-methods-wrapper"},N=["onClick"],z=["data-auth-type"],H={class:"icon","aria-hidden":"true",style:{height:"18px",width:"18px"}},J=["xlink:href"],M={class:"auth-method-name"},W=["disabled"],B={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},F={class:"auth-waiting"},X={class:"waiting-icon"},G={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},Q=["xlink:href"],Y={class:"waiting-title"},Z={class:"security-tips"},ee={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px",color:"#67c23a"}},ae=a(Object.assign({name:"Login"},{setup(a){const ae=l({loader:()=>k((()=>import("./localLogin.bda8795a.js")),["./localLogin.bda8795a.js","./index.2a422357.js","./index.22557c95.css","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=l({loader:()=>k((()=>import("./wechat.a2f86c03.js")),["./wechat.a2f86c03.js","./index.2a422357.js","./index.22557c95.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=l({loader:()=>k((()=>import("./feishu.320e8119.js")),["./feishu.320e8119.js","./index.2a422357.js","./index.22557c95.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=l({loader:()=>k((()=>import("./dingtalk.51b0d998.js")),["./dingtalk.51b0d998.js","./index.2a422357.js","./index.22557c95.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ie=l({loader:()=>k((()=>import("./oauth2.67aad8af.js")),["./oauth2.67aad8af.js","./index.2a422357.js","./index.22557c95.css","./oauth2.03d0b5c4.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=l({loader:()=>k((()=>import("./sms.aef97d9d.js")),["./sms.aef97d9d.js","./index.2a422357.js","./index.22557c95.css","./sms.844b2c56.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=l({loader:()=>k((()=>import("./secondaryAuth.2a0113ef.js")),["./secondaryAuth.2a0113ef.js","./index.2a422357.js","./index.22557c95.css","./verifyCode.bf902937.js","./verifyCode.3a036caf.css","./secondaryAuth.b6f7bb0a.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),oe=l({loader:()=>k((()=>import("./serverConfig.635db1d4.js")),["./serverConfig.635db1d4.js","./index.2a422357.js","./index.22557c95.css","./serverConfig.7b10f103.css"],import.meta.url),loadingComponent:C,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),re=t(),ve=n(0),ce=n([]),de=n("local"),pe=n(""),me=n(""),he=n(""),ye=n([]),ge=n([]),fe=n(!1),_e=n(!1),xe=n(),we=n(""),ke=n(!1),Ce=n(""),be=n(!1),Le=n(""),Te=n(""),Pe=n(""),Oe=n({}),Ee=n(0),Ie=n(80),je=n(3),qe=n(null),Re=i((()=>{const e=fe.value?Le.value:me.value;return ce.value.filter((a=>a.id!==e))})),Se=i((()=>Math.max(0,Re.value.length-je.value))),Ae=s();i((()=>ge.value.filter((e=>e.id!==me.value))));const De=e=>{logger.log("服务器配置完成:",e),_e.value=!1,Ve()},Ve=async()=>{var a,l,t,n,i,s,u,o,r,v,c,d;try{if((()=>{if(b.isClient()){let a=urlHashParams?urlHashParams.get("WebUrl"):"";try{const e=new URL(a);a=`${e.protocol}//${e.host}`}catch(e){a="",console.warn("解析 WebUrl 参数失败:",e)}if(a)return!1;if(!localStorage.getItem("server_host"))return!0}return!1})())return void(_e.value=!0);const p=(()=>{const e={};if(re.query.type&&(e.type=re.query.type),re.query.wp&&(e.wp=re.query.wp),re.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(re.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){ce.value=m.data.idpList;const e=re.query.idp_id||Ae.loginType;if(e&&"undefined"!==e){let u=!1;for(const a of m.data.idpList)e===a.id&&(u=!0,me.value=a.id,de.value=a.type,pe.value=a.templateType,ye.value=a.attrs,ye.value.name=a.name,ye.value.authType=a.type);u||(he.value=null==(a=ce.value[0])?void 0:a.id,me.value=null==(l=ce.value[0])?void 0:l.id,de.value=null==(t=ce.value[0])?void 0:t.type,pe.value=null==(n=ce.value[0])?void 0:n.templateType,ye.value=null==(i=ce.value[0])?void 0:i.attrs,ye.value.name=ce.value[0].name,ye.value.authType=null==(s=ce.value[0])?void 0:s.type)}else he.value=null==(u=ce.value[0])?void 0:u.id,me.value=null==(o=ce.value[0])?void 0:o.id,de.value=null==(r=ce.value[0])?void 0:r.type,pe.value=null==(v=ce.value[0])?void 0:v.templateType,ye.value=null==(c=ce.value[0])?void 0:c.attrs,ye.value.name=ce.value[0].name,ye.value.authType=null==(d=ce.value[0])?void 0:d.type;++ve.value}}catch(p){console.error("获取认证列表失败:",p),b.isClient()&&(_e.value=!0)}};Ve();const Ue=i((()=>{switch(de.value){case"local":case"msad":case"ldap":case"web":case"email":return ae;case"qiyewx":return le;case"feishu":return te;case"dingtalk":return ne;case"oauth2":case"cas":return ie;case"sms":return se;default:return"oauth2"===pe.value?ie:"local"}})),$e=i((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===Ce.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===Ce.value}])),Ke=()=>{fe.value=!1,ge.value=[],xe.value="",we.value="",Ce.value="",be.value=!1,Le.value&&(me.value=Le.value,de.value=Te.value,pe.value=Pe.value,ye.value={...Oe.value},Le.value="",Te.value="",Pe.value="",Oe.value={}),++ve.value,console.log("取消后恢复的状态:",{isSecondary:fe.value,auth_id:me.value,auth_type:de.value})},Ne=async e=>{const a=C.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=re.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},ze=i((()=>!["dingtalk","feishu","qiyewx"].includes(de.value)&&("oauth2"!==pe.value&&"cas"!==de.value||("cas"===de.value?1===parseInt(ye.value.casOpenType):"oauth2"===pe.value&&1===parseInt(ye.value.oauth2OpenType))))),He=()=>{Ee.value>0&&Ee.value--},Je=()=>{Ee.value<Se.value&&Ee.value++},Me=e=>{he.value=e.id,ye.value=e.attrs||{},ye.value.name=e.name,ye.value.authType=e.type,fe.value&&(ye.value.uniqKey=xe.value,ye.value.notPhone=ke.value),me.value=e.id,de.value=e.type,pe.value=e.templateType,++ve.value};return u(fe,(async()=>{fe.value&&(Le.value=me.value,Te.value=de.value,Pe.value=pe.value,Oe.value={...ye.value},console.log("二次认证数据:",{secondary:ge.value,secondaryLength:ge.value.length}),ge.value.length>0&&Me(ge.value[0]))})),o("secondary",ge),o("isSecondary",fe),o("uniqKey",xe),o("userName",we),o("notPhone",ke),o("last_id",he),o("contactType",Ce),o("hasContactInfo",be),(e,a)=>(r(),v("div",L,[c("div",T,[a[6]||(a[6]=c("div",{class:"left-panel"},[d(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),d('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),d(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),c("div",P,[d(" 服务器配置状态 "),_e.value?(r(),v("div",O,[p(m(oe),{onServerConfigured:De})])):fe.value?(r(),v(h,{key:2},[d(" 二次认证等待状态 "),c("div",F,[c("div",X,[(r(),v("svg",G,[c("use",{"xlink:href":`#icon-auth-${Te.value||de.value}`},null,8,Q)]))]),c("h4",Y,g(Oe.value.name||ye.value.name)+" 登录成功",1),a[5]||(a[5]=c("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),c("div",Z,[(r(),v("svg",ee,a[3]||(a[3]=[c("use",{"xlink:href":"#icon-shield"},null,-1)]))),a[4]||(a[4]=c("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(r(),v(h,{key:1},[d(" 正常登录状态 "),c("div",null,["local"===de.value?(r(),v("span",E,"本地账号登录")):ze.value?(r(),v("span",I,[c("div",j,[c("span",q,[(r(),v("svg",R,[c("use",{"xlink:href":"#icon-auth-"+de.value},null,8,S)])),y(" "+g(ye.value.name),1)])])])):d("v-if",!0),me.value?(r(),v("div",A,[d(' <component :is="getLoginType"></component> '),(r(),f(_(Ue.value),{auth_id:me.value,auth_info:ye.value},null,8,["auth_id","auth_info"])),d(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):d("v-if",!0),Re.value.length>0?(r(),v("div",D,[a[2]||(a[2]=c("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),c("div",V,[Ee.value>0?(r(),v("button",{key:0,class:"auth-nav-btn auth-nav-prev",onClick:He,disabled:0===Ee.value},[(r(),v("svg",$,a[0]||(a[0]=[c("use",{"xlink:href":"#icon-chevron-left"},null,-1)])))],8,U)):d("v-if",!0),c("div",K,[c("div",{class:"auth-methods-container",ref_key:"authMethodsContainer",ref:qe,style:x({transform:`translateX(-${Ee.value*Ie.value}px)`})},[(r(!0),v(h,null,w(Re.value,(e=>(r(),v("div",{key:e.id,class:"auth-method-item",onClick:a=>Me(e)},[c("div",{class:"auth-method-icon","data-auth-type":e.type},[(r(),v("svg",H,[c("use",{"xlink:href":`#icon-auth-${e.type}`},null,8,J)]))],8,z),c("div",M,g(e.name),1)],8,N)))),128))],4)]),Ee.value<Se.value?(r(),v("button",{key:1,class:"auth-nav-btn auth-nav-next",onClick:Je,disabled:Ee.value>=Se.value},[(r(),v("svg",B,a[1]||(a[1]=[c("use",{"xlink:href":"#icon-chevron-right"},null,-1)])))],8,W)):d("v-if",!0)])])):d("v-if",!0)])],2112))])]),d(" 二次认证弹窗 "),fe.value?(r(),f(m(ue),{key:0,"auth-info":{uniqKey:xe.value,contactType:Ce.value,hasContactInfo:be.value},"auth-id":me.value,"user-name":we.value,"last-id":he.value,"auth-methods":$e.value,onVerificationSuccess:Ne,onCancel:Ke},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):d("v-if",!0)]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]);export{ae as default};
