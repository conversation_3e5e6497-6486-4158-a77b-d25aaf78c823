/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
import{_ as e,b as t,q as o,o as n,d as r}from"./index.5ebe5a72.js";const i=e(Object.assign({name:"Verify"},{setup(e){const i=location.href.split("?")[1],a=new URLSearchParams(i),s=Object.fromEntries(a.entries()),c=t(),l=document.location.protocol+"//"+document.location.host,u=new URLSearchParams;"client"===s.type&&(u.set("type","client"),s.wp&&u.set("wp",s.wp));const p={method:"GET",url:`${l}/auth/user/v1/redirect_verify?redirect_url=${s.redirect_url}`,headers:{Accept:"application/json, text/plain, */*",Authorization:`${c.token.tokenType} ${c.token.accessToken}`}};return o.request(p).then((function(e){if(200===e.status){let t=e.data.url;if(u.toString()){const e=t.includes("?")?"&":"?";t+=e+u.toString()}window.location.href=t}})).catch((function(e){console.error(e)})),(e,t)=>(n(),r("div"))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/verify.vue"]]);export{i as default};
