/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
import{_ as e,b as t,q as o,o as n,d as r}from"./index.a5cb1178.js";const i=e(Object.assign({name:"Verify"},{setup(e){const i=location.href.split("?")[1],a=new URLSearchParams(i),c=Object.fromEntries(a.entries()),s=t(),l=document.location.protocol+"//"+document.location.host,u=new URLSearchParams;"client"===c.type&&(u.set("type","client"),c.wp&&u.set("wp",c.wp));const p={method:"GET",url:`${l}/auth/user/v1/redirect_verify?redirect_url=${c.redirect_url}`,headers:{Accept:"application/json, text/plain, */*",Authorization:`${s.token.tokenType} ${s.token.accessToken}`}};return o.request(p).then((function(e){if(200===e.status){let t=e.data.url;if(u.toString()){const e=t.includes("?")?"&":"?";t+=e+u.toString()}window.location.href=t}})).catch((function(e){console.error(e)})),(e,t)=>(n(),r("div"))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/verify.vue"]]);export{i as default};
