import { createApp, h } from 'vue'

const MessageBoxComponent = {
  name: 'BaseMessageBox',
  props: {
    title: {
      type: String,
      default: '提示'
    },
    message: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'warning', 'info', 'error'].includes(value)
    },
    showCancelButton: {
      type: Boolean,
      default: false
    },
    confirmButtonText: {
      type: String,
      default: '确定'
    },
    cancelButtonText: {
      type: String,
      default: '取消'
    }
  },
  data() {
    return {
      visible: true
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm')
      this.close()
    },
    handleCancel() {
      this.$emit('cancel')
      this.close()
    },
    close() {
      this.visible = false
      setTimeout(() => {
        this.$el.remove()
      }, 300)
    }
  },
  render() {
    if (!this.visible) return null
    
    return h('div', {
      class: 'base-message-box-overlay',
      style: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    }, [
      h('div', {
        class: 'base-message-box',
        style: {
          backgroundColor: '#fff',
          borderRadius: '4px',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
          minWidth: '300px',
          maxWidth: '500px',
          padding: '20px'
        }
      }, [
        // 标题
        h('div', {
          style: {
            fontSize: '16px',
            fontWeight: 'bold',
            marginBottom: '10px',
            color: '#303133'
          }
        }, this.title),
        
        // 内容
        h('div', {
          style: {
            fontSize: '14px',
            color: '#606266',
            marginBottom: '20px',
            lineHeight: '1.5'
          }
        }, this.message),
        
        // 按钮区域
        h('div', {
          style: {
            textAlign: 'right'
          }
        }, [
          this.showCancelButton && h('button', {
            style: {
              padding: '8px 16px',
              marginRight: '10px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              backgroundColor: '#fff',
              color: '#606266',
              cursor: 'pointer'
            },
            onClick: this.handleCancel
          }, this.cancelButtonText),
          
          h('button', {
            style: {
              padding: '8px 16px',
              border: 'none',
              borderRadius: '4px',
              backgroundColor: '#536ce6',
              color: '#fff',
              cursor: 'pointer'
            },
            onClick: this.handleConfirm
          }, this.confirmButtonText)
        ])
      ])
    ])
  }
}

const MessageBox = (options) => {
  return new Promise((resolve, reject) => {
    const container = document.createElement('div')
    document.body.appendChild(container)
    
    const app = createApp(MessageBoxComponent, {
      ...options,
      onConfirm: () => {
        app.unmount()
        document.body.removeChild(container)
        resolve('confirm')
      },
      onCancel: () => {
        app.unmount()
        document.body.removeChild(container)
        reject('cancel')
      }
    })
    
    app.mount(container)
  })
}

// 添加快捷方法
MessageBox.confirm = (message, title = '确认', options = {}) => {
  return MessageBox({
    message,
    title,
    showCancelButton: true,
    ...options
  })
}

MessageBox.alert = (message, title = '提示', options = {}) => {
  return MessageBox({
    message,
    title,
    showCancelButton: false,
    ...options
  })
}

export { MessageBox }
