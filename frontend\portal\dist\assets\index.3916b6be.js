/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import{x as e,y as a,u as l,r as t,c as n,b as i,z as u,p as o,h as s,o as r,d as v,e as c,j as d,m as p,k as m,t as y,g as h,f as g,A as f,w as _,B as w,L as x,C as k,F as C,i as b}from"./index.5a1fa56a.js";const T={class:"login-page"},E={class:"content"},P={class:"right-panel"},L={key:0},O={key:1},I={key:0,class:"title"},j={key:1,class:"title"},q={style:{"text-align":"center"}},R={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},S={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},A=["xlink:href"],V={key:2,class:"login_panel_form"},D={key:3},U=["onClick"],B={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},z=["xlink:href"],K={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},N={key:2,class:"auth-waiting"},$={class:"waiting-icon"},H={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},J=["xlink:href"],W={class:"waiting-title"},F={class:"security-tips"},M=Object.assign({name:"Login"},{setup(M){const G=a({loader:()=>w((()=>import("./localLogin.0c3bd5e8.js")),["./localLogin.0c3bd5e8.js","./index.5a1fa56a.js","./index.3cca5a4d.css","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Q=a({loader:()=>w((()=>import("./wechat.11d59616.js")),["./wechat.11d59616.js","./index.5a1fa56a.js","./index.3cca5a4d.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),X=a({loader:()=>w((()=>import("./feishu.da19abf5.js")),["./feishu.da19abf5.js","./index.5a1fa56a.js","./index.3cca5a4d.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Y=a({loader:()=>w((()=>import("./dingtalk.e17ef12d.js")),["./dingtalk.e17ef12d.js","./index.5a1fa56a.js","./index.3cca5a4d.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Z=a({loader:()=>w((()=>import("./oauth2.67d0589a.js")),["./oauth2.67d0589a.js","./index.5a1fa56a.js","./index.3cca5a4d.css","./oauth2.79676400.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ee=a({loader:()=>w((()=>import("./sms.5ea3c105.js")),["./sms.5ea3c105.js","./index.5a1fa56a.js","./index.3cca5a4d.css","./sms.ef70f8fb.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ae=a({loader:()=>w((()=>import("./secondaryAuth.4e0fda31.js")),["./secondaryAuth.4e0fda31.js","./verifyCode.677a5021.js","./index.5a1fa56a.js","./index.3cca5a4d.css","./verifyCode.978f9466.css","./secondaryAuth.170537bd.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=a({loader:()=>w((()=>import("./serverConfig.6a2281a9.js")),["./serverConfig.6a2281a9.js","./index.5a1fa56a.js","./index.3cca5a4d.css","./serverConfig.89a0076e.css"],import.meta.url),loadingComponent:x,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=l(),ne=t(0),ie=t([]),ue=t("local"),oe=t(""),se=t(""),re=t(""),ve=t([]),ce=t([]),de=t(!1),pe=t(!1),me=t(),ye=t(""),he=t(!1),ge=t(""),fe=t(!1),_e=t(""),we=t(""),xe=t(""),ke=t({}),Ce=n((()=>{const e=de.value?_e.value:se.value;return ie.value.filter((a=>a.id!==e))})),be=i();n((()=>ce.value.filter((e=>e.id!==se.value))));const Te=e=>{logger.log("服务器配置完成:",e),pe.value=!1,Ee()},Ee=async()=>{var a,l,t,n,i,u,o,s,r,v,c,d;try{if((()=>{if(k.isClient()){let a=urlHashParams?urlHashParams.get("WebUrl"):"";try{const e=new URL(a);a=`${e.protocol}//${e.host}`}catch(e){a="",console.warn("解析 WebUrl 参数失败:",e)}if(a)return!1;if(!localStorage.getItem("server_host"))return!0}return!1})())return void(pe.value=!0);const p=(()=>{const e={};if(te.query.type&&(e.type=te.query.type),te.query.wp&&(e.wp=te.query.wp),te.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(te.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){ie.value=m.data.idpList;const e=te.query.idp_id||be.loginType;if(e&&"undefined"!==e){let o=!1;for(const a of m.data.idpList)e===a.id&&(o=!0,se.value=a.id,ue.value=a.type,oe.value=a.templateType,ve.value=a.attrs,ve.value.name=a.name,ve.value.authType=a.type);o||(re.value=null==(a=ie.value[0])?void 0:a.id,se.value=null==(l=ie.value[0])?void 0:l.id,ue.value=null==(t=ie.value[0])?void 0:t.type,oe.value=null==(n=ie.value[0])?void 0:n.templateType,ve.value=null==(i=ie.value[0])?void 0:i.attrs,ve.value.name=ie.value[0].name,ve.value.authType=null==(u=ie.value[0])?void 0:u.type)}else re.value=null==(o=ie.value[0])?void 0:o.id,se.value=null==(s=ie.value[0])?void 0:s.id,ue.value=null==(r=ie.value[0])?void 0:r.type,oe.value=null==(v=ie.value[0])?void 0:v.templateType,ve.value=null==(c=ie.value[0])?void 0:c.attrs,ve.value.name=ie.value[0].name,ve.value.authType=null==(d=ie.value[0])?void 0:d.type;++ne.value}}catch(p){console.error("获取认证列表失败:",p),k.isClient()&&(pe.value=!0)}};Ee();const Pe=n((()=>{switch(ue.value){case"local":case"msad":case"ldap":case"web":case"email":return G;case"qiyewx":return Q;case"feishu":return X;case"dingtalk":return Y;case"oauth2":case"cas":return Z;case"sms":return ee;default:return"oauth2"===oe.value?Z:"local"}})),Le=n((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===ge.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===ge.value}])),Oe=()=>{de.value=!1,ce.value=[],me.value="",ye.value="",ge.value="",fe.value=!1,_e.value&&(se.value=_e.value,ue.value=we.value,oe.value=xe.value,ve.value={...ke.value},_e.value="",we.value="",xe.value="",ke.value={}),++ne.value,console.log("取消后恢复的状态:",{isSecondary:de.value,auth_id:se.value,auth_type:ue.value})},Ie=async e=>{const a=x.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=te.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},je=n((()=>!["dingtalk","feishu","qiyewx"].includes(ue.value)&&("oauth2"!==oe.value&&"cas"!==ue.value||("cas"===ue.value?1===parseInt(ve.value.casOpenType):"oauth2"===oe.value&&1===parseInt(ve.value.oauth2OpenType))))),qe=e=>{re.value=e.id,ve.value=e.attrs||{},ve.value.name=e.name,ve.value.authType=e.type,de.value&&(ve.value.uniqKey=me.value,ve.value.notPhone=he.value),se.value=e.id,ue.value=e.type,oe.value=e.templateType,++ne.value};return u(de,(async(e,a)=>{de.value&&(_e.value=se.value,we.value=ue.value,xe.value=oe.value,ke.value={...ve.value},console.log("二次认证数据:",{secondary:ce.value,secondaryLength:ce.value.length}),ce.value.length>0&&qe(ce.value[0]))})),o("secondary",ce),o("isSecondary",de),o("uniqKey",me),o("userName",ye),o("notPhone",he),o("last_id",re),o("contactType",ge),o("hasContactInfo",fe),(e,a)=>{const l=s("base-divider"),t=s("base-avatar"),n=s("base-carousel-item"),i=s("base-carousel"),u=s("base-icon");return r(),v("div",T,[c("div",E,[a[3]||(a[3]=c("div",{class:"left-panel"},null,-1)),c("div",P,[pe.value?(r(),v("div",L,[d(p(le),{onServerConfigured:Te})])):de.value?(r(),v("div",N,[c("div",$,[(r(),v("svg",H,[c("use",{"xlink:href":`#icon-auth-${we.value||ue.value}`},null,8,J)]))]),c("h4",W,y(ke.value.name||ve.value.name)+" 登录成功",1),a[2]||(a[2]=c("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),c("div",F,[d(u,{name:"shield",style:{color:"#67c23a"}}),a[1]||(a[1]=c("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])):(r(),v("div",O,["local"===ue.value?(r(),v("span",I,"本地账号登录")):je.value?(r(),v("span",j,[c("div",q,[c("span",R,[(r(),v("svg",S,[c("use",{"xlink:href":"#icon-auth-"+ue.value},null,8,A)])),m(" "+y(ve.value.name),1)])])])):h("",!0),se.value?(r(),v("div",V,[(r(),g(f(Pe.value),{auth_id:se.value,auth_info:ve.value},null,8,["auth_id","auth_info"]))])):h("",!0),Ce.value.length>0?(r(),v("div",D,[d(l,null,{default:_((()=>a[0]||(a[0]=[c("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)]))),_:1,__:[0]}),(r(),g(i,{key:ne.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:_((()=>[(r(!0),v(C,null,b(Math.ceil(Ce.value.length/2),(e=>(r(),g(n,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:_((()=>[(r(!0),v(C,null,b(Ce.value.slice(2*(e-1),2*(e-1)+2),(e=>(r(),v("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:a=>qe(e)},[c("div",null,[d(t,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:_((()=>[(r(),v("svg",B,[c("use",{"xlink:href":"#icon-auth-"+e.type},null,8,z)]))])),_:2},1024)]),c("div",K,y(e.name),1)],8,U)))),128))])),_:2},1024)))),128))])),_:1}))])):h("",!0)]))])]),de.value?(r(),g(p(ae),{key:0,"auth-info":{uniqKey:me.value,contactType:ge.value,hasContactInfo:fe.value},"auth-id":se.value,"user-name":ye.value,"last-id":re.value,"auth-methods":Le.value,onVerificationSuccess:Ie,onCancel:Oe},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):h("",!0)])}}});export{M as default};
