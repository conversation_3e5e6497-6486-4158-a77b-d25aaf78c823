<!doctype html><html lang="zh-cn"><head><undefined></undefined><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="./assets/favicon.2521b69c.ico"><title></title><script>// 实现日志打印封装方法
        class Logger {
            constructor(name, debug) {
                this.name = name
                this.debug = debug
            }
            log(...args) {
                if (!this.debug) {
                    return
                }
                const date = new Date()
                const currTime = date.toLocaleString('zh-CN') + ' ' + date.getMilliseconds()
                console.log(currTime, this.name, ...args)
            }
        }
        const urlHashParams = new URLSearchParams(window.location.hash)
        const logger = new Logger('portal', urlHashParams.get('asec_debug'))
        logger.log('启动')</script><script crossorigin src="./assets/index.2158f45a.js"></script></head><body><div id="app"></div><script>try {
            window.globalData = JSON.parse('<?- global_data_string ?>');
        } catch (error) {

        }
        window.getQRCode = function(config){
            new WwLogin(config);
        };</script></body><script>// 检测浏览器是否为IE
    var isIE = (!!window.ActiveXObject || "ActiveXObject" in window)

    // 如果是IE浏览器，则提示不支持
    if (isIE) {
        var unsupportedMessage = "<div style='padding: 20px; background-color: #f8d7da; color: #721c24;'>" +
            "<h3>对不起，您正在使用的浏览器版本过低。</h3>" +
            "<p>本网站不支持IE浏览器，请使用其它内核浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。</p>" +
            "<p>如果使用360浏览器、搜狗浏览器等请不要使用兼容模式。</p>" +
            "</div>";

        var body = document.getElementsByTagName("body")[0];
        body.innerHTML = unsupportedMessage;
    }</script></html>