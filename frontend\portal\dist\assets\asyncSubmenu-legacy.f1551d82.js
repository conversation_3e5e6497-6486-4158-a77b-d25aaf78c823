/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
System.register(["./index-legacy.00b16b45.js"],(function(e,t){"use strict";var n,a,o,u,r,i,c,l,f,s,d,m,v,p,b,_=document.createElement("style");return _.textContent='@charset "UTF-8";.el-sub-menu[data-v-1f4df5c8] .el-sub-menu__title{padding:6px;color:var(--1f4df5c8-normalText);color:rgba(255,255,255,.675)}.el-sub-menu[data-v-1f4df5c8] .el-sub-menu__title .el-sub-menu__icon-arrow{transform:rotate(0)}.el-sub-menu[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title .el-sub-menu__icon-arrow{transform:rotate(-90deg)}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title{padding-left:18px!important;flex:1;opacity:100%;height:40px;line-height:40px;border-left:4px #71BDDF solid;background:#465566!important;border-radius:4px}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title .el-sub-menu__icon-arrow{transform:rotate(-90deg)}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title i{color:var(--1f4df5c8-activeText)}.is-active[data-v-1f4df5c8]:not(.is-opened) .el-sub-menu__title span{opacity:100%;color:var(--1f4df5c8-activeText)}.is-active[data-v-1f4df5c8]:not(.is-opened){background:#465566!important}\n',document.head.appendChild(_),{setters:[function(e){n=e._,a=e.P,o=e.r,u=e.z,r=e.h,i=e.o,c=e.g,l=e.w,f=e.d,s=e.I,d=e.f,m=e.e,v=e.t,p=e.F,b=e.R}],execute:function(){var t={key:0,class:"gva-subMenu"},_=Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){a((function(e){return{"1f4df5c8-normalText":g.value,"1f4df5c8-activeText":x.value}}));var n=e,_=o(n.theme.activeBackground),x=o(n.theme.activeText),g=o(n.theme.normalText);return u((function(){return n.theme}),(function(){_.value=n.theme.activeBackground,x.value=n.theme.activeText,g.value=n.theme.normalText})),function(n,a){var o=r("el-sub-menu");return i(),c(o,{ref:"subMenu",index:e.routerInfo.name},{title:l((function(){return[e.isCollapse?(i(),f(p,{key:1},[e.routerInfo.meta.icon?(i(),f("i",{key:0,class:s(["iconfont",e.routerInfo.meta.icon])},null,2)):d("v-if",!0),m("span",null,v(e.routerInfo.meta.title),1)],64)):(i(),f("div",t,[e.routerInfo.meta.icon?(i(),f("i",{key:0,class:s(["iconfont",e.routerInfo.meta.icon])},null,2)):d("v-if",!0),m("span",null,v(e.routerInfo.meta.title),1)]))]})),default:l((function(){return[b(n.$slots,"default",{},void 0,!0)]})),_:3},8,["index"])}}});e("default",n(_,[["__scopeId","data-v-1f4df5c8"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/asyncSubmenu.vue"]]))}}}));
