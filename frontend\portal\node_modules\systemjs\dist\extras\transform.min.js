!function(t){var r=t.System.constructor.prototype,e=r.instantiate;r.instantiate=function(t,r,n){if(".wasm"===t.slice(-5))return e.call(this,t,r,n);var s=this;return fetch(t,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw Error(("Fetch error: "+t.status+" "+t.statusText+(r?" loading from "+r:"")||"")+" (SystemJS Error#"+7+" https://github.com/systemjs/systemjs/blob/main/docs/errors.md#7)");return t.text()})).then((function(r){return s.transform.call(this,t,r)})).then((function(r){return(0,eval)(r+"\n//# sourceURL="+t),s.getRegister(t)}))},r.transform=function(t,r){return r}}("undefined"!=typeof self?self:global);
//# sourceMappingURL=transform.min.js.map
