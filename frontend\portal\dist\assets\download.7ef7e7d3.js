/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{_ as e,r as a,h as n,o as i,d as o,e as t,j as l,w as s,f as d,k as r,g as c,I as w,M as u}from"./index.f6c71253.js";import{g as p,b as g}from"./browser.53cf9c7e.js";const v={class:"client"},h={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}},f={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},m={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px","margin-left":"39%",display:"none"}},y={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},x={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-left":"39%","margin-top":"60px",display:"none"}},_={key:0,class:"loading-overlay"},b={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},k={key:0,class:"loading-overlay"},F={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},S=e({__name:"download",setup(e){const S=a(""),M=a(""),L=a(!1),R=a(!1),z=a(!1),E=a(!1),I=a({windows:0,darwin:0}),U=e=>100===e?"完成":`${e}%`,j=()=>{L.value=!1,R.value=!1,z.value=!1,E.value=!1,Object.keys(I.value).forEach((e=>{I.value[e]=0}))},C=a(!1),O=async e=>{if(("android"===e||"ios"===e)&&C.value)return;C.value=!0;const a={windows:L,darwin:R,ios:z,android:E}[e];a.value=!0;try{const a=await p({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);if("ios"===e){const e=await g.toDataURL(a.data.data.download_url),n=document.getElementById("ioscanvas");if(M.value=e,n){const a=n.getContext("2d"),i=new Image;i.onload=()=>{n.width=i.width,n.height=i.height,a.drawImage(i,0,0)},i.src=e}}else if("android"===e){const e=window.location.port,n=new URL(a.data.data.download_url);let i;e?n.toString().includes("asec-deploy")?i=a.data.data.download_url:(n.port=e,i=n.toString()):(n.port="",i=n.toString());const o=await g.toDataURL(i),t=document.getElementById("canvas");if(S.value=o,t){const e=t.getContext("2d"),a=new Image;a.onload=()=>{t.width=a.width,t.height=a.height,e.drawImage(a,0,0)},a.src=o}}else{const n=window.location.port,i=new URL(a.data.data.download_url);let o,t;n?(i.toString().includes("asec-deploy")?o=a.data.data.download_url:(i.port=n,o=i.toString()),t=a.data.data.latest_filename.replace(/@(\d+)/,`@${n}`)):(i.port="",o=i.toString(),t=a.data.data.latest_filename);const l=await((e,a)=>new Promise(((n,i)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onprogress=e=>{if(e.lengthComputable){const n=e.loaded/e.total*100;I.value[a]=Math.round(n)}},o.onload=()=>{200===o.status?n(o.response):i(new Error("下载失败"))},o.onerror=()=>{i(new Error("网络错误"))},o.send()})))(o,e);((e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const n=document.createElement("a"),i=document.querySelector("body");n.href=window.URL.createObjectURL(e),n.download=a,n.style.display="none",i.appendChild(n),n.click(),i.removeChild(n),window.URL.revokeObjectURL(n.href)}j()})(l,t)}}catch(n){u({type:"error",message:n.message||"下载失败，请联系管理员"})}finally{a.value=!1}};return(e,a)=>{const u=n("base-link"),p=n("base-progress"),g=n("base-main");return i(),o("div",null,[t("div",v,[l(g,null,{default:s((()=>[t("div",h,[d(" Windows 客户端 "),t("div",{style:{float:"left","margin-right":"5%",width:"209px",height:"209px",background:"#F1F8FF"},onClick:a[0]||(a[0]=e=>O("windows"))},[(i(),o("svg",f,a[6]||(a[6]=[t("use",{"xlink:href":"#icon-windows"},null,-1)]))),(i(),o("svg",m,a[7]||(a[7]=[t("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[10]||(a[10]=t("br",null,null,-1)),l(u,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:s((()=>a[8]||(a[8]=[r("Windows客户端")]))),_:1,__:[8]}),l(u,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:s((()=>a[9]||(a[9]=[r("点击下载Windows客户端")]))),_:1,__:[9]}),L.value?(i(),c(p,{key:0,percentage:I.value.windows,format:U,style:{"margin-top":"10px"}},null,8,["percentage"])):d("v-if",!0)]),d(" Mac 客户端 "),t("div",{style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onClick:a[1]||(a[1]=e=>O("darwin"))},[(i(),o("svg",y,a[11]||(a[11]=[t("use",{"xlink:href":"#icon-mac"},null,-1)]))),(i(),o("svg",x,a[12]||(a[12]=[t("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),a[15]||(a[15]=t("br",null,null,-1)),l(u,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:s((()=>a[13]||(a[13]=[r("Mac客户端")]))),_:1,__:[13]}),l(u,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:s((()=>a[14]||(a[14]=[r("点击下载Mac客户端")]))),_:1,__:[14]}),R.value?(i(),c(p,{key:0,percentage:I.value.darwin,format:U,style:{"margin-top":"10px"}},null,8,["percentage"])):d("v-if",!0)]),d(" iOS 客户端 "),t("div",{class:w(["ios-container",{loading:z.value}]),style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%",position:"relative"},onMousemove:a[2]||(a[2]=e=>O("ios")),onMouseleave:a[3]||(a[3]=e=>C.value=!1)},[z.value?(i(),o("div",_,a[16]||(a[16]=[t("div",{class:"loading-spinner"},[t("div",{class:"spinner"}),t("div",{class:"loading-text"},"下载码生成中...")],-1)]))):d("v-if",!0),(i(),o("svg",b,a[17]||(a[17]=[t("use",{"xlink:href":"#icon-ios"},null,-1)]))),a[19]||(a[19]=t("br",null,null,-1)),l(u,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:s((()=>a[18]||(a[18]=[r("iOS客户端")]))),_:1,__:[18]}),a[20]||(a[20]=t("div",{id:"ios",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[t("canvas",{id:"ioscanvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],34),d(" Android 客户端 "),t("div",{class:w(["android-container",{loading:E.value}]),style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF",position:"relative"},onMousemove:a[4]||(a[4]=e=>O("android")),onMouseleave:a[5]||(a[5]=e=>C.value=!1)},[E.value?(i(),o("div",k,a[21]||(a[21]=[t("div",{class:"loading-spinner"},[t("div",{class:"spinner"}),t("div",{class:"loading-text"},"下载码生成中...")],-1)]))):d("v-if",!0),(i(),o("svg",F,a[22]||(a[22]=[t("use",{"xlink:href":"#icon-android"},null,-1)]))),a[24]||(a[24]=t("br",null,null,-1)),l(u,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:s((()=>a[23]||(a[23]=[r("Android客户端")]))),_:1,__:[23]}),a[25]||(a[25]=t("div",{id:"android",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[t("canvas",{id:"canvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],34)])])),_:1})])])}}},[["__scopeId","data-v-4f60b33c"],["__file","D:/asec-platform/frontend/portal/src/view/client/download.vue"]]);export{S as default};
