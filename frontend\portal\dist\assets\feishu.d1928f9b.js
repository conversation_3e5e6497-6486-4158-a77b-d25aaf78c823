/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{r as w,u as _,z as v,o as l,d as p,e as o,k as x,X as m}from"./index.d0594432.js";const q={style:{"text-align":"center"}},k={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},b={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},O={name:"<PERSON><PERSON><PERSON>"},E=Object.assign(O,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup(f){const d=w(0),a=f,h=async()=>{const n={type:"feishu",data:{idpId:a.auth_id}},e=await m(n);if(e.status===200)return e.data.uniqKey},i=_(),u=async()=>{await h();const n=a.auth_info.fsAppId;let e=`${window.location.origin}/#/status?stat=0`;if(i.query?.redirect){const t=i.query?.redirect.indexOf("?")>-1?i.query?.redirect.substring(i.query?.redirect.indexOf("?")+1):"";e=e+"&"+t}else if(i.query){const t=new URLSearchParams;for(const[r,s]of Object.entries(i.query))t.append(r,s);e=e+"&"+t.toString()}setTimeout(()=>{var t=`https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${n}&redirect_uri=${encodeURIComponent(e+"&auth_type=feishu")}&response_type=code&state=${a.auth_id}`,r=window.QRLogin({id:"login_container",goto:`${t}`,style:"width:300px;height:300px; border: 0; background-size: cover"}),s=function(c){var g=c.origin;if(r.matchOrigin(g)&&window.location.href.indexOf("/login")>-1){var y=c.data;window.location.href=`${t}&tmp_code=${y}`}};typeof window.addEventListener<"u"?window.addEventListener("message",s,!1):typeof window.attachEvent<"u"&&window.attachEvent("onmessage",s)},100)};return u(),v(a,async(n,e)=>{d.value++,await u()}),(n,e)=>(l(),p("div",{key:d.value},[o("div",q,[o("span",k,[(l(),p("svg",b,e[0]||(e[0]=[o("use",{"xlink:href":"#icon-auth-feishu"},null,-1)]))),e[1]||(e[1]=x(" \u98DE\u4E66\u8BA4\u8BC1 "))])]),e[2]||(e[2]=o("div",{id:"login_container",slot:"content",class:"wechat-class"},null,-1))]))}});export{E as default};
