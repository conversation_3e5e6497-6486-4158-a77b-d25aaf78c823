/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var a,n,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",r=i.toStringTag||"@@toStringTag";function s(e,i,o,r){var s=i&&i.prototype instanceof c?i:c,d=Object.create(s.prototype);return t(d,"_invoke",function(e,t,i){var o,r,s,c=0,d=i||[],u=!1,b={p:0,n:0,v:a,a:p,f:p.bind(a,4),d:function(e,t){return o=e,r=0,s=a,b.n=t,l}};function p(e,t){for(r=e,s=t,n=0;!u&&c&&!i&&n<d.length;n++){var i,o=d[n],p=b.p,v=o[2];e>3?(i=v===t)&&(s=o[(r=o[4])?5:(r=3,3)],o[4]=o[5]=a):o[0]<=p&&((i=e<2&&p<o[1])?(r=0,b.v=t,b.n=o[1]):p<v&&(i=e<3||o[0]>t||t>v)&&(o[4]=e,o[5]=t,b.n=v,r=0))}if(i||e>1)return l;throw u=!0,t}return function(i,d,v){if(c>1)throw TypeError("Generator is already running");for(u&&1===d&&p(d,v),r=d,s=v;(n=r<2?a:s)||!u;){o||(r?r<3?(r>1&&(b.n=-1),p(r,s)):b.n=s:b.v=s);try{if(c=2,o){if(r||(i="next"),n=o[i]){if(!(n=n.call(o,s)))throw TypeError("iterator result is not an object");if(!n.done)return n;s=n.value,r<2&&(r=0)}else 1===r&&(n=o.return)&&n.call(o),r<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),r=1);o=a}else if((n=(u=b.n<0)?s:e.call(t,b))!==l)break}catch(n){o=a,r=1,s=n}finally{c=1}}return{value:n,done:u}}}(e,o,r),!0),d}var l={};function c(){}function d(){}function u(){}n=Object.getPrototypeOf;var b=[][o]?n(n([][o]())):(t(n={},o,(function(){return this})),n),p=u.prototype=c.prototype=Object.create(b);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,t(e,r,"GeneratorFunction")),e.prototype=Object.create(p),e}return d.prototype=u,t(p,"constructor",u),t(u,"constructor",d),d.displayName="GeneratorFunction",t(u,r,"GeneratorFunction"),t(p),t(p,r,"Generator"),t(p,o,(function(){return this})),t(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:s,m:v}})()}function t(e,a,n,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,a,n,i){if(a)o?o(e,a,{value:n,enumerable:!i,configurable:!i,writable:!i}):e[a]=n;else{var r=function(a,n){t(e,a,(function(e){return this._invoke(a,n,e)}))};r("next",0),r("throw",1),r("return",2)}},t(e,a,n,i)}function a(e,t,a,n,i,o,r){try{var s=e[o](r),l=s.value}catch(e){return void a(e)}s.done?t(l):Promise.resolve(l).then(n,i)}System.register(["./index-legacy.50e341a4.js"],(function(t,n){"use strict";var i,o,r,s,l,c,d,u,b,p,v,f,g,m,x=document.createElement("style");return x.textContent='@charset "UTF-8";.setting-page[data-v-ab416471]{display:flex;height:100vh;font-family:PingFang SC,PingFang SC-Regular}.sidebar[data-v-ab416471]{width:60px;background:#667eea;display:flex;flex-direction:column;align-items:center;padding:20px 0;box-shadow:2px 0 8px rgba(0,0,0,.1)}.sidebar .sidebar-menu[data-v-ab416471]{display:flex;flex-direction:column;gap:16px}.sidebar .sidebar-menu .menu-item[data-v-ab416471]{width:48px;height:48px;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:8px;cursor:pointer;transition:all .3s ease;color:rgba(255,255,255,.7);font-size:10px}.sidebar .sidebar-menu .menu-item[data-v-ab416471]:hover{background:rgba(255,255,255,.1);color:#fff}.sidebar .sidebar-menu .menu-item.active[data-v-ab416471]{background:rgba(255,255,255,.2);color:#fff}.sidebar .sidebar-menu .menu-item .menu-icon[data-v-ab416471]{width:16px;height:16px;margin-bottom:4px}.sidebar .sidebar-menu .menu-item .menu-icon[data-v-ab416471]:before{content:"";display:block;width:100%;height:100%;background:currentColor;border-radius:2px}.main-content[data-v-ab416471]{flex:1;padding:20px;overflow-y:auto}.setting-container[data-v-ab416471]{height:calc(100% - 38px);background:white;border-radius:8px;overflow:hidden;box-shadow:0 2px 12px rgba(0,0,0,.1);margin:0 auto}.tabs-header[data-v-ab416471]{display:flex;border-bottom:1px solid #e4e7ed;background:#f5f7fa}.tabs-header .tab-item[data-v-ab416471]{padding:16px 24px;cursor:pointer;color:#606266;font-size:14px;font-weight:500;border-bottom:2px solid transparent;transition:all .3s ease}.tabs-header .tab-item[data-v-ab416471]:hover{color:#409eff;background:rgba(64,158,255,.05)}.tabs-header .tab-item.active[data-v-ab416471]{color:#409eff;border-bottom-color:#409eff;background:white;position:relative}.tabs-header .tab-item.active[data-v-ab416471]:after{content:"";position:absolute;bottom:-1px;left:0;right:0;height:1px;background:white}.tabs-content[data-v-ab416471]{min-height:400px}.tabs-content .tab-panel[data-v-ab416471]{padding:32px}.tabs-content .tab-panel .setting-update[data-v-ab416471]{padding-bottom:24px;margin-bottom:32px;border-bottom:1px solid #f0f0f0}.setting-section .setting-item[data-v-ab416471]{margin-bottom:32px}.setting-section .setting-item[data-v-ab416471]:last-child{margin-bottom:0}.setting-section .setting-platformAddress[data-v-ab416471]{padding-bottom:24px;border-bottom:1px solid #f0f0f0}.setting-section .setting-label[data-v-ab416471]{display:block;font-size:14px;font-weight:500;color:#303133;margin-bottom:12px}.setting-section .setting-input[data-v-ab416471]{width:320px}.setting-section .setting-input[data-v-ab416471] .el-input__inner{height:40px;border-radius:6px}.setting-section .setting-input[data-v-ab416471] .el-input__inner:focus{border-color:#409eff}.setting-section .setting-select[data-v-ab416471]{width:200px}.setting-section .setting-select[data-v-ab416471] .el-select__wrapper{height:40px;border-radius:6px}.setting-section .checkbox-group[data-v-ab416471]{display:flex;flex-direction:column;gap:12px}.setting-section .checkbox-group .setting-checkbox[data-v-ab416471] .el-checkbox__label{font-size:14px;color:#606266}.setting-section .checkbox-group .setting-checkbox[data-v-ab416471] .el-checkbox__input.is-checked .el-checkbox__inner{background-color:#409eff;border-color:#409eff}.about-section .about-title[data-v-ab416471]{font-size:16px;font-weight:600;color:#303133;margin:0 0 24px}.version-info .version-item[data-v-ab416471]{display:flex;align-items:center;justify-content:space-between;padding:16px 0}.version-info .version-item[data-v-ab416471]:last-child{border-bottom:none}.version-info .version-item .version-label[data-v-ab416471]{font-size:14px;color:#606266;flex-shrink:0}.version-info .version-item .version-value[data-v-ab416471]{font-size:14px;color:#303133;font-weight:500}.version-info .version-item .version-value-group[data-v-ab416471]{display:flex;align-items:center;gap:12px}.version-info .version-item .version-value-group .version-value[data-v-ab416471]{font-size:14px;color:#303133;font-weight:500}.copyright[data-v-ab416471]{margin-top:32px;padding-top:20px;border-top:1px solid #f0f0f0}.copyright p[data-v-ab416471]{font-size:12px;color:#909399;margin:0;text-align:center}.setting-footer[data-v-ab416471]{padding:20px 32px;border-top:1px solid #e4e7ed;background:#f5f7fa;display:flex;justify-content:flex-end;gap:12px}.setting-footer .el-button[data-v-ab416471]{padding:8px 20px;border-radius:6px;font-size:14px}@media (max-width: 768px){.setting-page[data-v-ab416471]{flex-direction:column}.sidebar[data-v-ab416471]{width:100%;height:auto;flex-direction:row;padding:16px}.sidebar .sidebar-menu[data-v-ab416471]{flex-direction:row;justify-content:center}.main-content[data-v-ab416471]{padding:24px 32px}.setting-container[data-v-ab416471]{margin:0;border-radius:0}.tabs-content .tab-panel[data-v-ab416471]{padding:20px}.setting-footer[data-v-ab416471]{padding:16px 20px}.tabs-header .tab-item[data-v-ab416471]{padding:12px 16px;font-size:13px}.setting-section .setting-select[data-v-ab416471]{width:100%}}\n',document.head.appendChild(x),{setters:[function(e){i=e._,o=e.r,r=e.N,s=e.h,l=e.o,c=e.d,d=e.f,u=e.e,b=e.I,p=e.j,v=e.w,f=e.t,g=e.M,m=e.k}],execute:function(){var n={class:"setting-page"},x={class:"main-content"},h={class:"setting-container"},y={class:"tabs-header"},k={class:"tabs-content"},w={key:0,class:"tab-panel"},_={class:"setting-section"},j={class:"setting-item setting-platformAddress"},V={class:"setting-item"},S={class:"checkbox-group"},z={key:1,class:"tab-panel"},O={class:"setting-section setting-update"},C={class:"setting-item"},P={class:"checkbox-group"},T={class:"setting-item"},U={class:"about-section"},F={class:"version-info"},G={class:"version-item"},E={class:"version-value-group"},I={class:"version-value"},A={class:"version-item"},N={class:"version-value"},q={class:"version-item"},D={class:"version-value"},J={__name:"setting",setup:function(t){var i=o("general"),J=o(""),M=o(!1),R=o(!0),B=o(!0),H=o("daily"),K=o("2.5.0"),L=o("2025.03.21 09:00"),Q=o("2025.03.21 09:00");r((function(){X()}));var W=function(){var t,n=(t=e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:g.info("正在检查更新..."),setTimeout((function(){g.success("当前已是最新版本")}),1500);case 1:return e.a(2)}}),t)})),function(){var e=this,n=arguments;return new Promise((function(i,o){var r=t.apply(e,n);function s(e){a(r,i,o,s,l,"next",e)}function l(e){a(r,i,o,s,l,"throw",e)}s(void 0)}))});return function(){return n.apply(this,arguments)}}(),X=function(){var e=localStorage.getItem("appSettings");if(e){var t=JSON.parse(e);J.value=t.platformAddress||"",M.value=t.autoStart||!1,R.value=void 0===t.autoConnect||t.autoConnect,B.value=void 0===t.autoUpdate||t.autoUpdate,H.value=t.updateFrequency||"daily"}};return function(e,t){var a=s("base-input"),o=s("base-checkbox"),r=s("base-option"),g=s("base-select"),X=s("base-button");return l(),c("div",n,[d(" 主内容区域 "),u("div",x,[u("div",h,[d(" 标签页导航 "),u("div",y,[u("div",{class:b(["tab-item",{active:"general"===i.value}]),onClick:t[0]||(t[0]=function(e){return i.value="general"})}," 通用设置 ",2),u("div",{class:b(["tab-item",{active:"version"===i.value}]),onClick:t[1]||(t[1]=function(e){return i.value="version"})}," 版本信息 ",2)]),d(" 标签页内容 "),u("div",k,[d(" 通用设置页面 "),"general"===i.value?(l(),c("div",w,[u("div",_,[u("div",j,[t[7]||(t[7]=u("label",{class:"setting-label"},"平台地址",-1)),p(a,{modelValue:J.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return J.value=e}),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:""},null,8,["modelValue"])]),u("div",V,[t[10]||(t[10]=u("label",{class:"setting-label"},"启动选项",-1)),u("div",S,[p(o,{modelValue:M.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return M.value=e}),class:"setting-checkbox"},{default:v((function(){return t[8]||(t[8]=[m(" 开机自启动 ")])})),_:1,__:[8]},8,["modelValue"]),p(o,{modelValue:R.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return R.value=e}),class:"setting-checkbox"},{default:v((function(){return t[9]||(t[9]=[m(" 启动后自动连接 ")])})),_:1,__:[9]},8,["modelValue"])])])])])):d("v-if",!0),d(" 版本信息页面 "),"version"===i.value?(l(),c("div",z,[u("div",O,[u("div",C,[t[12]||(t[12]=u("label",{class:"setting-label"},"更新选项",-1)),u("div",P,[p(o,{modelValue:B.value,"onUpdate:modelValue":t[5]||(t[5]=function(e){return B.value=e}),class:"setting-checkbox"},{default:v((function(){return t[11]||(t[11]=[m(" 自动检查更新 ")])})),_:1,__:[11]},8,["modelValue"])])]),u("div",T,[t[13]||(t[13]=u("label",{class:"setting-label"},"更新检查频率",-1)),p(g,{modelValue:H.value,"onUpdate:modelValue":t[6]||(t[6]=function(e){return H.value=e}),class:"setting-select",placeholder:"请选择"},{default:v((function(){return[p(r,{label:"每天",value:"daily"}),p(r,{label:"每周",value:"weekly"}),p(r,{label:"每月",value:"monthly"})]})),_:1},8,["modelValue"])])]),u("div",U,[t[18]||(t[18]=u("h3",{class:"about-title"},"关于安全客户端",-1)),u("div",F,[u("div",G,[t[15]||(t[15]=u("span",{class:"version-label"},"当前版本",-1)),u("div",E,[u("span",I,f(K.value),1),p(X,{text:"",type:"primary",size:"small",onClick:W},{default:v((function(){return t[14]||(t[14]=[m(" 检查更新 ")])})),_:1,__:[14]})])]),u("div",A,[t[16]||(t[16]=u("span",{class:"version-label"},"构建时间",-1)),u("span",N,f(L.value),1)]),u("div",q,[t[17]||(t[17]=u("span",{class:"version-label"},"上次更新时间",-1)),u("span",D,f(Q.value),1)])]),t[19]||(t[19]=u("div",{class:"copyright"},[u("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])])):d("v-if",!0)])])])])}}};t("default",i(J,[["__scopeId","data-v-ab416471"],["__file","D:/asec-platform/frontend/portal/src/view/client/setting.vue"]]))}}}))}();
