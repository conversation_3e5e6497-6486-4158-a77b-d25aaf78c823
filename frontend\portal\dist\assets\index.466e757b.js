/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
import{_ as e,u as a,a as t,b as o,U as s,r as l,z as n,K as r,P as c,h as u,o as i,d,j as f,w as v,T as m,F as p,i as h,m as b,g,f as x,B as k}from"./index.df61e453.js";import y from"./index.859de795.js";import"./menuItem.1c118cc1.js";import"./asyncSubmenu.725d27a6.js";const T=e(Object.assign({name:"Aside"},{setup(e){const T=a(),j=t(),_=o(),w=s(),B=l({}),F=()=>{switch(_.sideMode){case"#fff":B.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":B.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};F();const M=l("");n((()=>T),(()=>{M.value=T.meta.activeName||T.name}),{deep:!0}),n((()=>_.sideMode),(()=>{F()}));const q=l(!1);(()=>{M.value=T.meta.activeName||T.name;document.body.clientWidth<1e3&&(q.value=!q.value),c.on("collapse",(e=>{q.value=e}))})(),r((()=>{c.off("collapse")}));const D=(e,a,t,o)=>{var s,l;const n={},r={};(null==(s=w.routeMap[e])?void 0:s.parameters)&&(null==(l=w.routeMap[e])||l.parameters.forEach((e=>{"query"===e.type?n[e.key]=e.value:r[e.key]=e.value}))),e!==T.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):j.push({name:e,query:n,params:r}))};return(e,a)=>{const t=u("base-menu"),o=u("base-scrollbar");return i(),d("div",{style:k({background:b(_).sideMode})},[f(o,{height:"calc(100vh - 110px)"},{default:v((()=>[f(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:v((()=>[f(t,{collapse:q.value,"collapse-transition":!1,"default-active":M.value,"background-color":B.value.background,"active-text-color":B.value.active,mode:"vertical","unique-opened":!0,onSelect:D},{default:v((()=>[(i(!0),d(p,null,h(b(w).asyncRouters[0].children,(e=>(i(),d(p,null,[e.hidden?x("v-if",!0):(i(),g(y,{key:e.name,"is-collapse":q.value,"router-info":e,theme:B.value},null,8,["is-collapse","router-info","theme"]))],64)))),256))])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})],4)}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/index.vue"]]);export{T as default};
