/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(e,o,i,a){var c=o&&o.prototype instanceof u?o:u,l=Object.create(c.prototype);return r(l,"_invoke",function(e,r,o){var i,a,c,u=0,l=o||[],f=!1,p={p:0,n:0,v:t,a:v,f:v.bind(t,4),d:function(e,r){return i=e,a=0,c=t,p.n=r,s}};function v(e,r){for(a=e,c=r,n=0;!f&&u&&!o&&n<l.length;n++){var o,i=l[n],v=p.p,d=i[2];e>3?(o=d===r)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=v&&((o=e<2&&v<i[1])?(a=0,p.v=r,p.n=i[1]):v<d&&(o=e<3||i[0]>r||r>d)&&(i[4]=e,i[5]=r,p.n=d,a=0))}if(o||e>1)return s;throw f=!0,r}return function(o,l,d){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&v(l,d),a=l,c=d;(n=a<2?t:c)||!f;){i||(a?a<3?(a>1&&(p.n=-1),v(a,c)):p.n=c:p.v=c);try{if(u=2,i){if(a||(o="next"),n=i[o]){if(!(n=n.call(i,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,a<2&&(a=0)}else 1===a&&(n=i.return)&&n.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((n=(f=p.n<0)?c:e.call(r,p))!==s)break}catch(n){i=t,a=1,c=n}finally{u=1}}return{value:n,done:f}}}(e,i,a),!0),l}var s={};function u(){}function l(){}function f(){}n=Object.getPrototypeOf;var p=[][i]?n(n([][i]())):(r(n={},i,(function(){return this})),n),v=f.prototype=u.prototype=Object.create(p);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,r(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return l.prototype=f,r(v,"constructor",f),r(f,"constructor",l),l.displayName="GeneratorFunction",r(f,a,"GeneratorFunction"),r(v),r(v,a,"Generator"),r(v,i,(function(){return this})),r(v,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:d}})()}function r(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}r=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{var a=function(t,n){r(e,t,(function(e){return this._invoke(t,n,e)}))};a("next",0),a("throw",1),a("return",2)}},r(e,t,n,o)}function t(e,r,t,n,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void t(e)}c.done?r(s):Promise.resolve(s).then(n,o)}System.register(["./index-legacy.4bb28e53.js"],(function(r,n){"use strict";var o,i,a,c,s,u,l,f,p,v,d,g,m,h=document.createElement("style");return h.textContent='@charset "UTF-8";.server-config[data-v-17e3a57d]{width:100%;max-width:400px;margin:0 auto;padding:20px}.server-config .config-header[data-v-17e3a57d]{display:flex;align-items:center;justify-content:center;margin-bottom:30px}.server-config .config-header .title[data-v-17e3a57d]{font-size:20px;font-weight:600;color:#333}.server-config .config-form[data-v-17e3a57d]{margin-bottom:30px}.server-config .config-form .label[data-v-17e3a57d]{display:block;margin-bottom:8px;font-weight:500;color:#333}.server-config .config-form .input-tip[data-v-17e3a57d]{margin-top:6px;font-size:12px;color:#999;line-height:1.4}.server-config .config-form .submit-button[data-v-17e3a57d]{width:100%;height:44px;font-size:16px;font-weight:500}.server-config .config-tips .tip-item[data-v-17e3a57d]{display:flex;align-items:center;margin-bottom:12px;font-size:14px;color:#666}.server-config .config-tips .tip-item[data-v-17e3a57d]:last-child{margin-bottom:0}\n',document.head.appendChild(h),{setters:[function(e){o=e._,i=e.r,a=e.J,c=e.h,s=e.o,u=e.d,l=e.e,f=e.j,p=e.w,v=e.k,d=e.Y,g=e.$,m=e.M}],execute:function(){var n={class:"server-config"},h={class:"config-form"},b={class:"config-tips"},y={class:"tip-item"},x={class:"tip-item"},w=Object.assign({name:"ServerConfig"},{emits:["server-configured"],setup:function(r,o){var w=o.emit,_=i(null),k=i(!1),j=a({serverUrl:""}),U={serverUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的服务器地址（需包含 http:// 或 https://）",trigger:"blur"}]},O=function(){var r,n=(r=e().m((function r(){var t,n,o;return e().w((function(e){for(;;)switch(e.n){case 0:if(_.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,_.value.validate();case 2:if(e.v){e.n=3;break}return e.a(2);case 3:if(k.value=!0,!g(j.serverUrl)){e.n=10;break}return e.p=4,e.n=5,fetch("".concat(j.serverUrl,"/auth/idp/list"),{method:"GET",headers:{"Content-Type":"application/json"},timeout:5e3});case 5:if(!(t=e.v).ok&&401!==t.status){e.n=6;break}m.success("服务器连接成功！"),w("server-configured",j.serverUrl),e.n=7;break;case 6:throw new Error("服务器响应错误: ".concat(t.status));case 7:e.n=9;break;case 8:e.p=8,n=e.v,console.warn("服务器连接测试失败，但仍然保存配置:",n),m.warning("服务器地址已保存，但连接测试失败，请检查网络或服务器状态"),w("server-configured",j.serverUrl);case 9:e.n=11;break;case 10:m.error("服务器地址格式错误");case 11:e.n=13;break;case 12:e.p=12,o=e.v,console.error("配置服务器失败:",o),m.error("配置失败，请检查服务器地址格式");case 13:return e.p=13,k.value=!1,e.f(13);case 14:return e.a(2)}}),r,null,[[4,8],[1,12,13,14]])})),function(){var e=this,n=arguments;return new Promise((function(o,i){var a=r.apply(e,n);function c(e){t(a,o,i,c,s,"next",e)}function s(e){t(a,o,i,c,s,"throw",e)}c(void 0)}))});return function(){return n.apply(this,arguments)}}(),T=localStorage.getItem("server_host");return T&&(j.serverUrl=T),function(e,r){var t=c("base-input"),o=c("base-form-item"),i=c("base-button"),a=c("base-form"),g=c("base-icon");return s(),u("div",n,[r[5]||(r[5]=l("div",{class:"config-header"},[l("span",{class:"title"},"平台地址")],-1)),l("div",h,[f(a,{ref_key:"serverForm",ref:_,model:j,rules:U,onKeyup:d(O,["enter"])},{default:p((function(){return[f(o,{prop:"serverUrl"},{default:p((function(){return[f(t,{modelValue:j.serverUrl,"onUpdate:modelValue":r[0]||(r[0]=function(e){return j.serverUrl=e}),size:"large",placeholder:"输入您连接的平台服务器地址","suffix-icon":"link"},null,8,["modelValue"]),r[1]||(r[1]=l("div",{class:"input-tip"},' 请输入平台地址，如：https://*************" ',-1))]})),_:1,__:[1]}),f(o,null,{default:p((function(){return[f(i,{type:"primary",size:"large",class:"submit-button",loading:k.value,onClick:O},{default:p((function(){return r[2]||(r[2]=[v(" 连接服务器 ")])})),_:1,__:[2]},8,["loading"])]})),_:1})]})),_:1},8,["model"])]),l("div",b,[l("div",y,[f(g,{name:"warning",style:{color:"#f4a261","margin-right":"6px"}}),r[3]||(r[3]=l("span",null,"请确保服务器地址正确且网络连通",-1))]),l("div",x,[f(g,{name:"check",style:{color:"#67c23a","margin-right":"6px"}}),r[4]||(r[4]=l("span",null,"配置成功后将自动跳转到登录页面",-1))])])])}}});r("default",o(w,[["__scopeId","data-v-17e3a57d"],["__file","D:/asec-platform/frontend/portal/src/view/login/serverConfig/serverConfig.vue"]]))}}}))}();
