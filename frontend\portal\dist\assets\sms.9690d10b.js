/*! 
 Build based on gin-vue-admin 
 Time : 1749623364000 */
import{_ as e,r as t,l as a,b as s,h as n,o as i,d as l,e as o,t as u,m as r,j as d,w as c,k as p,g as _,f as m,n as y,M as f}from"./index.49a4551d.js";const v={class:"sms"},h={key:0,style:{"margin-bottom":"20px"}},g={key:1,style:{"margin-bottom":"20px"}},b={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},k={style:{"text-align":"center"}},x=e(Object.assign({name:"Sms"},{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}}},emits:["verification-success","back"],setup(e,{emit:x}){const w=t(""),I=a("userName");a("last_id");const N=a("isSecondary"),S=e,j=x,q=t(60);let C;const P=()=>{clearInterval(C)},V=async()=>{if(!S.auth_info.notPhone)return;const e={uniq_key:S.auth_info.uniqKey,idp_id:S.auth_id},t=await y(e);200===t.status&&-1!==t.data.code?(q.value=60,C=setInterval((()=>{q.value--,0===q.value&&P()}),1e3)):(f({showClose:!0,message:t.data.msg,type:"error"}),q.value=0)};V();const z=s(),K=async()=>{const e={uniq_key:S.auth_info.uniqKey,auth_code:w.value,user_name:S.userName||I.value,idp_id:S.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},t=await z.LoginIn(e,"accessory");-1!==t.code&&j("verification-success",t)},O=()=>{j("back"),N&&(N.value=!1)};return(t,a)=>{const s=n("base-button"),y=n("base-input");return i(),l("div",v,[a[3]||(a[3]=o("div",{style:{top:"10px","margin-bottom":"25px","text-align":"center"}},[o("span",{class:"title"},"短信认证")],-1)),o("div",null,[e.auth_info.notPhone?(i(),l("div",h,"验证码已发送至您账号("+u(e.userName||r(I))+")关联的手机，请注意查收",1)):(i(),l("div",g,"您的账号("+u(e.userName||r(I))+")未关联手机号码，请联系管理员！",1)),e.auth_info.notPhone?(i(),l("div",b,[d(y,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),placeholder:"短信验证码",class:"input-with-select"},{append:c((()=>[d(s,{type:"info",disabled:q.value>0,onClick:V},{default:c((()=>[p("重新发送 "+u(q.value>0?`(${q.value}秒)`:""),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue"])])):_("",!0),o("div",k,[e.auth_info.notPhone?(i(),m(s,{key:0,type:"primary",size:"large",disabled:!w.value,onClick:K},{default:c((()=>a[1]||(a[1]=[p("确 定 ")]))),_:1,__:[1]},8,["disabled"])):_("",!0),d(s,{type:"info",size:"large",onClick:O},{default:c((()=>a[2]||(a[2]=[p("取 消 ")]))),_:1,__:[2]})])])])}}}),[["__scopeId","data-v-403e93dc"]]);export{x as default};
