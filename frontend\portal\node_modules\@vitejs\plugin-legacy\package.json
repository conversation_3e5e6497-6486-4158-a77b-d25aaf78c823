{"name": "@vitejs/plugin-legacy", "version": "2.3.1", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts", "prepublishOnly": "npm run build"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-legacy"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-legacy#readme", "dependencies": {"@babel/standalone": "^7.20.0", "core-js": "^3.26.0", "magic-string": "^0.26.7", "regenerator-runtime": "^0.13.10", "systemjs": "^6.13.0"}, "peerDependencies": {"terser": "^5.4.0", "vite": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.19.6", "picocolors": "^1.0.0", "vite": "workspace:*"}}