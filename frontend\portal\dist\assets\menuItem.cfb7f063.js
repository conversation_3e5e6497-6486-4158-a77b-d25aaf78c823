/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{_ as e,O as t,r as a,z as n,h as o,o as i,d as r,e as s,t as l,j as u,g as m,w as c,f,I as d}from"./index.f6c71253.js";const p={key:0,style:{height:"34px","margin-top":"6px","margin-bottom":"6px",border:"4px","line-height":"34px","margin-left":"14px",background:"#2F3C4B","padding-left":"35px","margin-right":"29px"}},v={style:{"font-size":"12px",color:"#FFFFFF",opacity:"1"}},h={key:0,class:"icon iconfont iconfont-active","aria-hidden":"true"},x=["xlink:href"],g={key:1,class:"gva-menu-item"},k={key:0,class:"icon iconfont","aria-hidden":"true"},y=["xlink:href"],I={class:"gva-menu-item-title"},F={name:"MenuItem",setup(){}},b=e(Object.assign(F,{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"e04a4d90-activeBackground":b.value,"e04a4d90-normalText":B.value,"e04a4d90-hoverText":j.value,"e04a4d90-activeText":T.value})));const F=e,b=a(F.theme.activeBackground),T=a(F.theme.activeText),B=a(F.theme.normalText),_=a(F.theme.hoverBackground),j=a(F.theme.hoverText);return n((()=>F.theme),(()=>{b.value=F.theme.activeBackground,T.value=F.theme.activeText,B.value=F.theme.normalText,_.value=F.theme.hoverBackground,j.value=F.theme.hoverText})),(t,a)=>{const n=o("base-icon"),F=o("base-tooltip"),b=o("base-menu-item");return e.routerInfo.meta.isDisabled?(i(),r("div",p,[s("span",v,l(e.routerInfo.meta.title),1),u(n,{color:"#FFFFFF",size:"12px",style:{"padding-left":"17px"},name:"plus"})])):(i(),m(b,{key:1,index:e.routerInfo.name,class:d(e.isCollapse?"base-sam-menu-item":"base-gva-menu-item")},{default:c((()=>[e.isCollapse?(i(),m(F,{key:0,class:"sam-menu-item",effect:"light",content:e.routerInfo.meta.title,placement:"right"},{default:c((()=>[e.routerInfo.meta.icon?(i(),r("svg",h,[s("use",{"xlink:href":`#${e.routerInfo.meta.icon}`},null,8,x)])):f("v-if",!0)])),_:1},8,["content"])):(i(),r("div",g,[e.routerInfo.meta.icon?(i(),r("svg",k,[s("use",{"xlink:href":`#${e.routerInfo.meta.icon}`},null,8,y)])):f("v-if",!0),s("span",I,l(e.routerInfo.meta.title),1)]))])),_:1},8,["index","class"]))}}}),[["__scopeId","data-v-e04a4d90"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/menuItem.vue"]]);export{b as default};
