<template>
  <div>
    <div class="client">
      <base-main>
        <div style="height: 100%;display: flex;justify-content: center;align-items: center">
          <!-- Windows 客户端 -->
          <div
            style="float: left;margin-right: 5%;width: 209px;height: 209px;background: #F1F8FF;"
            @click="download('windows')"
          >
            <svg class="icon window-show" aria-hidden="true" style="font-size: 43px;margin-top: 60px">
              <use xlink:href="#icon-windows" />
            </svg>
            <svg class="icon window-hidden" aria-hidden="true" style="font-size: 43px;margin-top: 60px;margin-left: 39%;display: none">
              <use xlink:href="#icon-xiazai" />
            </svg>
            <br>
            <base-link class="window-show" :underline="false" style="margin-top: 42px">Windows客户端</base-link>
            <base-link class="window-hidden" :underline="false" style="margin-top: 30px;display: none">点击下载Windows客户端</base-link>
            <base-progress v-if="windowsloading" :percentage="downloadProgress.windows" :format="progressFormat" style="margin-top: 10px;" />
          </div>

          <!-- Mac 客户端 -->
          <div
            style="float: left;width: 209px;height: 209px;background: #F1F8FF;margin-right: 5%"
            @click="download('darwin')"
          >
            <svg class="icon window-show" aria-hidden="true" style="font-size: 43px;margin-top: 60px">
              <use xlink:href="#icon-mac" />
            </svg>
            <svg class="icon window-hidden" aria-hidden="true" style="font-size: 43px;margin-left: 39%;margin-top: 60px;display: none">
              <use xlink:href="#icon-xiazai" />
            </svg>
            <br>
            <base-link class="window-show" :underline="false" style="margin-top: 42px">Mac客户端</base-link>
            <base-link class="window-hidden" :underline="false" style="margin-top: 30px;display: none">点击下载Mac客户端</base-link>
            <base-progress v-if="macloading" :percentage="downloadProgress.darwin" :format="progressFormat" style="margin-top: 10px;" />
          </div>

          <!-- iOS 客户端 -->
          <div
            class="ios-container"
            :class="{ 'loading': iosloading }"
            style="float: left;width: 209px;height: 209px;background: #F1F8FF;margin-right: 5%;position: relative;"
            @mousemove="download('ios')"
            @mouseleave="hasExecuted = false"
          >
            <div v-if="iosloading" class="loading-overlay">
              <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">下载码生成中...</div>
              </div>
            </div>
            <svg class="icon window-show" aria-hidden="true" style="font-size: 43px;margin-top: 60px">
              <use xlink:href="#icon-ios" />
            </svg>
            <br>
            <base-link class="window-show" :underline="false" style="margin-top: 42px">iOS客户端</base-link>
            <div id="ios" class="window-hidden" style="width: 100%;height: 100%;display: none">
              <canvas id="ioscanvas" style="top: -16px;position: relative;width: 100%" />
            </div>
          </div>

          <!-- Android 客户端 -->
          <div
            class="android-container"
            :class="{ 'loading': androidloading }"
            style="float: left;width: 209px;height: 209px;background: #F1F8FF;position: relative;"
            @mousemove="download('android')"
            @mouseleave="hasExecuted = false"
          >
            <div v-if="androidloading" class="loading-overlay">
              <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">下载码生成中...</div>
              </div>
            </div>
            <svg class="icon window-show" aria-hidden="true" style="font-size: 43px;margin-top: 60px">
              <use xlink:href="#icon-android" />
            </svg>
            <br>
            <base-link class="window-show" :underline="false" style="margin-top: 42px">Android客户端</base-link>
            <div id="android" class="window-hidden" style="width: 100%;height: 100%;display: none">
              <canvas id="canvas" style="top: -16px;position: relative;width: 100%" />
            </div>
          </div>
        </div>
      </base-main>
    </div>
  </div>
</template>

<script setup>
// 使用轻量级 SVG 图标，已在 main.js 中全局加载
import { ref } from 'vue'
import { getDownloadUrl } from '@/api/system'
import { Message } from '@/components/base'
import QRCode from 'qrcode'
const androidQRCode = ref('')
const iosQRCode = ref('')

const windowsloading = ref(false)
const macloading = ref(false)
const iosloading = ref(false)
const androidloading = ref(false)

const downloadProgress = ref({
  windows: 0,
  darwin: 0
})

const progressFormat = (percentage) => {
  return percentage === 100 ? '完成' : `${percentage}%`
}

const myDownLoad = (url, fileName) => {
  getBolb(url).then((blob) => {
    saveAs(blob, fileName)
  })
}

const getBolb = (url, type) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    
    xhr.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100
        downloadProgress.value[type] = Math.round(percentComplete)
      }
    }
    
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      } else {
        reject(new Error('下载失败'))
      }
    }
    
    xhr.onerror = () => {
      reject(new Error('网络错误'))
    }
    
    xhr.send()
  })
}

const saveAs = (blob, filename) => {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')
    const body = document.querySelector('body')

    link.href = window.URL.createObjectURL(blob)
    link.download = filename

    link.style.display = 'none'
    body.appendChild(link)

    link.click()
    body.removeChild(link)

    window.URL.revokeObjectURL(link.href)
  }
  resetLoading()
}

const resetLoading = () => {
  windowsloading.value = false
  macloading.value = false
  iosloading.value = false
  androidloading.value = false
  Object.keys(downloadProgress.value).forEach(key => {
    downloadProgress.value[key] = 0
  })
}

const hasExecuted = ref(false)
const download = async(type) => {
  if ((type === 'android' || type === 'ios') && hasExecuted.value) {
    return
  }
  hasExecuted.value = true
  const loadingRef = {
    windows: windowsloading,
    darwin: macloading,
    ios: iosloading,
    android: androidloading
  }[type]
  loadingRef.value = true

  try {
    const res = await getDownloadUrl({ platform: type })
    if (res.data.code === 0) {
      if (type === 'ios') {
        const qrcode = await QRCode.toDataURL(res.data.data.download_url)
        const canvas = document.getElementById('ioscanvas')
        iosQRCode.value = qrcode
        
        if (canvas) {
          const ctx = canvas.getContext('2d')
          const img = new Image()
          img.onload = () => {
            canvas.width = img.width
            canvas.height = img.height
            ctx.drawImage(img, 0, 0)
          }
          img.src = qrcode
        }
      } else if (type === 'android') {
        const newPort = window.location.port
        const serverUrl = new URL(res.data.data.download_url)
        
        let modifiedDownloadUrl
        
        if (newPort) {
          //asec-deploy.oss-cn-guangzhou.aliyuncs.com 公有云下载不需要修改端口
          if (serverUrl.toString().includes('asec-deploy')) {
            modifiedDownloadUrl = res.data.data.download_url
          }else{
            serverUrl.port = newPort
            modifiedDownloadUrl = serverUrl.toString()
          }
        } else {
          serverUrl.port = '' // 移除端口
          modifiedDownloadUrl = serverUrl.toString()
        }

        const qrcode = await QRCode.toDataURL(modifiedDownloadUrl)
        const canvas = document.getElementById('canvas')
        androidQRCode.value = qrcode
        
        if (canvas) {
          const ctx = canvas.getContext('2d')
          const img = new Image()
          img.onload = () => {
            canvas.width = img.width
            canvas.height = img.height
            ctx.drawImage(img, 0, 0)
          }
          img.src = qrcode
        }
      } else {
        const newPort = window.location.port
        const serverUrl = new URL(res.data.data.download_url)
        
        let modifiedDownloadUrl
        let modifiedFilename
        
        if (newPort) {
          //asec-deploy.oss-cn-guangzhou.aliyuncs.com 公有云下载不需要修改端口
          if (serverUrl.toString().includes('asec-deploy')) {
            modifiedDownloadUrl = res.data.data.download_url
          }else{
            serverUrl.port = newPort
            modifiedDownloadUrl = serverUrl.toString()
          }

          modifiedFilename = res.data.data.latest_filename.replace(/@(\d+)/, `@${newPort}`)
        } else {
          serverUrl.port = '' // 移除端口
          modifiedDownloadUrl = serverUrl.toString()
          modifiedFilename = res.data.data.latest_filename
        }

        const blob = await getBolb(modifiedDownloadUrl, type)
        saveAs(blob, modifiedFilename)
      }
    } else {
      throw new Error(res.data.msg)
    }
  } catch (error) {
    Message({
      type: 'error',
      message: error.message || '下载失败，请联系管理员',
    })
  } finally {
    loadingRef.value = false
  }
}
</script>

<style lang="scss" scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.client {
  height: 100vh;
  text-align: center;
  background: #FFFFFF;
  max-height: calc(100vh - 68px);

  .el-main {
    height: 100%;
    div {
      div:hover {
        .window-show {
          display: none;
        }

        .window-hidden {
          display: block !important;

          span {
            margin-top: 42px !important;
          }
        }
      }
    }
  }
}

/* Loading 样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(241, 248, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #536ce6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: #606266;
}
</style>
