/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{_ as e}from"./lodash.def54e57.js";import{C as t,D as r,_ as n,h as o,o as i,d as a,e as l,f as s,j as c,w as u,t as p,k as f}from"./index.44d6e232.js";import{_ as y}from"./ASD.492c8837.js";var d=1,h=2,g=3,m=4,b=5,w=6,S=7,v=8,I=9,A=10,P=function(e,t){if("object"==typeof e&&"function"==typeof e.send){var r=this;this.transport=e,window.webChannel=r,this.send=function(e){"string"!=typeof e&&(e=JSON.stringify(e)),r.transport.send(e)},this.transport.onmessage=function(e){var t=e.data;switch("string"==typeof t&&(t=JSON.parse(t)),t.type){case d:r.handleSignal(t);break;case A:r.handleResponse(t);break;case h:r.handlePropertyUpdate(t);break;default:console.error("invalid message received:",e.data)}},this.execCallbacks={},this.execId=0,this.exec=function(e,t){t?(r.execId===Number.MAX_VALUE&&(r.execId=Number.MIN_VALUE),e.hasOwnProperty("id")?console.error("Cannot exec message with property id: "+JSON.stringify(e)):(e.id=r.execId++,r.execCallbacks[e.id]=t,r.send(e))):r.send(e)},this.objects={},this.handleSignal=function(e){var t=r.objects[e.object];t?t.signalEmitted(e.signal,e.args):console.warn("Unhandled signal: "+e.object+"::"+e.signal)},this.handleResponse=function(e){e.hasOwnProperty("id")?(r.execCallbacks[e.id](e.data),delete r.execCallbacks[e.id]):console.error("Invalid response message received: ",JSON.stringify(e))},this.handlePropertyUpdate=function(e){for(var t in e.data){var n=e.data[t],o=r.objects[n.object];o?o.propertyUpdate(n.signals,n.properties):console.warn("Unhandled property update: "+n.object+"::"+n.signal)}r.exec({type:m})},this.debug=function(e){r.send({type:b,data:e})},r.exec({type:g},(function(e){for(var n in e)new C(n,e[n],r);for(const t in r.objects)r.objects[t].unwrapProperties();t&&t(r),r.exec({type:m})}))}else console.error("The QWebChannel expects a transport object with a send function and onmessage callback property. Given is: transport: "+typeof e+", transport.send: "+typeof e.send)};function C(e,t,r){this.__id__=e,r.objects[e]=this,this.__objectSignals__={},this.__propertyCache__={};var n=this;function o(e,t){var o=e[0],i=e[1];n[o]={connect:function(e){"function"==typeof e?(n.__objectSignals__[i]=n.__objectSignals__[i]||[],n.__objectSignals__[i].push(e),t||"destroyed"===o||r.exec({type:S,object:n.__id__,signal:i})):console.error("Bad callback given to connect to signal "+o)},disconnect:function(e){if("function"==typeof e){n.__objectSignals__[i]=n.__objectSignals__[i]||[];var a=n.__objectSignals__[i].indexOf(e);-1!==a?(n.__objectSignals__[i].splice(a,1),t||0!==n.__objectSignals__[i].length||r.exec({type:v,object:n.__id__,signal:i})):console.error("Cannot find connection of signal "+o+" to "+e.name)}else console.error("Bad callback given to disconnect from signal "+o)}}}function i(e,t){var r=n.__objectSignals__[e];r&&r.forEach((function(e){e.apply(e,t)}))}this.unwrapQObject=function(e){if(e instanceof Array){for(var t=new Array(e.length),o=0;o<e.length;++o)t[o]=n.unwrapQObject(e[o]);return t}if(!e||!e["__QObject*__"]||void 0===e.id)return e;var i=e.id;if(r.objects[i])return r.objects[i];if(e.data){var a=new C(i,e.data,r);return a.destroyed.connect((function(){if(r.objects[i]===a){delete r.objects[i];var e=[];for(var t in a)e.push(t);for(var n in e)delete a[e[n]]}})),a.unwrapProperties(),a}console.error("Cannot unwrap unknown QObject "+i+" without data.")},this.unwrapProperties=function(){for(var e in n.__propertyCache__)n.__propertyCache__[e]=n.unwrapQObject(n.__propertyCache__[e])},this.propertyUpdate=function(e,t){for(var r in t){var o=t[r];n.__propertyCache__[r]=o}for(var a in e)i(a,e[a])},this.signalEmitted=function(e,t){i(e,this.unwrapQObject(t))},t.methods.forEach((function(e){var t=e[0],o=e[1];n[t]=function(){for(var e,t=[],i=0;i<arguments.length;++i){var a=arguments[i];"function"==typeof a?e=a:a instanceof C&&void 0!==r.objects[a.__id__]?t.push({id:a.__id__}):t.push(a)}r.exec({type:w,object:n.__id__,method:o,args:t},(function(t){if(void 0!==t){var r=n.unwrapQObject(t);e&&e(r)}}))}})),t.properties.forEach((function(e){var t=e[0],i=e[1],a=e[2];n.__propertyCache__[t]=e[3],a&&(1===a[0]&&(a[0]=i+"Changed"),o(a,!0)),Object.defineProperty(n,i,{configurable:!0,get:function(){var e=n.__propertyCache__[t];return void 0===e&&console.warn('Undefined value in property cache for property "'+i+'" in object '+n.__id__),e},set:function(e){if(void 0!==e){n.__propertyCache__[t]=e;var o=e;o instanceof C&&void 0!==r.objects[o.__id__]&&(o={id:o.__id__}),r.exec({type:I,object:n.__id__,property:t,value:o})}else console.warn("Property setter for "+i+" called with undefined value!")}})})),t.signals.forEach((function(e){o(e,!1)}));for(const a in t.enums)n[a]=t.enums[a]}const O=function(){console.log(window.qt),j()||(window.qt={webChannelTransport:{send(){var e;e="QWebChannel simulator activated !",console.log(`%c${e}`,"font-weight: bold;")},onmessage(){}}})},j=function(){return navigator.userAgent.includes("QtWebEngine")&&void 0!==window.qt};class E{constructor(e=e=>{}){O(),this.sendQueue=[],this.eventQueue=[],this.send=({module:e,action:t,strSerial:r,data:n=""})=>new Promise(((o,i)=>{this.sendQueue.push({module:e,action:t,strSerial:r,data:n,promise:{resolve:o,reject:i}})})),this.on=(e,t,r)=>{this.eventQueue.push({module:e,event:t,callback:r})},this.off=(e,t,r)=>{console.log("尚未初始化！")},new P(window.qt.webChannelTransport,(t=>{Object.keys(t).includes("objects");const r=t.objects;this.send=function(e){return({module:t,action:r,strSerial:n,data:o="",promise:i=null})=>new Promise(((a,l)=>(i&&i.reject&&i.resolve&&(a=i.resolve,l=i.reject),Object.keys(e).includes(t)?Object.keys(e[t]).includes(r)?"function"!=typeof e[t][r]?l(new Error("function"==typeof e[t][r].connect?`[SENDER]: ${r} 不是一个QT信号或者QT方法`:`[SENDER]:  action : ${r} 不是一个QT函数 !`)):void(-1===n?e[t][r](o,a):e[t][r](n,o,a)):l(new Error("[SENDER]: 该action"+r+" 不存在 !")):l(new Error("[SENDER]: 该module"+t+" 不存在 !")))))}(r),this.on=function(e){return(t,r,n)=>{if(!_.get(e,`${t}.${r}`))throw new Error(`[LISTENER]: ${r} is not a Qt signa!`);if("function"!=typeof e[t][r].connect)throw new Error("[LISTENER]: No Connect Function!");e[t][r].connect(n)}}(r),this.off=function(e){return(t,r,n)=>Object.keys(e).includes(r)?Object.keys(e[r]).includes("disconnect")?"function"!=typeof e[r].disconnect?reject(new Error("[LISTENER]: No Disconnect Function!")):void e[t][r].disconnect(n):reject(new Error(`[LISTENER]: ${r} is not a Qt signa!`)):reject(new Error("[LISTENER]: Unknown event name!"))}(r),this.sendQueue.length>0&&(this.sendQueue.forEach((e=>{this.send({module:e.module,action:e.action,strSerial:e.strSerial,data:e.data,promise:e.promise})})),this.sendQueue=[]),this.eventQueue.length>0&&(this.eventQueue.forEach((e=>{this.on(e.module,e.event,e.callback)})),this.eventQueue=[]),e(r)}))}}const W={paramsToString:e=>(function e(t){if("[object Array]"===Object.prototype.toString.call(t))t.forEach(((r,n)=>{"number"==typeof r?t[n]=r+"":"object"==typeof r&&e(r)}));else if("[object Object]"===Object.prototype.toString.call(t))for(const r in t)t.hasOwnProperty(r)&&("number"==typeof t[r]?t[r]+="":"object"==typeof t[r]&&e(t[r]))}(e),e),serialId:0,getStrSerialId(){return this.serialId++,this.serialId%10==0&&this.serialId++,this.serialId>9e8&&(this.serialId=1),this.serialId},getStrSerial(e=0){const t=1e8*this.getStrSerialId();return String(parseInt(String(t).substr(0,9))+parseInt(e))},interceptors(e,t){const r=this.sortParamsKey(e.strBody);if(this.paramsToString(e),r){const t=[];r.forEach((r=>{const n={Name:r,Value:encodeURIComponent(e.strBody[r])};t.push(n)})),e.strBody={Argument:t}}const n={ASM:e};return JSON.stringify(n)},sortParamsKey(t){if(!t||"{}"===JSON.stringify(t.strBody))return"";return Object.keys(t).sort(((t,r)=>{t=e.toString(t),r=e.toString(r);const n=e.max([t.length,r.length]);for(let e=0;e<n;e++){const n=(o=t.charAt(e),i=r.charAt(e),o>i?1:o<i?-1:0);if(0!==n)return n}var o,i;return 0}))},getStrLen(e){let t=0;if(!e)return t;for(var r=0;r<e.length;r++){var n=e.charCodeAt(r);t+=n>=0&&n<=128?1:2}return t},formatNum(e,t){let r=""+e;const n=t-r.length;for(var o=0;o<n;o++)r="0"+r;return r},getSubStr(e,t,r){let n=e.indexOf(t);if(-1===parseInt(n))return"";n+=t.length;const o=e.indexOf(r,n);return-1===parseInt(o)?"":e.substring(n,o)}},k=t=>(t=e.merge({time:6e4,timeoutReturn:{overtime:!0}},t),new Promise(((e,r)=>{setTimeout((()=>{e(t.timeoutReturn)}),t.time)})));var x=TypeError;const T=new Proxy({},{get(e,t){throw new Error(`Module "" has been externalized for browser compatibility. Cannot access ".${t}" in client code.`)}}),U=t(Object.freeze(Object.defineProperty({__proto__:null,default:T},Symbol.toStringTag,{value:"Module"})));var R="function"==typeof Map&&Map.prototype,D=Object.getOwnPropertyDescriptor&&R?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,N=R&&D&&"function"==typeof D.get?D.get:null,M=R&&Map.prototype.forEach,F="function"==typeof Set&&Set.prototype,$=Object.getOwnPropertyDescriptor&&F?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,L=F&&$&&"function"==typeof $.get?$.get:null,B=F&&Set.prototype.forEach,Q="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,G="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,q="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,z=Boolean.prototype.valueOf,V=Object.prototype.toString,H=Function.prototype.toString,K=String.prototype.match,J=String.prototype.slice,Z=String.prototype.replace,X=String.prototype.toUpperCase,Y=String.prototype.toLowerCase,ee=RegExp.prototype.test,te=Array.prototype.concat,re=Array.prototype.join,ne=Array.prototype.slice,oe=Math.floor,ie="function"==typeof BigInt?BigInt.prototype.valueOf:null,ae=Object.getOwnPropertySymbols,le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,se="function"==typeof Symbol&&"object"==typeof Symbol.iterator,ce="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===se||"symbol")?Symbol.toStringTag:null,ue=Object.prototype.propertyIsEnumerable,pe=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function fe(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||ee.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-oe(-e):oe(e);if(n!==e){var o=String(n),i=J.call(t,o.length+1);return Z.call(o,r,"$&_")+"."+Z.call(Z.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Z.call(t,r,"$&_")}var ye=U,de=ye.custom,he=Ae(de)?de:null,ge={__proto__:null,double:'"',single:"'"},me={__proto__:null,double:/(["\\])/g,single:/(['\\])/g},be=function e(t,n,o,i){var a=n||{};if(Ce(a,"quoteStyle")&&!Ce(ge,a.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Ce(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=!Ce(a,"customInspect")||a.customInspect;if("boolean"!=typeof l&&"symbol"!==l)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Ce(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Ce(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=a.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Ee(t,a);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var c=String(t);return s?fe(t,c):c}if("bigint"==typeof t){var u=String(t)+"n";return s?fe(t,u):u}var p=void 0===a.depth?5:a.depth;if(void 0===o&&(o=0),o>=p&&p>0&&"object"==typeof t)return _e(t)?"[Array]":"[Object]";var f=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=re.call(Array(e.indent+1)," ")}return{base:r,prev:re.call(Array(t+1),r)}}(a,o);if(void 0===i)i=[];else if(je(i,t)>=0)return"[Circular]";function y(t,r,n){if(r&&(i=ne.call(i)).push(r),n){var l={depth:a.depth};return Ce(a,"quoteStyle")&&(l.quoteStyle=a.quoteStyle),e(t,l,o+1,i)}return e(t,a,o+1,i)}if("function"==typeof t&&!Ie(t)){var d=function(e){if(e.name)return e.name;var t=K.call(H.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),h=Re(t,y);return"[Function"+(d?": "+d:" (anonymous)")+"]"+(h.length>0?" { "+re.call(h,", ")+" }":"")}if(Ae(t)){var g=se?Z.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):le.call(t);return"object"!=typeof t||se?g:ke(g)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var m="<"+Y.call(String(t.nodeName)),b=t.attributes||[],w=0;w<b.length;w++)m+=" "+b[w].name+"="+we(Se(b[w].value),"double",a);return m+=">",t.childNodes&&t.childNodes.length&&(m+="..."),m+="</"+Y.call(String(t.nodeName))+">"}if(_e(t)){if(0===t.length)return"[]";var S=Re(t,y);return f&&!function(e){for(var t=0;t<e.length;t++)if(je(e[t],"\n")>=0)return!1;return!0}(S)?"["+Ue(S,f)+"]":"[ "+re.call(S,", ")+" ]"}if(function(e){return"[object Error]"===Oe(e)&&ve(e)}(t)){var v=Re(t,y);return"cause"in Error.prototype||!("cause"in t)||ue.call(t,"cause")?0===v.length?"["+String(t)+"]":"{ ["+String(t)+"] "+re.call(v,", ")+" }":"{ ["+String(t)+"] "+re.call(te.call("[cause]: "+y(t.cause),v),", ")+" }"}if("object"==typeof t&&l){if(he&&"function"==typeof t[he]&&ye)return ye(t,{depth:p-o});if("symbol"!==l&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!N||!e||"object"!=typeof e)return!1;try{N.call(e);try{L.call(e)}catch(m){return!0}return e instanceof Map}catch(t){}return!1}(t)){var _=[];return M&&M.call(t,(function(e,r){_.push(y(r,t,!0)+" => "+y(e,t))})),Te("Map",N.call(t),_,f)}if(function(e){if(!L||!e||"object"!=typeof e)return!1;try{L.call(e);try{N.call(e)}catch(t){return!0}return e instanceof Set}catch(r){}return!1}(t)){var I=[];return B&&B.call(t,(function(e){I.push(y(e,t))})),Te("Set",L.call(t),I,f)}if(function(e){if(!Q||!e||"object"!=typeof e)return!1;try{Q.call(e,Q);try{G.call(e,G)}catch(m){return!0}return e instanceof WeakMap}catch(t){}return!1}(t))return xe("WeakMap");if(function(e){if(!G||!e||"object"!=typeof e)return!1;try{G.call(e,G);try{Q.call(e,Q)}catch(m){return!0}return e instanceof WeakSet}catch(t){}return!1}(t))return xe("WeakSet");if(function(e){if(!q||!e||"object"!=typeof e)return!1;try{return q.call(e),!0}catch(t){}return!1}(t))return xe("WeakRef");if(function(e){return"[object Number]"===Oe(e)&&ve(e)}(t))return ke(y(Number(t)));if(function(e){if(!e||"object"!=typeof e||!ie)return!1;try{return ie.call(e),!0}catch(t){}return!1}(t))return ke(y(ie.call(t)));if(function(e){return"[object Boolean]"===Oe(e)&&ve(e)}(t))return ke(z.call(t));if(function(e){return"[object String]"===Oe(e)&&ve(e)}(t))return ke(y(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==r&&t===r)return"{ [object globalThis] }";if(!function(e){return"[object Date]"===Oe(e)&&ve(e)}(t)&&!Ie(t)){var A=Re(t,y),P=pe?pe(t)===Object.prototype:t instanceof Object||t.constructor===Object,C=t instanceof Object?"":"null prototype",O=!P&&ce&&Object(t)===t&&ce in t?J.call(Oe(t),8,-1):C?"Object":"",j=(P||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(O||C?"["+re.call(te.call([],O||[],C||[]),": ")+"] ":"");return 0===A.length?j+"{}":f?j+"{"+Ue(A,f)+"}":j+"{ "+re.call(A,", ")+" }"}return String(t)};function we(e,t,r){var n=r.quoteStyle||t,o=ge[n];return o+e+o}function Se(e){return Z.call(String(e),/"/g,"&quot;")}function ve(e){return!ce||!("object"==typeof e&&(ce in e||void 0!==e[ce]))}function _e(e){return"[object Array]"===Oe(e)&&ve(e)}function Ie(e){return"[object RegExp]"===Oe(e)&&ve(e)}function Ae(e){if(se)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!le)return!1;try{return le.call(e),!0}catch(t){}return!1}var Pe=Object.prototype.hasOwnProperty||function(e){return e in this};function Ce(e,t){return Pe.call(e,t)}function Oe(e){return V.call(e)}function je(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function Ee(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return Ee(J.call(e,0,t.maxStringLength),t)+n}var o=me[t.quoteStyle||"single"];return o.lastIndex=0,we(Z.call(Z.call(e,o,"\\$1"),/[\x00-\x1f]/g,We),"single",t)}function We(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+X.call(t.toString(16))}function ke(e){return"Object("+e+")"}function xe(e){return e+" { ? }"}function Te(e,t,r,n){return e+" ("+t+") {"+(n?Ue(r,n):re.call(r,", "))+"}"}function Ue(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+re.call(e,","+r)+"\n"+t.prev}function Re(e,t){var r=_e(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=Ce(e,o)?t(e[o],e):""}var i,a="function"==typeof ae?ae(e):[];if(se){i={};for(var l=0;l<a.length;l++)i["$"+a[l]]=a[l]}for(var s in e)Ce(e,s)&&(r&&String(Number(s))===s&&s<e.length||se&&i["$"+s]instanceof Symbol||(ee.call(/[^\w$]/,s)?n.push(t(s,e)+": "+t(e[s],e)):n.push(s+": "+t(e[s],e))));if("function"==typeof ae)for(var c=0;c<a.length;c++)ue.call(e,a[c])&&n.push("["+t(a[c])+"]: "+t(e[a[c]],e));return n}var De=be,Ne=x,Me=function(e,t,r){for(var n,o=e;null!=(n=o.next);o=n)if(n.key===t)return o.next=n.next,r||(n.next=e.next,e.next=n),n},Fe=Object,$e=Error,Le=EvalError,Be=RangeError,Qe=ReferenceError,Ge=SyntaxError,qe=URIError,ze=Math.abs,Ve=Math.floor,He=Math.max,Ke=Math.min,Je=Math.pow,Ze=Math.round,Xe=Number.isNaN||function(e){return e!=e},Ye=Object.getOwnPropertyDescriptor;if(Ye)try{Ye([],"length")}catch(Gn){Ye=null}var et=Ye,tt=Object.defineProperty||!1;if(tt)try{tt({},"a",{value:1})}catch(Gn){tt=!1}var rt,nt,ot,it,at,lt,st,ct,ut=tt;function pt(){return lt?at:(lt=1,at="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function ft(){return ct?st:(ct=1,st=Fe.getPrototypeOf||null)}var yt,dt,ht=Object.prototype.toString,gt=Math.max,mt=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var o=0;o<t.length;o+=1)r[o+e.length]=t[o];return r},bt=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==ht.apply(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,n=function(e,t){for(var r=[],n=t||0,o=0;n<e.length;n+=1,o+=1)r[o]=e[n];return r}(arguments,1),o=gt(0,t.length-n.length),i=[],a=0;a<o;a++)i[a]="$"+a;if(r=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(i,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var o=t.apply(this,mt(n,arguments));return Object(o)===o?o:this}return t.apply(e,mt(n,arguments))})),t.prototype){var l=function(){};l.prototype=t.prototype,r.prototype=new l,l.prototype=null}return r},wt=Function.prototype.bind||bt,St=Function.prototype.call;function vt(){return dt?yt:(dt=1,yt=Function.prototype.apply)}var _t,It,At,Pt,Ct,Ot,jt,Et="undefined"!=typeof Reflect&&Reflect&&Reflect.apply,Wt=wt,kt=vt(),xt=St,Tt=Et||Wt.call(xt,kt),Ut=wt,Rt=x,Dt=St,Nt=Tt,Mt=function(e){if(e.length<1||"function"!=typeof e[0])throw new Rt("a function is required");return Nt(Ut,Dt,e)};var Ft=Fe,$t=$e,Lt=Le,Bt=Be,Qt=Qe,Gt=Ge,qt=x,zt=qe,Vt=ze,Ht=Ve,Kt=He,Jt=Ke,Zt=Je,Xt=Ze,Yt=function(e){return Xe(e)||0===e?e:e<0?-1:1},er=Function,tr=function(e){try{return er('"use strict"; return ('+e+").constructor;")()}catch(Gn){}},rr=et,nr=ut,or=function(){throw new qt},ir=rr?function(){try{return or}catch(e){try{return rr(arguments,"callee").get}catch(t){return or}}}():or,ar=function(){if(it)return ot;it=1;var e="undefined"!=typeof Symbol&&Symbol,t=nt?rt:(nt=1,rt=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var n in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0});return ot=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"==typeof e("foo")&&("symbol"==typeof Symbol("bar")&&t())))}}()(),lr=function(){if(Pt)return At;Pt=1;var e=pt(),t=ft(),r=function(){if(It)return _t;It=1;var e,t=Mt,r=et;try{e=[].__proto__===Array.prototype}catch(Gn){if(!Gn||"object"!=typeof Gn||!("code"in Gn)||"ERR_PROTO_ACCESS"!==Gn.code)throw Gn}var n=!!e&&r&&r(Object.prototype,"__proto__"),o=Object,i=o.getPrototypeOf;return _t=n&&"function"==typeof n.get?t([n.get]):"function"==typeof i&&function(e){return i(null==e?e:o(e))}}();return At=e?function(t){return e(t)}:t?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new TypeError("getProto: not an object");return t(e)}:r?function(e){return r(e)}:null}(),sr=ft(),cr=pt(),ur=vt(),pr=St,fr={},yr="undefined"!=typeof Uint8Array&&lr?lr(Uint8Array):jt,dr={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?jt:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?jt:ArrayBuffer,"%ArrayIteratorPrototype%":ar&&lr?lr([][Symbol.iterator]()):jt,"%AsyncFromSyncIteratorPrototype%":jt,"%AsyncFunction%":fr,"%AsyncGenerator%":fr,"%AsyncGeneratorFunction%":fr,"%AsyncIteratorPrototype%":fr,"%Atomics%":"undefined"==typeof Atomics?jt:Atomics,"%BigInt%":"undefined"==typeof BigInt?jt:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?jt:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?jt:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?jt:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":$t,"%eval%":eval,"%EvalError%":Lt,"%Float16Array%":"undefined"==typeof Float16Array?jt:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?jt:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?jt:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?jt:FinalizationRegistry,"%Function%":er,"%GeneratorFunction%":fr,"%Int8Array%":"undefined"==typeof Int8Array?jt:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?jt:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?jt:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":ar&&lr?lr(lr([][Symbol.iterator]())):jt,"%JSON%":"object"==typeof JSON?JSON:jt,"%Map%":"undefined"==typeof Map?jt:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&ar&&lr?lr((new Map)[Symbol.iterator]()):jt,"%Math%":Math,"%Number%":Number,"%Object%":Ft,"%Object.getOwnPropertyDescriptor%":rr,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?jt:Promise,"%Proxy%":"undefined"==typeof Proxy?jt:Proxy,"%RangeError%":Bt,"%ReferenceError%":Qt,"%Reflect%":"undefined"==typeof Reflect?jt:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?jt:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&ar&&lr?lr((new Set)[Symbol.iterator]()):jt,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?jt:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":ar&&lr?lr(""[Symbol.iterator]()):jt,"%Symbol%":ar?Symbol:jt,"%SyntaxError%":Gt,"%ThrowTypeError%":ir,"%TypedArray%":yr,"%TypeError%":qt,"%Uint8Array%":"undefined"==typeof Uint8Array?jt:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?jt:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?jt:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?jt:Uint32Array,"%URIError%":zt,"%WeakMap%":"undefined"==typeof WeakMap?jt:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?jt:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?jt:WeakSet,"%Function.prototype.call%":pr,"%Function.prototype.apply%":ur,"%Object.defineProperty%":nr,"%Object.getPrototypeOf%":sr,"%Math.abs%":Vt,"%Math.floor%":Ht,"%Math.max%":Kt,"%Math.min%":Jt,"%Math.pow%":Zt,"%Math.round%":Xt,"%Math.sign%":Yt,"%Reflect.getPrototypeOf%":cr};if(lr)try{null.error}catch(Gn){var hr=lr(lr(Gn));dr["%Error.prototype%"]=hr}var gr=function e(t){var r;if("%AsyncFunction%"===t)r=tr("async function () {}");else if("%GeneratorFunction%"===t)r=tr("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=tr("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&lr&&(r=lr(o.prototype))}return dr[t]=r,r},mr={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},br=wt,wr=function(){if(Ot)return Ct;Ot=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty;return Ct=wt.call(e,t)}(),Sr=br.call(pr,Array.prototype.concat),vr=br.call(ur,Array.prototype.splice),_r=br.call(pr,String.prototype.replace),Ir=br.call(pr,String.prototype.slice),Ar=br.call(pr,RegExp.prototype.exec),Pr=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Cr=/\\(\\)?/g,Or=function(e,t){var r,n=e;if(wr(mr,n)&&(n="%"+(r=mr[n])[0]+"%"),wr(dr,n)){var o=dr[n];if(o===fr&&(o=gr(n)),void 0===o&&!t)throw new qt("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new Gt("intrinsic "+e+" does not exist!")},jr=function(e,t){if("string"!=typeof e||0===e.length)throw new qt("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new qt('"allowMissing" argument must be a boolean');if(null===Ar(/^%?[^%]*%?$/,e))throw new Gt("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=Ir(e,0,1),r=Ir(e,-1);if("%"===t&&"%"!==r)throw new Gt("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new Gt("invalid intrinsic syntax, expected opening `%`");var n=[];return _r(e,Pr,(function(e,t,r,o){n[n.length]=r?_r(o,Cr,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",o=Or("%"+n+"%",t),i=o.name,a=o.value,l=!1,s=o.alias;s&&(n=s[0],vr(r,Sr([0,1],s)));for(var c=1,u=!0;c<r.length;c+=1){var p=r[c],f=Ir(p,0,1),y=Ir(p,-1);if(('"'===f||"'"===f||"`"===f||'"'===y||"'"===y||"`"===y)&&f!==y)throw new Gt("property names with quotes must have matching quotes");if("constructor"!==p&&u||(l=!0),wr(dr,i="%"+(n+="."+p)+"%"))a=dr[i];else if(null!=a){if(!(p in a)){if(!t)throw new qt("base intrinsic for "+e+" exists, but the property is not available.");return}if(rr&&c+1>=r.length){var d=rr(a,p);a=(u=!!d)&&"get"in d&&!("originalValue"in d.get)?d.get:a[p]}else u=wr(a,p),a=a[p];u&&!l&&(dr[i]=a)}}return a},Er=jr,Wr=Mt,kr=Wr([Er("%String.prototype.indexOf%")]),xr=function(e,t){var r=Er(e,!!t);return"function"==typeof r&&kr(e,".prototype.")>-1?Wr([r]):r},Tr=xr,Ur=be,Rr=x,Dr=jr("%Map%",!0),Nr=Tr("Map.prototype.get",!0),Mr=Tr("Map.prototype.set",!0),Fr=Tr("Map.prototype.has",!0),$r=Tr("Map.prototype.delete",!0),Lr=Tr("Map.prototype.size",!0),Br=!!Dr&&function(){var e,t={assert:function(e){if(!t.has(e))throw new Rr("Side channel does not contain "+Ur(e))},delete:function(t){if(e){var r=$r(e,t);return 0===Lr(e)&&(e=void 0),r}return!1},get:function(t){if(e)return Nr(e,t)},has:function(t){return!!e&&Fr(e,t)},set:function(t,r){e||(e=new Dr),Mr(e,t,r)}};return t},Qr=xr,Gr=be,qr=Br,zr=x,Vr=jr("%WeakMap%",!0),Hr=Qr("WeakMap.prototype.get",!0),Kr=Qr("WeakMap.prototype.set",!0),Jr=Qr("WeakMap.prototype.has",!0),Zr=Qr("WeakMap.prototype.delete",!0),Xr=x,Yr=be,en=(Vr?function(){var e,t,r={assert:function(e){if(!r.has(e))throw new zr("Side channel does not contain "+Gr(e))},delete:function(r){if(Vr&&r&&("object"==typeof r||"function"==typeof r)){if(e)return Zr(e,r)}else if(qr&&t)return t.delete(r);return!1},get:function(r){return Vr&&r&&("object"==typeof r||"function"==typeof r)&&e?Hr(e,r):t&&t.get(r)},has:function(r){return Vr&&r&&("object"==typeof r||"function"==typeof r)&&e?Jr(e,r):!!t&&t.has(r)},set:function(r,n){Vr&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new Vr),Kr(e,r,n)):qr&&(t||(t=qr()),t.set(r,n))}};return r}:qr)||Br||function(){var e,t={assert:function(e){if(!t.has(e))throw new Ne("Side channel does not contain "+De(e))},delete:function(t){var r=e&&e.next,n=function(e,t){if(e)return Me(e,t,!0)}(e,t);return n&&r&&r===n&&(e=void 0),!!n},get:function(t){return function(e,t){if(e){var r=Me(e,t);return r&&r.value}}(e,t)},has:function(t){return function(e,t){return!!e&&!!Me(e,t)}(e,t)},set:function(t,r){e||(e={next:void 0}),function(e,t,r){var n=Me(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(e,t,r)}};return t},tn=String.prototype.replace,rn=/%20/g,nn="RFC3986",on={default:nn,formatters:{RFC1738:function(e){return tn.call(e,rn,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:nn},an=on,ln=Object.prototype.hasOwnProperty,sn=Array.isArray,cn=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),un=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r},pn=1024,fn={arrayToObject:un,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],n=0;n<t.length;++n)for(var o=t[n],i=o.obj[o.prop],a=Object.keys(i),l=0;l<a.length;++l){var s=a[l],c=i[s];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:s}),r.push(c))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(sn(r)){for(var n=[],o=0;o<r.length;++o)void 0!==r[o]&&n.push(r[o]);t.obj[t.prop]=n}}}(t),e},decode:function(e,t,r){var n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(Gn){return n}},encode:function(e,t,r,n,o){if(0===e.length)return e;var i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var a="",l=0;l<i.length;l+=pn){for(var s=i.length>=pn?i.slice(l,l+pn):i,c=[],u=0;u<s.length;++u){var p=s.charCodeAt(u);45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||o===an.RFC1738&&(40===p||41===p)?c[c.length]=s.charAt(u):p<128?c[c.length]=cn[p]:p<2048?c[c.length]=cn[192|p>>6]+cn[128|63&p]:p<55296||p>=57344?c[c.length]=cn[224|p>>12]+cn[128|p>>6&63]+cn[128|63&p]:(u+=1,p=65536+((1023&p)<<10|1023&s.charCodeAt(u)),c[c.length]=cn[240|p>>18]+cn[128|p>>12&63]+cn[128|p>>6&63]+cn[128|63&p])}a+=c.join("")}return a},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(sn(e)){for(var r=[],n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)},merge:function e(t,r,n){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(sn(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(n&&(n.plainObjects||n.allowPrototypes)||!ln.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var o=t;return sn(t)&&!sn(r)&&(o=un(t,n)),sn(t)&&sn(r)?(r.forEach((function(r,o){if(ln.call(t,o)){var i=t[o];i&&"object"==typeof i&&r&&"object"==typeof r?t[o]=e(i,r,n):t.push(r)}else t[o]=r})),t):Object.keys(r).reduce((function(t,o){var i=r[o];return ln.call(t,o)?t[o]=e(t[o],i,n):t[o]=i,t}),o)}},yn=function(){var e,t={assert:function(e){if(!t.has(e))throw new Xr("Side channel does not contain "+Yr(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,r){e||(e=en()),e.set(t,r)}};return t},dn=fn,hn=on,gn=Object.prototype.hasOwnProperty,mn={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},bn=Array.isArray,wn=Array.prototype.push,Sn=function(e,t){wn.apply(e,bn(t)?t:[t])},vn=Date.prototype.toISOString,_n=hn.default,In={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:dn.encode,encodeValuesOnly:!1,filter:void 0,format:_n,formatter:hn.formatters[_n],indices:!1,serializeDate:function(e){return vn.call(e)},skipNulls:!1,strictNullHandling:!1},An={},Pn=function e(t,r,n,o,i,a,l,s,c,u,p,f,y,d,h,g,m,b){for(var w,S=t,v=b,_=0,I=!1;void 0!==(v=v.get(An))&&!I;){var A=v.get(t);if(_+=1,void 0!==A){if(A===_)throw new RangeError("Cyclic object value");I=!0}void 0===v.get(An)&&(_=0)}if("function"==typeof u?S=u(r,S):S instanceof Date?S=y(S):"comma"===n&&bn(S)&&(S=dn.maybeMap(S,(function(e){return e instanceof Date?y(e):e}))),null===S){if(a)return c&&!g?c(r,In.encoder,m,"key",d):r;S=""}if("string"==typeof(w=S)||"number"==typeof w||"boolean"==typeof w||"symbol"==typeof w||"bigint"==typeof w||dn.isBuffer(S))return c?[h(g?r:c(r,In.encoder,m,"key",d))+"="+h(c(S,In.encoder,m,"value",d))]:[h(r)+"="+h(String(S))];var P,C=[];if(void 0===S)return C;if("comma"===n&&bn(S))g&&c&&(S=dn.maybeMap(S,c)),P=[{value:S.length>0?S.join(",")||null:void 0}];else if(bn(u))P=u;else{var O=Object.keys(S);P=p?O.sort(p):O}var j=s?String(r).replace(/\./g,"%2E"):String(r),E=o&&bn(S)&&1===S.length?j+"[]":j;if(i&&bn(S)&&0===S.length)return E+"[]";for(var W=0;W<P.length;++W){var k=P[W],x="object"==typeof k&&k&&void 0!==k.value?k.value:S[k];if(!l||null!==x){var T=f&&s?String(k).replace(/\./g,"%2E"):String(k),U=bn(S)?"function"==typeof n?n(E,T):E:E+(f?"."+T:"["+T+"]");b.set(t,_);var R=yn();R.set(An,b),Sn(C,e(x,U,n,o,i,a,l,s,"comma"===n&&g&&bn(S)?null:c,u,p,f,y,d,h,g,m,R))}}return C},Cn=fn,On=Object.prototype.hasOwnProperty,jn=Array.isArray,En={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:Cn.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},Wn=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},kn=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw new RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},xn=function(e,t,r,n){if(e){var o=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,a=r.depth>0&&/(\[[^[\]]*])/.exec(o),l=a?o.slice(0,a.index):o,s=[];if(l){if(!r.plainObjects&&On.call(Object.prototype,l)&&!r.allowPrototypes)return;s.push(l)}for(var c=0;r.depth>0&&null!==(a=i.exec(o))&&c<r.depth;){if(c+=1,!r.plainObjects&&On.call(Object.prototype,a[1].slice(1,-1))&&!r.allowPrototypes)return;s.push(a[1])}if(a){if(!0===r.strictDepth)throw new RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");s.push("["+o.slice(a.index)+"]")}return function(e,t,r,n){var o=0;if(e.length>0&&"[]"===e[e.length-1]){var i=e.slice(0,-1).join("");o=Array.isArray(t)&&t[i]?t[i].length:0}for(var a=n?t:kn(t,r,o),l=e.length-1;l>=0;--l){var s,c=e[l];if("[]"===c&&r.parseArrays)s=r.allowEmptyArrays&&(""===a||r.strictNullHandling&&null===a)?[]:Cn.combine([],a);else{s=r.plainObjects?{__proto__:null}:{};var u="["===c.charAt(0)&&"]"===c.charAt(c.length-1)?c.slice(1,-1):c,p=r.decodeDotInKeys?u.replace(/%2E/g,"."):u,f=parseInt(p,10);r.parseArrays||""!==p?!isNaN(f)&&c!==p&&String(f)===p&&f>=0&&r.parseArrays&&f<=r.arrayLimit?(s=[])[f]=a:"__proto__"!==p&&(s[p]=a):s={0:a}}a=s}return a}(s,t,r,n)}};const Tn={formats:on,parse:function(e,t){var r=function(e){if(!e)return En;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?En.charset:e.charset,r=void 0===e.duplicates?En.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw new TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||En.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:En.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:En.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:En.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:En.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:En.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:En.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:En.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:En.decoder,delimiter:"string"==typeof e.delimiter||Cn.isRegExp(e.delimiter)?e.delimiter:En.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:En.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:En.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:En.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:En.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:En.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:En.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}}(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?function(e,t){var r={__proto__:null},n=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;n=n.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var o=t.parameterLimit===1/0?void 0:t.parameterLimit,i=n.split(t.delimiter,t.throwOnLimitExceeded?o+1:o);if(t.throwOnLimitExceeded&&i.length>o)throw new RangeError("Parameter limit exceeded. Only "+o+" parameter"+(1===o?"":"s")+" allowed.");var a,l=-1,s=t.charset;if(t.charsetSentinel)for(a=0;a<i.length;++a)0===i[a].indexOf("utf8=")&&("utf8=%E2%9C%93"===i[a]?s="utf-8":"utf8=%26%2310003%3B"===i[a]&&(s="iso-8859-1"),l=a,a=i.length);for(a=0;a<i.length;++a)if(a!==l){var c,u,p=i[a],f=p.indexOf("]="),y=-1===f?p.indexOf("="):f+1;-1===y?(c=t.decoder(p,En.decoder,s,"key"),u=t.strictNullHandling?null:""):(c=t.decoder(p.slice(0,y),En.decoder,s,"key"),u=Cn.maybeMap(kn(p.slice(y+1),t,jn(r[c])?r[c].length:0),(function(e){return t.decoder(e,En.decoder,s,"value")}))),u&&t.interpretNumericEntities&&"iso-8859-1"===s&&(u=Wn(String(u))),p.indexOf("[]=")>-1&&(u=jn(u)?[u]:u);var d=On.call(r,c);d&&"combine"===t.duplicates?r[c]=Cn.combine(r[c],u):d&&"last"!==t.duplicates||(r[c]=u)}return r}(e,r):e,o=r.plainObjects?{__proto__:null}:{},i=Object.keys(n),a=0;a<i.length;++a){var l=i[a],s=xn(l,n[l],r,"string"==typeof e);o=Cn.merge(o,s,r)}return!0===r.allowSparse?o:Cn.compact(o)},stringify:function(e,t){var r,n=e,o=function(e){if(!e)return In;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||In.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=hn.default;if(void 0!==e.format){if(!gn.call(hn.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var n,o=hn.formatters[r],i=In.filter;if(("function"==typeof e.filter||bn(e.filter))&&(i=e.filter),n=e.arrayFormat in mn?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":In.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a=void 0===e.allowDots?!0===e.encodeDotInKeys||In.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:In.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:In.allowEmptyArrays,arrayFormat:n,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:In.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?In.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:In.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:In.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:In.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:In.encodeValuesOnly,filter:i,format:r,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:In.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:In.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:In.strictNullHandling}}(t);"function"==typeof o.filter?n=(0,o.filter)("",n):bn(o.filter)&&(r=o.filter);var i=[];if("object"!=typeof n||null===n)return"";var a=mn[o.arrayFormat],l="comma"===a&&o.commaRoundTrip;r||(r=Object.keys(n)),o.sort&&r.sort(o.sort);for(var s=yn(),c=0;c<r.length;++c){var u=r[c],p=n[u];o.skipNulls&&null===p||Sn(i,Pn(p,u,a,l,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,s))}var f=i.join(o.delimiter),y=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),f.length>0?y+f:""}};class Un{constructor(e){return this.responseEvent="ResponseToWeb",this.callbackList={},this.qtObject=null,this.processId=0,this.initProcessId(),this.initIpcInstance()}initProcessId(){const t=Tn.parse(location.search.substring(1));this.processId=e.get(t,"ProcessId",0)}async initIpcInstance(){return j()?this.ipcInstance=new E((e=>{this.addResponseListener(e,this.responseEvent)})):this.ipcInstance=null,this}send(t,r,n,o){let i={},a=(e,a)=>{if(o.isNeedId){n.id=W.getStrSerial(this.processId);const o=j()?(new Error).stack.split("\n"):[],i={resolve:e,reject:a,request:{module:t,action:r,request:n,startTime:(new Date).getTime()},stackTrace:o};this.callbackList[n.id]=i}try{i=W.interceptors(n,r)}catch(l){throw console.log(l),new Error("参数转换错误")}this.ipcInstance.send({module:t,action:r,strSerial:o.isNeedId?n.id:-1,data:i,resolve:e,reject:a})};if(e.isSafeInteger(e.get(o,"timeout.time"))){a=(async t=>{if(t=e.merge({request:null,callback:null,time:6e4,timeoutReturn:{errcode:11100003,errmsg:""},retry:3,retryDelay:1e3},t),e.isNil(t.callback))return!1;let r;for(let n=0;n<t.retry&&(r=await Promise.race([new Promise(t.callback),k(t)]),console.log("overtime:request:result",{result:r,nowTry:n}),e.get(r,"overtime",!1));n++)console.error("overtime:request:fail"),console.error(JSON.stringify({...e.omit(t,["callback"]),nowTry:n})),n===t.retry-1?r=t.timeoutReturn:await k({time:t.retryDelay});return r})(e.merge({callback:a,request:{module:t,action:r,data:i}},o.timeout))}else a=new Promise(a);return a}on(e,t,r){this.ipcInstance.on(e,t,r)}off(e,t,r){this.ipcInstance.off(e,t,r)}addResponseListener(t,r){const n=(t,r=null,n=null)=>{try{let r={};if(e.isNil(n)||e.isEmpty(n)||(r=e.isString(n)?JSON.parse(n):n),e.isUndefined(t)&&e.isEmpty(t))throw new Error("serial 为空或者未定义");const o=this.callbackList[t];e.isUndefined(o)||(o.resolve(r.result),o.request.response=r.result||{},o.request.endTime=(new Date).getTime()),delete this.callbackList[t]}catch(Gn){console.error("小助手返回错误="),console.error(Gn)}};e.isObject(t)&&Object.keys(t).forEach((e=>{this.ipcInstance.on(e,r,n)}))}}const Rn={init:t=>(new Un).then((t=>({$ipcSend:(r,n,o={},i={})=>{if(e.isNil(r)||e.isNil(n)||e.isEmpty(r)||e.isEmpty(n))throw new Error("module或action不能为空");if(o&&!e.isObject(o))throw new Error("params必须为object类型");return i=e.merge({isNeedId:!0,timeout:{time:!1}},i),t.send(r,n,o,i)},$ipcOn:(e,r,n)=>{t.on(e,r,n)},$ipcOff:(e,r,n)=>{t.off(e,r,n)},$processId:t.processId})))},Dn={ipcClient:null,_initPromise:null,isQtEnvironment:()=>"undefined"!=typeof window&&(window.qt||window.QWebChannel||/QtWebEngine/.test(navigator.userAgent)),async init(){return this._initPromise||(this._initPromise=this._doInit()),this._initPromise},async _doInit(){if(!this.ipcClient)try{this.isQtEnvironment()?(this.ipcClient=await Rn.init(),console.log("IPC 初始化成功")):(console.warn("非 QT 环境，使用模拟 IPC 客户端"),this.ipcClient=this._createMockIpcClient())}catch(e){console.error("IPC 初始化失败:",e),this.ipcClient=this._createMockIpcClient()}return this.ipcClient},_createMockIpcClient:()=>({$ipcSend:(e,t,r={})=>(console.warn(`模拟 IPC 调用: ${e}.${t}`,r),Promise.reject(new Error(`IPC not available in current environment (${e}.${t})`))),$ipcOn:(e,t,r)=>{console.warn(`模拟 IPC 监听: ${e}.${t}`)},$ipcOff:(e,t,r)=>{console.warn(`模拟 IPC 取消监听: ${e}.${t}`)},$processId:0}),async getClientBaseInfo(e=!1){return await this.init(),this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_GetClientBaseInfo",{},{timeout:{time:e}})},async updateClientBaseInfo(){return await this.init(),this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_UpdateAgentInfo")},async getNetworkList(e){return await this.init(),this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_GetNetworkList",e)},async switchAgentMode(e){return await this.init(),this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_SwitchAgentMode",e)},async AuthEnd(e){return await this.init(),this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_AuthEnd",e)},getSecurityCheckPolicy(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetSecurityCheckPolicy",e)},callAgentOneFunc(e){const t={WhatToDo:"CallOneFunc",...e};return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_CallOneFunc",t)},getSecurityCheckItem(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_DoSecurityCheckItem",e)},async securityCheckEnd(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_DoSecurityCheckEnd",e)},getNeetInstallPatchList(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetNeetInstallPatchList")},repairSecCheckItem(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_RepairSecCheckItem",e)},getAuthBaseInfo(){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_GetAuthBaseInfo")},dot1xAuth(e,t=!1){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_Dot1xAuth",e,{timeout:{time:t}})},setUIContext(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_SetUIContext",e)},getAllUsbKeyCert(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_GetAllUsbKeyCert",e)},async toRequestWeb(t=null,r){return await this.init(),this.ipcClient.$ipcOn("UIPlatform_Window","ToRequestWeb",((t,n,o)=>{if(e.isEmpty(o))o={};else try{o=e.isObject(o)?o:JSON.parse(o)}catch(Gn){console.error(Gn),o={}}r(n,o)}))},requestRemoteScreen(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_RequestRemoteScreen")},checkRemoteCtrlState(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_CheckRemoteCtrlState")},debugOut(e){return this.ipcClient.$ipcSend("UIPlatform_Window","DebugOut",{strInfo:e},{isNeedId:!1})},fileTools:{config:{asm_root:"",dll:"MsacFileApi.dll"},async ActionLocalFile(e,t,r){if(t=t||"save",!e)return!1;const n=await this.GetAsmDir();switch(t){case"save":return this.SaveStrToFile(n+e,r);case"del":return this.DeleteOneFile(n+e);case"read":return this.ReadStrFromFile(n+e);default:console.info("ActionLocalFile Type 错误！")}},async GetAsmDir(){if(""===this.config.asm_root){const t={WhereIsModule:this.config.dll,WhatFuncToCall:"GetDir",RequestParam:"ASM"},r=await Dn.callAgentOneFunc(t);e.isEmpty(r)||(this.config.asm_root=r)}return this.config.asm_root},async ReadStrFromFile(t){t=t.replace(/\s/g,"");const r={WhereIsModule:this.config.dll,WhatFuncToCall:"ReadStrFromFile",RequestParam:t},n=await Dn.callAgentOneFunc(r);return e.isEmpty(n)?"":n},async GetFileList(t){const r={WhereIsModule:this.config.dll,WhatFuncToCall:"GetFileList",RequestParam:t},n=await Dn.callAgentOneFunc(r);return e.isEmpty(n)?"":n},async DeleteOneFile(t){const r={WhereIsModule:this.config.dll,WhatFuncToCall:"DeleteOneFile",RequestParam:t},n=await Dn.callAgentOneFunc(r);return e.isEmpty(n)?"":n},async DeleteOneFileNever(e){const t=await this.GetAsmDir(),r={WhereIsModule:this.config.dll,WhatFuncToCall:"DeleteOneFile",RequestParam:t+e};await Dn.callAgentOneFunc(r)},async SaveStrToFile(e,t){t=(e=e.replace(/\s/g,""))+"|"+t;const r={WhereIsModule:this.config.dll,WhatFuncToCall:"SaveStrToFile",RequestParam:t};await Dn.callAgentOneFunc(r)}},getLocalLangue(){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_IsiGetLangue")},setLocalLangue(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_IsiSetLangue",e)},logOut(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_AssUserLogout",e)},windowOpenUrl(e,t=0){-1===(e=e.replace(/\s/g,"")).indexOf("://")&&(e="http://"+e);const r={File:e,Params:"",ShowType:t,NeedWait:0};return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_AssShellOpen",r)},getSecCheckResult(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetPersistantSecCheckResult")},setNetIsolate(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_NetIsolate",e)},getNetIsolate(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_GetPolicy",e)},SetTitleDimension(e){return this.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",e)},isDomainNoFake(){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_IsDomainNoFake")},getThirdLinkageMenu(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_GetThirdLinkageMenu")},operateThirdLinkageMenu(e){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_OperateThirdLinkageMenu",e)},spaToAuth(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SPAToAuthServer",e)},setLoginRet(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SetLoginRet",e)},showWnd(e){return this.ipcClient.$ipcSend("UIPlatform_Window","ShowWnd",e)},getGatewayInfos(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_GetGatewayInfos",e)},spaChangeNet(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SwitchToInAndEx",e)},setDevAccessInfo(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_SetDevAccessInfo",e)},openRDC(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_OpenRDC",e)},getInstalledPatchList(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetInstalledPatchList")},getPatchRepairDetailInfo(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetPatchRepairDetailInfo")},doRepairPatch(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_DoRepairPatch",e)},terminateWnd(){return this.ipcClient.$ipcSend("UIPlatform_Window","TerminateWnd")},normalnizeWnd(){return this.ipcClient.$ipcSend("UIPlatform_Window","NormalnizeWnd")},async maximizeWnd(){return await this.init(),this.ipcClient.$ipcSend("UIPlatform_Window","MaximizeWnd")},async minimizeWnd(){return await this.init(),this.ipcClient.$ipcSend("UIPlatform_Window","MinimizeWnd")},async hideWend(){return await this.init(),this.ipcClient.$ipcSend("UIPlatform_Window","HideWnd")},notifyAssUIExcuteResult(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_NotifyAssUIExcuteResult",e)},windowCustomAppOpenUrl(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_AssShellOpen",{ShowType:0,NeedWait:0,ResType:1,...e})},refreshDeviceStatus(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_RefreshDeviceStatus",e)},changeNetMode(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_ChangeNetMode",e)},refreshVPNPolicy(){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_RefreshVPNPolicy",{})},checkSdcInstall(e){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_CheckSDCClientInstalled")},openSdcApp(e){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_OperateSDCLinkage",e)},getFreeDriverLetter(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_GetFreeDriverLetter")},windowFileServeOpenUrl(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_AssShellOpen",e)},openDevTools(){window.eleIPC.send("openDevTools")},evokeAndFocus(){window.eleIPC.send("evokeAndFocus")},elelWriteLog(e){isElectron()&&window.eleIPC.send("log",e)},notifyResGroup(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_NotifyResGroup",e)},setVPNStatus(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SetVPNStatus",e)},setVPNInterceptMode(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SetVPNInterceptMode",e)},queryVPNInfo(){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_QueryVPNInfo")},elelWindowIsVisible:()=>isElectron()?window.eleIPC.invoke("isVisible"):""},Nn=""+new URL("avator.bd83723a.png",import.meta.url).href,Mn={class:"layout-header"},Fn={id:"u-header-menu",class:"right-wrapper"},$n={id:"u-avator",ref:"countMenu"},Ln={class:"user-info"},Bn={class:"user-name"};const Qn=n({name:"ClientHeader",data:()=>({drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1}),computed:{isAccess:()=>!1},watch:{userId(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted(){},beforeDestroy(){},methods:{minimizeWnd(){Dn.minimizeWnd()},maximizeWndOrNot(){this.isMaxWindow?(Dn.normalnizeWnd(),this.isMaxWindow=!1):(Dn.maximizeWnd(),this.isMaxWindow=!0)},dropdownVisiHandle(){},async closeWnd(){if(TestQtModule("UIPlatform_Window","HideWnd"))EventBus.$emit("closeAssui"),this.$nextTick((()=>{Dn.hideWend()}));else try{await Dn.init(),await Dn.ipcClient.$ipcSend("UIPlatform_Window","TerminateWnd")}catch(e){this.$message.error("操作失败，因小助手版本低。请重启小助手或电脑以升级。")}},async setHandle(e){if("changeLange"===e){const e=this.$i18n.locale;setLang("zh"===e?"en":"zh")}else"changeMode"===e&&this.changeMode()},userMenuHandle(e,t={}){switch(this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},async logoutHandle(){if(this.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1===this.logoutType){try{let e;this.isSsoAuth()&&(e=await ssoLogout(_.get(this.clientInfo,"accessStatus.lastAuthType")));const t=await proxyApi.cutoffDevice({device_id:_.get(this.clientInfo,"detail.DeviceID",0),remark:"LogOut"});if(0!==parseInt(_.get(t,"errcode")))return _.get(t,"errmsg")||this.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),void loading.destory();commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),await Dn.logOut({IsCredibleDevice:_.get(this.clientInfo,"detail.IsTrustDev","0")}),this.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,this.isDot1xMode&&this.setClientInfo(_.merge({},this.clientInfo,{basic:{IsOnline:0}})),this.setAuthInfo({...this.authInfo,basic:{}}),this.setClientInfo({...this.clientInfo,accessStatus:{}});const r=(new Date).getTime();this.$router.push({name:"message",params:{forceTo:!0},query:{t:r}}),_.isString(e)&&""!==e&&(console.log("logoutUrl:".logoutUrl),Dn.windowOpenUrl(e))}catch(e){console.error("退出登录错误",e)}loading.destory()}},async getCountMenuWidth(){const e=this.isZtpUser?44:0,t=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);try{await Dn.init(),await Dn.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(t)+e})}catch(r){console.warn("设置标题尺寸失败:",r)}},hdEventHandle(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer(){this.deviceInnerBack=!1},changeVisible(e){this.drawer=e}}},[["render",function(e,t,r,n,d,h){const g=o("base-icon"),m=o("el-dropdown-item"),b=o("el-dropdown-menu"),w=o("el-dropdown");return i(),a("div",Mn,[t[3]||(t[3]=l("div",{class:"header-logo"},[s("如果图片加载失败就隐藏"),l("img",{src:y,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),t[4]||(t[4]=l("div",{id:"u-electron-drag"},null,-1)),l("ul",Fn,[l("li",$n,[c(w,{id:"ui-headNav-header-div-account_info",placement:"bottom-start",onCommand:h.userMenuHandle,onVisibleChange:h.dropdownVisiHandle},{default:u((()=>[l("div",Ln,[t[0]||(t[0]=l("div",{class:"user-face"},[l("img",{src:Nn,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),l("span",Bn,p(d.username),1)]),c(b,{slot:"dropdown",class:"header-count-menu"},{default:u((()=>[c(m,{id:"ui-headNav-header-li-cancel_account",command:"lougOut"},{default:u((()=>[c(g,{name:"logout",style:{"margin-right":"6px"}}),t[1]||(t[1]=f("注销 "))])),_:1,__:[1]})])),_:1})])),_:1},8,["onCommand","onVisibleChange"])],512),t[2]||(t[2]=l("div",{class:"user-divider"},null,-1)),c(g,{class:"window-operate",name:d.isMaxWindow?"fullscreen_exit":"fullscreen",onClick:h.maximizeWndOrNot},null,8,["name","onClick"]),c(g,{class:"window-operate",name:"minus",onClick:h.minimizeWnd},null,8,["onClick"]),c(g,{class:"window-operate",name:"close",style:{"margin-right":"16px"},onClick:h.closeWnd},null,8,["onClick"])])])}],["__scopeId","data-v-30488b75"],["__file","D:/asec-platform/frontend/portal/src/view/client/header.vue"]]);export{Qn as default};
