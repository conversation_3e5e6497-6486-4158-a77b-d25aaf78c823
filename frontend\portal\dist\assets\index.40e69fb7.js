/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{_ as e,c as o,h as t,o as n,g as r,w as s,d as a,F as i,i as l,f as u,A as f}from"./index.2a422357.js";import d from"./menuItem.0b069a5d.js";import m from"./asyncSubmenu.ad5e1770.js";const c=e(Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:<PERSON>olean},theme:{default:function(){return{}},type:Object}},setup(e){const c=e,p=o((()=>c.routerInfo.children&&c.routerInfo.children.filter((e=>!e.hidden)).length?m:d));return(o,d)=>{const m=t("AsideComponent");return e.routerInfo.hidden?u("v-if",!0):(n(),r(f(p.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:s((()=>[e.routerInfo.children&&e.routerInfo.children.length?(n(!0),a(i,{key:0},l(e.routerInfo.children,(o=>(n(),r(m,{key:o.name,"is-collapse":!1,"router-info":o,theme:e.theme},null,8,["router-info","theme"])))),128)):u("v-if",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/index.vue"]]);export{c as default};
