/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
import{_ as e,u as a,a as s,r as l,b as t,c as n,z as u,K as o,P as r,h as i,o as v,d as m,j as c,w as d,F as p,i as y,g,e as f,B as h,m as b,k as S,t as I,R as q,S as O,f as w,G as k,H as C}from"./index.9684b1fb.js";import{J as N}from"./index-browser-esm.c2d3b5c9.js";const x={class:"router-history"},J=["tab"],j=e(Object.assign({name:"HistoryComponent"},{setup(e){const j=a(),V=s(),E=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),P=l([]),_=l(""),A=l(!1),R=t(),T=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),L=l(0),H=l(0),$=l(!1),z=l(!1),B=l(""),D=n((()=>N("$..defaultRouter[0]",R.userInfo)[0]||"dashboard")),F=()=>{P.value=[{name:D.value,meta:{title:"总览"},query:{},params:{}}],V.push({name:D.value}),A.value=!1,sessionStorage.setItem("historys",JSON.stringify(P.value))},G=()=>{let e;const a=P.value.findIndex((a=>(E(a)===B.value&&(e=a),E(a)===B.value))),s=P.value.findIndex((e=>E(e)===_.value));P.value.splice(0,a),a>s&&V.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},K=()=>{let e;const a=P.value.findIndex((a=>(E(a)===B.value&&(e=a),E(a)===B.value))),s=P.value.findIndex((e=>E(e)===_.value));P.value.splice(a+1,P.value.length),a<s&&V.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},U=()=>{let e;P.value=P.value.filter((a=>(E(a)===B.value&&(e=a),E(a)===B.value))),V.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},X=e=>{if(!P.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,P.value.push(a)}window.sessionStorage.setItem("activeValue",E(e))},Y=l({});u((()=>P.value),(()=>{Y.value={},P.value.forEach((e=>{Y.value[E(e)]=e}))}));const M=e=>{const a=Y.value[e];V.push({name:a.name,query:a.query,params:a.params})},Q=e=>{const a=P.value.findIndex((a=>E(a)===e));E(j)===e&&(1===P.value.length?V.push({name:D.value}):a<P.value.length-1?V.push({name:P.value[a+1].name,query:P.value[a+1].query,params:P.value[a+1].params}):V.push({name:P.value[a-1].name,query:P.value[a-1].query,params:P.value[a-1].params})),P.value.splice(a,1)};u((()=>A.value),(()=>{A.value?document.body.addEventListener("click",(()=>{A.value=!1})):document.body.removeEventListener("click",(()=>{A.value=!1}))})),u((()=>j),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(P.value=P.value.filter((e=>!e.meta.closeTab)),X(e),sessionStorage.setItem("historys",JSON.stringify(P.value)),_.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),u((()=>P.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(P.value))}),{deep:!0});return(()=>{r.on("closeThisPage",(()=>{Q(T(j))})),r.on("closeAllPage",(()=>{F()})),r.on("mobile",(e=>{z.value=e})),r.on("collapse",(e=>{$.value=e}));const e=[{name:D.value,meta:{title:"总览"},query:{},params:{}}];P.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?_.value=window.sessionStorage.getItem("activeValue"):_.value=E(j),X(j),"true"===window.sessionStorage.getItem("needCloseAll")&&(F(),window.sessionStorage.removeItem("needCloseAll"))})(),o((()=>{r.off("collapse"),r.off("mobile")})),(e,a)=>{const s=i("el-tab-pane"),l=i("el-tabs");return v(),m("div",x,[c(l,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),closable:!(1===P.value.length&&e.$route.name===D.value),type:"card",onContextmenu:a[1]||(a[1]=O((e=>(e=>{if(1===P.value.length&&j.name===D.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;A.value=!0,s=$.value?54:220,z.value&&(s=0),L.value=e.clientX-s,H.value=e.clientY+10,B.value=a.substring(4)}})(e)),["prevent"])),onTabChange:M,onTabRemove:Q},{default:d((()=>[(v(!0),m(p,null,y(P.value,(e=>(v(),g(s,{key:T(e),label:e.meta.title,name:T(e),tab:e,class:"gva-tab"},{label:d((()=>[f("span",{tab:e,style:h({color:_.value===T(e)?b(R).activeColor:"#333"})},[f("i",{class:"dot",style:h({backgroundColor:_.value===T(e)?b(R).activeColor:"#ddd"})},null,4),S(" "+I(b(q)(e.meta.title,e)),1)],12,J)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),w("自定义右键菜单html代码"),k(f("ul",{style:h({left:L.value+"px",top:H.value+"px"}),class:"contextmenu"},[f("li",{onClick:F},"关闭所有"),f("li",{onClick:G},"关闭左侧"),f("li",{onClick:K},"关闭右侧"),f("li",{onClick:U},"关闭其他")],4),[[C,A.value]])])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/historyComponent/history.vue"]]);export{j as default};
