/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import{r as e,l as a,c as t,b as s,h as n,o as i,d as o,e as l,t as c,m as u,j as r,w as d,g as y,f as p,_ as f,n as m,M as _,k as v}from"./index.5a1fa56a.js";const h={class:"verify-code"},g={style:{top:"10px","margin-bottom":"25px","text-align":"center"}},b={class:"title"},k={key:0,class:"message-text"},C={key:1,class:"message-text"},x={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},I={style:{"text-align":"center"}},T=f(Object.assign({name:"VerifyCode"},{props:{auth_info:{type:Object,default:function(){return{}}},auth_id:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}},secondaryType:{type:String,default:"sms"}},emits:["verification-success","back","cancel"],setup(f,{emit:T}){const w=e(""),N=a("userName");a("last_id"),a("isSecondary");const S=f;console.log("verifyCode组件接收到的属性:",{secondaryType:S.secondaryType,authInfo:S.auth_info,canVerify:"email"===S.secondaryType?!1!==S.auth_info.hasEmail:!1!==S.auth_info.notPhone});const V=T,j=t((()=>void 0!==S.auth_info.hasContactInfo?S.auth_info.hasContactInfo:(S.secondaryType,!1!==S.auth_info.hasContactInfo))),q=e(60);let z;const K=()=>{clearInterval(z)},O=async()=>{if(!j.value)return;const e={uniq_key:S.auth_info.uniqKey,idp_id:S.auth_id};try{const a=await m(e);200===a.status&&-1!==a.data.code?(q.value=60,z=setInterval((()=>{q.value--,0===q.value&&K()}),1e3)):(_({showClose:!0,message:a.data.msg,type:"error"}),q.value=0)}catch(a){_({showClose:!0,message:"发送验证码失败",type:"error"}),q.value=0}};O();const E=s(),L=async()=>{const e={uniq_key:S.auth_info.uniqKey,auth_code:w.value,user_name:S.userName||N.value,idp_id:S.auth_id,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"},a=await E.LoginIn(e,"accessory");-1!==a.code&&V("verification-success",a)},M=()=>{V("cancel")};return(e,a)=>{const t=n("base-button"),s=n("base-input");return i(),o("div",h,[l("div",g,[l("span",b,c("email"===f.secondaryType?"邮件认证":"短信认证"),1)]),l("div",null,[j.value?(i(),o("div",k,"验证码已发送至您账号("+c(f.userName||u(N))+")关联的"+c("email"===f.secondaryType?"邮箱":"手机")+"，请注意查收",1)):(i(),o("div",C,"您的账号("+c(f.userName||u(N))+")未关联"+c("email"===f.secondaryType?"邮箱":"手机号码")+"，请联系管理员！",1)),j.value?(i(),o("div",x,[r(s,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),placeholder:"email"===f.secondaryType?"邮箱验证码":"短信验证码",class:"input-with-select"},{append:d((()=>[r(t,{type:"info",disabled:q.value>0,onClick:O},{default:d((()=>[v("重新发送 "+c(q.value>0?`(${q.value}秒)`:""),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue","placeholder"])])):y("",!0),l("div",I,[j.value?(i(),p(t,{key:0,type:"primary",size:"large",disabled:!w.value,onClick:L},{default:d((()=>a[1]||(a[1]=[v("确 定 ")]))),_:1,__:[1]},8,["disabled"])):y("",!0),r(t,{type:"info",size:"large",onClick:M},{default:d((()=>a[2]||(a[2]=[v("取 消 ")]))),_:1,__:[2]})])])])}}}),[["__scopeId","data-v-2c12ced5"]]);export{T as default};
