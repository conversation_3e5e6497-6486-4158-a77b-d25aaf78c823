/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import{_ as n,o as e,d as r,e as s}from"./index.5a1fa56a.js";const a=""+new URL("notFound.4e921f05.png",import.meta.url).href;const i=n({name:"Error"},[["render",function(n,i,t,l,o,p){return e(),r("div",null,i[0]||(i[0]=[s("div",{class:"big"},[s("div",{class:"inner"},[s("img",{src:a}),s("p",null,"未知错误"),s("p",{style:{"font-size":"18px","line-height":"40px"}},"常见问题为当前此角色无访问权限，如果确定要使用，请联系管理员进行分配"),s("p",null,"↓")])],-1)]))}]]);export{i as default};
