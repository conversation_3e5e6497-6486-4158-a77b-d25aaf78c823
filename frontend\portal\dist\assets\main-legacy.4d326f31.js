/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
System.register(["./index-legacy.5c3cde30.js","./index-legacy.60f18f5a.js"],(function(t,e){"use strict";var a,s,c,n,o,p,i,r,d,f=document.createElement("style");return f.textContent=".access-main{&[data-v-79882dcf]{padding:16px 16px 16px 0;width:100%;height:100%;display:flex;border-left:16px solid #f2f2f6;box-sizing:border-box;border-image:linear-gradient(to right,#fcfcfc,#fafafa,#ffffff);justify-content:center;align-items:center;background-color:#fcfcf6}.content-wrapper {&[data-v-79882dcf] {margin: 0px; width: 100%; height: 100%;} .access-proxy-status {&[data-v-79882dcf] {height: 102px; background: url("+new URL("proxy_background.51cd78fd.png",e.meta.url).href+") no-repeat center center; background-size: cover; position: relative;} .access-proxy-status-text[data-v-79882dcf] {position: absolute; font-size: 16px; font-weight: 600; height: 22px; width: 64px; top: 12px; left: 16px;} .access-proxy-status-span {&[data-v-79882dcf] {position: absolute; left: 50%; transform: translateX(-260px); width: 300px; height: 100px; display: flex; flex-direction: column; align-items: center; justify-content: center;} .access-proxy-status-btn[data-v-79882dcf] {margin-top: 10px; height: 32px; width: 152px;}}} .access-common-status {&[data-v-79882dcf] {height: 41px; font-size: 13px; font-weight: 400; display: flex; align-items: center; justify-content: space-between; margin-top: 16px; background-color: #fff; box-sizing: border-box;} .access-common-status-span[data-v-79882dcf] {margin-left: 16px;} .access-common-status-detail[data-v-79882dcf] {margin-right: 16px; color: #536ce6;}} .access-app {&[data-v-79882dcf] {height: calc(100% - 215px); font-size: 13px; font-weight: 400; display: flex; flex-direction: column; align-items: center; justify-content: center; margin-top: 16px;} .access-app-page[data-v-79882dcf] {width: 100%; overflow: auto;}}}}\n",document.head.appendChild(f),{setters:[function(t){a=t.default},function(t){s=t._,c=t.h,n=t.o,o=t.d,p=t.e,i=t.j,r=t.w,d=t.k}],execute:function(){var e={class:"access-main"},f={class:"content-wrapper"},l={class:"access-proxy-status"},u={class:"access-proxy-status-span"},x={class:"access-app"};t("default",s({name:"Access",components:{AppPage:a}},[["render",function(t,a,s,g,m,h){var y=c("base-button"),b=c("AppPage");return n(),o("div",e,[p("ul",f,[p("li",l,[a[2]||(a[2]=p("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),p("span",u,[a[1]||(a[1]=p("span",{class:"access-proxy-status-tips"}," 点击连接，即可安全便捷地访问应用 ",-1)),i(y,{class:"access-proxy-status-btn",color:"#626aef",type:"primary"},{default:r((function(){return a[0]||(a[0]=[d(" 一键连接 ")])})),_:1,__:[0]})])]),a[3]||(a[3]=p("li",{class:"access-common-status"},[p("span",{class:"access-common-status-span"},[p("span",null,"准入状态（企业网络下使用）："),p("span",{style:{color:"red"}},"未入网"),p("span",null,"（请重新建立连接）")]),p("span",{class:"access-common-status-detail"},[p("span",null,"查看详情")])],-1)),p("li",x,[i(b,{class:"access-app-page"})])])])}],["__scopeId","data-v-79882dcf"]]))}}}));
