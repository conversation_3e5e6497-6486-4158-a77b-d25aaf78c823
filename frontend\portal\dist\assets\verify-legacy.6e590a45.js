/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
System.register(["./index-legacy.f4c7ba6b.js"],(function(t,e){"use strict";var n,r,c,o,i;return{setters:[function(t){n=t._,r=t.b,c=t.q,o=t.o,i=t.d}],execute:function(){var e=Object.assign({name:"Verify"},{setup:function(t){var e=location.href.split("?")[1],n=new URLSearchParams(e),a=Object.fromEntries(n.entries()),s=r(),u=document.location.protocol+"//"+document.location.host,l=new URLSearchParams;"client"===a.type&&(l.set("type","client"),a.wp&&l.set("wp",a.wp));var f={method:"GET",url:"".concat(u,"/auth/user/v1/redirect_verify?redirect_url=").concat(a.redirect_url),headers:{Accept:"application/json, text/plain, */*",Authorization:"".concat(s.token.tokenType," ").concat(s.token.accessToken)}};return c.request(f).then((function(t){if(200===t.status){var e=t.data.url;if(l.toString()){var n=e.includes("?")?"&":"?";e+=n+l.toString()}window.location.href=e}})).catch((function(t){console.error(t)})),function(t,e){return o(),i("div")}}});t("default",n(e,[["__file","D:/asec-platform/frontend/portal/src/view/login/verify.vue"]]))}}}));
