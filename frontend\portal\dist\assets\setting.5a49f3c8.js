/*! 
 Build based on gin-vue-admin 
 Time : 1749732095000 */
import{_ as e,r as a,N as s,h as l,o as t,d as i,f as n,e as o,I as c,j as v,w as u,t as d,M as r,k as p}from"./index.8e61b3f9.js";const b={class:"setting-page"},m={class:"main-content"},g={class:"setting-container"},_={class:"tabs-header"},f={class:"tabs-content"},V={key:0,class:"tab-panel"},y={class:"setting-section"},h={class:"setting-item setting-platformAddress"},k={class:"setting-item"},x={class:"checkbox-group"},U={key:1,class:"tab-panel"},S={class:"setting-section setting-update"},C={class:"setting-item"},I={class:"checkbox-group"},w={class:"setting-item"},j={class:"about-section"},A={class:"version-info"},N={class:"version-item"},q={class:"version-value-group"},z={class:"version-value"},D={class:"version-item"},F={class:"version-value"},J={class:"version-item"},M={class:"version-value"},O=e({__name:"setting",setup(e){const O=a("general"),T=a(""),B=a(!1),E=a(!0),G=a(!0),H=a("daily"),K=a("2.5.0"),L=a("2025.03.21 09:00"),P=a("2025.03.21 09:00");s((()=>{R()}));const Q=async()=>{r.info("正在检查更新..."),setTimeout((()=>{r.success("当前已是最新版本")}),1500)},R=()=>{const e=localStorage.getItem("appSettings");if(e){const a=JSON.parse(e);T.value=a.platformAddress||"",B.value=a.autoStart||!1,E.value=void 0===a.autoConnect||a.autoConnect,G.value=void 0===a.autoUpdate||a.autoUpdate,H.value=a.updateFrequency||"daily"}};return(e,a)=>{const s=l("base-input"),r=l("base-checkbox"),R=l("base-option"),W=l("base-select"),X=l("base-button");return t(),i("div",b,[n(" 主内容区域 "),o("div",m,[o("div",g,[n(" 标签页导航 "),o("div",_,[o("div",{class:c(["tab-item",{active:"general"===O.value}]),onClick:a[0]||(a[0]=e=>O.value="general")}," 通用设置 ",2),o("div",{class:c(["tab-item",{active:"version"===O.value}]),onClick:a[1]||(a[1]=e=>O.value="version")}," 版本信息 ",2)]),n(" 标签页内容 "),o("div",f,[n(" 通用设置页面 "),"general"===O.value?(t(),i("div",V,[o("div",y,[o("div",h,[a[7]||(a[7]=o("label",{class:"setting-label"},"平台地址",-1)),v(s,{modelValue:T.value,"onUpdate:modelValue":a[2]||(a[2]=e=>T.value=e),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:""},null,8,["modelValue"])]),o("div",k,[a[10]||(a[10]=o("label",{class:"setting-label"},"启动选项",-1)),o("div",x,[v(r,{modelValue:B.value,"onUpdate:modelValue":a[3]||(a[3]=e=>B.value=e),class:"setting-checkbox"},{default:u((()=>a[8]||(a[8]=[p(" 开机自启动 ")]))),_:1,__:[8]},8,["modelValue"]),v(r,{modelValue:E.value,"onUpdate:modelValue":a[4]||(a[4]=e=>E.value=e),class:"setting-checkbox"},{default:u((()=>a[9]||(a[9]=[p(" 启动后自动连接 ")]))),_:1,__:[9]},8,["modelValue"])])])])])):n("v-if",!0),n(" 版本信息页面 "),"version"===O.value?(t(),i("div",U,[o("div",S,[o("div",C,[a[12]||(a[12]=o("label",{class:"setting-label"},"更新选项",-1)),o("div",I,[v(r,{modelValue:G.value,"onUpdate:modelValue":a[5]||(a[5]=e=>G.value=e),class:"setting-checkbox"},{default:u((()=>a[11]||(a[11]=[p(" 自动检查更新 ")]))),_:1,__:[11]},8,["modelValue"])])]),o("div",w,[a[13]||(a[13]=o("label",{class:"setting-label"},"更新检查频率",-1)),v(W,{modelValue:H.value,"onUpdate:modelValue":a[6]||(a[6]=e=>H.value=e),class:"setting-select",placeholder:"请选择"},{default:u((()=>[v(R,{label:"每天",value:"daily"}),v(R,{label:"每周",value:"weekly"}),v(R,{label:"每月",value:"monthly"})])),_:1},8,["modelValue"])])]),o("div",j,[a[18]||(a[18]=o("h3",{class:"about-title"},"关于安全客户端",-1)),o("div",A,[o("div",N,[a[15]||(a[15]=o("span",{class:"version-label"},"当前版本",-1)),o("div",q,[o("span",z,d(K.value),1),v(X,{text:"",type:"primary",size:"small",onClick:Q},{default:u((()=>a[14]||(a[14]=[p(" 检查更新 ")]))),_:1,__:[14]})])]),o("div",D,[a[16]||(a[16]=o("span",{class:"version-label"},"构建时间",-1)),o("span",F,d(L.value),1)]),o("div",J,[a[17]||(a[17]=o("span",{class:"version-label"},"上次更新时间",-1)),o("span",M,d(P.value),1)])]),a[19]||(a[19]=o("div",{class:"copyright"},[o("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])])):n("v-if",!0)])])])])}}},[["__scopeId","data-v-ab416471"],["__file","D:/asec-platform/frontend/portal/src/view/client/setting.vue"]]);export{O as default};
