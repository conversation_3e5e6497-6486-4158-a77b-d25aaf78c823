/*! 
 Build based on gin-vue-admin 
 Time : 1749637440000 */
import{x as e,y as a,u as l,r as t,c as n,b as u,z as i,p as s,h as o,o as r,d as v,e as c,k as d,t as p,g as m,f as y,A as h,j as f,w as g,m as _,B as x,L as w,F as k,i as b}from"./index.8e727eba.js";const C={class:"login-page"},T={class:"content"},E={class:"right-panel"},O={key:0},L={key:0,class:"title"},P={key:1,class:"title"},I={style:{"text-align":"center"}},j={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},q={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},A=["xlink:href"],R={key:2,class:"login_panel_form"},S={key:3},V=["onClick"],D={class:"icon","aria-hidden":"true",style:{height:"25px",width:"24px"}},B=["xlink:href"],z={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis","margin-top":"5px","font-size":"12px"}},K={key:1,class:"auth-waiting"},N={class:"waiting-icon"},U={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},J=["xlink:href"],F={class:"waiting-title"},M={class:"security-tips"},$=Object.assign({name:"Login"},{setup($){const G=a({loader:()=>x((()=>import("./localLogin.9bf8f93c.js")),["./localLogin.9bf8f93c.js","./index.8e727eba.js","./index.6cd752aa.css","./localLogin.f639b4eb.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),H=a({loader:()=>x((()=>import("./wechat.d1704334.js")),["./wechat.d1704334.js","./index.8e727eba.js","./index.6cd752aa.css","./wechat.3b1b375f.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Q=a({loader:()=>x((()=>import("./feishu.ad93b3e6.js")),["./feishu.ad93b3e6.js","./index.8e727eba.js","./index.6cd752aa.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),W=a({loader:()=>x((()=>import("./dingtalk.be3eb0d3.js")),["./dingtalk.be3eb0d3.js","./index.8e727eba.js","./index.6cd752aa.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),X=a({loader:()=>x((()=>import("./oauth2.9583496b.js")),["./oauth2.9583496b.js","./index.8e727eba.js","./index.6cd752aa.css","./oauth2.79676400.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Y=a({loader:()=>x((()=>import("./sms.fe14e408.js")),["./sms.fe14e408.js","./index.8e727eba.js","./index.6cd752aa.css","./sms.ef70f8fb.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),Z=a({loader:()=>x((()=>import("./secondaryAuth.67ee1d64.js")),["./secondaryAuth.67ee1d64.js","./verifyCode.f97dc97d.js","./index.8e727eba.js","./index.6cd752aa.css","./verifyCode.978f9466.css","./secondaryAuth.170537bd.css"],import.meta.url),loadingComponent:w,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ee=l(),ae=t(0),le=t([]),te=t("local"),ne=t(""),ue=t(""),ie=t(""),se=t([]),oe=t([]),re=t(!1),ve=t(),ce=t(""),de=t(!1),pe=t(""),me=t(!1),ye=t(""),he=t(""),fe=t(""),ge=t({}),_e=n((()=>{const e=re.value?ye.value:ue.value;return le.value.filter((a=>a.id!==e))})),xe=u();n((()=>oe.value.filter((e=>e.id!==ue.value))));(async()=>{var a,l,t,n,u,i,s,o,r,v,c,d;try{const p=(()=>{const e={};if(ee.query.type&&(e.type=ee.query.type),ee.query.wp&&(e.wp=ee.query.wp),ee.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(ee.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(p).length>0&&(localStorage.setItem("client_params",JSON.stringify(p)),sessionStorage.setItem("client_params",JSON.stringify(p)));const m=await e({url:"/auth/login/v1/user/main_idp/list",method:"get"});if(200===m.status){le.value=m.data.idpList;const e=ee.query.idp_id||xe.loginType;if(e&&"undefined"!==e){let s=!1;for(const a of m.data.idpList)e===a.id&&(s=!0,ue.value=a.id,te.value=a.type,ne.value=a.templateType,se.value=a.attrs,se.value.name=a.name,se.value.authType=a.type);s||(ie.value=null==(a=le.value[0])?void 0:a.id,ue.value=null==(l=le.value[0])?void 0:l.id,te.value=null==(t=le.value[0])?void 0:t.type,ne.value=null==(n=le.value[0])?void 0:n.templateType,se.value=null==(u=le.value[0])?void 0:u.attrs,se.value.name=le.value[0].name,se.value.authType=null==(i=le.value[0])?void 0:i.type)}else ie.value=null==(s=le.value[0])?void 0:s.id,ue.value=null==(o=le.value[0])?void 0:o.id,te.value=null==(r=le.value[0])?void 0:r.type,ne.value=null==(v=le.value[0])?void 0:v.templateType,se.value=null==(c=le.value[0])?void 0:c.attrs,se.value.name=le.value[0].name,se.value.authType=null==(d=le.value[0])?void 0:d.type;++ae.value}}catch(p){console.error(p)}})();const we=n((()=>{switch(te.value){case"local":case"msad":case"ldap":case"web":case"email":return G;case"qiyewx":return H;case"feishu":return Q;case"dingtalk":return W;case"oauth2":case"cas":return X;case"sms":return Y;default:return"oauth2"===ne.value?X:"local"}})),ke=n((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===pe.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===pe.value}])),be=()=>{re.value=!1,oe.value=[],ve.value="",ce.value="",pe.value="",me.value=!1,ye.value&&(ue.value=ye.value,te.value=he.value,ne.value=fe.value,se.value={...ge.value},ye.value="",he.value="",fe.value="",ge.value={}),++ae.value,console.log("取消后恢复的状态:",{isSecondary:re.value,auth_id:ue.value,auth_type:te.value})},Ce=async e=>{const a=w.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=ee.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},Te=n((()=>!["dingtalk","feishu","qiyewx"].includes(te.value)&&("oauth2"!==ne.value&&"cas"!==te.value||("cas"===te.value?1===parseInt(se.value.casOpenType):"oauth2"===ne.value&&1===parseInt(se.value.oauth2OpenType))))),Ee=e=>{ie.value=e.id,se.value=e.attrs||{},se.value.name=e.name,se.value.authType=e.type,re.value&&(se.value.uniqKey=ve.value,se.value.notPhone=de.value),ue.value=e.id,te.value=e.type,ne.value=e.templateType,++ae.value};return i(re,(async(e,a)=>{re.value&&(ye.value=ue.value,he.value=te.value,fe.value=ne.value,ge.value={...se.value},console.log("二次认证数据:",{secondary:oe.value,secondaryLength:oe.value.length}),oe.value.length>0&&Ee(oe.value[0]))})),s("secondary",oe),s("isSecondary",re),s("uniqKey",ve),s("userName",ce),s("notPhone",de),s("last_id",ie),s("contactType",pe),s("hasContactInfo",me),(e,a)=>{const l=o("base-divider"),t=o("base-avatar"),n=o("base-carousel-item"),u=o("base-carousel"),i=o("base-icon");return r(),v("div",C,[c("div",T,[a[3]||(a[3]=c("div",{class:"left-panel"},null,-1)),c("div",E,[re.value?(r(),v("div",K,[c("div",N,[(r(),v("svg",U,[c("use",{"xlink:href":`#icon-auth-${he.value||te.value}`},null,8,J)]))]),c("h4",F,p(ge.value.name||se.value.name)+" 登录成功",1),a[2]||(a[2]=c("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),c("div",M,[f(i,{name:"shield",style:{color:"#67c23a"}}),a[1]||(a[1]=c("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])):(r(),v("div",O,["local"===te.value?(r(),v("span",L,"本地账号登录")):Te.value?(r(),v("span",P,[c("div",I,[c("span",j,[(r(),v("svg",q,[c("use",{"xlink:href":"#icon-auth-"+te.value},null,8,A)])),d(" "+p(se.value.name),1)])])])):m("",!0),ue.value?(r(),v("div",R,[(r(),y(h(we.value),{auth_id:ue.value,auth_info:se.value},null,8,["auth_id","auth_info"]))])):m("",!0),_e.value.length>0?(r(),v("div",S,[f(l,null,{default:g((()=>a[0]||(a[0]=[c("span",{style:{color:"#929298"}}," 其他登录方式 ",-1)]))),_:1,__:[0]}),(r(),y(u,{key:ae.value,autoplay:!1,"indicator-position":"none",height:"70px",style:{width:"100%",background:"#ffffff"}},{default:g((()=>[(r(!0),v(k,null,b(Math.ceil(_e.value.length/2),(e=>(r(),y(n,{key:e,style:{display:"flex","justify-content":"center","align-items":"center"}},{default:g((()=>[(r(!0),v(k,null,b(_e.value.slice(2*(e-1),2*(e-1)+2),(e=>(r(),v("div",{key:e.id,class:"auth-class",style:{cursor:"pointer",float:"left",width:"100px",height:"50px","text-align":"center"},onClick:a=>Ee(e)},[c("div",null,[f(t,{style:{background:"#ffffff",border:"1px #EBEBEB solid"}},{default:g((()=>[(r(),v("svg",D,[c("use",{"xlink:href":"#icon-auth-"+e.type},null,8,B)]))])),_:2},1024)]),c("div",z,p(e.name),1)],8,V)))),128))])),_:2},1024)))),128))])),_:1}))])):m("",!0)]))])]),re.value?(r(),y(_(Z),{key:0,"auth-info":{uniqKey:ve.value,contactType:pe.value,hasContactInfo:me.value},"auth-id":ue.value,"user-name":ce.value,"last-id":ie.value,"auth-methods":ke.value,onVerificationSuccess:Ce,onCancel:be},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):m("",!0)])}}});export{$ as default};
