/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{_ as e,u as a,a as s,r as l,b as t,c as n,z as u,H as o,O as r,h as i,o as v,d as m,j as c,w as d,F as p,i as y,g,e as f,K as h,m as b,k as S,t as I,S as q,J as O,f as w,Q as k,U as C}from"./index.44d6e232.js";import{J}from"./index-browser-esm.c2d3b5c9.js";const N={class:"router-history"},x=["tab"],j=e(Object.assign({name:"HistoryComponent"},{setup(e){const j=a(),V=s(),E=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),_=l([]),A=l(""),P=l(!1),T=t(),L=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),R=l(0),H=l(0),U=l(!1),$=l(!1),z=l(""),D=n((()=>J("$..defaultRouter[0]",T.userInfo)[0]||"dashboard")),F=()=>{_.value=[{name:D.value,meta:{title:"总览"},query:{},params:{}}],V.push({name:D.value}),P.value=!1,sessionStorage.setItem("historys",JSON.stringify(_.value))},K=()=>{let e;const a=_.value.findIndex((a=>(E(a)===z.value&&(e=a),E(a)===z.value))),s=_.value.findIndex((e=>E(e)===A.value));_.value.splice(0,a),a>s&&V.push(e),sessionStorage.setItem("historys",JSON.stringify(_.value))},Q=()=>{let e;const a=_.value.findIndex((a=>(E(a)===z.value&&(e=a),E(a)===z.value))),s=_.value.findIndex((e=>E(e)===A.value));_.value.splice(a+1,_.value.length),a<s&&V.push(e),sessionStorage.setItem("historys",JSON.stringify(_.value))},X=()=>{let e;_.value=_.value.filter((a=>(E(a)===z.value&&(e=a),E(a)===z.value))),V.push(e),sessionStorage.setItem("historys",JSON.stringify(_.value))},Y=e=>{if(!_.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,_.value.push(a)}window.sessionStorage.setItem("activeValue",E(e))},B=l({});u((()=>_.value),(()=>{B.value={},_.value.forEach((e=>{B.value[E(e)]=e}))}));const G=e=>{const a=B.value[e];V.push({name:a.name,query:a.query,params:a.params})},M=e=>{const a=_.value.findIndex((a=>E(a)===e));E(j)===e&&(1===_.value.length?V.push({name:D.value}):a<_.value.length-1?V.push({name:_.value[a+1].name,query:_.value[a+1].query,params:_.value[a+1].params}):V.push({name:_.value[a-1].name,query:_.value[a-1].query,params:_.value[a-1].params})),_.value.splice(a,1)};u((()=>P.value),(()=>{P.value?document.body.addEventListener("click",(()=>{P.value=!1})):document.body.removeEventListener("click",(()=>{P.value=!1}))})),u((()=>j),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(_.value=_.value.filter((e=>!e.meta.closeTab)),Y(e),sessionStorage.setItem("historys",JSON.stringify(_.value)),A.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),u((()=>_.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(_.value))}),{deep:!0});return(()=>{r.on("closeThisPage",(()=>{M(L(j))})),r.on("closeAllPage",(()=>{F()})),r.on("mobile",(e=>{$.value=e})),r.on("collapse",(e=>{U.value=e}));const e=[{name:D.value,meta:{title:"总览"},query:{},params:{}}];_.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?A.value=window.sessionStorage.getItem("activeValue"):A.value=E(j),Y(j),"true"===window.sessionStorage.getItem("needCloseAll")&&(F(),window.sessionStorage.removeItem("needCloseAll"))})(),o((()=>{r.off("collapse"),r.off("mobile")})),(e,a)=>{const s=i("el-tab-pane"),l=i("el-tabs");return v(),m("div",N,[c(l,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),closable:!(1===_.value.length&&e.$route.name===D.value),type:"card",onContextmenu:a[1]||(a[1]=O((e=>(e=>{if(1===_.value.length&&j.name===D.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;P.value=!0,s=U.value?54:220,$.value&&(s=0),R.value=e.clientX-s,H.value=e.clientY+10,z.value=a.substring(4)}})(e)),["prevent"])),onTabChange:G,onTabRemove:M},{default:d((()=>[(v(!0),m(p,null,y(_.value,(e=>(v(),g(s,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:d((()=>[f("span",{tab:e,style:h({color:A.value===L(e)?b(T).activeColor:"#333"})},[f("i",{class:"dot",style:h({backgroundColor:A.value===L(e)?b(T).activeColor:"#ddd"})},null,4),S(" "+I(b(q)(e.meta.title,e)),1)],12,x)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),w("自定义右键菜单html代码"),k(f("ul",{style:h({left:R.value+"px",top:H.value+"px"}),class:"contextmenu"},[f("li",{onClick:F},"关闭所有"),f("li",{onClick:K},"关闭左侧"),f("li",{onClick:Q},"关闭右侧"),f("li",{onClick:X},"关闭其他")],4),[[C,P.value]])])}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/historyComponent/history.vue"]]);export{j as default};
