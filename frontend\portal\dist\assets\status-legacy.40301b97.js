/*! 
 Build based on gin-vue-admin 
 Time : 1749730625000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,a,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",r=i.toStringTag||"@@toStringTag";function s(t,i,o,r){var s=i&&i.prototype instanceof c?i:c,u=Object.create(s.prototype);return e(u,"_invoke",function(t,e,i){var o,r,s,c=0,u=i||[],g=!1,p={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(t,e){return o=t,r=0,s=n,p.n=e,l}};function d(t,e){for(r=t,s=e,a=0;!g&&c&&!i&&a<u.length;a++){var i,o=u[a],d=p.p,h=o[2];t>3?(i=h===e)&&(s=o[(r=o[4])?5:(r=3,3)],o[4]=o[5]=n):o[0]<=d&&((i=t<2&&d<o[1])?(r=0,p.v=e,p.n=o[1]):d<h&&(i=t<3||o[0]>e||e>h)&&(o[4]=t,o[5]=e,p.n=h,r=0))}if(i||t>1)return l;throw g=!0,e}return function(i,u,h){if(c>1)throw TypeError("Generator is already running");for(g&&1===u&&d(u,h),r=u,s=h;(a=r<2?n:s)||!g;){o||(r?r<3?(r>1&&(p.n=-1),d(r,s)):p.n=s:p.v=s);try{if(c=2,o){if(r||(i="next"),a=o[i]){if(!(a=a.call(o,s)))throw TypeError("iterator result is not an object");if(!a.done)return a;s=a.value,r<2&&(r=0)}else 1===r&&(a=o.return)&&a.call(o),r<2&&(s=TypeError("The iterator does not provide a '"+i+"' method"),r=1);o=n}else if((a=(g=p.n<0)?s:t.call(e,p))!==l)break}catch(a){o=n,r=1,s=a}finally{c=1}}return{value:a,done:g}}}(t,o,r),!0),u}var l={};function c(){}function u(){}function g(){}a=Object.getPrototypeOf;var p=[][o]?a(a([][o]())):(e(a={},o,(function(){return this})),a),d=g.prototype=c.prototype=Object.create(p);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,e(t,r,"GeneratorFunction")),t.prototype=Object.create(d),t}return u.prototype=g,e(d,"constructor",g),e(g,"constructor",u),u.displayName="GeneratorFunction",e(g,r,"GeneratorFunction"),e(d),e(d,r,"Generator"),e(d,o,(function(){return this})),e(d,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:s,m:h}})()}function e(t,n,a,i){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}e=function(t,n,a,i){if(n)o?o(t,n,{value:a,enumerable:!i,configurable:!i,writable:!i}):t[n]=a;else{var r=function(n,a){e(t,n,(function(t){return this._invoke(n,a,t)}))};r("next",0),r("throw",1),r("return",2)}},e(t,n,a,i)}function n(t,e,n,a,i,o,r){try{var s=t[o](r),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(a,i)}function a(t){return function(){var e=this,a=arguments;return new Promise((function(i,o){var r=t.apply(e,a);function s(t){n(r,i,o,s,l,"next",t)}function l(t){n(r,i,o,s,l,"throw",t)}s(void 0)}))}}System.register(["./index-legacy.f4c7ba6b.js","./secondaryAuth-legacy.261548df.js","./verifyCode-legacy.676145a7.js"],(function(e,n){"use strict";var i,o,r,s,l,c,u,g,p,d,h,f,v,y,m,x=document.createElement("style");return x.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",n.meta.url).href+");background-size:cover;background-position:center;min-height:100vh}.login-page .header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.login-page .content{display:flex;height:calc(100% - 60px)}.login-page .content .left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.login-page .content .left-panel .image{width:718px;height:470px;margin-bottom:20px}.login-page .auth-class:hover .el-avatar{border:1px #204ED9 solid!important}.login-page .right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:#fff;border-radius:10px;box-shadow:0 0 10px rgba(0,0,0,.2);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.login-page .right-panel .auth-status{text-align:center;padding:20px 0}.login-page .right-panel .auth-status .status-icon{margin-bottom:20px}.login-page .right-panel .auth-status .status-title{font-size:20px;color:#333;margin-bottom:10px;font-weight:500}.login-page .right-panel .auth-status .status-message{color:#666;font-size:14px;margin-bottom:30px}.login-page .right-panel .auth-status .loading-dots{display:flex;justify-content:center;gap:8px}.login-page .right-panel .auth-status .loading-dots span{width:8px;height:8px;border-radius:50%;background-color:#0082ef;animation:loading-bounce 1.4s ease-in-out infinite both}.login-page .right-panel .auth-status .loading-dots span:nth-child(1){animation-delay:-.32s}.login-page .right-panel .auth-status .loading-dots span:nth-child(2){animation-delay:-.16s}.login-page .right-panel .auth-status .loading-dots span:nth-child(3){animation-delay:0s}.login-page .right-panel .auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.login-page .right-panel .auth-waiting .waiting-icon{margin-bottom:15px}.login-page .right-panel .auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.login-page .right-panel .auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4}@keyframes loading-bounce{0%,80%,to{transform:scale(0)}40%{transform:scale(1)}}\n",document.head.appendChild(x),{setters:[function(t){i=t._,o=t.u,r=t.a,s=t.r,l=t.b,c=t.c,u=t.p,g=t.o,p=t.d,d=t.e,h=t.f,f=t.F,v=t.g,y=t.L},function(t){m=t.default},function(){}],execute:function(){var x=""+new URL("login_building.7b8b4335.png",n.meta.url).href,b={class:"login-page"},w={class:"content"},A={class:"right-panel"},U={key:0,class:"auth-status"},S={class:"status-icon"},O={class:"icon","aria-hidden":"true",style:{height:"48px",width:"48px",color:"#0082ef"}},k={class:"auth-waiting"},j={class:"waiting-icon"},q={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},C=Object.assign({name:"Status"},{setup:function(e){var n=o(),i=r(),C=s({}),P=n.query,E=P.code,K=P.state,T=n.query,F=T.auth_type,I=T.redirect_url,B=T.type,G=T.wp,N=window.location.host,Q=window.location.protocol,D=l(),_=s(null),z=s(!1),R=s([]),J=s(!1),W=s(""),Y=s(""),Z=s(Array.isArray(K)?K[0]:K),H=s(""),L=s("phone"),M=s(!0),V=c((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===L.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===L.value}]})),X=function(){var e=a(t().m((function e(n){var a,i,o;return t().w((function(t){for(;;)switch(t.n){case 0:_.value=y.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{a=I||"/",n.clientParams&&((i=new URLSearchParams).set("type",n.clientParams.type),n.clientParams.wp&&i.set("wp",n.clientParams.wp),a+=(a.includes("?")?"&":"?")+i.toString()),window.location.href=a}finally{null===(o=_.value)||void 0===o||o.close()}case 1:return t.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),$=function(){z.value=!1,J.value=!1;var t=new URLSearchParams;t.set("idp_id",Array.isArray(K)?K[0]:K),I&&t.set("redirect",encodeURIComponent(I)),"client"===B&&(t.set("type","client"),G&&t.set("wp",G));var e="/login?".concat(t.toString());i.push(e)},tt=function(){var e=a(t().m((function e(){var n,a,i,o,r,s;return t().w((function(t){for(;;)switch(t.n){case 0:return _.value=y.service({fullscreen:!0,text:"登录中，请稍候..."}),t.p=1,n={clientId:"client_portal",grantType:"implicit",redirect_uri:"".concat(Q,"//").concat(N,"/#/status"),idpId:Array.isArray(K)?K[0]:K,authWeb:{authWebCode:Array.isArray(E)?E[0]:E}},t.n=2,D.LoginIn(n,F,Z.value);case 2:-1!==(a=t.v).code?a.isSecondary&&(J.value=a.isSecondary,R.value=a.secondary,Y.value=a.secondary[0].id,W.value=a.uniqKey,H.value=a.userName,L.value=a.contactType,M.value=a.hasContactInfo||!1,C.value.uniqKey=a.uniqKey,C.value.name=a.secondary[0].name,C.value.notPhone=a.notPhone,z.value=!0):(i="".concat(Q,"//").concat(N,"/#/login?idp_id=").concat(Array.isArray(K)?K[0]:K),I&&(i+="&redirect=".concat(I)),"client"===B&&(i+="&type=client",G&&(i+="&wp=".concat(G))),location.href=i),t.n=4;break;case 3:t.p=3,s=t.v,console.error("登录处理失败:",s),o="".concat(Q,"//").concat(N,"/#/login?idp_id=").concat(Array.isArray(K)?K[0]:K),"client"===B&&(o+="&type=client",G&&(o+="&wp=".concat(G))),location.href=o;case 4:return t.p=4,null===(r=_.value)||void 0===r||r.close(),t.f(4);case 5:return t.a(2)}}),e,null,[[1,3,4,5]])})));return function(){return e.apply(this,arguments)}}();return tt(),c((function(){return R.value.filter((function(t){return t.id!==Y.value}))})),u("userName",H),u("isSecondary",J),u("contactType",L),u("hasContactInfo",M),u("uniqKey",W),u("last_id",Z),function(t,e){return g(),p("div",b,[e[8]||(e[8]=d("div",{class:"header"},[d("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAATCAYAAACKsM07AAAAAXNSR0IArs4c6QAAAq5JREFUSEudk11IU2EYx//PcZtmZlREiNgHlNsZqEHmWUYXUhdFIHUh0U3eFNGneGZRXe2uiO2MLKPoqrqRLvqEIvoES3fMGw13Vkr0odCNgmVsuZ3zxFk5t7NNWS8ceJ/n/J//733eD8I8wy2rhxncCcCRRzZDRG3hgHQtnw3l9W/hIrFK/QKgcr5FABjXyqNr4WtK5NLlBYhedQ+Y76UXMfDQjAlozjAj2qsFpPsFAVyy+oTAO1NFjBEt6Kk2Y1EOfQCQnJuDgacRxTOnTSPl7MDZoa4jg0cJEOZM6HJEkU6asUsOXSIgOf8HMHSObxgJbvtk7SInwN0eOs+EMxbxbk3xPDZz1d7+zUWs9wBUPKshxoVw0HN2QYC75Y6Dq9Z8BXhVujhuW1Q1erFubDa3yTdQGp+2l83GuhGrGFakwQUBoty3D6DuHAcWB2B+OQcD00UC7R/2Sy/TBVlbJMrqc4C35zOaN894rQU9TQsAQiqAhv8CAKyT4f4YaIykzsZq5Dz9Zgnptmo2KP8jBGCDUaET3SZgaYYHUVALSHIWYP2JkWK7faKbAFd6AQM9P9loGws2Rq2LEWX1KsBHLPnJaHm08rOvKWbmU6sUZfUAwDetJiwIzRF/w6NcW+aU+5oF0IPsf9SqKdItK+AtwI2ZYvptF0pWDPnrfuUC1HYMLo4bsQmAU+/hr456NUXamgK42gdqiBJD2Sb8QlO27IDvlc05VbqRGPYMjU0oJtbvgrEsq3O21UaC9e+TWyTKoSsAjllFBJwKKx6/6O3vAhtHC7xZXZriOU5mmzNGbJzAmbfBdBP0Gq3sWVj8ses7wCsLATBoyiGUVJLoVQ+C+UaOg/qmKdJqd3tvA5Ngvo3CB9EhEuXQOwD1luoEmM7FE8s77Y7J62BuLdw9WTHwB+of7onYTsUzAAAAAElFTkSuQmCC",alt:"公司logo",class:"logo"}),h(' <h1 class="company-name">安数达</h1>\r\n      <span class="separator"></span> '),d("span",{class:"header-text"},"ASec安全平台")],-1)),d("div",w,[e[7]||(e[7]=d("div",{class:"left-panel"},[h(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),d("img",{src:x,alt:"宣传图",class:"image"}),h(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),d("div",A,[h(" 显示当前认证状态 "),z.value?(g(),p(f,{key:1},[h(" 如果需要二次认证，显示等待提示 "),d("div",k,[d("div",j,[(g(),p("svg",q,e[4]||(e[4]=[d("use",{"xlink:href":"#icon-auth-verify_code"},null,-1)])))]),e[5]||(e[5]=d("h4",{class:"waiting-title"},"需要进行安全验证",-1)),e[6]||(e[6]=d("p",{class:"waiting-message"},"请完成二次身份验证以确保账户安全",-1))])],2112)):(g(),p("div",U,[d("div",S,[(g(),p("svg",O,e[0]||(e[0]=[d("use",{"xlink:href":"#icon-auth-qiyewx"},null,-1)])))]),e[1]||(e[1]=d("h3",{class:"status-title"},"正在登录",-1)),e[2]||(e[2]=d("p",{class:"status-message"},"正在处理信息...",-1)),e[3]||(e[3]=d("div",{class:"loading-dots"},[d("span"),d("span"),d("span")],-1))]))])]),h(" 新的辅助认证组件 "),z.value?(g(),v(m,{key:0,"auth-info":{uniqKey:W.value,contactType:L.value,hasContactInfo:M.value},"auth-id":Y.value,"user-name":H.value,"last-id":Z.value,"auth-methods":V.value,onVerificationSuccess:X,onCancel:$},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):h("v-if",!0)])}}});e("default",i(C,[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/status.vue"]]))}}}))}();
