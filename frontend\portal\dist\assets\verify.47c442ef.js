/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
import{b as t,q as e,o,d as n}from"./index.8abc592d.js";const r=Object.assign({name:"Verify"},{setup(r){const c=location.href.split("?")[1],i=new URLSearchParams(c),s=Object.fromEntries(i.entries()),a=t(),l=document.location.protocol+"//"+document.location.host,u=new URLSearchParams;"client"===s.type&&(u.set("type","client"),s.wp&&u.set("wp",s.wp));const d={method:"GET",url:`${l}/auth/user/v1/redirect_verify?redirect_url=${s.redirect_url}`,headers:{Accept:"application/json, text/plain, */*",Authorization:`${a.token.tokenType} ${a.token.accessToken}`}};return e.request(d).then((function(t){if(200===t.status){let e=t.data.url;if(u.toString()){const t=e.includes("?")?"&":"?";e+=t+u.toString()}window.location.href=e}})).catch((function(t){console.error(t)})),(t,e)=>(o(),n("div"))}});export{r as default};
