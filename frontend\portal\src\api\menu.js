import service from '@/utils/request'
// @Summary 用户登录 获取动态路由
// @Produce  application/json
// @Param 可以什么都不填 调一下即可
// @Router /menu/getMenu [post]
export const asyncMenu = () => {
  // return service({
  //   url: '/menu/getMenu',
  //   method: 'post'
  // })
  return new Promise(function(resolve, reject) {
    resolve({
      'code': 0, 'data': {
        'menus': [
          {
            'ID': 9,
            'CreatedAt': '2022-09-21T21:35:16.381+08:00',
            'UpdatedAt': '2022-09-21T21:35:16.381+08:00',
            'parentId': '0',
            'path': 'clientLogin',
            'name': 'clientLogin',
            'hidden': true,
            'component': 'view/login/clientLogin.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '客户端登陆',
              'topTitle': '客户端登陆',
              'icon': 'message',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '9',
            'children': null,
            'parameters': [],
            'btns': null,
          },
          {
            'ID': 0,
            'CreatedAt': '2022-07-09T19:02:48.587+08:00',
            'UpdatedAt': '2022-07-09T19:02:48.587+08:00',
            'parentId': '0',
            'path': 'dashboard',
            'name': 'dashboard',
            'hidden': false,
            'component': 'view/app/index.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '应用门户',
              'topTitle': '',
              'icon': 'icon-yingyongliebiao',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '0',
            'children': null,
            'parameters': [],
            'btns': null,
          },
          {
            'ID': 0,
            'CreatedAt': '2022-07-09T19:02:48.587+08:00',
            'UpdatedAt': '2022-07-09T19:02:48.587+08:00',
            'parentId': '0',
            'path': 'download',
            'name': 'download',
            'hidden': false,
            'component': 'view/client/download.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '客户端下载',
              'topTitle': '客户端下载',
              'icon': 'icon-kehuduanxiazai',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '0',
            'children': null,
            'parameters': [],
            'btns': null,
          },
          {
            'ID': 8,
            'CreatedAt': '2022-09-21T21:35:16.381+08:00',
            'UpdatedAt': '2022-09-21T21:35:16.381+08:00',
            'parentId': '0',
            'path': 'person',
            'name': 'person',
            'hidden': true,
            'component': 'view/person/person.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '个人信息',
              'topTitle': '个人信息',
              'icon': 'message',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '8',
            'children': null,
            'parameters': [],
            'btns': null,
          }],
      }, 'msg': '获取成功',
    })
  })
}

// @Summary 获取menu列表
// @Produce  application/json
// @Param {
//  page     int
//	pageSize int
// }
// @Router /menu/getMenuList [post]
export const getMenuList = (data) => {
  return service({
    url: '/menu/getMenuList', method: 'post', data,
  })
}

// @Summary 新增基础menu
// @Produce  application/json
// @Param menu Object
// @Router /menu/getMenuList [post]
export const addBaseMenu = (data) => {
  return service({
    url: '/menu/addBaseMenu', method: 'post', data,
  })
}

// @Summary 获取基础路由列表
// @Produce  application/json
// @Param 可以什么都不填 调一下即可
// @Router /menu/getBaseMenuTree [post]
export const getBaseMenuTree = () => {
  return service({
    url: '/menu/getBaseMenuTree', method: 'post',
  })
}

// @Summary 添加用户menu关联关系
// @Produce  application/json
// @Param menus Object authorityId string
// @Router /menu/getMenuList [post]
export const addMenuAuthority = (data) => {
  return service({
    url: '/menu/addMenuAuthority', method: 'post', data,
  })
}

// @Summary 获取用户menu关联关系
// @Produce  application/json
// @Param authorityId string
// @Router /menu/getMenuAuthority [post]
export const getMenuAuthority = (data) => {
  return service({
    url: '/menu/getMenuAuthority', method: 'post', data,
  })
}

// @Summary 获取用户menu关联关系
// @Produce  application/json
// @Param ID float64
// @Router /menu/deleteBaseMenu [post]
export const deleteBaseMenu = (data) => {
  return service({
    url: '/menu/deleteBaseMenu', method: 'post', data,
  })
}

// @Summary 修改menu列表
// @Produce  application/json
// @Param menu Object
// @Router /menu/updateBaseMenu [post]
export const updateBaseMenu = (data) => {
  return service({
    url: '/menu/updateBaseMenu', method: 'post', data,
  })
}

// @Tags menu
// @Summary 根据id获取菜单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body api.GetById true "根据id获取菜单"
// @Success 200 {string} json "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /menu/getBaseMenuById [post]
export const getBaseMenuById = (data) => {
  return service({
    url: '/menu/getBaseMenuById', method: 'post', data,
  })
}
