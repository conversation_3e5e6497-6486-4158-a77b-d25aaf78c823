/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as Q,J as Y,r,G as Z,K as p,h as s,o as v,f as x,w as i,j as l,C as T,e as d,I as D,T as ee,d as B,k as P,g as $,M as O}from"./index.d0594432.js";import{_ as oe}from"./ASD.492c8837.js";import{g as ae}from"./browser.ff6109de.js";const te={style:{background:"'#273444'"}},ne={class:"downloadWin"},se={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},le={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-bottom":"42px","margin-top":"60px",display:"none"}},ie={key:1,class:"download-complete"},re={name:"downloadWin"},de=Object.assign(re,{setup(ce){Y(t=>({"4220daf8":t.activeBackground,"53d1bf38":t.normalText}));const n=r(!1),m=r(!0),f=r(!1),V=r("1"),h=r({});h.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const k=r(!1),w=r(0),g=r(!1),q=3;let F=0;const S=()=>{const t=document.body.clientWidth;t<1e3||t>=1e3&&t<1200?(f.value=!1,m.value=!1,n.value=!0):(f.value=!1,m.value=!0,n.value=!1)};S();const z=r(!1);Z(()=>{p.emit("collapse",n.value),p.emit("mobile",f.value),p.on("showLoading",()=>{z.value=!0}),p.on("closeLoading",()=>{z.value=!1}),window.onresize=()=>(()=>{S(),p.emit("collapse",n.value),p.emit("mobile",f.value)})()});const E=r("#1f2a36"),b=r(!1),L=()=>{n.value=!n.value,m.value=!n.value,b.value=!n.value,p.emit("collapse",n.value)},N=()=>{b.value=!b.value,m.value=!!n.value,L()},j=t=>t===100?"\u4E0B\u8F7D\u5B8C\u6210":`${t}%`,G=async t=>{if(t==="windows"){k.value=!0,w.value=0,g.value=!1,F=0;try{const e=await ae({platform:t});if(e.data.code===0){const o=window.location.port,a=new URL(e.data.data.download_url);let u;o?a.toString().includes("asec-deploy")?u=e.data.data.download_url:(a.port=o,u=a.toString()):(a.port="",u=a.toString());const c=o?e.data.data.latest_filename.replace(/@(\d+)/,`@${o}`):e.data.data.latest_filename;await M(u,c),g.value=!0,O({type:"success",message:"\u4E0B\u8F7D\u5B8C\u6210"})}else throw new Error(e.data.msg)}catch(e){O({type:"error",message:e.message||"\u4E0B\u8F7D\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458"})}finally{k.value=!1,setTimeout(()=>{g.value=!1},3e3)}}},M=async(t,e)=>{try{const o=await H(t);I(o,e)}catch(o){if(F<q&&o.message==="\u7F51\u7EDC\u8FDE\u63A5\u8D85\u65F6")return F++,M(t,e);throw new Error(`\u5B89\u88C5\u5305\u4E0B\u8F7D\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u6216\u8054\u7CFB\u7BA1\u7406\u5458\u3002\u9519\u8BEF: ${o.message}`)}},H=t=>new Promise((e,o)=>{const a=new XMLHttpRequest;a.open("GET",t,!0),a.responseType="blob",a.timeout=5*60*1e3;let u=Date.now();a.onprogress=c=>{if(c.lengthComputable){const _=c.loaded/c.total*100;w.value=Math.round(_)}else{const _=(Date.now()-u)/1e3,y=c.loaded/_*60,C=c.loaded/y*100;w.value=Math.min(99,Math.round(C))}},a.onload=()=>{a.status===200?e(a.response):o(new Error(`HTTP \u9519\u8BEF: ${a.status}`))},a.onerror=()=>{o(new Error("\u7F51\u7EDC\u9519\u8BEF"))},a.ontimeout=()=>{o(new Error("\u7F51\u7EDC\u8FDE\u63A5\u8D85\u65F6"))},a.send()}),I=(t,e)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(t,e);else{const o=document.createElement("a"),a=document.querySelector("body");o.href=window.URL.createObjectURL(t),o.download=e,o.style.display="none",a.appendChild(o),o.click(),a.removeChild(o),window.URL.revokeObjectURL(o.href)}};return(t,e)=>{const o=s("base-row"),a=s("base-icon"),u=s("el-menu-item"),c=s("el-menu"),_=s("el-scrollbar"),U=s("Expand"),y=s("el-icon"),C=s("Fold"),A=s("base-aside"),W=s("el-link"),J=s("el-progress"),K=s("base-main"),R=s("base-container");return v(),x(R,{class:"layout-cont"},{default:i(()=>[l(R,{class:T([m.value?"openside":"hideside",f.value?"mobile":""])},{default:i(()=>[l(o,{class:T([b.value?"shadowBg":""]),onClick:e[0]||(e[0]=X=>N())},null,8,["class"]),l(A,{class:"main-cont main-left gva-aside"},{default:i(()=>[d("div",{class:T(["tilte",[m.value?"openlogoimg":"hidelogoimg"]]),style:D({background:E.value})},e[2]||(e[2]=[d("img",{alt:"",class:"logoimg",src:oe},null,-1)]),6),d("div",te,[l(_,{style:{height:"calc(100vh - 110px)"}},{default:i(()=>[l(ee,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:i(()=>[l(c,{collapse:n.value,"collapse-transition":!1,"default-active":V.value,"background-color":h.value.background,"active-text-color":h.value.activeText,class:"el-menu-vertical","unique-opened":""},{default:i(()=>[l(u,{index:"1"},{default:i(()=>[l(a,{name:"xiazai",size:"16px"}),e[3]||(e[3]=d("span",null,"\u5BA2\u6237\u7AEF\u4E0B\u8F7D",-1))]),_:1,__:[3]})]),_:1},8,["collapse","default-active","background-color","active-text-color"])]),_:1})]),_:1})]),d("div",{class:"footer",style:D({background:E.value})},[d("div",{class:"menu-total",onClick:L},[n.value?(v(),x(y,{key:0,color:"#FFFFFF",size:"14px"},{default:i(()=>[l(U)]),_:1})):(v(),x(y,{key:1,color:"#FFFFFF",size:"14px"},{default:i(()=>[l(C)]),_:1}))])],4)]),_:1}),l(K,{class:"main-cont main-right client"},{default:i(()=>[d("div",ne,[d("div",{style:{"margin-bottom":"5%",float:"left","margin-right":"5%",width:"205px",height:"209px",background:"#F1F8FF",position:"relative"},onClick:e[1]||(e[1]=X=>G("windows"))},[(v(),B("svg",se,e[4]||(e[4]=[d("use",{"xlink:href":"#icon-windows"},null,-1)]))),(v(),B("svg",le,e[5]||(e[5]=[d("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[8]||(e[8]=d("br",null,null,-1)),l(W,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:i(()=>e[6]||(e[6]=[P(" Windows\u5BA2\u6237\u7AEF ")])),_:1,__:[6]}),l(W,{class:"window-hidden",underline:!1,style:{"margin-top":"42px",display:"none"}},{default:i(()=>e[7]||(e[7]=[P(" \u70B9\u51FB\u4E0B\u8F7DWindows\u5BA2\u6237\u7AEF ")])),_:1,__:[7]}),k.value?(v(),x(J,{key:0,percentage:w.value,format:j,"stroke-width":10,style:{"margin-top":"20px"}},null,8,["percentage"])):$("",!0),g.value?(v(),B("div",ie,"\u4E0B\u8F7D\u5B8C\u6210")):$("",!0)])])]),_:1})]),_:1},8,["class"])]),_:1})}}}),ve=Q(de,[["__scopeId","data-v-07719df4"]]);export{ve as default};
