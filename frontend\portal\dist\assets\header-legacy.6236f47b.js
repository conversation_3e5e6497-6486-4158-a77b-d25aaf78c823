/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(e,o,i,a){var c=o&&o.prototype instanceof u?o:u,s=Object.create(c.prototype);return t(s,"_invoke",function(e,t,o){var i,a,c,u=0,s=o||[],p=!1,f={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(e,t){return i=e,a=0,c=n,f.n=t,l}};function d(e,t){for(a=e,c=t,r=0;!p&&u&&!o&&r<s.length;r++){var o,i=s[r],d=f.p,y=i[2];e>3?(o=y===t)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=n):i[0]<=d&&((o=e<2&&d<i[1])?(a=0,f.v=t,f.n=i[1]):d<y&&(o=e<3||i[0]>t||t>y)&&(i[4]=e,i[5]=t,f.n=y,a=0))}if(o||e>1)return l;throw p=!0,t}return function(o,s,y){if(u>1)throw TypeError("Generator is already running");for(p&&1===s&&d(s,y),a=s,c=y;(r=a<2?n:c)||!p;){i||(a?a<3?(a>1&&(f.n=-1),d(a,c)):f.n=c:f.v=c);try{if(u=2,i){if(a||(o="next"),r=i[o]){if(!(r=r.call(i,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,a<2&&(a=0)}else 1===a&&(r=i.return)&&r.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=n}else if((r=(p=f.n<0)?c:e.call(t,f))!==l)break}catch(r){i=n,a=1,c=r}finally{u=1}}return{value:r,done:p}}}(e,i,a),!0),s}var l={};function u(){}function s(){}function p(){}r=Object.getPrototypeOf;var f=[][i]?r(r([][i]())):(t(r={},i,(function(){return this})),r),d=p.prototype=u.prototype=Object.create(f);function y(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,t(e,a,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=p,t(d,"constructor",p),t(p,"constructor",s),s.displayName="GeneratorFunction",t(p,a,"GeneratorFunction"),t(d),t(d,a,"Generator"),t(d,i,(function(){return this})),t(d,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:y}})()}function t(e,n,r,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}t=function(e,n,r,o){if(n)i?i(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var a=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};a("next",0),a("throw",1),a("return",2)}},t(e,n,r,o)}function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e,t,n){return(t=u(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t,n,r,o,i,a){try{var c=e[i](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function c(e){i(a,r,o,c,l,"next",e)}function l(e){i(a,r,o,c,l,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,u(r.key),r)}}function l(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}System.register(["./lodash-legacy.8c2f1c99.js","./index-legacy.e795fa57.js","./ASD-legacy.b6ffb1bc.js"],(function(t,n){"use strict";var o,i,c,u,f,d,y,h,g,m,b,v,w,S,P=document.createElement("style");return P.textContent='@charset "UTF-8";.layout-header[data-v-30488b75]{height:42px;display:flex;justify-content:space-between;align-items:center;background:linear-gradient(315deg,#536CE6,#647be9);box-shadow:0 2px 6px rgba(46,60,128,.2);color:#fff}.layout-header .header-title[data-v-30488b75]{line-height:42px;font-size:18px;font-weight:500}.layout-header .header-logo[data-v-30488b75]{height:42px;display:flex;align-items:center}.layout-header .header-logo img[data-v-30488b75]{max-width:79px;max-height:28px}.layout-header #u-electron-drag[data-v-30488b75]{display:flex;flex:1;height:100%;-webkit-app-region:drag}.layout-header .right-wrapper[data-v-30488b75]{display:flex;align-items:center;height:100%}.layout-header .right-wrapper>li[data-v-30488b75]:hover{background:#4256b8}.layout-header .right-wrapper .user-divider[data-v-30488b75]{width:1px;height:14px;margin-left:16px;margin-right:16px;background:#e6e6e6}.layout-header .right-wrapper .user-info[data-v-30488b75]{display:flex;align-items:center;height:42px;padding:0 14px;cursor:pointer}.layout-header .right-wrapper .user-info .user-face[data-v-30488b75]{width:32px;height:32px;border-radius:50%;overflow:hidden;margin-right:6px}.layout-header .right-wrapper .user-info .user-face img[data-v-30488b75]{width:100%;height:100%;display:block}.layout-header .right-wrapper .user-info .user-name[data-v-30488b75]{color:#fff;display:inline-block;max-width:100px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;word-break:break-all}.layout-header .right-wrapper .set-icon-wrapper[data-v-30488b75],.layout-header .right-wrapper .menu-msg[data-v-30488b75]{width:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;height:42px;position:relative}.layout-header .right-wrapper .set-icon-wrapper .icon-shezhi[data-v-30488b75],.layout-header .right-wrapper .menu-msg .icon-shezhi[data-v-30488b75]{color:#bac4f5;font-size:18px}.layout-header .right-wrapper .is-message[data-v-30488b75]:after{content:"";position:absolute;top:17px;right:13px;width:8px;height:8px;border-radius:50%;background:#FF4D4D}.layout-header .right-wrapper .window-operate[data-v-30488b75],.layout-header .right-wrapper #ui-headNav-header-li-msg_list[data-v-30488b75]{width:24px;height:100%;margin-left:4px;filter:brightness(1.5);display:flex;align-items:center;justify-content:center;cursor:pointer}.layout-header .right-wrapper .window-operate .iconfont[data-v-30488b75],.layout-header .right-wrapper #ui-headNav-header-li-msg_list .iconfont[data-v-30488b75]{color:#bac4f5;font-size:16px}.count-title[data-v-30488b75]{color:#686e84}.count-title i[data-v-30488b75]{font-style:normal;color:#3c404d}.el-dropdown-menu.header-count-menu[data-v-30488b75] .el-dropdown-menu__item{padding-left:40px;position:relative}.el-dropdown-menu.header-count-menu[data-v-30488b75] .el-dropdown-menu__item i{position:absolute;top:0;bottom:0;left:16px;font-size:14px}.el-dropdown-menu.header-count-menu[data-v-30488b75] .el-dropdown-menu__item .icon-qianduanruwang-yuyanqiehuan{font-size:15px}.el-dropdown-menu.header-count-menu[data-v-30488b75] .el-dropdown-menu__item .icon-qiehuanzhanghu1,.el-dropdown-menu.header-count-menu[data-v-30488b75] .el-dropdown-menu__item .icon-jisuruwang,.el-dropdown-menu.header-count-menu[data-v-30488b75] .el-dropdown-menu__item .icon-neiwaiwangqiehuan{font-size:16px}.s-title[data-v-30488b75]{margin-top:18px;margin-left:18px;font-size:13px;line-height:18px;font-weight:500;color:#3c404d}.s-content[data-v-30488b75]{padding:24px 32px 29px;font-size:13px;line-height:18px}.s-content .s-text[data-v-30488b75]{color:#686e84}.change-reg-info[data-v-30488b75]{padding-left:8px;line-height:20px;font-size:14px;font-weight:500;color:#3c404d}body .el-dialog-ip-box{width:260px}body .el-dialog-ip-box .el-message-box__content{padding:20px 15px}.s-content .el-radio{margin-right:13px}.s-content .el-radio .el-radio__label{padding-left:8px;font-size:13px;color:#3c404d;line-height:18px}#ip-info-dialog .ip-content{margin-top:24px;margin-bottom:24px;padding:0 24px;line-height:20px;font-size:14px;color:#3c404d}#ip-info-dialog .netcard-list{margin-top:16px;padding:0 24px}#ip-info-dialog .netcard-list li{display:flex;align-items:center;line-height:20px;font-size:14px;color:#3c404d;margin-bottom:10px}#ip-info-dialog .netcard-list li:last-child{margin-bottom:24px}#ip-info-dialog .netcard-list li i{font-size:16px;margin-left:16px}#ip-info-dialog .netcard-list li .icon-lianjie{color:#29cc88}#ip-info-dialog .netcard-list li .icon-duankailianjie{color:#e65353}#ip-info-dialog .el-dialog__footer button{height:40px;line-height:40px;border-bottom-right-radius:4px}.loginout-m-confirm-dialog .v-header{line-height:45px;border-bottom:1px solid #EDEDF1;padding:0 24px;font-size:16px;color:#3c404d}.loginout-m-confirm-dialog .v-header i{font-size:16px;color:#ffbf00;margin-right:6px;font-weight:400}.loginout-m-confirm-dialog .outline-tips{padding:24px;line-height:20px;color:#3c404d;font-size:14px}\n',document.head.appendChild(P),{setters:[function(e){o=e._},function(e){i=e.C,c=e.D,u=e._,f=e.h,d=e.o,y=e.d,h=e.e,g=e.f,m=e.j,b=e.w,v=e.t,w=e.k},function(e){S=e._}],execute:function(){var P=1,I=2,A=3,C=4,O=5,x=6,j=7,E=8,k=9,W=10,T=function(e,t){if("object"===p(e)&&"function"==typeof e.send){var n=this;this.transport=e,window.webChannel=n,this.send=function(e){"string"!=typeof e&&(e=JSON.stringify(e)),n.transport.send(e)},this.transport.onmessage=function(e){var t=e.data;switch("string"==typeof t&&(t=JSON.parse(t)),t.type){case P:n.handleSignal(t);break;case W:n.handleResponse(t);break;case I:n.handlePropertyUpdate(t);break;default:console.error("invalid message received:",e.data)}},this.execCallbacks={},this.execId=0,this.exec=function(e,t){t?(n.execId===Number.MAX_VALUE&&(n.execId=Number.MIN_VALUE),e.hasOwnProperty("id")?console.error("Cannot exec message with property id: "+JSON.stringify(e)):(e.id=n.execId++,n.execCallbacks[e.id]=t,n.send(e))):n.send(e)},this.objects={},this.handleSignal=function(e){var t=n.objects[e.object];t?t.signalEmitted(e.signal,e.args):console.warn("Unhandled signal: "+e.object+"::"+e.signal)},this.handleResponse=function(e){e.hasOwnProperty("id")?(n.execCallbacks[e.id](e.data),delete n.execCallbacks[e.id]):console.error("Invalid response message received: ",JSON.stringify(e))},this.handlePropertyUpdate=function(e){for(var t in e.data){var r=e.data[t],o=n.objects[r.object];o?o.propertyUpdate(r.signals,r.properties):console.warn("Unhandled property update: "+r.object+"::"+r.signal)}n.exec({type:C})},this.debug=function(e){n.send({type:O,data:e})},n.exec({type:A},(function(e){for(var r in e)new D(r,e[r],n);for(var o in n.objects)n.objects[o].unwrapProperties();t&&t(n),n.exec({type:C})}))}else console.error("The QWebChannel expects a transport object with a send function and onmessage callback property. Given is: transport: "+p(e)+", transport.send: "+p(e.send))};function D(e,t,n){this.__id__=e,n.objects[e]=this,this.__objectSignals__={},this.__propertyCache__={};var r=this;function o(e,t){var o=e[0],i=e[1];r[o]={connect:function(e){"function"==typeof e?(r.__objectSignals__[i]=r.__objectSignals__[i]||[],r.__objectSignals__[i].push(e),t||"destroyed"===o||n.exec({type:j,object:r.__id__,signal:i})):console.error("Bad callback given to connect to signal "+o)},disconnect:function(e){if("function"==typeof e){r.__objectSignals__[i]=r.__objectSignals__[i]||[];var a=r.__objectSignals__[i].indexOf(e);-1!==a?(r.__objectSignals__[i].splice(a,1),t||0!==r.__objectSignals__[i].length||n.exec({type:E,object:r.__id__,signal:i})):console.error("Cannot find connection of signal "+o+" to "+e.name)}else console.error("Bad callback given to disconnect from signal "+o)}}}function i(e,t){var n=r.__objectSignals__[e];n&&n.forEach((function(e){e.apply(e,t)}))}for(var a in this.unwrapQObject=function(e){if(e instanceof Array){for(var t=new Array(e.length),o=0;o<e.length;++o)t[o]=r.unwrapQObject(e[o]);return t}if(!e||!e["__QObject*__"]||void 0===e.id)return e;var i=e.id;if(n.objects[i])return n.objects[i];if(e.data){var a=new D(i,e.data,n);return a.destroyed.connect((function(){if(n.objects[i]===a){delete n.objects[i];var e=[];for(var t in a)e.push(t);for(var r in e)delete a[e[r]]}})),a.unwrapProperties(),a}console.error("Cannot unwrap unknown QObject "+i+" without data.")},this.unwrapProperties=function(){for(var e in r.__propertyCache__)r.__propertyCache__[e]=r.unwrapQObject(r.__propertyCache__[e])},this.propertyUpdate=function(e,t){for(var n in t){var o=t[n];r.__propertyCache__[n]=o}for(var a in e)i(a,e[a])},this.signalEmitted=function(e,t){i(e,this.unwrapQObject(t))},t.methods.forEach((function(e){var t=e[0],o=e[1];r[t]=function(){for(var e,t=[],i=0;i<arguments.length;++i){var a=arguments[i];"function"==typeof a?e=a:a instanceof D&&void 0!==n.objects[a.__id__]?t.push({id:a.__id__}):t.push(a)}n.exec({type:x,object:r.__id__,method:o,args:t},(function(t){if(void 0!==t){var n=r.unwrapQObject(t);e&&e(n)}}))}})),t.properties.forEach((function(e){var t=e[0],i=e[1],a=e[2];r.__propertyCache__[t]=e[3],a&&(1===a[0]&&(a[0]=i+"Changed"),o(a,!0)),Object.defineProperty(r,i,{configurable:!0,get:function(){var e=r.__propertyCache__[t];return void 0===e&&console.warn('Undefined value in property cache for property "'+i+'" in object '+r.__id__),e},set:function(e){if(void 0!==e){r.__propertyCache__[t]=e;var o=e;o instanceof D&&void 0!==n.objects[o.__id__]&&(o={id:o.__id__}),n.exec({type:k,object:r.__id__,property:t,value:o})}else console.warn("Property setter for "+i+" called with undefined value!")}})})),t.signals.forEach((function(e){o(e,!1)})),t.enums)r[a]=t.enums[a]}var U=function(){console.log(window.qt),N()||(window.qt={webChannelTransport:{send:function(){var e;e="QWebChannel simulator activated !",console.log("%c".concat(e),"font-weight: bold;")},onmessage:function(){}}})},N=function(){return navigator.userAgent.includes("QtWebEngine")&&void 0!==window.qt};var R=l((function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){};s(this,e),U(),this.sendQueue=[],this.eventQueue=[],this.send=function(e){var n=e.module,r=e.action,o=e.strSerial,i=e.data,a=void 0===i?"":i;return new Promise((function(e,i){t.sendQueue.push({module:n,action:r,strSerial:o,data:a,promise:{resolve:e,reject:i}})}))},this.on=function(e,n,r){t.eventQueue.push({module:e,event:n,callback:r})},this.off=function(e,t,n){console.log("尚未初始化！")},new T(window.qt.webChannelTransport,(function(e){Object.keys(e).includes("objects");var r=e.objects;t.send=function(e){return function(t){var n=t.module,r=t.action,o=t.strSerial,i=t.data,a=void 0===i?"":i,c=t.promise,l=void 0===c?null:c;return new Promise((function(t,i){return l&&l.reject&&l.resolve&&(t=l.resolve,i=l.reject),Object.keys(e).includes(n)?Object.keys(e[n]).includes(r)?"function"!=typeof e[n][r]?i(new Error("function"==typeof e[n][r].connect?"[SENDER]: ".concat(r," 不是一个QT信号或者QT方法"):"[SENDER]:  action : ".concat(r," 不是一个QT函数 !"))):void(-1===o?e[n][r](a,t):e[n][r](o,a,t)):i(new Error("[SENDER]: 该action"+r+" 不存在 !")):i(new Error("[SENDER]: 该module"+n+" 不存在 !"))}))}}(r),t.on=function(e){return function(t,n,r){if(!_.get(e,"".concat(t,".").concat(n)))throw new Error("[LISTENER]: ".concat(n," is not a Qt signa!"));if("function"!=typeof e[t][n].connect)throw new Error("[LISTENER]: No Connect Function!");e[t][n].connect(r)}}(r),t.off=function(e){return function(t,n,r){return Object.keys(e).includes(n)?Object.keys(e[n]).includes("disconnect")?"function"!=typeof e[n].disconnect?reject(new Error("[LISTENER]: No Disconnect Function!")):void e[t][n].disconnect(r):reject(new Error("[LISTENER]: ".concat(n," is not a Qt signa!"))):reject(new Error("[LISTENER]: Unknown event name!"))}}(r),t.sendQueue.length>0&&(t.sendQueue.forEach((function(e){t.send({module:e.module,action:e.action,strSerial:e.strSerial,data:e.data,promise:e.promise})})),t.sendQueue=[]),t.eventQueue.length>0&&(t.eventQueue.forEach((function(e){t.on(e.module,e.event,e.callback)})),t.eventQueue=[]),n(r)}))})),F={paramsToString:function(e){return function e(t){if("[object Array]"===Object.prototype.toString.call(t))t.forEach((function(n,r){"number"==typeof n?t[r]=n+"":"object"===p(n)&&e(n)}));else if("[object Object]"===Object.prototype.toString.call(t))for(var n in t)t.hasOwnProperty(n)&&("number"==typeof t[n]?t[n]+="":"object"===p(t[n])&&e(t[n]))}(e),e},serialId:0,getStrSerialId:function(){return this.serialId++,this.serialId%10==0&&this.serialId++,this.serialId>9e8&&(this.serialId=1),this.serialId},getStrSerial:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=1e8*this.getStrSerialId();return String(parseInt(String(t).substr(0,9))+parseInt(e))},interceptors:function(e,t){var n=this.sortParamsKey(e.strBody);if(this.paramsToString(e),n){var r=[];n.forEach((function(t){var n={Name:t,Value:encodeURIComponent(e.strBody[t])};r.push(n)})),e.strBody={Argument:r}}var o={ASM:e};return JSON.stringify(o)},sortParamsKey:function(e){if(!e||"{}"===JSON.stringify(e.strBody))return"";var t=Object.keys(e).sort((function(e,t){e=o.toString(e),t=o.toString(t);for(var n,r,i=o.max([e.length,t.length]),a=0;a<i;a++){var c=(n=e.charAt(a),r=t.charAt(a),n>r?1:n<r?-1:0);if(0!==c)return c}return 0}));return t},getStrLen:function(e){var t=0;if(!e)return t;for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);t+=r>=0&&r<=128?1:2}return t},formatNum:function(e,t){for(var n=""+e,r=t-n.length,o=0;o<r;o++)n="0"+n;return n},getSubStr:function(e,t,n){var r=e.indexOf(t);if(-1===parseInt(r))return"";r+=t.length;var o=e.indexOf(n,r);return-1===parseInt(o)?"":e.substring(r,o)}},M=function(e){return e=o.merge({time:6e4,timeoutReturn:{overtime:!0}},e),new Promise((function(t,n){setTimeout((function(){t(e.timeoutReturn)}),e.time)}))},L=function(){var t=a(e().m((function t(n){var i,a,c;return e().w((function(e){for(;;)switch(e.n){case 0:if(n=o.merge({request:null,callback:null,time:6e4,timeoutReturn:{errcode:11100003,errmsg:""},retry:3,retryDelay:1e3},n),!o.isNil(n.callback)){e.n=1;break}return e.a(2,!1);case 1:i=function(){return Promise.race([new Promise(n.callback),M(n)])},c=0;case 2:if(!(c<n.retry)){e.n=8;break}return e.n=3,i();case 3:if(a=e.v,console.log("overtime:request:result",{result:a,nowTry:c}),!o.get(a,"overtime",!1)){e.n=6;break}if(console.error("overtime:request:fail"),console.error(JSON.stringify(r(r({},o.omit(n,["callback"])),{},{nowTry:c}))),c!==n.retry-1){e.n=4;break}a=n.timeoutReturn,e.n=5;break;case 4:return e.n=5,M({time:n.retryDelay});case 5:e.n=7;break;case 6:return e.a(3,8);case 7:c++,e.n=2;break;case 8:return e.a(2,a)}}),t)})));return function(e){return t.apply(this,arguments)}}(),$=TypeError,B=new Proxy({},{get:function(e,t){throw new Error('Module "" has been externalized for browser compatibility. Cannot access ".'.concat(t,'" in client code.'))}}),z=Object.freeze(Object.defineProperty({__proto__:null,default:B},Symbol.toStringTag,{value:"Module"})),G=i(z),Q="function"==typeof Map&&Map.prototype,q=Object.getOwnPropertyDescriptor&&Q?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,V=Q&&q&&"function"==typeof q.get?q.get:null,H=Q&&Map.prototype.forEach,K="function"==typeof Set&&Set.prototype,J=Object.getOwnPropertyDescriptor&&K?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Z=K&&J&&"function"==typeof J.get?J.get:null,X=K&&Set.prototype.forEach,Y="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,ee="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,te="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,ne=Boolean.prototype.valueOf,re=Object.prototype.toString,oe=Function.prototype.toString,ie=String.prototype.match,ae=String.prototype.slice,ce=String.prototype.replace,le=String.prototype.toUpperCase,ue=String.prototype.toLowerCase,se=RegExp.prototype.test,pe=Array.prototype.concat,fe=Array.prototype.join,de=Array.prototype.slice,ye=Math.floor,he="function"==typeof BigInt?BigInt.prototype.valueOf:null,ge=Object.getOwnPropertySymbols,me="function"==typeof Symbol&&"symbol"===p(Symbol.iterator)?Symbol.prototype.toString:null,be="function"==typeof Symbol&&"object"===p(Symbol.iterator),ve="function"==typeof Symbol&&Symbol.toStringTag&&(p(Symbol.toStringTag)===be||"symbol")?Symbol.toStringTag:null,we=Object.prototype.propertyIsEnumerable,Se=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function _e(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||se.call(/e/,t))return t;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var r=e<0?-ye(-e):ye(e);if(r!==e){var o=String(r),i=ae.call(t,o.length+1);return ce.call(o,n,"$&_")+"."+ce.call(ce.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return ce.call(t,n,"$&_")}var Pe=G,Ie=Pe.custom,Ae=De(Ie)?Ie:null,Ce={__proto__:null,double:'"',single:"'"},Oe={__proto__:null,double:/(["\\])/g,single:/(['\\])/g},xe=function e(t,n,r,o){var i=n||{};if(Ne(i,"quoteStyle")&&!Ne(Ce,i.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Ne(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var a=!Ne(i,"customInspect")||i.customInspect;if("boolean"!=typeof a&&"symbol"!==a)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Ne(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Ne(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var l=i.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Me(t,i);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var u=String(t);return l?_e(t,u):u}if("bigint"==typeof t){var s=String(t)+"n";return l?_e(t,s):s}var f=void 0===i.depth?5:i.depth;if(void 0===r&&(r=0),r>=f&&f>0&&"object"===p(t))return We(t)?"[Array]":"[Object]";var d=function(e,t){var n;if("\t"===e.indent)n="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;n=fe.call(Array(e.indent+1)," ")}return{base:n,prev:fe.call(Array(t+1),n)}}(i,r);if(void 0===o)o=[];else if(Fe(o,t)>=0)return"[Circular]";function y(t,n,a){if(n&&(o=de.call(o)).push(n),a){var c={depth:i.depth};return Ne(i,"quoteStyle")&&(c.quoteStyle=i.quoteStyle),e(t,c,r+1,o)}return e(t,i,r+1,o)}if("function"==typeof t&&!Te(t)){var h=function(e){if(e.name)return e.name;var t=ie.call(oe.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),g=Qe(t,y);return"[Function"+(h?": "+h:" (anonymous)")+"]"+(g.length>0?" { "+fe.call(g,", ")+" }":"")}if(De(t)){var m=be?ce.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):me.call(t);return"object"!==p(t)||be?m:$e(m)}if(function(e){if(!e||"object"!==p(e))return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var b="<"+ue.call(String(t.nodeName)),v=t.attributes||[],w=0;w<v.length;w++)b+=" "+v[w].name+"="+je(Ee(v[w].value),"double",i);return b+=">",t.childNodes&&t.childNodes.length&&(b+="..."),b+="</"+ue.call(String(t.nodeName))+">"}if(We(t)){if(0===t.length)return"[]";var S=Qe(t,y);return d&&!function(e){for(var t=0;t<e.length;t++)if(Fe(e[t],"\n")>=0)return!1;return!0}(S)?"["+Ge(S,d)+"]":"[ "+fe.call(S,", ")+" ]"}if(function(e){return"[object Error]"===Re(e)&&ke(e)}(t)){var _=Qe(t,y);return"cause"in Error.prototype||!("cause"in t)||we.call(t,"cause")?0===_.length?"["+String(t)+"]":"{ ["+String(t)+"] "+fe.call(_,", ")+" }":"{ ["+String(t)+"] "+fe.call(pe.call("[cause]: "+y(t.cause),_),", ")+" }"}if("object"===p(t)&&a){if(Ae&&"function"==typeof t[Ae]&&Pe)return Pe(t,{depth:f-r});if("symbol"!==a&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!V||!e||"object"!==p(e))return!1;try{V.call(e);try{Z.call(e)}catch(b){return!0}return e instanceof Map}catch(t){}return!1}(t)){var P=[];return H&&H.call(t,(function(e,n){P.push(y(n,t,!0)+" => "+y(e,t))})),ze("Map",V.call(t),P,d)}if(function(e){if(!Z||!e||"object"!==p(e))return!1;try{Z.call(e);try{V.call(e)}catch(t){return!0}return e instanceof Set}catch(n){}return!1}(t)){var I=[];return X&&X.call(t,(function(e){I.push(y(e,t))})),ze("Set",Z.call(t),I,d)}if(function(e){if(!Y||!e||"object"!==p(e))return!1;try{Y.call(e,Y);try{ee.call(e,ee)}catch(b){return!0}return e instanceof WeakMap}catch(t){}return!1}(t))return Be("WeakMap");if(function(e){if(!ee||!e||"object"!==p(e))return!1;try{ee.call(e,ee);try{Y.call(e,Y)}catch(b){return!0}return e instanceof WeakSet}catch(t){}return!1}(t))return Be("WeakSet");if(function(e){if(!te||!e||"object"!==p(e))return!1;try{return te.call(e),!0}catch(t){}return!1}(t))return Be("WeakRef");if(function(e){return"[object Number]"===Re(e)&&ke(e)}(t))return $e(y(Number(t)));if(function(e){if(!e||"object"!==p(e)||!he)return!1;try{return he.call(e),!0}catch(t){}return!1}(t))return $e(y(he.call(t)));if(function(e){return"[object Boolean]"===Re(e)&&ke(e)}(t))return $e(ne.call(t));if(function(e){return"[object String]"===Re(e)&&ke(e)}(t))return $e(y(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==c&&t===c)return"{ [object globalThis] }";if(!function(e){return"[object Date]"===Re(e)&&ke(e)}(t)&&!Te(t)){var A=Qe(t,y),C=Se?Se(t)===Object.prototype:t instanceof Object||t.constructor===Object,O=t instanceof Object?"":"null prototype",x=!C&&ve&&Object(t)===t&&ve in t?ae.call(Re(t),8,-1):O?"Object":"",j=(C||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(x||O?"["+fe.call(pe.call([],x||[],O||[]),": ")+"] ":"");return 0===A.length?j+"{}":d?j+"{"+Ge(A,d)+"}":j+"{ "+fe.call(A,", ")+" }"}return String(t)};function je(e,t,n){var r=n.quoteStyle||t,o=Ce[r];return o+e+o}function Ee(e){return ce.call(String(e),/"/g,"&quot;")}function ke(e){return!ve||!("object"===p(e)&&(ve in e||void 0!==e[ve]))}function We(e){return"[object Array]"===Re(e)&&ke(e)}function Te(e){return"[object RegExp]"===Re(e)&&ke(e)}function De(e){if(be)return e&&"object"===p(e)&&e instanceof Symbol;if("symbol"===p(e))return!0;if(!e||"object"!==p(e)||!me)return!1;try{return me.call(e),!0}catch(t){}return!1}var Ue=Object.prototype.hasOwnProperty||function(e){return e in this};function Ne(e,t){return Ue.call(e,t)}function Re(e){return re.call(e)}function Fe(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}function Me(e,t){if(e.length>t.maxStringLength){var n=e.length-t.maxStringLength,r="... "+n+" more character"+(n>1?"s":"");return Me(ae.call(e,0,t.maxStringLength),t)+r}var o=Oe[t.quoteStyle||"single"];return o.lastIndex=0,je(ce.call(ce.call(e,o,"\\$1"),/[\x00-\x1f]/g,Le),"single",t)}function Le(e){var t=e.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return n?"\\"+n:"\\x"+(t<16?"0":"")+le.call(t.toString(16))}function $e(e){return"Object("+e+")"}function Be(e){return e+" { ? }"}function ze(e,t,n,r){return e+" ("+t+") {"+(r?Ge(n,r):fe.call(n,", "))+"}"}function Ge(e,t){if(0===e.length)return"";var n="\n"+t.prev+t.base;return n+fe.call(e,","+n)+"\n"+t.prev}function Qe(e,t){var n=We(e),r=[];if(n){r.length=e.length;for(var o=0;o<e.length;o++)r[o]=Ne(e,o)?t(e[o],e):""}var i,a="function"==typeof ge?ge(e):[];if(be){i={};for(var c=0;c<a.length;c++)i["$"+a[c]]=a[c]}for(var l in e)Ne(e,l)&&(n&&String(Number(l))===l&&l<e.length||be&&i["$"+l]instanceof Symbol||(se.call(/[^\w$]/,l)?r.push(t(l,e)+": "+t(e[l],e)):r.push(l+": "+t(e[l],e))));if("function"==typeof ge)for(var u=0;u<a.length;u++)we.call(e,a[u])&&r.push("["+t(a[u])+"]: "+t(e[a[u]],e));return r}var qe=xe,Ve=$,He=function(e,t,n){for(var r,o=e;null!=(r=o.next);o=r)if(r.key===t)return o.next=r.next,n||(r.next=e.next,e.next=r),r},Ke=Object,Je=Error,Ze=EvalError,Xe=RangeError,Ye=ReferenceError,et=SyntaxError,tt=URIError,nt=Math.abs,rt=Math.floor,ot=Math.max,it=Math.min,at=Math.pow,ct=Math.round,lt=Number.isNaN||function(e){return e!=e},ut=Object.getOwnPropertyDescriptor;if(ut)try{ut([],"length")}catch(no){ut=null}var st=ut,pt=Object.defineProperty||!1;if(pt)try{pt({},"a",{value:1})}catch(no){pt=!1}var ft,dt,yt,ht,gt,mt,bt,vt,wt=pt;function St(){return mt?gt:(mt=1,gt="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function _t(){return vt?bt:(vt=1,bt=Ke.getPrototypeOf||null)}var Pt,It,At=Object.prototype.toString,Ct=Math.max,Ot=function(e,t){for(var n=[],r=0;r<e.length;r+=1)n[r]=e[r];for(var o=0;o<t.length;o+=1)n[o+e.length]=t[o];return n},xt=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==At.apply(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var n,r=function(e,t){for(var n=[],r=t||0,o=0;r<e.length;r+=1,o+=1)n[o]=e[r];return n}(arguments,1),o=Ct(0,t.length-r.length),i=[],a=0;a<o;a++)i[a]="$"+a;if(n=Function("binder","return function ("+function(e,t){for(var n="",r=0;r<e.length;r+=1)n+=e[r],r+1<e.length&&(n+=t);return n}(i,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof n){var o=t.apply(this,Ot(r,arguments));return Object(o)===o?o:this}return t.apply(e,Ot(r,arguments))})),t.prototype){var c=function(){};c.prototype=t.prototype,n.prototype=new c,c.prototype=null}return n},jt=Function.prototype.bind||xt,Et=Function.prototype.call;function kt(){return It?Pt:(It=1,Pt=Function.prototype.apply)}var Wt,Tt,Dt,Ut,Nt,Rt,Ft,Mt="undefined"!=typeof Reflect&&Reflect&&Reflect.apply,Lt=jt,$t=kt(),Bt=Et,zt=Mt||Lt.call(Bt,$t),Gt=jt,Qt=$,qt=Et,Vt=zt,Ht=function(e){if(e.length<1||"function"!=typeof e[0])throw new Qt("a function is required");return Vt(Gt,qt,e)};var Kt=Ke,Jt=Je,Zt=Ze,Xt=Xe,Yt=Ye,en=et,tn=$,nn=tt,rn=nt,on=rt,an=ot,cn=it,ln=at,un=ct,sn=function(e){return lt(e)||0===e?e:e<0?-1:1},pn=Function,fn=function(e){try{return pn('"use strict"; return ('+e+").constructor;")()}catch(no){}},dn=st,yn=wt,hn=function(){throw new tn},gn=dn?function(){try{return hn}catch(e){try{return dn(arguments,"callee").get}catch(t){return hn}}}():hn,mn=function(){if(ht)return yt;ht=1;var e="undefined"!=typeof Symbol&&Symbol,t=dt?ft:(dt=1,ft=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"===p(Symbol.iterator))return!0;var e={},t=Symbol("test"),n=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(var r in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0});return yt=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"===p(e("foo"))&&("symbol"===p(Symbol("bar"))&&t())))}}()(),bn=function(){if(Ut)return Dt;Ut=1;var e=St(),t=_t(),n=function(){if(Tt)return Wt;Tt=1;var e,t=Ht,n=st;try{e=[].__proto__===Array.prototype}catch(no){if(!no||"object"!==p(no)||!("code"in no)||"ERR_PROTO_ACCESS"!==no.code)throw no}var r=!!e&&n&&n(Object.prototype,"__proto__"),o=Object,i=o.getPrototypeOf;return Wt=r&&"function"==typeof r.get?t([r.get]):"function"==typeof i&&function(e){return i(null==e?e:o(e))}}();return Dt=e?function(t){return e(t)}:t?function(e){if(!e||"object"!==p(e)&&"function"!=typeof e)throw new TypeError("getProto: not an object");return t(e)}:n?function(e){return n(e)}:null}(),vn=_t(),wn=St(),Sn=kt(),_n=Et,Pn={},In="undefined"!=typeof Uint8Array&&bn?bn(Uint8Array):Ft,An={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?Ft:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?Ft:ArrayBuffer,"%ArrayIteratorPrototype%":mn&&bn?bn([][Symbol.iterator]()):Ft,"%AsyncFromSyncIteratorPrototype%":Ft,"%AsyncFunction%":Pn,"%AsyncGenerator%":Pn,"%AsyncGeneratorFunction%":Pn,"%AsyncIteratorPrototype%":Pn,"%Atomics%":"undefined"==typeof Atomics?Ft:Atomics,"%BigInt%":"undefined"==typeof BigInt?Ft:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?Ft:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?Ft:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?Ft:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Jt,"%eval%":eval,"%EvalError%":Zt,"%Float16Array%":"undefined"==typeof Float16Array?Ft:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?Ft:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?Ft:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?Ft:FinalizationRegistry,"%Function%":pn,"%GeneratorFunction%":Pn,"%Int8Array%":"undefined"==typeof Int8Array?Ft:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?Ft:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?Ft:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":mn&&bn?bn(bn([][Symbol.iterator]())):Ft,"%JSON%":"object"===("undefined"==typeof JSON?"undefined":p(JSON))?JSON:Ft,"%Map%":"undefined"==typeof Map?Ft:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&mn&&bn?bn((new Map)[Symbol.iterator]()):Ft,"%Math%":Math,"%Number%":Number,"%Object%":Kt,"%Object.getOwnPropertyDescriptor%":dn,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?Ft:Promise,"%Proxy%":"undefined"==typeof Proxy?Ft:Proxy,"%RangeError%":Xt,"%ReferenceError%":Yt,"%Reflect%":"undefined"==typeof Reflect?Ft:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?Ft:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&mn&&bn?bn((new Set)[Symbol.iterator]()):Ft,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?Ft:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":mn&&bn?bn(""[Symbol.iterator]()):Ft,"%Symbol%":mn?Symbol:Ft,"%SyntaxError%":en,"%ThrowTypeError%":gn,"%TypedArray%":In,"%TypeError%":tn,"%Uint8Array%":"undefined"==typeof Uint8Array?Ft:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?Ft:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?Ft:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?Ft:Uint32Array,"%URIError%":nn,"%WeakMap%":"undefined"==typeof WeakMap?Ft:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?Ft:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?Ft:WeakSet,"%Function.prototype.call%":_n,"%Function.prototype.apply%":Sn,"%Object.defineProperty%":yn,"%Object.getPrototypeOf%":vn,"%Math.abs%":rn,"%Math.floor%":on,"%Math.max%":an,"%Math.min%":cn,"%Math.pow%":ln,"%Math.round%":un,"%Math.sign%":sn,"%Reflect.getPrototypeOf%":wn};if(bn)try{null.error}catch(no){var Cn=bn(bn(no));An["%Error.prototype%"]=Cn}var On=function e(t){var n;if("%AsyncFunction%"===t)n=fn("async function () {}");else if("%GeneratorFunction%"===t)n=fn("function* () {}");else if("%AsyncGeneratorFunction%"===t)n=fn("async function* () {}");else if("%AsyncGenerator%"===t){var r=e("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&bn&&(n=bn(o.prototype))}return An[t]=n,n},xn={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},jn=jt,En=function(){if(Rt)return Nt;Rt=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty;return Nt=jt.call(e,t)}(),kn=jn.call(_n,Array.prototype.concat),Wn=jn.call(Sn,Array.prototype.splice),Tn=jn.call(_n,String.prototype.replace),Dn=jn.call(_n,String.prototype.slice),Un=jn.call(_n,RegExp.prototype.exec),Nn=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Rn=/\\(\\)?/g,Fn=function(e,t){var n,r=e;if(En(xn,r)&&(r="%"+(n=xn[r])[0]+"%"),En(An,r)){var o=An[r];if(o===Pn&&(o=On(r)),void 0===o&&!t)throw new tn("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new en("intrinsic "+e+" does not exist!")},Mn=function(e,t){if("string"!=typeof e||0===e.length)throw new tn("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new tn('"allowMissing" argument must be a boolean');if(null===Un(/^%?[^%]*%?$/,e))throw new en("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=function(e){var t=Dn(e,0,1),n=Dn(e,-1);if("%"===t&&"%"!==n)throw new en("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==t)throw new en("invalid intrinsic syntax, expected opening `%`");var r=[];return Tn(e,Nn,(function(e,t,n,o){r[r.length]=n?Tn(o,Rn,"$1"):t||e})),r}(e),r=n.length>0?n[0]:"",o=Fn("%"+r+"%",t),i=o.name,a=o.value,c=!1,l=o.alias;l&&(r=l[0],Wn(n,kn([0,1],l)));for(var u=1,s=!0;u<n.length;u+=1){var p=n[u],f=Dn(p,0,1),d=Dn(p,-1);if(('"'===f||"'"===f||"`"===f||'"'===d||"'"===d||"`"===d)&&f!==d)throw new en("property names with quotes must have matching quotes");if("constructor"!==p&&s||(c=!0),En(An,i="%"+(r+="."+p)+"%"))a=An[i];else if(null!=a){if(!(p in a)){if(!t)throw new tn("base intrinsic for "+e+" exists, but the property is not available.");return}if(dn&&u+1>=n.length){var y=dn(a,p);a=(s=!!y)&&"get"in y&&!("originalValue"in y.get)?y.get:a[p]}else s=En(a,p),a=a[p];s&&!c&&(An[i]=a)}}return a},Ln=Mn,$n=Ht,Bn=$n([Ln("%String.prototype.indexOf%")]),zn=function(e,t){var n=Ln(e,!!t);return"function"==typeof n&&Bn(e,".prototype.")>-1?$n([n]):n},Gn=zn,Qn=xe,qn=$,Vn=Mn("%Map%",!0),Hn=Gn("Map.prototype.get",!0),Kn=Gn("Map.prototype.set",!0),Jn=Gn("Map.prototype.has",!0),Zn=Gn("Map.prototype.delete",!0),Xn=Gn("Map.prototype.size",!0),Yn=!!Vn&&function(){var e,t={assert:function(e){if(!t.has(e))throw new qn("Side channel does not contain "+Qn(e))},delete:function(t){if(e){var n=Zn(e,t);return 0===Xn(e)&&(e=void 0),n}return!1},get:function(t){if(e)return Hn(e,t)},has:function(t){return!!e&&Jn(e,t)},set:function(t,n){e||(e=new Vn),Kn(e,t,n)}};return t},er=zn,tr=xe,nr=Yn,rr=$,or=Mn("%WeakMap%",!0),ir=er("WeakMap.prototype.get",!0),ar=er("WeakMap.prototype.set",!0),cr=er("WeakMap.prototype.has",!0),lr=er("WeakMap.prototype.delete",!0),ur=$,sr=xe,pr=(or?function(){var e,t,n={assert:function(e){if(!n.has(e))throw new rr("Side channel does not contain "+tr(e))},delete:function(n){if(or&&n&&("object"===p(n)||"function"==typeof n)){if(e)return lr(e,n)}else if(nr&&t)return t.delete(n);return!1},get:function(n){return or&&n&&("object"===p(n)||"function"==typeof n)&&e?ir(e,n):t&&t.get(n)},has:function(n){return or&&n&&("object"===p(n)||"function"==typeof n)&&e?cr(e,n):!!t&&t.has(n)},set:function(n,r){or&&n&&("object"===p(n)||"function"==typeof n)?(e||(e=new or),ar(e,n,r)):nr&&(t||(t=nr()),t.set(n,r))}};return n}:nr)||Yn||function(){var e,t={assert:function(e){if(!t.has(e))throw new Ve("Side channel does not contain "+qe(e))},delete:function(t){var n=e&&e.next,r=function(e,t){if(e)return He(e,t,!0)}(e,t);return r&&n&&n===r&&(e=void 0),!!r},get:function(t){return function(e,t){if(e){var n=He(e,t);return n&&n.value}}(e,t)},has:function(t){return function(e,t){return!!e&&!!He(e,t)}(e,t)},set:function(t,n){e||(e={next:void 0}),function(e,t,n){var r=He(e,t);r?r.value=n:e.next={key:t,next:e.next,value:n}}(e,t,n)}};return t},fr=String.prototype.replace,dr=/%20/g,yr="RFC3986",hr={default:yr,formatters:{RFC1738:function(e){return fr.call(e,dr,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:yr},gr=hr,mr=Object.prototype.hasOwnProperty,br=Array.isArray,vr=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),wr=function(e,t){for(var n=t&&t.plainObjects?{__proto__:null}:{},r=0;r<e.length;++r)void 0!==e[r]&&(n[r]=e[r]);return n},Sr=1024,_r={arrayToObject:wr,assign:function(e,t){return Object.keys(t).reduce((function(e,n){return e[n]=t[n],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],n=[],r=0;r<t.length;++r)for(var o=t[r],i=o.obj[o.prop],a=Object.keys(i),c=0;c<a.length;++c){var l=a[c],u=i[l];"object"===p(u)&&null!==u&&-1===n.indexOf(u)&&(t.push({obj:i,prop:l}),n.push(u))}return function(e){for(;e.length>1;){var t=e.pop(),n=t.obj[t.prop];if(br(n)){for(var r=[],o=0;o<n.length;++o)void 0!==n[o]&&r.push(n[o]);t.obj[t.prop]=r}}}(t),e},decode:function(e,t,n){var r=e.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(no){return r}},encode:function(e,t,n,r,o){if(0===e.length)return e;var i=e;if("symbol"===p(e)?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===n)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var a="",c=0;c<i.length;c+=Sr){for(var l=i.length>=Sr?i.slice(c,c+Sr):i,u=[],s=0;s<l.length;++s){var f=l.charCodeAt(s);45===f||46===f||95===f||126===f||f>=48&&f<=57||f>=65&&f<=90||f>=97&&f<=122||o===gr.RFC1738&&(40===f||41===f)?u[u.length]=l.charAt(s):f<128?u[u.length]=vr[f]:f<2048?u[u.length]=vr[192|f>>6]+vr[128|63&f]:f<55296||f>=57344?u[u.length]=vr[224|f>>12]+vr[128|f>>6&63]+vr[128|63&f]:(s+=1,f=65536+((1023&f)<<10|1023&l.charCodeAt(s)),u[u.length]=vr[240|f>>18]+vr[128|f>>12&63]+vr[128|f>>6&63]+vr[128|63&f])}a+=u.join("")}return a},isBuffer:function(e){return!(!e||"object"!==p(e))&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(br(e)){for(var n=[],r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)},merge:function e(t,n,r){if(!n)return t;if("object"!==p(n)&&"function"!=typeof n){if(br(t))t.push(n);else{if(!t||"object"!==p(t))return[t,n];(r&&(r.plainObjects||r.allowPrototypes)||!mr.call(Object.prototype,n))&&(t[n]=!0)}return t}if(!t||"object"!==p(t))return[t].concat(n);var o=t;return br(t)&&!br(n)&&(o=wr(t,r)),br(t)&&br(n)?(n.forEach((function(n,o){if(mr.call(t,o)){var i=t[o];i&&"object"===p(i)&&n&&"object"===p(n)?t[o]=e(i,n,r):t.push(n)}else t[o]=n})),t):Object.keys(n).reduce((function(t,o){var i=n[o];return mr.call(t,o)?t[o]=e(t[o],i,r):t[o]=i,t}),o)}},Pr=function(){var e,t={assert:function(e){if(!t.has(e))throw new ur("Side channel does not contain "+sr(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,n){e||(e=pr()),e.set(t,n)}};return t},Ir=_r,Ar=hr,Cr=Object.prototype.hasOwnProperty,Or={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},xr=Array.isArray,jr=Array.prototype.push,Er=function(e,t){jr.apply(e,xr(t)?t:[t])},kr=Date.prototype.toISOString,Wr=Ar.default,Tr={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Ir.encode,encodeValuesOnly:!1,filter:void 0,format:Wr,formatter:Ar.formatters[Wr],indices:!1,serializeDate:function(e){return kr.call(e)},skipNulls:!1,strictNullHandling:!1},Dr={},Ur=function e(t,n,r,o,i,a,c,l,u,s,f,d,y,h,g,m,b,v){for(var w,S=t,_=v,P=0,I=!1;void 0!==(_=_.get(Dr))&&!I;){var A=_.get(t);if(P+=1,void 0!==A){if(A===P)throw new RangeError("Cyclic object value");I=!0}void 0===_.get(Dr)&&(P=0)}if("function"==typeof s?S=s(n,S):S instanceof Date?S=y(S):"comma"===r&&xr(S)&&(S=Ir.maybeMap(S,(function(e){return e instanceof Date?y(e):e}))),null===S){if(a)return u&&!m?u(n,Tr.encoder,b,"key",h):n;S=""}if("string"==typeof(w=S)||"number"==typeof w||"boolean"==typeof w||"symbol"===p(w)||"bigint"==typeof w||Ir.isBuffer(S))return u?[g(m?n:u(n,Tr.encoder,b,"key",h))+"="+g(u(S,Tr.encoder,b,"value",h))]:[g(n)+"="+g(String(S))];var C,O=[];if(void 0===S)return O;if("comma"===r&&xr(S))m&&u&&(S=Ir.maybeMap(S,u)),C=[{value:S.length>0?S.join(",")||null:void 0}];else if(xr(s))C=s;else{var x=Object.keys(S);C=f?x.sort(f):x}var j=l?String(n).replace(/\./g,"%2E"):String(n),E=o&&xr(S)&&1===S.length?j+"[]":j;if(i&&xr(S)&&0===S.length)return E+"[]";for(var k=0;k<C.length;++k){var W=C[k],T="object"===p(W)&&W&&void 0!==W.value?W.value:S[W];if(!c||null!==T){var D=d&&l?String(W).replace(/\./g,"%2E"):String(W),U=xr(S)?"function"==typeof r?r(E,D):E:E+(d?"."+D:"["+D+"]");v.set(t,P);var N=Pr();N.set(Dr,v),Er(O,e(T,U,r,o,i,a,c,l,"comma"===r&&m&&xr(S)?null:u,s,f,d,y,h,g,m,b,N))}}return O},Nr=_r,Rr=Object.prototype.hasOwnProperty,Fr=Array.isArray,Mr={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:Nr.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},Lr=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},$r=function(e,t,n){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&n>=t.arrayLimit)throw new RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},Br=function(e,t,n,r){if(e){var o=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,a=n.depth>0&&/(\[[^[\]]*])/.exec(o),c=a?o.slice(0,a.index):o,l=[];if(c){if(!n.plainObjects&&Rr.call(Object.prototype,c)&&!n.allowPrototypes)return;l.push(c)}for(var u=0;n.depth>0&&null!==(a=i.exec(o))&&u<n.depth;){if(u+=1,!n.plainObjects&&Rr.call(Object.prototype,a[1].slice(1,-1))&&!n.allowPrototypes)return;l.push(a[1])}if(a){if(!0===n.strictDepth)throw new RangeError("Input depth exceeded depth option of "+n.depth+" and strictDepth is true");l.push("["+o.slice(a.index)+"]")}return function(e,t,n,r){var o=0;if(e.length>0&&"[]"===e[e.length-1]){var i=e.slice(0,-1).join("");o=Array.isArray(t)&&t[i]?t[i].length:0}for(var a=r?t:$r(t,n,o),c=e.length-1;c>=0;--c){var l,u=e[c];if("[]"===u&&n.parseArrays)l=n.allowEmptyArrays&&(""===a||n.strictNullHandling&&null===a)?[]:Nr.combine([],a);else{l=n.plainObjects?{__proto__:null}:{};var s="["===u.charAt(0)&&"]"===u.charAt(u.length-1)?u.slice(1,-1):u,p=n.decodeDotInKeys?s.replace(/%2E/g,"."):s,f=parseInt(p,10);n.parseArrays||""!==p?!isNaN(f)&&u!==p&&String(f)===p&&f>=0&&n.parseArrays&&f<=n.arrayLimit?(l=[])[f]=a:"__proto__"!==p&&(l[p]=a):l={0:a}}a=l}return a}(l,t,n,r)}},zr=function(e,t){var n,r=e,o=function(e){if(!e)return Tr;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||Tr.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=Ar.default;if(void 0!==e.format){if(!Cr.call(Ar.formatters,e.format))throw new TypeError("Unknown format option provided.");n=e.format}var r,o=Ar.formatters[n],i=Tr.filter;if(("function"==typeof e.filter||xr(e.filter))&&(i=e.filter),r=e.arrayFormat in Or?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":Tr.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a=void 0===e.allowDots?!0===e.encodeDotInKeys||Tr.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:Tr.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:Tr.allowEmptyArrays,arrayFormat:r,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:Tr.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?Tr.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:Tr.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:Tr.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:Tr.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:Tr.encodeValuesOnly,filter:i,format:n,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:Tr.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:Tr.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:Tr.strictNullHandling}}(t);"function"==typeof o.filter?r=(0,o.filter)("",r):xr(o.filter)&&(n=o.filter);var i=[];if("object"!==p(r)||null===r)return"";var a=Or[o.arrayFormat],c="comma"===a&&o.commaRoundTrip;n||(n=Object.keys(r)),o.sort&&n.sort(o.sort);for(var l=Pr(),u=0;u<n.length;++u){var s=n[u],f=r[s];o.skipNulls&&null===f||Er(i,Ur(f,s,a,c,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,l))}var d=i.join(o.delimiter),y=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),d.length>0?y+d:""},Gr=function(e,t){var n=function(e){if(!e)return Mr;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?Mr.charset:e.charset,n=void 0===e.duplicates?Mr.duplicates:e.duplicates;if("combine"!==n&&"first"!==n&&"last"!==n)throw new TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||Mr.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:Mr.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:Mr.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:Mr.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:Mr.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:Mr.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:Mr.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:Mr.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:Mr.decoder,delimiter:"string"==typeof e.delimiter||Nr.isRegExp(e.delimiter)?e.delimiter:Mr.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:Mr.depth,duplicates:n,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:Mr.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:Mr.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:Mr.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:Mr.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:Mr.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}}(t);if(""===e||null==e)return n.plainObjects?{__proto__:null}:{};for(var r="string"==typeof e?function(e,t){var n={__proto__:null},r=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;r=r.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var o=t.parameterLimit===1/0?void 0:t.parameterLimit,i=r.split(t.delimiter,t.throwOnLimitExceeded?o+1:o);if(t.throwOnLimitExceeded&&i.length>o)throw new RangeError("Parameter limit exceeded. Only "+o+" parameter"+(1===o?"":"s")+" allowed.");var a,c=-1,l=t.charset;if(t.charsetSentinel)for(a=0;a<i.length;++a)0===i[a].indexOf("utf8=")&&("utf8=%E2%9C%93"===i[a]?l="utf-8":"utf8=%26%2310003%3B"===i[a]&&(l="iso-8859-1"),c=a,a=i.length);for(a=0;a<i.length;++a)if(a!==c){var u,s,p=i[a],f=p.indexOf("]="),d=-1===f?p.indexOf("="):f+1;-1===d?(u=t.decoder(p,Mr.decoder,l,"key"),s=t.strictNullHandling?null:""):(u=t.decoder(p.slice(0,d),Mr.decoder,l,"key"),s=Nr.maybeMap($r(p.slice(d+1),t,Fr(n[u])?n[u].length:0),(function(e){return t.decoder(e,Mr.decoder,l,"value")}))),s&&t.interpretNumericEntities&&"iso-8859-1"===l&&(s=Lr(String(s))),p.indexOf("[]=")>-1&&(s=Fr(s)?[s]:s);var y=Rr.call(n,u);y&&"combine"===t.duplicates?n[u]=Nr.combine(n[u],s):y&&"last"!==t.duplicates||(n[u]=s)}return n}(e,n):e,o=n.plainObjects?{__proto__:null}:{},i=Object.keys(r),a=0;a<i.length;++a){var c=i[a],l=Br(c,r[c],n,"string"==typeof e);o=Nr.merge(o,l,n)}return!0===n.allowSparse?o:Nr.compact(o)},Qr={formats:hr,parse:Gr,stringify:zr},qr=function(){return l((function e(t){return s(this,e),this.responseEvent="ResponseToWeb",this.callbackList={},this.qtObject=null,this.processId=0,this.initProcessId(),this.initIpcInstance()}),[{key:"initProcessId",value:function(){var e=Qr.parse(location.search.substring(1));this.processId=o.get(e,"ProcessId",0)}},{key:"initIpcInstance",value:(t=a(e().m((function t(){var n=this;return e().w((function(e){for(;;)if(0===e.n)return N()?this.ipcInstance=new R((function(e){n.addResponseListener(e,n.responseEvent)})):this.ipcInstance=null,e.a(2,this)}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"send",value:function(e,t,n,r){var i=this,a={},c=function(o,c){if(r.isNeedId){n.id=F.getStrSerial(i.processId);var l=N()?(new Error).stack.split("\n"):[],u={resolve:o,reject:c,request:{module:e,action:t,request:n,startTime:(new Date).getTime()},stackTrace:l};i.callbackList[n.id]=u}try{a=F.interceptors(n,t)}catch(s){throw console.log(s),new Error("参数转换错误")}i.ipcInstance.send({module:e,action:t,strSerial:r.isNeedId?n.id:-1,data:a,resolve:o,reject:c})};if(o.isSafeInteger(o.get(r,"timeout.time"))){var l=o.merge({callback:c,request:{module:e,action:t,data:a}},r.timeout);c=L(l)}else c=new Promise(c);return c}},{key:"on",value:function(e,t,n){this.ipcInstance.on(e,t,n)}},{key:"off",value:function(e,t,n){this.ipcInstance.off(e,t,n)}},{key:"addResponseListener",value:function(e,t){var n=this,r=function(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{var r={};if(o.isNil(t)||o.isEmpty(t)||(r=o.isString(t)?JSON.parse(t):t),o.isUndefined(e)&&o.isEmpty(e))throw new Error("serial 为空或者未定义");var i=n.callbackList[e];o.isUndefined(i)||(i.resolve(r.result),i.request.response=r.result||{},i.request.endTime=(new Date).getTime()),delete n.callbackList[e]}catch(no){console.error("小助手返回错误="),console.error(no)}};o.isObject(e)&&Object.keys(e).forEach((function(e){n.ipcInstance.on(e,t,r)}))}}]);var t}(),Vr=function(e){return(new qr).then((function(e){var t={$ipcSend:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(o.isNil(t)||o.isNil(n)||o.isEmpty(t)||o.isEmpty(n))throw new Error("module或action不能为空");if(r&&!o.isObject(r))throw new Error("params必须为object类型");return i=o.merge({isNeedId:!0,timeout:{time:!1}},i),e.send(t,n,r,i)},$ipcOn:function(t,n,r){e.on(t,n,r)},$ipcOff:function(t,n,r){e.off(t,n,r)},$processId:e.processId};return t}))},Hr={ipcClient:null,_initPromise:null,isQtEnvironment:function(){return"undefined"!=typeof window&&(window.qt||window.QWebChannel||/QtWebEngine/.test(navigator.userAgent))},init:function(){var t=this;return a(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:if(!t._initPromise){e.n=1;break}return e.a(2,t._initPromise);case 1:return t._initPromise=t._doInit(),e.a(2,t._initPromise)}}),n)})))()},_doInit:function(){var t=this;return a(e().m((function n(){var r;return e().w((function(e){for(;;)switch(e.n){case 0:if(t.ipcClient){e.n=6;break}if(e.p=1,!t.isQtEnvironment()){e.n=3;break}return e.n=2,Vr();case 2:t.ipcClient=e.v,console.log("IPC 初始化成功"),e.n=4;break;case 3:console.warn("非 QT 环境，使用模拟 IPC 客户端"),t.ipcClient=t._createMockIpcClient();case 4:e.n=6;break;case 5:e.p=5,r=e.v,console.error("IPC 初始化失败:",r),t.ipcClient=t._createMockIpcClient();case 6:return e.a(2,t.ipcClient)}}),n,null,[[1,5]])})))()},_createMockIpcClient:function(){return{$ipcSend:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return console.warn("模拟 IPC 调用: ".concat(e,".").concat(t),n),Promise.reject(new Error("IPC not available in current environment (".concat(e,".").concat(t,")")))},$ipcOn:function(e,t,n){console.warn("模拟 IPC 监听: ".concat(e,".").concat(t))},$ipcOff:function(e,t,n){console.warn("模拟 IPC 取消监听: ".concat(e,".").concat(t))},$processId:0}},getClientBaseInfo:function(){var t=arguments,n=this;return a(e().m((function r(){var o;return e().w((function(e){for(;;)switch(e.n){case 0:return o=t.length>0&&void 0!==t[0]&&t[0],e.n=1,n.init();case 1:return e.a(2,n.ipcClient.$ipcSend("AssUIPluginBase","WebCall_GetClientBaseInfo",{},{timeout:{time:o}}))}}),r)})))()},updateClientBaseInfo:function(){var t=this;return a(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t.init();case 1:return e.a(2,t.ipcClient.$ipcSend("AssUIPluginBase","WebCall_UpdateAgentInfo"))}}),n)})))()},getNetworkList:function(t){var n=this;return a(e().m((function r(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,n.init();case 1:return e.a(2,n.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_GetNetworkList",t))}}),r)})))()},switchAgentMode:function(t){var n=this;return a(e().m((function r(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,n.init();case 1:return e.a(2,n.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_SwitchAgentMode",t))}}),r)})))()},AuthEnd:function(t){var n=this;return a(e().m((function r(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,n.init();case 1:return e.a(2,n.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_AuthEnd",t))}}),r)})))()},getSecurityCheckPolicy:function(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetSecurityCheckPolicy",e)},callAgentOneFunc:function(e){var t=r({WhatToDo:"CallOneFunc"},e);return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_CallOneFunc",t)},getSecurityCheckItem:function(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_DoSecurityCheckItem",e)},securityCheckEnd:function(t){var n=this;return a(e().m((function r(){return e().w((function(e){for(;;)if(0===e.n)return e.a(2,n.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_DoSecurityCheckEnd",t))}),r)})))()},getNeetInstallPatchList:function(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetNeetInstallPatchList")},repairSecCheckItem:function(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_RepairSecCheckItem",e)},getAuthBaseInfo:function(){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_GetAuthBaseInfo")},dot1xAuth:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_Dot1xAuth",e,{timeout:{time:t}})},setUIContext:function(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_SetUIContext",e)},getAllUsbKeyCert:function(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_GetAllUsbKeyCert",e)},toRequestWeb:function(){var t=arguments,n=this;return a(e().m((function r(){var i;return e().w((function(e){for(;;)switch(e.n){case 0:return i=t.length>1?t[1]:void 0,e.n=1,n.init();case 1:return e.a(2,n.ipcClient.$ipcOn("UIPlatform_Window","ToRequestWeb",(function(e,t,n){if(o.isEmpty(n))n={};else try{n=o.isObject(n)?n:JSON.parse(n)}catch(no){console.error(no),n={}}i(t,n)})))}}),r)})))()},requestRemoteScreen:function(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_RequestRemoteScreen")},checkRemoteCtrlState:function(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_CheckRemoteCtrlState")},debugOut:function(e){return this.ipcClient.$ipcSend("UIPlatform_Window","DebugOut",{strInfo:e},{isNeedId:!1})},fileTools:{config:{asm_root:"",dll:"MsacFileApi.dll"},ActionLocalFile:function(t,n,r){var o=this;return a(e().m((function i(){var a,c;return e().w((function(e){for(;;)switch(e.n){case 0:if(n=n||"save",t){e.n=1;break}return e.a(2,!1);case 1:return e.n=2,o.GetAsmDir();case 2:a=e.v,c=n,e.n="save"===c?3:"del"===c?4:"read"===c?5:6;break;case 3:return e.a(2,o.SaveStrToFile(a+t,r));case 4:return e.a(2,o.DeleteOneFile(a+t));case 5:return e.a(2,o.ReadStrFromFile(a+t));case 6:console.info("ActionLocalFile Type 错误！");case 7:return e.a(2)}}),i)})))()},GetAsmDir:function(){var t=this;return a(e().m((function n(){var r,i;return e().w((function(e){for(;;)switch(e.n){case 0:if(""!==t.config.asm_root){e.n=2;break}return r={WhereIsModule:t.config.dll,WhatFuncToCall:"GetDir",RequestParam:"ASM"},e.n=1,Hr.callAgentOneFunc(r);case 1:i=e.v,o.isEmpty(i)||(t.config.asm_root=i);case 2:return e.a(2,t.config.asm_root)}}),n)})))()},ReadStrFromFile:function(t){var n=this;return a(e().m((function r(){var i,a;return e().w((function(e){for(;;)switch(e.n){case 0:return t=t.replace(/\s/g,""),i={WhereIsModule:n.config.dll,WhatFuncToCall:"ReadStrFromFile",RequestParam:t},e.n=1,Hr.callAgentOneFunc(i);case 1:if(a=e.v,o.isEmpty(a)){e.n=2;break}return e.a(2,a);case 2:return e.a(2,"")}}),r)})))()},GetFileList:function(t){var n=this;return a(e().m((function r(){var i,a;return e().w((function(e){for(;;)switch(e.n){case 0:return i={WhereIsModule:n.config.dll,WhatFuncToCall:"GetFileList",RequestParam:t},e.n=1,Hr.callAgentOneFunc(i);case 1:if(a=e.v,o.isEmpty(a)){e.n=2;break}return e.a(2,a);case 2:return e.a(2,"")}}),r)})))()},DeleteOneFile:function(t){var n=this;return a(e().m((function r(){var i,a;return e().w((function(e){for(;;)switch(e.n){case 0:return i={WhereIsModule:n.config.dll,WhatFuncToCall:"DeleteOneFile",RequestParam:t},e.n=1,Hr.callAgentOneFunc(i);case 1:if(a=e.v,o.isEmpty(a)){e.n=2;break}return e.a(2,a);case 2:return e.a(2,"")}}),r)})))()},DeleteOneFileNever:function(t){var n=this;return a(e().m((function r(){var o,i;return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,n.GetAsmDir();case 1:return o=e.v,i={WhereIsModule:n.config.dll,WhatFuncToCall:"DeleteOneFile",RequestParam:o+t},e.n=2,Hr.callAgentOneFunc(i);case 2:return e.a(2)}}),r)})))()},SaveStrToFile:function(t,n){var r=this;return a(e().m((function o(){var i;return e().w((function(e){for(;;)switch(e.n){case 0:return t=t.replace(/\s/g,""),n=t+"|"+n,i={WhereIsModule:r.config.dll,WhatFuncToCall:"SaveStrToFile",RequestParam:n},e.n=1,Hr.callAgentOneFunc(i);case 1:return e.a(2)}}),o)})))()}},getLocalLangue:function(){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_IsiGetLangue")},setLocalLangue:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_IsiSetLangue",e)},logOut:function(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_AssUserLogout",e)},windowOpenUrl:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;-1===(e=e.replace(/\s/g,"")).indexOf("://")&&(e="http://"+e);var n={File:e,Params:"",ShowType:t,NeedWait:0};return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_AssShellOpen",n)},getSecCheckResult:function(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetPersistantSecCheckResult")},setNetIsolate:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_NetIsolate",e)},getNetIsolate:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_GetPolicy",e)},SetTitleDimension:function(e){return this.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",e)},isDomainNoFake:function(){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_IsDomainNoFake")},getThirdLinkageMenu:function(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_GetThirdLinkageMenu")},operateThirdLinkageMenu:function(e){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_OperateThirdLinkageMenu",e)},spaToAuth:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SPAToAuthServer",e)},setLoginRet:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SetLoginRet",e)},showWnd:function(e){return this.ipcClient.$ipcSend("UIPlatform_Window","ShowWnd",e)},getGatewayInfos:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_GetGatewayInfos",e)},spaChangeNet:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SwitchToInAndEx",e)},setDevAccessInfo:function(e){return this.ipcClient.$ipcSend("AssUIPluginAuth","WebCall_SetDevAccessInfo",e)},openRDC:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_OpenRDC",e)},getInstalledPatchList:function(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetInstalledPatchList")},getPatchRepairDetailInfo:function(){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_GetPatchRepairDetailInfo")},doRepairPatch:function(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_DoRepairPatch",e)},terminateWnd:function(){return this.ipcClient.$ipcSend("UIPlatform_Window","TerminateWnd")},normalnizeWnd:function(){return this.ipcClient.$ipcSend("UIPlatform_Window","NormalnizeWnd")},maximizeWnd:function(){var t=this;return a(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t.init();case 1:return e.a(2,t.ipcClient.$ipcSend("UIPlatform_Window","MaximizeWnd"))}}),n)})))()},minimizeWnd:function(){var t=this;return a(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t.init();case 1:return e.a(2,t.ipcClient.$ipcSend("UIPlatform_Window","MinimizeWnd"))}}),n)})))()},hideWend:function(){var t=this;return a(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,t.init();case 1:return e.a(2,t.ipcClient.$ipcSend("UIPlatform_Window","HideWnd"))}}),n)})))()},notifyAssUIExcuteResult:function(e){return this.ipcClient.$ipcSend("AssUIPluginSecurityCheck","WebCall_NotifyAssUIExcuteResult",e)},windowCustomAppOpenUrl:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_AssShellOpen",r(r({},{ShowType:0,NeedWait:0,ResType:1}),e))},refreshDeviceStatus:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_RefreshDeviceStatus",e)},changeNetMode:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_ChangeNetMode",e)},refreshVPNPolicy:function(){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_RefreshVPNPolicy",{})},checkSdcInstall:function(e){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_CheckSDCClientInstalled")},openSdcApp:function(e){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_OperateSDCLinkage",e)},getFreeDriverLetter:function(){return this.ipcClient.$ipcSend("AssUIPluginTools","WebCall_GetFreeDriverLetter")},windowFileServeOpenUrl:function(e){return this.ipcClient.$ipcSend("AssUIPluginBase","WebCall_AssShellOpen",e)},openDevTools:function(){window.eleIPC.send("openDevTools")},evokeAndFocus:function(){window.eleIPC.send("evokeAndFocus")},elelWriteLog:function(e){isElectron()&&window.eleIPC.send("log",e)},notifyResGroup:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_NotifyResGroup",e)},setVPNStatus:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SetVPNStatus",e)},setVPNInterceptMode:function(e){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_SetVPNInterceptMode",e)},queryVPNInfo:function(){return this.ipcClient.$ipcSend("AssUIPluginZTPModule","WebCall_QueryVPNInfo")},elelWindowIsVisible:function(){return isElectron()?window.eleIPC.invoke("isVisible"):""}},Kr=""+new URL("avator.bd83723a.png",n.meta.url).href,Jr={name:"ClientHeader",data:function(){return{drawer:!1,direction:"rtl",Qty:0,drawerTitle:"",username:"duanyc",countCommand:"changePassword",drawerSize:424,showLogout:!1,logoutMsg:"",logoutType:1,isMaxWindow:!1,deviceInnerBack:!1,avator:"",showSwitch:!1,IsolateType:"",IsOpenIsolate:0,drawerData:{},showIpDialog:!1,ipText:"",netCardList:[],isDox8021x:!1}},computed:{isAccess:function(){return!1}},watch:{userId:function(e,t){console.log("用户id变动",e,t),console.debug("用户id变动")}},mounted:function(){},beforeDestroy:function(){},methods:{minimizeWnd:function(){Hr.minimizeWnd()},maximizeWndOrNot:function(){this.isMaxWindow?(Hr.normalnizeWnd(),this.isMaxWindow=!1):(Hr.maximizeWnd(),this.isMaxWindow=!0)},dropdownVisiHandle:function(){},closeWnd:function(){var t=this;return a(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:if(TestQtModule("UIPlatform_Window","HideWnd")){e.n=6;break}return e.p=1,e.n=2,Hr.init();case 2:return e.n=3,Hr.ipcClient.$ipcSend("UIPlatform_Window","TerminateWnd");case 3:e.n=5;break;case 4:e.p=4,e.v,t.$message.error("操作失败，因小助手版本低。请重启小助手或电脑以升级。");case 5:return e.a(2);case 6:EventBus.$emit("closeAssui"),t.$nextTick((function(){Hr.hideWend()}));case 7:return e.a(2)}}),n,null,[[1,4]])})))()},setHandle:function(t){var n=this;return a(e().m((function r(){var o;return e().w((function(e){for(;;)switch(e.n){case 0:"changeLange"===t?(o=n.$i18n.locale,setLang("zh"===o?"en":"zh")):"changeMode"===t&&n.changeMode();case 1:return e.a(2)}}),r)})))()},userMenuHandle:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(this.countCommand=e,e){case"changePassword":if(!this.changePasswordHandle(t))return;break;case"myDevice":this.drawerSize=500,this.drawerTitle="";break;case"changeCount":this.drawerSize=581,this.drawerTitle="";break;case"lougOut":this.logoutMsg="注销后会取消自动身份认证功能，您确定要注销吗？",this.showLogout=!0,this.logoutType=1;break;case"switchNetwork":this.showSwitch=!0}"lougOut"!==e&&"switchNetwork"!==e&&(this.drawer=!0)},logoutHandle:function(){var t=this;return a(e().m((function n(){var o,i,a,c;return e().w((function(e){for(;;)switch(e.n){case 0:if(t.showLogout=!1,loading.start({msg:i18n.t("header.logouting")}),1!==t.logoutType){e.n=9;break}if(e.p=1,!t.isSsoAuth()){e.n=3;break}return e.n=2,ssoLogout(_.get(t.clientInfo,"accessStatus.lastAuthType"));case 2:o=e.v;case 3:return e.n=4,proxyApi.cutoffDevice({device_id:_.get(t.clientInfo,"detail.DeviceID",0),remark:"LogOut"});case 4:if(i=e.v,0===parseInt(_.get(i,"errcode"))){e.n=5;break}return _.get(i,"errmsg")||t.$message.error("注销失败！可能是因为网络不可用，或者服务器繁忙。"),loading.destory(),e.a(2);case 5:return commonUtil.setLoginRet({token:"",UserID:"",LoginRet:"0"}),e.n=6,Hr.logOut({IsCredibleDevice:_.get(t.clientInfo,"detail.IsTrustDev","0")});case 6:t.setGateInfos({state:2,gateWayMap:{},total:0,VPNStatus:0}),clearToken(),localStorage.removeItem("auditNextStatus"),localStorage.removeItem("auditCheckNextStatus"),authIndex.config.AutoLogin=-1,t.isDot1xMode&&t.setClientInfo(_.merge({},t.clientInfo,{basic:{IsOnline:0}})),t.setAuthInfo(r(r({},t.authInfo),{basic:{}})),t.setClientInfo(r(r({},t.clientInfo),{accessStatus:{}})),a=(new Date).getTime(),t.$router.push({name:"message",params:{forceTo:!0},query:{t:a}}),_.isString(o)&&""!==o&&(console.log("logoutUrl:".logoutUrl),Hr.windowOpenUrl(o)),e.n=8;break;case 7:e.p=7,c=e.v,console.error("退出登录错误",c);case 8:loading.destory();case 9:return e.a(2)}}),n,null,[[1,7]])})))()},getCountMenuWidth:function(){var t=this;return a(e().m((function n(){var r,o,i;return e().w((function(e){for(;;)switch(e.n){case 0:return r=t.isZtpUser?44:0,o=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0),e.p=1,e.n=2,Hr.init();case 2:return e.n=3,Hr.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(o)+r});case 3:e.n=5;break;case 4:e.p=4,i=e.v,console.warn("设置标题尺寸失败:",i);case 5:return e.a(2)}}),n,null,[[1,4]])})))()},hdEventHandle:function(e){if("router"===e.type)this.userMenuHandle(e.val)},closeDrawer:function(){this.deviceInnerBack=!1},changeVisible:function(e){this.drawer=e}}},Zr={class:"layout-header"},Xr={id:"u-header-menu",class:"right-wrapper"},Yr={id:"u-avator",ref:"countMenu"},eo={class:"user-info"},to={class:"user-name"};t("default",u(Jr,[["render",function(e,t,n,r,o,i){var a=f("base-icon"),c=f("el-dropdown-item"),l=f("el-dropdown-menu"),u=f("el-dropdown");return d(),y("div",Zr,[t[3]||(t[3]=h("div",{class:"header-logo"},[g("如果图片加载失败就隐藏"),h("img",{src:S,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),t[4]||(t[4]=h("div",{id:"u-electron-drag"},null,-1)),h("ul",Xr,[h("li",Yr,[m(u,{id:"ui-headNav-header-div-account_info",placement:"bottom-start",onCommand:i.userMenuHandle,onVisibleChange:i.dropdownVisiHandle},{default:b((function(){return[h("div",eo,[t[0]||(t[0]=h("div",{class:"user-face"},[h("img",{src:Kr,alt:"",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none' "})],-1)),h("span",to,v(o.username),1)]),m(l,{slot:"dropdown",class:"header-count-menu"},{default:b((function(){return[m(c,{id:"ui-headNav-header-li-cancel_account",command:"lougOut"},{default:b((function(){return[m(a,{name:"logout",style:{"margin-right":"6px"}}),t[1]||(t[1]=w("注销 "))]})),_:1,__:[1]})]})),_:1})]})),_:1},8,["onCommand","onVisibleChange"])],512),t[2]||(t[2]=h("div",{class:"user-divider"},null,-1)),m(a,{class:"window-operate",name:o.isMaxWindow?"fullscreen_exit":"fullscreen",onClick:i.maximizeWndOrNot},null,8,["name","onClick"]),m(a,{class:"window-operate",name:"minus",onClick:i.minimizeWnd},null,8,["onClick"]),m(a,{class:"window-operate",name:"close",style:{"margin-right":"16px"},onClick:i.closeWnd},null,8,["onClick"])])])}],["__scopeId","data-v-30488b75"],["__file","D:/asec-platform/frontend/portal/src/view/client/header.vue"]]))}}}))}();
