<template xmlns="http://www.w3.org/1999/html">
  <base-container class="layout-cont">
    <div :class="[isSider?'openside':'hideside',isMobile ? 'mobile': '']" class="layout-wrapper">
      <div :class="[isShadowBg?'shadowBg':'']" @click="changeShadow()" class="shadow-overlay" />
      <base-aside class="main-cont main-left gva-aside" :collapsed="isCollapse">
        <div class="tilte" :class="[isSider?'openlogoimg':'hidelogoimg']" :style="{background: backgroundColor}">
          <img alt class="logoimg" src="@/assets/ASD.png">
          <!--          <div>-->
          <!--            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>-->
          <!--            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>-->
          <!--          </div>-->
        </div>
        <Aside class="aside" />
        <div class="footer" :style="{background: backgroundColor}">
          <div class="menu-total" @click="totalCollapse">
            <svg v-if="isCollapse" class="icon menu-footer-icon" aria-hidden="true"><use xlink:href="#icon-expand" /></svg>
            <svg v-else class="icon menu-footer-icon" aria-hidden="true"><use xlink:href="#icon-fold" /></svg>
          </div>
        </div>
      </base-aside>
      <!-- 分块滑动功能 -->
      <base-main class="main-cont main-right">
        <transition :duration="{ enter: 800, leave: 100 }" mode="out-in" name="el-fade-in-linear">
          <div
            :style="{width: `calc(100% - ${isMobile?'0px':isCollapse?'54px':'220px'})`}"
            class="topfix"
          >
            <div class="header-row">
              <div class="header-col">
                <header class="header-cont">
                  <div class="header-content pd-0">
                    <div class="header-menu-col" style="z-index:100">
                      <!--                      <div class="menu-total" @click="totalCollapse">-->
                      <!--                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>-->
                      <!--                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>-->
                      <!--                      </div>-->
                    </div>
                    <div class="breadcrumb-col">
                      <nav class="breadcrumb">
                        <div
                          v-for="item in matched.slice(1,matched.length)"
                          :key="item.path"
                          class="breadcrumb-item"
                        >{{ fmtTitle(item.meta.topTitle || '', route) }}
                        </div>
                      </nav>
                    </div>
                    <div class="user-col">
                      <div class="right-box">
                        <!--                        <Search />-->
                        <div class="dropdown" @click="toggleDropdown">
                          <div class="dp-flex justify-content-center align-items height-full width-full">
                            <span class="header-avatar" style="cursor: pointer">
                              <!-- 展示当前登录用户名 -->
                              <span style="margin-right: 9px;color: #252631">{{
                                userStore.userInfo.displayName ? userStore.userInfo.displayName : userStore.userInfo.name
                              }}</span>
                              <base-icon name="zhankai" size="10px" />
                            </span>
                          </div>
                          <div v-if="dropdownVisible" class="dropdown-menu">
                            <!-- <div class="dropdown-item">
                              <span style="font-weight: 600;">
                                当前角色：{{ JSONPath('$..roles[0][name]', userStore.userInfo)[0] }}
                              </span>
                            </div> -->
                            <div class="dropdown-item" @click="toPerson">
                              <svg class="icon dropdown-item-icon" aria-hidden="true"><use xlink:href="#icon-person" /></svg>
                              <span>个人信息</span>
                            </div>
                            <div class="dropdown-item" @click="loginOut()">
                              <svg class="icon dropdown-item-icon" aria-hidden="true"><use xlink:href="#icon-logout" /></svg>
                              <span>注销登录</span>
                            </div>
                          </div>
                        </div>
                        <!--                        <base-button type="text"-->
                        <!--                                   class="iconfont icon-rizhi1"-->
                        <!--                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"-->
                        <!--                                   @click="toLog"-->
                        <!--                        >日志中心-->
                        <!--                        </base-button>-->
                      </div>
                    </div>
                  </div>
                </header>
              </div>
            </div>
            <!-- 当前面包屑用路由自动生成可根据需求修改 -->
            <!--
            :to="{ path: item.path }" 暂时注释不用-->
            <!--            <HistoryComponent ref="layoutHistoryComponent"/>-->
          </div>
        </transition>
        <div class="router-view-container" :class="{ 'loading': loadingFlag }">
          <div v-if="loadingFlag" class="loading-overlay">
            <div class="loading-spinner">
              <div class="spinner"></div>
              <div class="loading-text">正在加载中</div>
            </div>
          </div>
          <router-view
            v-if="reloadFlag"
            v-slot="{ Component }"
            class="admin-box"
          >
            <div>
              <transition mode="out-in" name="el-fade-in-linear">
                <keep-alive :include="routerStore.keepAliveRouters">
                  <component :is="Component" />
                </keep-alive>
              </transition>
            </div>
          </router-view>
        </div>
        <!--        <BottomInfo />-->
        <!--        <setting />-->
      </base-main>
    </div>

  </base-container>
</template>

<script>
export default {
  name: 'Layout',
}
</script>

<script setup>
import Aside from '@/view/layout/aside/index.vue'
// 使用轻量级 SVG 图标，已在 main.js 中全局加载
// import HistoryComponent from '@/view/layout/aside/historyComponent/history.vue'
// CustomPic 组件已删除
import { setUserAuthority } from '@/api/user'
import { emitter } from '@/utils/bus.js'
import { computed, ref, onMounted, onUnmounted, nextTick, getCurrentInstance, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useRouterStore } from '@/pinia/modules/router'
import { fmtTitle } from '@/utils/fmtRouterTitle'
import { JSONPath } from 'jsonpath-plus'
import { useUserStore } from '@/pinia/modules/user'
import { getUserInfo } from '@/api/user'
import Cookies from 'js-cookie'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const routerStore = useRouterStore()
// 三种窗口适配
const isCollapse = ref(true)
const isSider = ref(false)
const isMobile = ref(false)

// 时间选择
const day = ref('7')

const initPage = () => {
  const screenWidth = document.body.clientWidth
  if (screenWidth < 1000) {
    isMobile.value = false
    isSider.value = false
    isCollapse.value = true
  } else if (screenWidth >= 1000 && screenWidth < 1200) {
    isMobile.value = false
    isSider.value = false
    isCollapse.value = true
  } else {
    isMobile.value = false
    isSider.value = false
    isCollapse.value = true
  }
}

initPage()

const loadingFlag = ref(false)
onMounted(() => {
  // 挂载一些通用的事件
  emitter.emit('collapse', isCollapse.value)
  emitter.emit('mobile', isMobile.value)
  emitter.on('reload', reload)
  emitter.on('showLoading', () => {
    loadingFlag.value = true
  })
  emitter.on('closeLoading', () => {
    loadingFlag.value = false
  })
  window.onresize = () => {
    return (() => {
      initPage()
      emitter.emit('collapse', isCollapse.value)
      emitter.emit('mobile', isMobile.value)
    })()
  }
  if (userStore.loadingInstance) {
    userStore.loadingInstance.close()
  }

  // 添加点击外部区域关闭菜单的功能
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 添加点击外部区域关闭菜单的处理函数
const handleClickOutside = (event) => {
  // 如果菜单是展开状态且不是移动端
  if (!isCollapse.value && !isMobile.value) {
    const asideElement = document.querySelector('.gva-aside')
    const menuToggleElement = document.querySelector('.menu-total')

    // 检查点击是否在菜单区域外
    if (asideElement && !asideElement.contains(event.target) &&
        menuToggleElement && !menuToggleElement.contains(event.target)) {
      // 关闭菜单
      isCollapse.value = true
      isSider.value = false
      isShadowBg.value = false
      emitter.emit('collapse', isCollapse.value)
    }
  }

  // 处理下拉菜单的点击外部关闭
  if (dropdownVisible.value) {
    const dropdownElement = document.querySelector('.dropdown')
    if (dropdownElement && !dropdownElement.contains(event.target)) {
      dropdownVisible.value = false
    }
  }
}

const textColor = computed(() => {
  if (userStore.sideMode === 'dark') {
    return '#fff'
  } else if (userStore.sideMode === 'light') {
    return '#273444'
  } else {
    return userStore.baseColor
  }
})

const backgroundColor = computed(() => {
  if (userStore.sideMode === 'dark') {
    return '#273444'
  } else if (userStore.sideMode === 'light') {
    return '#fff'
  } else {
    return userStore.sideMode
  }
})

const matched = computed(() => route.meta.matched)

const changeUserAuth = async(id) => {
  const res = await setUserAuthority({
    authorityId: id,
  })
  if (res.code === 0) {
    window.sessionStorage.setItem('needCloseAll', 'true')
    window.location.reload()
  }
}

const reloadFlag = ref(true)
let reloadTimer = null
const reload = async() => {
  if (reloadTimer) {
    window.clearTimeout(reloadTimer)
  }
  reloadTimer = window.setTimeout(async() => {
    if (route.meta.keepAlive) {
      reloadFlag.value = false
      await nextTick()
      reloadFlag.value = true
    } else {
      const title = route.meta.title
      router.push({ name: 'Reload', params: { title }})
    }
  }, 400)
}

const isShadowBg = ref(false)
const dropdownVisible = ref(false)

const totalCollapse = () => {
  isCollapse.value = !isCollapse.value
  isSider.value = !isCollapse.value
  isShadowBg.value = !isCollapse.value
  emitter.emit('collapse', isCollapse.value)
}

const toggleDropdown = () => {
  dropdownVisible.value = !dropdownVisible.value
}

const toPerson = () => {
  router.push({ name: 'person' })
}

const toLog = () => {
  router.push({ name: 'log' })
}

const changeShadow = () => {
  isShadowBg.value = !isShadowBg.value
  isSider.value = !!isCollapse.value
  totalCollapse()
}

const instance = getCurrentInstance()
const loginOut = async () => {
  const host = document.location.protocol + '//' + document.location.host
  const clineData = {
    action: 1,
    msg: '',
    platform: document.location.hostname
  }
  const websocket = ref({})
  const wsUrl = ref('ws://127.0.0.1:50001')
  const platform = navigator.platform
  if (platform.indexOf('Mac') === 0 || platform === 'MacIntel') {
    wsUrl.value = 'wss://127.0.0.1:50001'
  }
  const initWebSocket = () => {
    websocket.value = new WebSocket(wsUrl.value)
    websocket.value.onopen = async () => {
      console.log('socket连接成功')
      await  sendMessage(JSON.stringify(clineData))
    }
    websocket.value.onmessage = async (e) => {
      console.log(e)
      await closeWebSocket()
    }
    websocket.value.onerror = () => {
      console.log('socket连接错误')
    }
  }
  // 发送消息
  const sendMessage = async (msg) => {
    console.log(msg, '0')
    await websocket.value.send(msg)
  }
  // 关闭链接（在页面销毁时可销毁链接）
  const closeWebSocket = async () => {
    console.log('socket断开链接')
    await websocket.value.close()
  }
  console.log(`asecagent://?web=${JSON.stringify(clineData)}`)
  await userStore.LoginOut()
  initWebSocket()
  Cookies.remove('asce_sms')
}
provide('day', day)
</script>

<style lang="scss">
@import '@/style/mobile.scss';

.dark {
  background-color: #273444 !important;
  color: #fff !important;
}

.light {
  background-color: #fff !important;
  color: #000 !important;
}

.icon-rizhi1 {
  span {
    margin-left: 5px;
  }
}

.day-select {
  height: 23px;
  width: 88px;
  margin-left: 15px;

  div {
    height: 23px;
    width: 88px;

    input {
      height: 23px;
      width: 50px;
      font-size: 12px;
      color: #2972C8;
    }
  }
}

.hidelogoimg {
  overflow: hidden !important;
  width: 54px !important;
  padding-left: 9px !important;
  .logoimg{
    margin-left: -15px;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

.layout-wrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

.shadow-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

.shadowBg {
  display: block !important;
}

.header-row {
  display: flex;
  width: 100%;
}

.header-col {
  flex: 1;
}

.header-content {
  display: flex;
  align-items: center;
  padding: 0;
}

.header-menu-col {
  flex: 0 0 auto;
}

.breadcrumb-col {
  flex: 1;
}

.user-col {
  flex: 0 0 auto;
  min-width: 200px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 8px;
  color: #c0c4cc;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  color: #606266;
  .dropdown-item-icon {
    margin-right: 4px;
    width: 14px;
    height: 14px;
  }
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

.dropdown-item .icon {
  margin-right: 8px;
  font-size: 14px;
}

/* Loading 样式 */
.router-view-container {
  position: relative;
  flex: 1;
  margin-left: 35px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #536ce6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.menu-footer-icon {
  color: #FFFFFF; 
  font-size: 14px;
  width: 16px;
  height: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #606266;
}
</style>
