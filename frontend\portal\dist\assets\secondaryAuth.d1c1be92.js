/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
import{r as e,c as a,h as t,o as s,d as l,e as n,F as i,i as u,j as c,w as o,f as r,g as d,_ as h,t as v,k as f}from"./index.df61e453.js";import y from"./verifyCode.8740ac20.js";const p={class:"secondary-auth-overlay"},m={class:"secondary-auth-container"},_={key:0,class:"auth-selector"},k={class:"auth-methods"},b={class:"auth-method-content"},g={class:"icon","aria-hidden":"true"},I=["xlink:href"],j={class:"auth-method-name"},x={class:"selector-footer"},C=h(Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup(h,{emit:C}){const S=h,q=e(!0),w=e(null),A=a((()=>S.authMethods.filter((e=>e.available)))),M=e=>{w.value=e,q.value=!1};1===A.value.length&&M(A.value[0]);const N=C,O=()=>{N("cancel")},B=e=>{"client"===route.query.type&&(e.clientParams={type:"client",wp:route.query.wp||"50001"}),N("verification-success",e)};return(e,a)=>{const C=t("base-avatar"),S=t("base-card"),N=t("base-button");return s(),l("div",p,[n("div",m,[q.value?(s(),l("div",_,[a[3]||(a[3]=n("h2",{class:"title"},"请选择二次认证方式",-1)),n("div",k,[(s(!0),l(i,null,u(A.value,(e=>(s(),d(S,{key:e.type,class:"auth-method-card",onClick:a=>M(e)},{default:o((()=>[n("div",b,[c(C,null,{default:o((()=>[(s(),l("svg",g,[n("use",{"xlink:href":"#icon-auth-"+e.icon},null,8,I)]))])),_:2},1024),n("div",j,v(e.name),1)])])),_:2},1032,["onClick"])))),128))]),n("div",x,[c(N,{type:"info",onClick:a[0]||(a[0]=()=>O())},{default:o((()=>a[2]||(a[2]=[f("取消")]))),_:1,__:[2]})])])):r("v-if",!0),r(" 加载统一的验证码组件，传入不同的模板类型 "),!q.value&&w.value?(s(),d(y,{key:1,auth_info:h.authInfo,auth_id:h.authId,"user-name":h.userName,last_id:h.lastId,"secondary-type":w.value.type,onVerificationSuccess:B,onBack:a[1]||(a[1]=e=>q.value=!0),onCancel:O},null,8,["auth_info","auth_id","user-name","last_id","secondary-type"])):r("v-if",!0)])])}}}),[["__scopeId","data-v-24e922d3"],["__file","D:/asec-platform/frontend/portal/src/view/login/secondaryAuth/secondaryAuth.vue"]]);export{C as default};
