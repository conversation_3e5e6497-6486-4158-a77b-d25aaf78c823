/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{b as Y,a as Z,u as ee,S as oe,r as u,G as se,K as f,c as z,U as te,p as ne,h as F,N as ae,o as d,f as S,w as _,e,C as j,j as y,I as A,d as h,T as E,F as le,i as ie,k as V,t as J,m as w,Q as re,O as K,V as ce,g as N,W as de,A as ue}from"./index.d0594432.js";import{_ as ve}from"./ASD.492c8837.js";import me from"./index.e5a9e31d.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.2c60b68d.js";import"./menuItem.5f1822c7.js";import"./asyncSubmenu.4ad0e585.js";/*! js-cookie v3.0.5 | MIT */function I(i){for(var n=1;n<arguments.length;n++){var p=arguments[n];for(var v in p)i[v]=p[v]}return i}var pe={read:function(i){return i[0]==='"'&&(i=i.slice(1,-1)),i.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(i){return encodeURIComponent(i).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function W(i,n){function p(a,t,s){if(!(typeof document>"u")){s=I({},n,s),typeof s.expires=="number"&&(s.expires=new Date(Date.now()+s.expires*864e5)),s.expires&&(s.expires=s.expires.toUTCString()),a=encodeURIComponent(a).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var l="";for(var c in s)!s[c]||(l+="; "+c,s[c]!==!0&&(l+="="+s[c].split(";")[0]));return document.cookie=a+"="+i.write(t,a)+l}}function v(a){if(!(typeof document>"u"||arguments.length&&!a)){for(var t=document.cookie?document.cookie.split("; "):[],s={},l=0;l<t.length;l++){var c=t[l].split("="),k=c.slice(1).join("=");try{var g=decodeURIComponent(c[0]);if(s[g]=i.read(k,g),a===g)break}catch{}}return a?s[a]:s}}return Object.create({set:p,get:v,remove:function(a,t){p(a,"",I({},t,{expires:-1}))},withAttributes:function(a){return W(this.converter,I({},this.attributes,a))},withConverter:function(a){return W(I({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(i)}})}var fe=W(pe,{path:"/"});const ge={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},he={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},_e={class:"header-row"},we={class:"header-col"},ye={class:"header-cont"},ke={class:"header-content pd-0"},xe={class:"breadcrumb-col"},be={class:"breadcrumb"},Ce={class:"user-col"},Fe={class:"right-box"},Se={class:"dp-flex justify-content-center align-items height-full width-full"},Ie={class:"header-avatar",style:{cursor:"pointer"}},Me={style:{"margin-right":"9px",color:"#252631"}},Re={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},Ue={key:0,class:"dropdown-menu"},Be={name:"Layout"},Ne=Object.assign(Be,{setup(i){const n=Y(),p=Z(),v=ee(),a=oe(),t=u(!0),s=u(!1),l=u(!1),c=u("7"),k=()=>{const m=document.body.clientWidth;m<1e3||m>=1e3&&m<1200,l.value=!1,s.value=!1,t.value=!0};k();const g=u(!1);se(()=>{f.emit("collapse",t.value),f.emit("mobile",l.value),f.on("reload",P),f.on("showLoading",()=>{g.value=!0}),f.on("closeLoading",()=>{g.value=!1}),window.onresize=()=>(()=>{k(),f.emit("collapse",t.value),f.emit("mobile",l.value)})(),n.loadingInstance&&n.loadingInstance.close()}),z(()=>n.sideMode==="dark"?"#fff":n.sideMode==="light"?"#273444":n.baseColor);const M=z(()=>n.sideMode==="dark"?"#273444":n.sideMode==="light"?"#fff":n.sideMode),L=z(()=>v.meta.matched),R=u(!0);let U=null;const P=async()=>{U&&window.clearTimeout(U),U=window.setTimeout(async()=>{if(v.meta.keepAlive)R.value=!1,await te(),R.value=!0;else{const m=v.meta.title;p.push({name:"Reload",params:{title:m}})}},400)},x=u(!1),B=u(!1),$=()=>{t.value=!t.value,s.value=!t.value,x.value=!t.value,f.emit("collapse",t.value)},G=()=>{B.value=!B.value},Q=()=>{p.push({name:"person"})},q=()=>{x.value=!x.value,s.value=!!t.value,$()},H=async()=>{document.location.protocol+""+document.location.host;const m={action:1,msg:"",platform:document.location.hostname},o=u({}),b=u("ws://127.0.0.1:50001"),C=navigator.platform;(C.indexOf("Mac")===0||C==="MacIntel")&&(b.value="wss://127.0.0.1:50001");const D=()=>{o.value=new WebSocket(b.value),o.value.onopen=async()=>{console.log("socket\u8FDE\u63A5\u6210\u529F"),await O(JSON.stringify(m))},o.value.onmessage=async r=>{console.log(r),await T()},o.value.onerror=()=>{console.log("socket\u8FDE\u63A5\u9519\u8BEF")}},O=async r=>{console.log(r,"0"),await o.value.send(r)},T=async()=>{console.log("socket\u65AD\u5F00\u94FE\u63A5"),await o.value.close()};console.log(`asecagent://?web=${JSON.stringify(m)}`),await n.LoginOut(),D(),fe.remove("asce_sms")};return ne("day",c),(m,o)=>{const b=F("base-aside"),C=F("router-view"),D=F("base-main"),O=F("base-container"),T=ae("loading");return d(),S(O,{class:"layout-cont"},{default:_(()=>[e("div",{class:j([[s.value?"openside":"hideside",l.value?"mobile":""],"layout-wrapper"])},[e("div",{class:j([[x.value?"shadowBg":""],"shadow-overlay"]),onClick:o[0]||(o[0]=r=>q())},null,2),y(b,{class:"main-cont main-left gva-aside",collapsed:t.value},{default:_(()=>[e("div",{class:j(["tilte",[s.value?"openlogoimg":"hidelogoimg"]]),style:A({background:M.value})},o[3]||(o[3]=[e("img",{alt:"",class:"logoimg",src:ve},null,-1)]),6),y(me,{class:"aside"}),e("div",{class:"footer",style:A({background:M.value})},[e("div",{class:"menu-total",onClick:$},[t.value?(d(),h("svg",ge,o[4]||(o[4]=[e("use",{"xlink:href":"#icon-expand"},null,-1)]))):(d(),h("svg",he,o[5]||(o[5]=[e("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)]),_:1},8,["collapsed"]),y(D,{class:"main-cont main-right"},{default:_(()=>[y(E,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:_(()=>[e("div",{style:A({width:`calc(100% - ${l.value?"0px":t.value?"54px":"220px"})`}),class:"topfix"},[e("div",_e,[e("div",we,[e("header",ye,[e("div",ke,[o[10]||(o[10]=e("div",{class:"header-menu-col",style:{"z-index":"100"}},null,-1)),e("div",xe,[e("nav",be,[(d(!0),h(le,null,ie(L.value.slice(1,L.value.length),r=>(d(),h("div",{key:r.path,class:"breadcrumb-item"},[V(J(w(re)(r.meta.topTitle||"",w(v)))+" ",1),r.meta.title==="\u603B\u89C8"?K((d(),h("select",{key:0,"onUpdate:modelValue":o[1]||(o[1]=X=>c.value=X),class:"day-select form-select"},o[6]||(o[6]=[e("option",{value:"7"},"\u6700\u8FD17\u5929",-1),e("option",{value:"30"},"\u6700\u8FD130\u5929",-1),e("option",{value:"90"},"\u6700\u8FD190\u5929",-1)]),512)),[[ce,c.value]]):N("",!0)]))),128))])]),e("div",Ce,[e("div",Fe,[e("div",{class:"dropdown",onClick:G},[e("div",Se,[e("span",Ie,[e("span",Me,J(w(n).userInfo.displayName?w(n).userInfo.displayName:w(n).userInfo.name),1),(d(),h("svg",Re,o[7]||(o[7]=[e("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),B.value?(d(),h("div",Ue,[e("div",{class:"dropdown-item",onClick:Q},o[8]||(o[8]=[e("svg",{class:"icon","aria-hidden":"true"},[e("use",{"xlink:href":"#icon-avatar"})],-1),V(" \u4E2A\u4EBA\u4FE1\u606F ")])),e("div",{class:"dropdown-item",onClick:o[2]||(o[2]=r=>H())},o[9]||(o[9]=[e("svg",{class:"icon","aria-hidden":"true"},[e("use",{"xlink:href":"#icon-reading-lamp"})],-1),V(" \u767B \u51FA ")]))])):N("",!0)])])])])])])])],4)]),_:1}),R.value?K((d(),S(C,{key:0,"element-loading-text":"\u6B63\u5728\u52A0\u8F7D\u4E2D",class:"admin-box"},{default:_(({Component:r})=>[e("div",null,[y(E,{mode:"out-in",name:"el-fade-in-linear"},{default:_(()=>[(d(),S(de,{include:w(a).keepAliveRouters},[(d(),S(ue(r)))],1032,["include"]))]),_:2},1024)])]),_:1})),[[T,g.value]]):N("",!0)]),_:1})],2)]),_:1})}}});export{Ne as default};
