/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
import{_ as e,b as a,a as o,u as t,V as s,r as n,O as i,R as l,c,W as r,p as d,h as u,G as v,o as m,g as p,w as f,e as g,J as h,j as y,B as w,f as b,d as x,T as k,F as C,i as I,k as F,t as _,m as j,U as S,H as U,X as N,Y as O,A as z}from"./index.e2f48a61.js";import{_ as A}from"./ASD.492c8837.js";import M from"./index.3f7f0dc4.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.2ca8c358.js";import"./menuItem.9ce6f016.js";import"./asyncSubmenu.d763f339.js";
/*! js-cookie v3.0.5 | MIT */function R(e){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var t in o)e[t]=o[t]}return e}var D=function e(a,o){function t(e,t,s){if("undefined"!=typeof document){"number"==typeof(s=R({},o,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var n="";for(var i in s)s[i]&&(n+="; "+i,!0!==s[i]&&(n+="="+s[i].split(";")[0]));return document.cookie=e+"="+a.write(t,e)+n}}return Object.create({set:t,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var o=document.cookie?document.cookie.split("; "):[],t={},s=0;s<o.length;s++){var n=o[s].split("="),i=n.slice(1).join("=");try{var l=decodeURIComponent(n[0]);if(t[l]=a.read(i,l),e===l)break}catch(c){}}return e?t[e]:t}},remove:function(e,a){t(e,"",R({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,R({},this.attributes,a))},withConverter:function(a){return e(R({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const B={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},T={class:"header-row"},$={class:"header-col"},E={class:"header-cont"},J={class:"header-content pd-0"},V={class:"breadcrumb-col"},G={class:"breadcrumb"},H={class:"user-col"},W={class:"right-box"},P={class:"dp-flex justify-content-center align-items height-full width-full"},X={class:"header-avatar",style:{cursor:"pointer"}},Y={style:{"margin-right":"9px",color:"#252631"}},q={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},K={key:0,class:"dropdown-menu"},Q=e(Object.assign({name:"Layout"},{setup(e){const R=a(),Q=o(),Z=t(),ee=s(),ae=n(!0),oe=n(!1),te=n(!1),se=n("7"),ne=()=>{document.body.clientWidth;te.value=!1,oe.value=!1,ae.value=!0};ne();const ie=n(!1);i((()=>{l.emit("collapse",ae.value),l.emit("mobile",te.value),l.on("reload",ue),l.on("showLoading",(()=>{ie.value=!0})),l.on("closeLoading",(()=>{ie.value=!1})),window.onresize=()=>(ne(),l.emit("collapse",ae.value),void l.emit("mobile",te.value)),R.loadingInstance&&R.loadingInstance.close()})),c((()=>"dark"===R.sideMode?"#fff":"light"===R.sideMode?"#273444":R.baseColor));const le=c((()=>"dark"===R.sideMode?"#273444":"light"===R.sideMode?"#fff":R.sideMode)),ce=c((()=>Z.meta.matched)),re=n(!0);let de=null;const ue=async()=>{de&&window.clearTimeout(de),de=window.setTimeout((async()=>{if(Z.meta.keepAlive)re.value=!1,await r(),re.value=!0;else{const e=Z.meta.title;Q.push({name:"Reload",params:{title:e}})}}),400)},ve=n(!1),me=n(!1),pe=()=>{ae.value=!ae.value,oe.value=!ae.value,ve.value=!ae.value,l.emit("collapse",ae.value)},fe=()=>{me.value=!me.value},ge=()=>{Q.push({name:"person"})};return d("day",se),(e,a)=>{const o=u("base-aside"),t=u("router-view"),s=u("base-main"),i=u("base-container"),l=v("loading");return m(),p(i,{class:"layout-cont"},{default:f((()=>[g("div",{class:h([[oe.value?"openside":"hideside",te.value?"mobile":""],"layout-wrapper"])},[g("div",{class:h([[ve.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(ve.value=!ve.value,oe.value=!!ae.value,void pe()))},null,2),y(o,{class:"main-cont main-left gva-aside",collapsed:ae.value},{default:f((()=>[g("div",{class:h(["tilte",[oe.value?"openlogoimg":"hidelogoimg"]]),style:w({background:le.value})},[a[3]||(a[3]=g("img",{alt:"",class:"logoimg",src:A},null,-1)),b("          <div>"),b('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),b('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),b("          </div>")],6),y(M,{class:"aside"}),g("div",{class:"footer",style:w({background:le.value})},[g("div",{class:"menu-total",onClick:pe},[ae.value?(m(),x("svg",B,a[4]||(a[4]=[g("use",{"xlink:href":"#icon-expand"},null,-1)]))):(m(),x("svg",L,a[5]||(a[5]=[g("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),b(" 分块滑动功能 "),y(s,{class:"main-cont main-right"},{default:f((()=>[y(k,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[g("div",{style:w({width:`calc(100% - ${te.value?"0px":ae.value?"54px":"220px"})`}),class:"topfix"},[g("div",T,[g("div",$,[g("header",E,[g("div",J,[a[10]||(a[10]=g("div",{class:"header-menu-col",style:{"z-index":"100"}},[b('                      <div class="menu-total" @click="totalCollapse">'),b('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),b('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),b("                      </div>")],-1)),g("div",V,[g("nav",G,[(m(!0),x(C,null,I(ce.value.slice(1,ce.value.length),(e=>(m(),x("div",{key:e.path,class:"breadcrumb-item"},[F(_(j(S)(e.meta.topTitle||"",j(Z)))+" ",1),"总览"===e.meta.title?U((m(),x("select",{key:0,"onUpdate:modelValue":a[1]||(a[1]=e=>se.value=e),class:"day-select form-select"},[...a[6]||(a[6]=[g("option",{value:"7"},"最近7天",-1),g("option",{value:"30"},"最近30天",-1),g("option",{value:"90"},"最近90天",-1)])],512)),[[N,se.value]]):b("v-if",!0)])))),128))])]),g("div",H,[g("div",W,[b("                        <Search />"),g("div",{class:"dropdown",onClick:fe},[g("div",P,[g("span",X,[b(" 展示当前登录用户名 "),g("span",Y,_(j(R).userInfo.displayName?j(R).userInfo.displayName:j(R).userInfo.name),1),(m(),x("svg",q,a[7]||(a[7]=[g("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),me.value?(m(),x("div",K,[b(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),g("div",{class:"dropdown-item",onClick:ge},a[8]||(a[8]=[g("svg",{class:"icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-avatar"})],-1),F(" 个人信息 ")])),g("div",{class:"dropdown-item",onClick:a[2]||(a[2]=e=>(async()=>{document.location.protocol,document.location.host;const e={action:1,msg:"",platform:document.location.hostname},a=n({}),o=n("ws://127.0.0.1:50001"),t=navigator.platform;0!==t.indexOf("Mac")&&"MacIntel"!==t||(o.value="wss://127.0.0.1:50001");const s=async e=>{console.log(e,"0"),await a.value.send(e)},i=async()=>{console.log("socket断开链接"),await a.value.close()};console.log(`asecagent://?web=${JSON.stringify(e)}`),await R.LoginOut(),a.value=new WebSocket(o.value),a.value.onopen=async()=>{console.log("socket连接成功"),await s(JSON.stringify(e))},a.value.onmessage=async e=>{console.log(e),await i()},a.value.onerror=()=>{console.log("socket连接错误")},D.remove("asce_sms")})())},a[9]||(a[9]=[g("svg",{class:"icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-reading-lamp"})],-1),F(" 登 出 ")]))])):b("v-if",!0)]),b('                        <base-button type="text"'),b('                                   class="iconfont icon-rizhi1"'),b('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),b('                                   @click="toLog"'),b("                        >日志中心"),b("                        </base-button>")])])])])])]),b(" 当前面包屑用路由自动生成可根据需求修改 "),b('\r\n            :to="{ path: item.path }" 暂时注释不用'),b('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)])),_:1}),re.value?U((m(),p(t,{key:0,"element-loading-text":"正在加载中",class:"admin-box"},{default:f((({Component:e})=>[g("div",null,[y(k,{mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[(m(),p(O,{include:j(ee).keepAliveRouters},[(m(),p(z(e)))],1032,["include"]))])),_:2},1024)])])),_:1})),[[l,ie.value]]):b("v-if",!0),b("        <BottomInfo />"),b("        <setting />")])),_:1})],2)])),_:1})}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]);export{Q as default};
