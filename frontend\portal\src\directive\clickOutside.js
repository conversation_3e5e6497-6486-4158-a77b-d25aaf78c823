// 点击外部区域指令
export default {
  install: (app) => {
    app.directive('click-outside', {
      mounted(el, binding) {
        el._clickOutsideHandler = (event) => {
          // 检查点击的元素是否在指令绑定的元素内部
          if (!(el === event.target || el.contains(event.target))) {
            // 如果点击在外部，执行绑定的函数
            if (typeof binding.value === 'function') {
              binding.value(event)
            }
          }
        }
        
        // 添加事件监听器
        document.addEventListener('click', el._clickOutsideHandler)
      },
      
      unmounted(el) {
        // 移除事件监听器
        if (el._clickOutsideHandler) {
          document.removeEventListener('click', el._clickOutsideHandler)
          delete el._clickOutsideHandler
        }
      }
    })
  }
}
