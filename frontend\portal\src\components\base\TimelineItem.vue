<template>
  <div class="base-timeline-item">
    <div class="base-timeline-item__tail"></div>
    <div class="base-timeline-item__node" :class="nodeClass" :style="nodeStyle">
      <slot name="dot">
        <div class="base-timeline-item__node-normal"></div>
      </slot>
    </div>
    <div class="base-timeline-item__wrapper">
      <div v-if="timestamp" class="base-timeline-item__timestamp" :class="timestampClass">
        {{ timestamp }}
      </div>
      <div class="base-timeline-item__content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseTimelineItem',
  props: {
    timestamp: {
      type: String,
      default: ''
    },
    hideTimestamp: {
      type: Boolean,
      default: false
    },
    placement: {
      type: String,
      default: 'bottom',
      validator: (value) => ['top', 'bottom'].includes(value)
    },
    type: {
      type: String,
      default: '',
      validator: (value) => ['primary', 'success', 'warning', 'danger', 'info', ''].includes(value)
    },
    color: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'normal',
      validator: (value) => ['normal', 'large'].includes(value)
    },
    icon: {
      type: String,
      default: ''
    }
  },
  computed: {
    nodeClass() {
      const classes = [`base-timeline-item__node--${this.size}`]
      if (this.type) {
        classes.push(`base-timeline-item__node--${this.type}`)
      }
      return classes
    },
    nodeStyle() {
      const style = {}
      if (this.color) {
        style.backgroundColor = this.color
        style.borderColor = this.color
      }
      return style
    },
    timestampClass() {
      return [
        `base-timeline-item__timestamp--${this.placement}`
      ]
    }
  }
}
</script>

<style scoped>
.base-timeline-item {
  position: relative;
  padding-bottom: 20px;
}

.base-timeline-item__tail {
  position: absolute;
  left: 4px;
  height: 100%;
  border-left: 2px solid #e4e7ed;
}

.base-timeline-item:last-child .base-timeline-item__tail {
  display: none;
}

.base-timeline-item__node {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.base-timeline-item__node--normal {
  left: -1px;
  width: 12px;
  height: 12px;
}

.base-timeline-item__node--large {
  left: -2px;
  width: 14px;
  height: 14px;
}

.base-timeline-item__node-normal {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #c0c4cc;
}

.base-timeline-item__node--primary .base-timeline-item__node-normal {
  background-color: #536ce6;
}

.base-timeline-item__node--success .base-timeline-item__node-normal {
  background-color: #67c23a;
}

.base-timeline-item__node--warning .base-timeline-item__node-normal {
  background-color: #e6a23c;
}

.base-timeline-item__node--danger .base-timeline-item__node-normal {
  background-color: #f56c6c;
}

.base-timeline-item__node--info .base-timeline-item__node-normal {
  background-color: #909399;
}

.base-timeline-item__wrapper {
  position: relative;
  padding-left: 28px;
  top: -3px;
}

.base-timeline-item__timestamp {
  color: #909399;
  line-height: 1;
  font-size: 13px;
}

.base-timeline-item__timestamp--top {
  margin-bottom: 8px;
  padding-top: 4px;
}

.base-timeline-item__timestamp--bottom {
  margin-top: 8px;
}

.base-timeline-item__content {
  color: #303133;
}
</style>
