/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
!function(){function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,o,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.toStringTag||"@@toStringTag";function l(n,i,r,a){var l=i&&i.prototype instanceof u?i:u,c=Object.create(l.prototype);return e(c,"_invoke",function(n,e,i){var r,a,l,u=0,c=i||[],s=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(n,e){return r=n,a=0,l=t,f.n=e,d}};function p(n,e){for(a=n,l=e,o=0;!s&&u&&!i&&o<c.length;o++){var i,r=c[o],p=f.p,w=r[2];n>3?(i=w===e)&&(l=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=t):r[0]<=p&&((i=n<2&&p<r[1])?(a=0,f.v=e,f.n=r[1]):p<w&&(i=n<3||r[0]>e||e>w)&&(r[4]=n,r[5]=e,f.n=w,a=0))}if(i||n>1)return d;throw s=!0,e}return function(i,c,w){if(u>1)throw TypeError("Generator is already running");for(s&&1===c&&p(c,w),a=c,l=w;(o=a<2?t:l)||!s;){r||(a?a<3?(a>1&&(f.n=-1),p(a,l)):f.n=l:f.v=l);try{if(u=2,r){if(a||(i="next"),o=r[i]){if(!(o=o.call(r,l)))throw TypeError("iterator result is not an object");if(!o.done)return o;l=o.value,a<2&&(a=0)}else 1===a&&(o=r.return)&&o.call(r),a<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=t}else if((o=(s=f.n<0)?l:n.call(e,f))!==d)break}catch(o){r=t,a=1,l=o}finally{u=1}}return{value:o,done:s}}}(n,r,a),!0),c}var d={};function u(){}function c(){}function s(){}o=Object.getPrototypeOf;var f=[][r]?o(o([][r]())):(e(o={},r,(function(){return this})),o),p=s.prototype=u.prototype=Object.create(f);function w(n){return Object.setPrototypeOf?Object.setPrototypeOf(n,s):(n.__proto__=s,e(n,a,"GeneratorFunction")),n.prototype=Object.create(p),n}return c.prototype=s,e(p,"constructor",s),e(s,"constructor",c),c.displayName="GeneratorFunction",e(s,a,"GeneratorFunction"),e(p),e(p,a,"Generator"),e(p,r,(function(){return this})),e(p,"toString",(function(){return"[object Generator]"})),(n=function(){return{w:l,m:w}})()}function e(n,t,o,i){var r=Object.defineProperty;try{r({},"",{})}catch(n){r=0}e=function(n,t,o,i){if(t)r?r(n,t,{value:o,enumerable:!i,configurable:!i,writable:!i}):n[t]=o;else{var a=function(t,o){e(n,t,(function(n){return this._invoke(t,o,n)}))};a("next",0),a("throw",1),a("return",2)}},e(n,t,o,i)}function t(n,e,t,o,i,r,a){try{var l=n[r](a),d=l.value}catch(n){return void t(n)}l.done?e(d):Promise.resolve(d).then(o,i)}System.register(["./index-legacy.21dbeba9.js","./browser-legacy.63da4671.js"],(function(e,o){"use strict";var i,r,a,l,d,u,c,s,f,p,w,v,h,g,m,y,b=document.createElement("style");return b.textContent='@charset "UTF-8";.icon[data-v-4f60b33c]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.client[data-v-4f60b33c]{height:100vh;text-align:center;background:#FFFFFF;max-height:calc(100vh - 68px)}.client .el-main[data-v-4f60b33c]{height:100%}.client .el-main div div:hover .window-show[data-v-4f60b33c]{display:none}.client .el-main div div:hover .window-hidden[data-v-4f60b33c]{display:block!important}.client .el-main div div:hover .window-hidden span[data-v-4f60b33c]{margin-top:42px!important}\n',document.head.appendChild(b),{setters:[function(n){i=n._,r=n.r,a=n.h,l=n.G,d=n.o,u=n.d,c=n.e,s=n.j,f=n.w,p=n.f,w=n.k,v=n.g,h=n.H,g=n.M},function(n){m=n.g,y=n.b}],execute:function(){var o={class:"client"},b={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}},x={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},_={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px","margin-left":"39%",display:"none"}},k={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},F={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-left":"39%","margin-top":"60px",display:"none"}},O={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},S={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},j={__name:"download",setup:function(e){var i=r(""),j=r(""),E=r(!1),C=r(!1),M=r(!1),L=r(!1),R=r({windows:0,darwin:0}),T=function(n){return 100===n?"完成":"".concat(n,"%")},U=function(n,e){return new Promise((function(t,o){var i=new XMLHttpRequest;i.open("GET",n,!0),i.responseType="blob",i.onprogress=function(n){if(n.lengthComputable){var t=n.loaded/n.total*100;R.value[e]=Math.round(t)}},i.onload=function(){200===i.status?t(i.response):o(new Error("下载失败"))},i.onerror=function(){o(new Error("网络错误"))},i.send()}))},z=function(n,e){if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(n,e);else{var t=document.createElement("a"),o=document.querySelector("body");t.href=window.URL.createObjectURL(n),t.download=e,t.style.display="none",o.appendChild(t),t.click(),o.removeChild(t),window.URL.revokeObjectURL(t.href)}G()},G=function(){E.value=!1,C.value=!1,M.value=!1,L.value=!1,Object.keys(R.value).forEach((function(n){R.value[n]=0}))},I=r(!1),P=function(){var e,o=(e=n().m((function e(t){var o,r,a,l,d,u,c,s,f,p,w,v,h,b,x,_,k,F,O;return n().w((function(n){for(;;)switch(n.n){case 0:if("android"!==t&&"ios"!==t||!I.value){n.n=1;break}return n.a(2);case 1:return I.value=!0,(o={windows:E,darwin:C,ios:M,android:L}[t]).value=!0,n.p=2,n.n=3,m({platform:t});case 3:if(0!==(r=n.v).data.code){n.n=10;break}if("ios"!==t){n.n=5;break}return n.n=4,y.toDataURL(r.data.data.download_url);case 4:a=n.v,l=document.getElementById("ioscanvas"),j.value=a,l&&(d=l.getContext("2d"),(u=new Image).onload=function(){l.width=u.width,l.height=u.height,d.drawImage(u,0,0)},u.src=a),n.n=9;break;case 5:if("android"!==t){n.n=7;break}return c=window.location.port,s=new URL(r.data.data.download_url),c?s.toString().includes("asec-deploy")?f=r.data.data.download_url:(s.port=c,f=s.toString()):(s.port="",f=s.toString()),n.n=6,y.toDataURL(f);case 6:p=n.v,w=document.getElementById("canvas"),i.value=p,w&&(v=w.getContext("2d"),(h=new Image).onload=function(){w.width=h.width,w.height=h.height,v.drawImage(h,0,0)},h.src=p),n.n=9;break;case 7:return b=window.location.port,x=new URL(r.data.data.download_url),b?(x.toString().includes("asec-deploy")?_=r.data.data.download_url:(x.port=b,_=x.toString()),k=r.data.data.latest_filename.replace(/@(\d+)/,"@".concat(b))):(x.port="",_=x.toString(),k=r.data.data.latest_filename),n.n=8,U(_,t);case 8:F=n.v,z(F,k);case 9:n.n=11;break;case 10:throw new Error(r.data.msg);case 11:n.n=13;break;case 12:n.p=12,O=n.v,g({type:"error",message:O.message||"下载失败，请联系管理员"});case 13:return n.p=13,o.value=!1,n.f(13);case 14:return n.a(2)}}),e,null,[[2,12,13,14]])})),function(){var n=this,o=arguments;return new Promise((function(i,r){var a=e.apply(n,o);function l(n){t(a,i,r,l,d,"next",n)}function d(n){t(a,i,r,l,d,"throw",n)}l(void 0)}))});return function(n){return o.apply(this,arguments)}}();return function(n,e){var t=a("el-link"),i=a("el-progress"),r=a("base-main"),g=l("loading");return d(),u("div",null,[c("div",o,[s(r,null,{default:f((function(){return[c("div",b,[p(" Windows 客户端 "),c("div",{style:{float:"left","margin-right":"5%",width:"209px",height:"209px",background:"#F1F8FF"},onClick:e[0]||(e[0]=function(n){return P("windows")})},[(d(),u("svg",x,e[6]||(e[6]=[c("use",{"xlink:href":"#icon-windows"},null,-1)]))),(d(),u("svg",_,e[7]||(e[7]=[c("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[10]||(e[10]=c("br",null,null,-1)),s(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:f((function(){return e[8]||(e[8]=[w("Windows客户端")])})),_:1,__:[8]}),s(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:f((function(){return e[9]||(e[9]=[w("点击下载Windows客户端")])})),_:1,__:[9]}),E.value?(d(),v(i,{key:0,percentage:R.value.windows,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):p("v-if",!0)]),p(" Mac 客户端 "),c("div",{style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onClick:e[1]||(e[1]=function(n){return P("darwin")})},[(d(),u("svg",k,e[11]||(e[11]=[c("use",{"xlink:href":"#icon-mac"},null,-1)]))),(d(),u("svg",F,e[12]||(e[12]=[c("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[15]||(e[15]=c("br",null,null,-1)),s(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:f((function(){return e[13]||(e[13]=[w("Mac客户端")])})),_:1,__:[13]}),s(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:f((function(){return e[14]||(e[14]=[w("点击下载Mac客户端")])})),_:1,__:[14]}),C.value?(d(),v(i,{key:0,percentage:R.value.darwin,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):p("v-if",!0)]),p(" iOS 客户端 "),h((d(),u("div",{"element-loading-text":"下载码生成中...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onMousemove:e[2]||(e[2]=function(n){return P("ios")}),onMouseleave:e[3]||(e[3]=function(n){return I.value=!1})},[(d(),u("svg",O,e[16]||(e[16]=[c("use",{"xlink:href":"#icon-ios"},null,-1)]))),e[18]||(e[18]=c("br",null,null,-1)),s(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:f((function(){return e[17]||(e[17]=[w("iOS客户端")])})),_:1,__:[17]}),e[19]||(e[19]=c("div",{id:"ios",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[c("canvas",{id:"ioscanvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[g,M.value]]),p(" Android 客户端 "),h((d(),u("div",{"element-loading-text":"下载码生成中...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF"},onMousemove:e[4]||(e[4]=function(n){return P("android")}),onMouseleave:e[5]||(e[5]=function(n){return I.value=!1})},[(d(),u("svg",S,e[20]||(e[20]=[c("use",{"xlink:href":"#icon-android"},null,-1)]))),e[22]||(e[22]=c("br",null,null,-1)),s(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:f((function(){return e[21]||(e[21]=[w("Android客户端")])})),_:1,__:[21]}),e[23]||(e[23]=c("div",{id:"android",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[c("canvas",{id:"canvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[g,L.value]])])]})),_:1})])])}}};e("default",i(j,[["__scopeId","data-v-4f60b33c"],["__file","D:/asec-platform/frontend/portal/src/view/client/download.vue"]]))}}}))}();
