
import _ from 'lodash'

// 客户端参数不支持数字，数字转换为字符串
const ipcHelpers = {

  // 参数全部转为字符串
  paramsToString(params) {
    function _toString(params) {
      if (Object.prototype.toString.call(params) === '[object Array]') {
        params.forEach((item, index) => {
          if (typeof (item) === 'number') {
            params[index] = item + ''
          } else if (typeof (item) === 'object') {
            _toString(item)
          }
        })
      } else if (Object.prototype.toString.call(params) === '[object Object]') {
        for (const k in params) {
          if (params.hasOwnProperty(k)) {
            if (typeof (params[k]) === 'number') {
              params[k] += ''
            } else if (typeof (params[k]) === 'object') {
              _toString(params[k])
            }
          }
        }
      }
    }
    _toString(params)
    return params
  },

  serialId: 0,

  getStrSerialId() {
    this.serialId++

    // 如果serialId能被10整除，就再+1
    if (this.serialId % 10 === 0) {
      this.serialId++
    }

    // 如果serialId大于9000000000，就重置为1
    if (this.serialId > 900000000) {
      this.serialId = 1
    }

    return this.serialId
  },

  // 客户端要求序列号必须为多少位的字符串
  getStrSerial(processId = 0) {
    const strSerial = this.getStrSerialId() * 100000000
    return String(parseInt(String(strSerial).substr(0, 9)) + parseInt(processId))
  },

  // 参数处理
  interceptors(params, action) {
    const sortKey = this.sortParamsKey(params.strBody) // 参数需要按首字母排序
    this.paramsToString(params)
    // strBody 参数格式化
    if (sortKey) {
      const Argument = []
      sortKey.forEach(key => {
        const obj = { Name: key, Value: encodeURIComponent(params.strBody[key]) } // 客户端转发会将post参数拼接到url，需要转码
        Argument.push(obj)
      })
      params.strBody = { Argument }
    }

    return JSON.stringify(params)
  },
  sortParamsKey(params) {
    if (!params || JSON.stringify(params.strBody) === '{}') {
      return ''
    }
    const keys = Object.keys(params)
    const newKeys = keys.sort(
      (a, b) => {
        const compare = function(str1, str2) {
          if (str1 > str2) {
            return 1
          } else if (str1 < str2) {
            return -1
          }
          return 0
        }
        a = _.toString(a)
        b = _.toString(b)

        const max = _.max([a.length, b.length])
        for (let index = 0; index < max; index++) {
          const tmp = compare(a.charAt(index), b.charAt(index))
          if (tmp !== 0) {
            return tmp
          }
        }
        return 0
      })
    return newKeys
  },

  getStrLen(str) {
    // 中文字符, 如用用String.length属性, 中文算一个字符的
    let totallength = 0
    if (!str) {
      return totallength
    }

    for (var i = 0; i < str.length; i++) {
      var intCode = str.charCodeAt(i)
      if (intCode >= 0 && intCode <= 128) {
        totallength = totallength + 1// 非中文单个字符长度加1
      } else {
        totallength = totallength + 2// 中文字符长度则加2
      }
    }
    return totallength
  },

  formatNum(num, formatlen) {
    let sn = '' + num + ''
    const l = formatlen - sn.length
    for (var i = 0; i < l; i++) {
      sn = '0' + sn
    }

    return sn
  },

  getSubStr(Str, First, Last) {
    let nStart = Str.indexOf(First)
    if (parseInt(nStart) === -1) {
      return ''
    }
    nStart = nStart + First.length
    const nStop = Str.indexOf(Last, nStart)
    if (parseInt(nStop) === -1) {
      return ''
    }
    return Str.substring(nStart, nStop)
  }
}
export default ipcHelpers
