/*! 
 Build based on gin-vue-admin 
 Time : 1749829828000 */
import{_ as e,D as t,o as n,d as u,e as a,F as c,i as o,I as i,t as r}from"./index.a5cb1178.js";const s=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],m={class:"layout-aside"},d={class:"menu-wrapper"},l=["onClick"],h={class:"icon menu-item-icon","aria-hidden":"true"},p=["xlink:href"],g={class:"menu-item-title"};const f=e({name:"ClientMenu",data:()=>({currentRouteCode:"101"}),computed:{computedMenu(){return this.computedMenuFun()}},watch:{$route:{handler(e,t){if(logger.log("路由变化",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun(){const e=[];return s&&s.forEach((t=>{if(t.meta&&t.meta.menu){const{name:n,icon:u,uiId:a}=t.meta.menu,c={name:n,icon:u,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:a};e.push(c)}})),e},changeMenu(e,n={},u=0){logger.log("切换菜单:",e,n);const a=t.getClientParams(),c={...n,...a};logger.log("切换菜单携带客户端参数:",c),this.$router.push({path:e,query:c}),this.currentRouteCode=this.cutOut(u)},routerInterceptor(e){const t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:e=>e&&e.length?e.substr(0,3):e}},[["render",function(e,t,s,f,C,M){return n(),u("div",m,[a("ul",d,[(n(!0),u(c,null,o(M.computedMenu,(e=>(n(),u("li",{key:e.code,class:i(["menu-item",M.cutOut(e.code)===C.currentRouteCode?"active-menu-item":""]),onClick:t=>M.changeMenu(e.url,e.params,e.code)},[(n(),u("svg",h,[a("use",{"xlink:href":"#"+e.icon},null,8,p)])),a("div",g,r(e.name),1)],10,l)))),128))])])}],["__scopeId","data-v-aa2fe5d8"],["__file","D:/asec-platform/frontend/portal/src/view/client/menu.vue"]]);export{f as default};
