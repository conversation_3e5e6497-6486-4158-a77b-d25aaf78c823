/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import{b as e,a,u as o,U as s,r as l,K as n,Q as t,c as i,V as c,p as r,h as d,D as u,o as v,f as m,w as p,e as f,H as g,j as h,O as b,d as y,T as w,F as x,i as k,k as C,t as F,m as j,S as I,E as O,W as U,g as R,X as _,A as z}from"./index.5a1fa56a.js";import{_ as A}from"./ASD.492c8837.js";import M from"./index.56b46bb7.js";import"./index-browser-esm.c2d3b5c9.js";import"./index.836cb6fd.js";import"./menuItem.37f8bc48.js";import"./asyncSubmenu.79aa3c88.js";
/*! js-cookie v3.0.5 | MIT */function S(e){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var s in o)e[s]=o[s]}return e}var D=function e(a,o){function s(e,s,l){if("undefined"!=typeof document){"number"==typeof(l=S({},o,l)).expires&&(l.expires=new Date(Date.now()+864e5*l.expires)),l.expires&&(l.expires=l.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var n="";for(var t in l)l[t]&&(n+="; "+t,!0!==l[t]&&(n+="="+l[t].split(";")[0]));return document.cookie=e+"="+a.write(s,e)+n}}return Object.create({set:s,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var o=document.cookie?document.cookie.split("; "):[],s={},l=0;l<o.length;l++){var n=o[l].split("="),t=n.slice(1).join("=");try{var i=decodeURIComponent(n[0]);if(s[i]=a.read(t,i),e===i)break}catch(c){}}return e?s[e]:s}},remove:function(e,a){s(e,"",S({},a,{expires:-1}))},withAttributes:function(a){return e(this.converter,S({},this.attributes,a))},withConverter:function(a){return e(S({},this.converter,a),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const B={key:0,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},T={key:1,class:"icon",style:{color:"#FFFFFF","font-size":"14px"},"aria-hidden":"true"},L={class:"header-row"},N={class:"header-col"},E={class:"header-cont"},W={class:"header-content pd-0"},J={class:"breadcrumb-col"},V={class:"breadcrumb"},$={class:"user-col"},H={class:"right-box"},K={class:"dp-flex justify-content-center align-items height-full width-full"},Q={class:"header-avatar",style:{cursor:"pointer"}},X={style:{"margin-right":"9px",color:"#252631"}},q={class:"icon",style:{"font-size":"10px",color:"#252631",opacity:"0.5"},"aria-hidden":"true"},G={key:0,class:"dropdown-menu"},P=Object.assign({name:"Layout"},{setup(S){const P=e(),Y=a(),Z=o(),ee=s(),ae=l(!0),oe=l(!1),se=l(!1),le=l("7"),ne=()=>{document.body.clientWidth;se.value=!1,oe.value=!1,ae.value=!0};ne();const te=l(!1);n((()=>{t.emit("collapse",ae.value),t.emit("mobile",se.value),t.on("reload",ue),t.on("showLoading",(()=>{te.value=!0})),t.on("closeLoading",(()=>{te.value=!1})),window.onresize=()=>(ne(),t.emit("collapse",ae.value),void t.emit("mobile",se.value)),P.loadingInstance&&P.loadingInstance.close()})),i((()=>"dark"===P.sideMode?"#fff":"light"===P.sideMode?"#273444":P.baseColor));const ie=i((()=>"dark"===P.sideMode?"#273444":"light"===P.sideMode?"#fff":P.sideMode)),ce=i((()=>Z.meta.matched)),re=l(!0);let de=null;const ue=async()=>{de&&window.clearTimeout(de),de=window.setTimeout((async()=>{if(Z.meta.keepAlive)re.value=!1,await c(),re.value=!0;else{const e=Z.meta.title;Y.push({name:"Reload",params:{title:e}})}}),400)},ve=l(!1),me=l(!1),pe=()=>{ae.value=!ae.value,oe.value=!ae.value,ve.value=!ae.value,t.emit("collapse",ae.value)},fe=()=>{me.value=!me.value},ge=()=>{Y.push({name:"person"})};return r("day",le),(e,a)=>{const o=d("base-aside"),s=d("router-view"),n=d("base-main"),t=d("base-container"),i=u("loading");return v(),m(t,{class:"layout-cont"},{default:p((()=>[f("div",{class:g([[oe.value?"openside":"hideside",se.value?"mobile":""],"layout-wrapper"])},[f("div",{class:g([[ve.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(ve.value=!ve.value,oe.value=!!ae.value,void pe()))},null,2),h(o,{class:"main-cont main-left gva-aside",collapsed:ae.value},{default:p((()=>[f("div",{class:g(["tilte",[oe.value?"openlogoimg":"hidelogoimg"]]),style:b({background:ie.value})},a[3]||(a[3]=[f("img",{alt:"",class:"logoimg",src:A},null,-1)]),6),h(M,{class:"aside"}),f("div",{class:"footer",style:b({background:ie.value})},[f("div",{class:"menu-total",onClick:pe},[ae.value?(v(),y("svg",B,a[4]||(a[4]=[f("use",{"xlink:href":"#icon-expand"},null,-1)]))):(v(),y("svg",T,a[5]||(a[5]=[f("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)])),_:1},8,["collapsed"]),h(n,{class:"main-cont main-right"},{default:p((()=>[h(w,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:p((()=>[f("div",{style:b({width:`calc(100% - ${se.value?"0px":ae.value?"54px":"220px"})`}),class:"topfix"},[f("div",L,[f("div",N,[f("header",E,[f("div",W,[a[10]||(a[10]=f("div",{class:"header-menu-col",style:{"z-index":"100"}},null,-1)),f("div",J,[f("nav",V,[(v(!0),y(x,null,k(ce.value.slice(1,ce.value.length),(e=>(v(),y("div",{key:e.path,class:"breadcrumb-item"},[C(F(j(I)(e.meta.topTitle||"",j(Z)))+" ",1),"总览"===e.meta.title?O((v(),y("select",{key:0,"onUpdate:modelValue":a[1]||(a[1]=e=>le.value=e),class:"day-select form-select"},a[6]||(a[6]=[f("option",{value:"7"},"最近7天",-1),f("option",{value:"30"},"最近30天",-1),f("option",{value:"90"},"最近90天",-1)]),512)),[[U,le.value]]):R("",!0)])))),128))])]),f("div",$,[f("div",H,[f("div",{class:"dropdown",onClick:fe},[f("div",K,[f("span",Q,[f("span",X,F(j(P).userInfo.displayName?j(P).userInfo.displayName:j(P).userInfo.name),1),(v(),y("svg",q,a[7]||(a[7]=[f("use",{"xlink:href":"#icon-caret-bottom"},null,-1)])))])]),me.value?(v(),y("div",G,[f("div",{class:"dropdown-item",onClick:ge},a[8]||(a[8]=[f("svg",{class:"icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-avatar"})],-1),C(" 个人信息 ")])),f("div",{class:"dropdown-item",onClick:a[2]||(a[2]=e=>(async()=>{document.location.protocol,document.location.host;const e={action:1,msg:"",platform:document.location.hostname},a=l({}),o=l("ws://127.0.0.1:50001"),s=navigator.platform;0!==s.indexOf("Mac")&&"MacIntel"!==s||(o.value="wss://127.0.0.1:50001");const n=async e=>{console.log(e,"0"),await a.value.send(e)},t=async()=>{console.log("socket断开链接"),await a.value.close()};console.log(`asecagent://?web=${JSON.stringify(e)}`),await P.LoginOut(),a.value=new WebSocket(o.value),a.value.onopen=async()=>{console.log("socket连接成功"),await n(JSON.stringify(e))},a.value.onmessage=async e=>{console.log(e),await t()},a.value.onerror=()=>{console.log("socket连接错误")},D.remove("asce_sms")})())},a[9]||(a[9]=[f("svg",{class:"icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-reading-lamp"})],-1),C(" 登 出 ")]))])):R("",!0)])])])])])])])],4)])),_:1}),re.value?O((v(),m(s,{key:0,"element-loading-text":"正在加载中",class:"admin-box"},{default:p((({Component:e})=>[f("div",null,[h(w,{mode:"out-in",name:"el-fade-in-linear"},{default:p((()=>[(v(),m(_,{include:j(ee).keepAliveRouters},[(v(),m(z(e)))],1032,["include"]))])),_:2},1024)])])),_:1})),[[i,te.value]]):R("",!0)])),_:1})],2)])),_:1})}}});export{P as default};
