
import { createHtmlPlugin } from 'vite-plugin-html'
// import usePluginImport from 'vite-plugin-importer';
// import { viteLogo } from './src/core/config'
import Banner from 'vite-plugin-banner'
import * as path from 'path'
import * as dotenv from 'dotenv'
import * as fs from 'fs'
import vuePlugin from '@vitejs/plugin-vue'
// @see https://cn.vitejs.dev/config/
export default ({
  command,
  mode
}) => {
  const NODE_ENV = process.env.NODE_ENV || 'development'
  const envFiles = [
    `.env.${NODE_ENV}`
  ]
  for (const file of envFiles) {
    const envConfig = dotenv.parse(fs.readFileSync(file))
    for (const k in envConfig) {
      process.env[k] = envConfig[k]
    }
  }

  // viteLogo(process.env)

  const timestamp = Date.parse(new Date())

  // const rollupOptions = {
  //   output: {
  //     entryFileNames: `gva/gin-vue-admin-[name].${timestamp}.js`,
  //     chunkFileNames: `js/gin-vue-admin-[name].${timestamp}.js`,
  //     assetFileNames: `assets/gin-vue-admin-[name].${timestamp}.[ext]`
  //   }
  // }

  const optimizeDeps = {}

  const alias = {
    '@': path.resolve(__dirname, './src'),
    'vue$': 'vue/dist/vue.runtime.esm-bundler.js',
  }

  const esbuild = {}

  return {
    base: './', // index.html文件所在位置
    root: './', // js导入的资源路径，src
    resolve: {
      alias,
    },
    define: {
      'process.env': {}
    },
    server: {
      // 如果使用docker-compose开发模式，设置为false
      open: true,
      port: process.env.VITE_CLI_PORT,
      proxy: {
        // 把key的路径代理到target位置
        // detail: https://cli.vuejs.org/config/#devserver-proxy
        [process.env.VITE_BASE_API]: { // 需要代理的路径   例如 '/api'
          target: `${process.env.VITE_BASE_PATH}:${process.env.VITE_SERVER_PORT}/`, // 代理到 目标路径
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp('^' + process.env.VITE_BASE_API), ''),
        }
      },
    },
    build: {
      target: ['es2020', 'chrome63', 'firefox67', 'safari11.1'],
      minify: 'esbuild', // 是否进行压缩,boolean | 'terser' | 'esbuild',默认使用esbuild
      manifest: false, // 是否产出manifest.json
      sourcemap: false, // 是否产出sourcemap.json
      outDir: 'dist', // 产出目录
      rollupOptions: {
        output: {
          // 确保生成的模块可以在本地文件系统中正常工作
          format: 'iife', // 使用 IIFE 格式而不是 ES 模块
          inlineDynamicImports: true, // 内联动态导入
          entryFileNames: 'assets/[name].[hash].js',
          chunkFileNames: 'assets/[name].[hash].js',
          assetFileNames: 'assets/[name].[hash].[ext]'
        }
      },
    },
    esbuild,
    optimizeDeps,
    plugins: [
      vuePlugin(),
      [Banner(`\n Build based on gin-vue-admin \n Time : ${timestamp}`)],
      createHtmlPlugin({
        inject: {
          injectData: {
            timestamp: new Date().getTime(),
          },
          tags: [{
            head: [
              {
                tag: 'script',
                attrs: {
                  src: '/js/main.js?timestamp=$timestamp',
                },
              },
            ],
          },]
        },
      }),
      // 自定义插件：移除 HTML 中的 type="module"
      {
        name: 'remove-module-type',
        transformIndexHtml: {
          enforce: 'post',
          transform(html) {
            return html
              .replace(/type="module"\s+crossorigin/g, 'crossorigin')
              .replace(/type="module"/g, '');
          }
        }
      }
    ],
    css: {
      preprocessorOptions: {
        less: {
          // 支持内联 JavaScript
          javascriptEnabled: true,
        },
        scss: {
          additionalData: `@import "@/style/variables.scss";`
        }
      }
    },
  }
}
