/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
import{_ as a,u as e,a as r,b as t,r as s,N as i,p as c,f as l,L as o,M as n}from"./index.2a422357.js";const u=a(Object.assign({name:"WxOAuthCallback"},{setup(a){const u=e(),p=r(),y=t(),{code:d,state:_,auth_type:f,redirect_url:h}=u.query,m=s(Array.isArray(_)?_[0]:_),x=s("");return i((async()=>{const a=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const a={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(d)?d[0]:d}};!0===await y.LoginIn(a,"qiyewx_oauth",m.value)?await p.push({name:"verify",query:{redirect_url:h}}):n.error("登录失败，请重试")}catch(e){console.error("登录过程出错:",e),n.error("登录过程出错，请重试")}finally{a.close()}})),c("userName",x),(a,e)=>l(" 空模板，因为所有逻辑都在 script 中处理 ")}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/wx_oauth_callback.vue"]]);export{u as default};
