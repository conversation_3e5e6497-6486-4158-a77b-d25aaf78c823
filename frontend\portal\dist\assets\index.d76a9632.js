/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
import{_ as e,o as r,d as n,e as s,f as i}from"./index.4f1b43e7.js";const l=""+new URL("notFound.4e921f05.png",import.meta.url).href;const t=e({name:"Error"},[["render",function(e,t,o,a,f,c){return r(),n("div",null,t[0]||(t[0]=[s("div",{class:"big"},[s("div",{class:"inner"},[s("img",{src:l}),s("p",null,"未知错误"),s("p",{style:{"font-size":"18px","line-height":"40px"}},"常见问题为当前此角色无访问权限，如果确定要使用，请联系管理员进行分配"),s("p",null,"↓"),i('        <img src="" class="leftPic">')])],-1)]))}],["__file","D:/asec-platform/frontend/portal/src/view/error/index.vue"]]);export{t as default};
