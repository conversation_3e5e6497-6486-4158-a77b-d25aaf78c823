/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
!function(){function n(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,i,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",r=o.toStringTag||"@@toStringTag";function l(n,o,a,r){var l=o&&o.prototype instanceof s?o:s,c=Object.create(l.prototype);return e(c,"_invoke",function(n,e,o){var a,r,l,s=0,c=o||[],u=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(n,e){return a=n,r=0,l=t,f.n=e,d}};function p(n,e){for(r=n,l=e,i=0;!u&&s&&!o&&i<c.length;i++){var o,a=c[i],p=f.p,v=a[2];n>3?(o=v===e)&&(l=a[(r=a[4])?5:(r=3,3)],a[4]=a[5]=t):a[0]<=p&&((o=n<2&&p<a[1])?(r=0,f.v=e,f.n=a[1]):p<v&&(o=n<3||a[0]>e||e>v)&&(a[4]=n,a[5]=e,f.n=v,r=0))}if(o||n>1)return d;throw u=!0,e}return function(o,c,v){if(s>1)throw TypeError("Generator is already running");for(u&&1===c&&p(c,v),r=c,l=v;(i=r<2?t:l)||!u;){a||(r?r<3?(r>1&&(f.n=-1),p(r,l)):f.n=l:f.v=l);try{if(s=2,a){if(r||(o="next"),i=a[o]){if(!(i=i.call(a,l)))throw TypeError("iterator result is not an object");if(!i.done)return i;l=i.value,r<2&&(r=0)}else 1===r&&(i=a.return)&&i.call(a),r<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),r=1);a=t}else if((i=(u=f.n<0)?l:n.call(e,f))!==d)break}catch(i){a=t,r=1,l=i}finally{s=1}}return{value:i,done:u}}}(n,a,r),!0),c}var d={};function s(){}function c(){}function u(){}i=Object.getPrototypeOf;var f=[][a]?i(i([][a]())):(e(i={},a,(function(){return this})),i),p=u.prototype=s.prototype=Object.create(f);function v(n){return Object.setPrototypeOf?Object.setPrototypeOf(n,u):(n.__proto__=u,e(n,r,"GeneratorFunction")),n.prototype=Object.create(p),n}return c.prototype=u,e(p,"constructor",u),e(u,"constructor",c),c.displayName="GeneratorFunction",e(u,r,"GeneratorFunction"),e(p),e(p,r,"Generator"),e(p,a,(function(){return this})),e(p,"toString",(function(){return"[object Generator]"})),(n=function(){return{w:l,m:v}})()}function e(n,t,i,o){var a=Object.defineProperty;try{a({},"",{})}catch(n){a=0}e=function(n,t,i,o){if(t)a?a(n,t,{value:i,enumerable:!o,configurable:!o,writable:!o}):n[t]=i;else{var r=function(t,i){e(n,t,(function(n){return this._invoke(t,i,n)}))};r("next",0),r("throw",1),r("return",2)}},e(n,t,i,o)}function t(n,e,t,i,o,a,r){try{var l=n[a](r),d=l.value}catch(n){return void t(n)}l.done?e(d):Promise.resolve(d).then(i,o)}System.register(["./index-legacy.4bb28e53.js","./browser-legacy.56848361.js"],(function(e,i){"use strict";var o,a,r,l,d,s,c,u,f,p,v,w,h,g,y,m=document.createElement("style");return m.textContent='@charset "UTF-8";.icon[data-v-4f60b33c]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.client[data-v-4f60b33c]{height:100vh;text-align:center;background:#FFFFFF;max-height:calc(100vh - 68px)}.client .el-main[data-v-4f60b33c]{height:100%}.client .el-main div div:hover .window-show[data-v-4f60b33c]{display:none}.client .el-main div div:hover .window-hidden[data-v-4f60b33c]{display:block!important}.client .el-main div div:hover .window-hidden span[data-v-4f60b33c]{margin-top:42px!important}.loading-overlay[data-v-4f60b33c]{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(241,248,255,.9);display:flex;align-items:center;justify-content:center;z-index:10;border-radius:4px}.loading-spinner[data-v-4f60b33c]{display:flex;flex-direction:column;align-items:center;gap:8px}.spinner[data-v-4f60b33c]{width:24px;height:24px;border:2px solid #f3f3f3;border-top:2px solid #409eff;border-radius:50%;animation:spin-4f60b33c 1s linear infinite}@keyframes spin-4f60b33c{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-text[data-v-4f60b33c]{font-size:12px;color:#606266}\n',document.head.appendChild(m),{setters:[function(n){o=n._,a=n.r,r=n.h,l=n.o,d=n.d,s=n.e,c=n.j,u=n.w,f=n.f,p=n.k,v=n.g,w=n.I,h=n.M},function(n){g=n.g,y=n.b}],execute:function(){var i={class:"client"},m={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}},b={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},x={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px","margin-left":"39%",display:"none"}},_={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},k={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-left":"39%","margin-top":"60px",display:"none"}},F={key:0,class:"loading-overlay"},j={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},O={key:0,class:"loading-overlay"},S={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},E={__name:"download",setup:function(e){var o=a(""),E=a(""),z=a(!1),C=a(!1),M=a(!1),L=a(!1),R=a({windows:0,darwin:0}),T=function(n){return 100===n?"完成":"".concat(n,"%")},U=function(n,e){return new Promise((function(t,i){var o=new XMLHttpRequest;o.open("GET",n,!0),o.responseType="blob",o.onprogress=function(n){if(n.lengthComputable){var t=n.loaded/n.total*100;R.value[e]=Math.round(t)}},o.onload=function(){200===o.status?t(o.response):i(new Error("下载失败"))},o.onerror=function(){i(new Error("网络错误"))},o.send()}))},I=function(n,e){if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(n,e);else{var t=document.createElement("a"),i=document.querySelector("body");t.href=window.URL.createObjectURL(n),t.download=e,t.style.display="none",i.appendChild(t),t.click(),i.removeChild(t),window.URL.revokeObjectURL(t.href)}G()},G=function(){z.value=!1,C.value=!1,M.value=!1,L.value=!1,Object.keys(R.value).forEach((function(n){R.value[n]=0}))},P=a(!1),B=function(){var e,i=(e=n().m((function e(t){var i,a,r,l,d,s,c,u,f,p,v,w,m,b,x,_,k,F,j;return n().w((function(n){for(;;)switch(n.n){case 0:if("android"!==t&&"ios"!==t||!P.value){n.n=1;break}return n.a(2);case 1:return P.value=!0,(i={windows:z,darwin:C,ios:M,android:L}[t]).value=!0,n.p=2,n.n=3,g({platform:t});case 3:if(0!==(a=n.v).data.code){n.n=10;break}if("ios"!==t){n.n=5;break}return n.n=4,y.toDataURL(a.data.data.download_url);case 4:r=n.v,l=document.getElementById("ioscanvas"),E.value=r,l&&(d=l.getContext("2d"),(s=new Image).onload=function(){l.width=s.width,l.height=s.height,d.drawImage(s,0,0)},s.src=r),n.n=9;break;case 5:if("android"!==t){n.n=7;break}return c=window.location.port,u=new URL(a.data.data.download_url),c?u.toString().includes("asec-deploy")?f=a.data.data.download_url:(u.port=c,f=u.toString()):(u.port="",f=u.toString()),n.n=6,y.toDataURL(f);case 6:p=n.v,v=document.getElementById("canvas"),o.value=p,v&&(w=v.getContext("2d"),(m=new Image).onload=function(){v.width=m.width,v.height=m.height,w.drawImage(m,0,0)},m.src=p),n.n=9;break;case 7:return b=window.location.port,x=new URL(a.data.data.download_url),b?(x.toString().includes("asec-deploy")?_=a.data.data.download_url:(x.port=b,_=x.toString()),k=a.data.data.latest_filename.replace(/@(\d+)/,"@".concat(b))):(x.port="",_=x.toString(),k=a.data.data.latest_filename),n.n=8,U(_,t);case 8:F=n.v,I(F,k);case 9:n.n=11;break;case 10:throw new Error(a.data.msg);case 11:n.n=13;break;case 12:n.p=12,j=n.v,h({type:"error",message:j.message||"下载失败，请联系管理员"});case 13:return n.p=13,i.value=!1,n.f(13);case 14:return n.a(2)}}),e,null,[[2,12,13,14]])})),function(){var n=this,i=arguments;return new Promise((function(o,a){var r=e.apply(n,i);function l(n){t(r,o,a,l,d,"next",n)}function d(n){t(r,o,a,l,d,"throw",n)}l(void 0)}))});return function(n){return i.apply(this,arguments)}}();return function(n,e){var t=r("base-link"),o=r("base-progress"),a=r("base-main");return l(),d("div",null,[s("div",i,[c(a,null,{default:u((function(){return[s("div",m,[f(" Windows 客户端 "),s("div",{style:{float:"left","margin-right":"5%",width:"209px",height:"209px",background:"#F1F8FF"},onClick:e[0]||(e[0]=function(n){return B("windows")})},[(l(),d("svg",b,e[6]||(e[6]=[s("use",{"xlink:href":"#icon-windows"},null,-1)]))),(l(),d("svg",x,e[7]||(e[7]=[s("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[10]||(e[10]=s("br",null,null,-1)),c(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:u((function(){return e[8]||(e[8]=[p("Windows客户端")])})),_:1,__:[8]}),c(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:u((function(){return e[9]||(e[9]=[p("点击下载Windows客户端")])})),_:1,__:[9]}),z.value?(l(),v(o,{key:0,percentage:R.value.windows,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):f("v-if",!0)]),f(" Mac 客户端 "),s("div",{style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onClick:e[1]||(e[1]=function(n){return B("darwin")})},[(l(),d("svg",_,e[11]||(e[11]=[s("use",{"xlink:href":"#icon-mac"},null,-1)]))),(l(),d("svg",k,e[12]||(e[12]=[s("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[15]||(e[15]=s("br",null,null,-1)),c(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:u((function(){return e[13]||(e[13]=[p("Mac客户端")])})),_:1,__:[13]}),c(t,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:u((function(){return e[14]||(e[14]=[p("点击下载Mac客户端")])})),_:1,__:[14]}),C.value?(l(),v(o,{key:0,percentage:R.value.darwin,format:T,style:{"margin-top":"10px"}},null,8,["percentage"])):f("v-if",!0)]),f(" iOS 客户端 "),s("div",{class:w(["ios-container",{loading:M.value}]),style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%",position:"relative"},onMousemove:e[2]||(e[2]=function(n){return B("ios")}),onMouseleave:e[3]||(e[3]=function(n){return P.value=!1})},[M.value?(l(),d("div",F,e[16]||(e[16]=[s("div",{class:"loading-spinner"},[s("div",{class:"spinner"}),s("div",{class:"loading-text"},"下载码生成中...")],-1)]))):f("v-if",!0),(l(),d("svg",j,e[17]||(e[17]=[s("use",{"xlink:href":"#icon-ios"},null,-1)]))),e[19]||(e[19]=s("br",null,null,-1)),c(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:u((function(){return e[18]||(e[18]=[p("iOS客户端")])})),_:1,__:[18]}),e[20]||(e[20]=s("div",{id:"ios",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[s("canvas",{id:"ioscanvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],34),f(" Android 客户端 "),s("div",{class:w(["android-container",{loading:L.value}]),style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF",position:"relative"},onMousemove:e[4]||(e[4]=function(n){return B("android")}),onMouseleave:e[5]||(e[5]=function(n){return P.value=!1})},[L.value?(l(),d("div",O,e[21]||(e[21]=[s("div",{class:"loading-spinner"},[s("div",{class:"spinner"}),s("div",{class:"loading-text"},"下载码生成中...")],-1)]))):f("v-if",!0),(l(),d("svg",S,e[22]||(e[22]=[s("use",{"xlink:href":"#icon-android"},null,-1)]))),e[24]||(e[24]=s("br",null,null,-1)),c(t,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:u((function(){return e[23]||(e[23]=[p("Android客户端")])})),_:1,__:[23]}),e[25]||(e[25]=s("div",{id:"android",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[s("canvas",{id:"canvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],34)])]})),_:1})])])}}};e("default",o(E,[["__scopeId","data-v-4f60b33c"],["__file","D:/asec-platform/frontend/portal/src/view/client/download.vue"]]))}}}))}();
