<template>
  <div 
    :class="[
      'base-progress',
      `base-progress--${type}`,
      { 'base-progress--text-inside': textInside }
    ]"
    :style="progressStyle"
  >
    <div class="base-progress__outer">
      <div class="base-progress__inner">
        <div 
          class="base-progress__bar"
          :style="barStyle"
        >
          <div 
            v-if="textInside" 
            class="base-progress__text-inside"
          >
            {{ displayText }}
          </div>
        </div>
      </div>
    </div>
    <div 
      v-if="!textInside && showText" 
      class="base-progress__text"
    >
      {{ displayText }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseProgress',
  props: {
    percentage: {
      type: Number,
      default: 0,
      validator: (value) => value >= 0 && value <= 100
    },
    type: {
      type: String,
      default: 'line',
      validator: (value) => ['line', 'circle', 'dashboard'].includes(value)
    },
    strokeWidth: {
      type: Number,
      default: 6
    },
    textInside: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      default: '',
      validator: (value) => ['', 'success', 'exception', 'warning'].includes(value)
    },
    color: {
      type: [String, Array, Function],
      default: ''
    },
    width: {
      type: Number,
      default: 126
    },
    showText: {
      type: Boolean,
      default: true
    },
    strokeLinecap: {
      type: String,
      default: 'round',
      validator: (value) => ['butt', 'round', 'square'].includes(value)
    },
    format: {
      type: Function,
      default: null
    }
  },
  computed: {
    progressStyle() {
      return {
        height: this.type === 'line' ? `${this.strokeWidth}px` : 'auto',
        width: this.type !== 'line' ? `${this.width}px` : '100%'
      }
    },
    barStyle() {
      const style = {
        width: `${this.percentage}%`,
        borderRadius: this.strokeLinecap === 'round' ? `${this.strokeWidth / 2}px` : '0'
      }
      
      if (this.color) {
        if (typeof this.color === 'string') {
          style.backgroundColor = this.color
        } else if (Array.isArray(this.color)) {
          style.background = `linear-gradient(to right, ${this.color.join(', ')})`
        } else if (typeof this.color === 'function') {
          style.backgroundColor = this.color(this.percentage)
        }
      } else {
        style.backgroundColor = this.statusColor
      }
      
      return style
    },
    statusColor() {
      const colors = {
        success: '#67c23a',
        exception: '#f56c6c',
        warning: '#e6a23c'
      }
      
      if (this.status && colors[this.status]) {
        return colors[this.status]
      }
      
      if (this.percentage === 100) {
        return colors.success
      }
      
      return '#536ce6'
    },
    displayText() {
      if (this.format && typeof this.format === 'function') {
        return this.format(this.percentage)
      }
      
      if (this.status === 'success') {
        return '✓'
      } else if (this.status === 'exception') {
        return '✕'
      }
      
      return `${this.percentage}%`
    }
  }
}
</script>

<style lang="scss" scoped>
.base-progress {
  position: relative;
  line-height: 1;
  
  &--line {
    display: flex;
    align-items: center;
    width: 100%;
  }
  
  &--circle,
  &--dashboard {
    display: inline-block;
  }
  
  &__outer {
    height: 100%;
    background-color: #f5f7fa;
    border-radius: 100px;
    overflow: hidden;
    position: relative;
    vertical-align: middle;
    flex: 1;
  }
  
  &__inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #f5f7fa;
    border-radius: 100px;
    overflow: hidden;
    width: 100%;
  }
  
  &__bar {
    position: relative;
    height: 100%;
    background-color: #536ce6;
    border-radius: 100px;
    transition: width 0.6s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__text {
    font-size: 14px;
    color: #606266;
    margin-left: 8px;
    min-width: 50px;
    text-align: left;
    
    .base-progress--line & {
      display: inline-block;
      vertical-align: middle;
    }
  }
  
  &__text-inside {
    color: #fff;
    font-size: 12px;
    margin: 0;
    white-space: nowrap;
    text-align: center;
    width: 100%;
  }
  
  // 状态样式
  &--success {
    .base-progress__bar {
      background-color: #67c23a;
    }
    
    .base-progress__text {
      color: #67c23a;
    }
  }
  
  &--exception {
    .base-progress__bar {
      background-color: #f56c6c;
    }
    
    .base-progress__text {
      color: #f56c6c;
    }
  }
  
  &--warning {
    .base-progress__bar {
      background-color: #e6a23c;
    }
    
    .base-progress__text {
      color: #e6a23c;
    }
  }
  
  // 文字内置样式
  &--text-inside {
    .base-progress__outer {
      margin-right: 0;
    }
  }
}
</style>
