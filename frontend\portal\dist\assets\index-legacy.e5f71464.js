/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,o,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.toStringTag||"@@toStringTag";function c(e,i,r,a){var c=i&&i.prototype instanceof u?i:u,s=Object.create(c.prototype);return n(s,"_invoke",function(e,n,i){var r,a,c,u=0,s=i||[],d=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,n){return r=e,a=0,c=t,f.n=n,l}};function p(e,n){for(a=e,c=n,o=0;!d&&u&&!i&&o<s.length;o++){var i,r=s[o],p=f.p,v=r[2];e>3?(i=v===n)&&(c=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=t):r[0]<=p&&((i=e<2&&p<r[1])?(a=0,f.v=n,f.n=r[1]):p<v&&(i=e<3||r[0]>n||n>v)&&(r[4]=e,r[5]=n,f.n=v,a=0))}if(i||e>1)return l;throw d=!0,n}return function(i,s,v){if(u>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,v),a=s,c=v;(o=a<2?t:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),p(a,c)):f.n=c:f.v=c);try{if(u=2,r){if(a||(i="next"),o=r[i]){if(!(o=o.call(r,c)))throw TypeError("iterator result is not an object");if(!o.done)return o;c=o.value,a<2&&(a=0)}else 1===a&&(o=r.return)&&o.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=t}else if((o=(d=f.n<0)?c:e.call(n,f))!==l)break}catch(o){r=t,a=1,c=o}finally{u=1}}return{value:o,done:d}}}(e,r,a),!0),s}var l={};function u(){}function s(){}function d(){}o=Object.getPrototypeOf;var f=[][r]?o(o([][r]())):(n(o={},r,(function(){return this})),o),p=d.prototype=u.prototype=Object.create(f);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,n(e,a,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=d,n(p,"constructor",d),n(d,"constructor",s),s.displayName="GeneratorFunction",n(d,a,"GeneratorFunction"),n(p),n(p,a,"Generator"),n(p,r,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:v}})()}function n(e,t,o,i){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}n=function(e,t,o,i){if(t)r?r(e,t,{value:o,enumerable:!i,configurable:!i,writable:!i}):e[t]=o;else{var a=function(t,o){n(e,t,(function(e){return this._invoke(t,o,e)}))};a("next",0),a("throw",1),a("return",2)}},n(e,t,o,i)}function t(e,n,t,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void t(e)}c.done?n(l):Promise.resolve(l).then(o,i)}function o(e){return function(){var n=this,o=arguments;return new Promise((function(i,r){var a=e.apply(n,o);function c(e){t(a,i,r,c,l,"next",e)}function l(e){t(a,i,r,c,l,"throw",e)}c(void 0)}))}}System.register(["./index-legacy.4bb28e53.js","./ASD-legacy.b6ffb1bc.js","./index-legacy.d25507eb.js","./index-browser-esm-legacy.6966c248.js","./index-legacy.560e870a.js","./menuItem-legacy.8fc53e8d.js","./asyncSubmenu-legacy.8b3d4a7e.js"],(function(n,t){"use strict";var i,r,a,c,l,u,s,d,f,p,v,m,g,h,x,b,y,w,k,C,j,I,_,S,z,O,N,T,U,E,A,M,R=document.createElement("style");return R.textContent='@charset "UTF-8";@media screen and (min-width: 320px) and (max-width: 750px){.el-header,.layout-cont .main-cont .breadcrumb{padding:0 5px}.layout-cont .right-box{margin-right:5px}.el-main .admin-box{margin-left:0;margin-right:0}.el-main .big.admin-box{padding:0}.el-main .big .bottom .chart-player{height:auto!important;margin-bottom:15px}.el-main .big .bottom .todoapp{background-color:#fff;padding-bottom:10px}.card .car-left,.card .car-right{width:100%;height:100%}.card{padding-left:5px;padding-right:5px}.card .text{width:100%}.card .text h4{white-space:break-spaces}.shadow{margin-left:4px;margin-right:4px}.shadow .grid-content{margin-bottom:10px;padding:0}.el-dialog{width:90%}.el-transfer .el-transfer-panel{width:40%;display:inline-block}.el-transfer .el-transfer__buttons{padding:0 5px;display:inline-block}}.dark{background-color:#273444!important;color:#fff!important}.light{background-color:#fff!important;color:#000!important}.icon-rizhi1 span{margin-left:5px}.day-select{height:23px;width:88px;margin-left:15px}.day-select div{height:23px;width:88px}.day-select div input{height:23px;width:50px;font-size:12px;color:#2972c8}.hidelogoimg{overflow:hidden!important;width:54px!important;padding-left:9px!important}.hidelogoimg .logoimg{margin-left:-15px}*,*:before,*:after{box-sizing:border-box}.layout-wrapper{display:flex;min-height:100vh;width:100%}.shadow-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:999;display:none}.shadowBg{display:block!important}.header-row{display:flex;width:100%}.header-col{flex:1}.header-content{display:flex;align-items:center;padding:0}.header-menu-col{flex:0 0 auto}.breadcrumb-col{flex:1}.user-col{flex:0 0 auto;min-width:200px}.breadcrumb{display:flex;align-items:center;gap:8px}.breadcrumb-item{display:flex;align-items:center;color:#606266;font-size:14px}.breadcrumb-item:not(:last-child):after{content:"/";margin:0 8px;color:#c0c4cc}.dropdown{position:relative;display:inline-block}.dropdown-menu{position:absolute;top:100%;right:0;background-color:#fff;border:1px solid #e4e7ed;border-radius:4px;box-shadow:0 2px 12px rgba(0,0,0,.1);z-index:1000;min-width:120px;padding:4px 0}.dropdown-item{display:flex;align-items:center;padding:8px 16px;cursor:pointer;transition:background-color .3s;font-size:14px;color:#606266}.dropdown-item .dropdown-item-icon{margin-right:4px;width:14px;height:14px}.dropdown-item:hover{background-color:#f5f7fa}.dropdown-item .icon{margin-right:8px;font-size:14px}.router-view-container{position:relative;flex:1;margin-left:35px}.loading-overlay{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,.9);display:flex;align-items:center;justify-content:center;z-index:9999}.loading-spinner{display:flex;flex-direction:column;align-items:center;gap:12px}.spinner{width:32px;height:32px;border:3px solid #f3f3f3;border-top:3px solid #409eff;border-radius:50%;animation:spin 1s linear infinite}.menu-footer-icon{color:#fff;font-size:14px;width:16px;height:16px}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-text{font-size:14px;color:#606266}\n',document.head.appendChild(R),{setters:[function(e){i=e._,r=e.b,a=e.a,c=e.u,l=e.U,u=e.r,s=e.N,d=e.P,f=e.K,p=e.c,v=e.V,m=e.p,g=e.h,h=e.o,x=e.g,b=e.w,y=e.e,w=e.I,k=e.j,C=e.B,j=e.f,I=e.d,_=e.T,S=e.F,z=e.i,O=e.t,N=e.m,T=e.R,U=e.W,E=e.A},function(e){A=e._},function(e){M=e.default},function(){},function(){},function(){},function(){}],execute:function(){/*! js-cookie v3.0.5 | MIT */function t(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)e[o]=t[o]}return e}var R=function e(n,o){function i(e,i,r){if("undefined"!=typeof document){"number"==typeof(r=t({},o,r)).expires&&(r.expires=new Date(Date.now()+864e5*r.expires)),r.expires&&(r.expires=r.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var c in r)r[c]&&(a+="; "+c,!0!==r[c]&&(a+="="+r[c].split(";")[0]));return document.cookie=e+"="+n.write(i,e)+a}}return Object.create({set:i,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],o={},i=0;i<t.length;i++){var r=t[i].split("="),a=r.slice(1).join("=");try{var c=decodeURIComponent(r[0]);if(o[c]=n.read(a,c),e===c)break}catch(l){}}return e?o[e]:o}},remove:function(e,n){i(e,"",t({},n,{expires:-1}))},withAttributes:function(n){return e(this.converter,t({},this.attributes,n))},withConverter:function(n){return e(t({},this.converter,n),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(n)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),B={key:0,class:"icon menu-footer-icon","aria-hidden":"true"},D={key:1,class:"icon menu-footer-icon","aria-hidden":"true"},F={class:"header-row"},G={class:"header-col"},P={class:"header-cont"},L={class:"header-content pd-0"},q={class:"breadcrumb-col"},J={class:"breadcrumb"},V={class:"user-col"},W={class:"right-box"},$={class:"dp-flex justify-content-center align-items height-full width-full"},H={class:"header-avatar",style:{cursor:"pointer"}},K={style:{"margin-right":"9px",color:"#252631"}},Q={key:0,class:"dropdown-menu"},X={key:0,class:"loading-overlay"},Y=Object.assign({name:"Layout"},{setup:function(n){var t=r(),i=a(),Y=c(),Z=l(),ee=u(!0),ne=u(!1),te=u(!1),oe=u("7"),ie=function(){document.body.clientWidth;te.value=!1,ne.value=!1,ee.value=!0};ie();var re=u(!1);s((function(){d.emit("collapse",ee.value),d.emit("mobile",te.value),d.on("reload",de),d.on("showLoading",(function(){re.value=!0})),d.on("closeLoading",(function(){re.value=!1})),window.onresize=function(){return ie(),d.emit("collapse",ee.value),void d.emit("mobile",te.value)},t.loadingInstance&&t.loadingInstance.close(),document.addEventListener("click",ae)})),f((function(){document.removeEventListener("click",ae)}));var ae=function(e){if(!ee.value&&!te.value){var n=document.querySelector(".gva-aside"),t=document.querySelector(".menu-total");n&&!n.contains(e.target)&&t&&!t.contains(e.target)&&(ee.value=!0,ne.value=!1,fe.value=!1,d.emit("collapse",ee.value))}if(pe.value){var o=document.querySelector(".dropdown");o&&!o.contains(e.target)&&(pe.value=!1)}};p((function(){return"dark"===t.sideMode?"#fff":"light"===t.sideMode?"#273444":t.baseColor}));var ce=p((function(){return"dark"===t.sideMode?"#273444":"light"===t.sideMode?"#fff":t.sideMode})),le=p((function(){return Y.meta.matched})),ue=u(!0),se=null,de=function(){var n=o(e().m((function n(){return e().w((function(n){for(;;)switch(n.n){case 0:se&&window.clearTimeout(se),se=window.setTimeout(o(e().m((function n(){var t;return e().w((function(e){for(;;)switch(e.n){case 0:if(!Y.meta.keepAlive){e.n=2;break}return ue.value=!1,e.n=1,v();case 1:ue.value=!0,e.n=3;break;case 2:t=Y.meta.title,i.push({name:"Reload",params:{title:t}});case 3:return e.a(2)}}),n)}))),400);case 1:return n.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}(),fe=u(!1),pe=u(!1),ve=function(){ee.value=!ee.value,ne.value=!ee.value,fe.value=!ee.value,d.emit("collapse",ee.value)},me=function(){pe.value=!pe.value},ge=function(){i.push({name:"person"})},he=function(){var n=o(e().m((function n(){var i,r,a,c,l,s,d;return e().w((function(n){for(;;)switch(n.n){case 0:return document.location.protocol,document.location.host,i={action:1,msg:"",platform:document.location.hostname},r=u({}),a=u("ws://127.0.0.1:50001"),0!==(c=navigator.platform).indexOf("Mac")&&"MacIntel"!==c||(a.value="wss://127.0.0.1:50001"),l=function(){r.value=new WebSocket(a.value),r.value.onopen=o(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("socket连接成功"),e.n=1,s(JSON.stringify(i));case 1:return e.a(2)}}),n)}))),r.value.onmessage=function(){var n=o(e().m((function n(t){return e().w((function(e){for(;;)switch(e.n){case 0:return console.log(t),e.n=1,d();case 1:return e.a(2)}}),n)})));return function(e){return n.apply(this,arguments)}}(),r.value.onerror=function(){console.log("socket连接错误")}},s=function(){var n=o(e().m((function n(t){return e().w((function(e){for(;;)switch(e.n){case 0:return console.log(t,"0"),e.n=1,r.value.send(t);case 1:return e.a(2)}}),n)})));return function(e){return n.apply(this,arguments)}}(),d=function(){var n=o(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return console.log("socket断开链接"),e.n=1,r.value.close();case 1:return e.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}(),console.log("asecagent://?web=".concat(JSON.stringify(i))),n.n=1,t.LoginOut();case 1:l(),R.remove("asce_sms");case 2:return n.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return m("day",oe),function(e,n){var o=g("base-aside"),i=g("base-icon"),r=g("router-view"),a=g("base-main"),c=g("base-container");return h(),x(c,{class:"layout-cont"},{default:b((function(){return[y("div",{class:w([[ne.value?"openside":"hideside",te.value?"mobile":""],"layout-wrapper"])},[y("div",{class:w([[fe.value?"shadowBg":""],"shadow-overlay"]),onClick:n[0]||(n[0]=function(e){return fe.value=!fe.value,ne.value=!!ee.value,void ve()})},null,2),k(o,{class:"main-cont main-left gva-aside",collapsed:ee.value},{default:b((function(){return[y("div",{class:w(["tilte",[ne.value?"openlogoimg":"hidelogoimg"]]),style:C({background:ce.value})},[n[2]||(n[2]=y("img",{alt:"",class:"logoimg",src:A},null,-1)),j("          <div>"),j('            <div v-if="isSider" class="tit-text">{{ $GIN_VUE_ADMIN.appName }}</div>'),j('            <div v-if="isSider" class="introduction-text">{{ $GIN_VUE_ADMIN.introduction }}</div>'),j("          </div>")],6),k(M,{class:"aside"}),y("div",{class:"footer",style:C({background:ce.value})},[y("div",{class:"menu-total",onClick:ve},[ee.value?(h(),I("svg",B,n[3]||(n[3]=[y("use",{"xlink:href":"#icon-expand"},null,-1)]))):(h(),I("svg",D,n[4]||(n[4]=[y("use",{"xlink:href":"#icon-fold"},null,-1)])))])],4)]})),_:1},8,["collapsed"]),j(" 分块滑动功能 "),k(a,{class:"main-cont main-right"},{default:b((function(){return[k(_,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:b((function(){return[y("div",{style:C({width:"calc(100% - ".concat(te.value?"0px":ee.value?"54px":"220px",")")}),class:"topfix"},[y("div",F,[y("div",G,[y("header",P,[y("div",L,[n[7]||(n[7]=y("div",{class:"header-menu-col",style:{"z-index":"100"}},[j('                      <div class="menu-total" @click="totalCollapse">'),j('                        <div v-if="isCollapse" class="gvaIcon gvaIcon-arrow-double-right"/>'),j('                        <div v-else class="gvaIcon gvaIcon-arrow-double-left"/>'),j("                      </div>")],-1)),y("div",q,[y("nav",J,[(h(!0),I(S,null,z(le.value.slice(1,le.value.length),(function(e){return h(),I("div",{key:e.path,class:"breadcrumb-item"},O(N(T)(e.meta.topTitle||"",N(Y))),1)})),128))])]),y("div",V,[y("div",W,[j("                        <Search />"),y("div",{class:"dropdown",onClick:me},[y("div",$,[y("span",H,[j(" 展示当前登录用户名 "),y("span",K,O(N(t).userInfo.displayName?N(t).userInfo.displayName:N(t).userInfo.name),1),k(i,{name:"zhankai",size:"10px"})])]),pe.value?(h(),I("div",Q,[j(' <div class="dropdown-item">\r\n                              <span style="font-weight: 600;">\r\n                                当前角色：{{ JSONPath(\'$..roles[0][name]\', userStore.userInfo)[0] }}\r\n                              </span>\r\n                            </div> '),y("div",{class:"dropdown-item",onClick:ge},n[5]||(n[5]=[y("svg",{class:"icon dropdown-item-icon","aria-hidden":"true"},[y("use",{"xlink:href":"#icon-person"})],-1),y("span",null,"个人信息",-1)])),y("div",{class:"dropdown-item",onClick:n[1]||(n[1]=function(e){return he()})},n[6]||(n[6]=[y("svg",{class:"icon dropdown-item-icon","aria-hidden":"true"},[y("use",{"xlink:href":"#icon-logout"})],-1),y("span",null,"注销登录",-1)]))])):j("v-if",!0)]),j('                        <base-button type="text"'),j('                                   class="iconfont icon-rizhi1"'),j('                                   style="font-size: 14px;font-weight:500 !important;color:#2972C8;padding-left: 20px;padding-right: 15px"'),j('                                   @click="toLog"'),j("                        >日志中心"),j("                        </base-button>")])])])])])]),j(" 当前面包屑用路由自动生成可根据需求修改 "),j('\r\n            :to="{ path: item.path }" 暂时注释不用'),j('            <HistoryComponent ref="layoutHistoryComponent"/>')],4)]})),_:1}),y("div",{class:w(["router-view-container",{loading:re.value}])},[re.value?(h(),I("div",X,n[8]||(n[8]=[y("div",{class:"loading-spinner"},[y("div",{class:"spinner"}),y("div",{class:"loading-text"},"正在加载中")],-1)]))):j("v-if",!0),ue.value?(h(),x(r,{key:1,class:"admin-box"},{default:b((function(e){var n=e.Component;return[y("div",null,[k(_,{mode:"out-in",name:"el-fade-in-linear"},{default:b((function(){return[(h(),x(U,{include:N(Z).keepAliveRouters},[(h(),x(E(n)))],1032,["include"]))]})),_:2},1024)])]})),_:1})):j("v-if",!0)],2),j("        <BottomInfo />"),j("        <setting />")]})),_:1})],2)]})),_:1})}}});n("default",i(Y,[["__file","D:/asec-platform/frontend/portal/src/view/layout/index.vue"]]))}}}))}();
