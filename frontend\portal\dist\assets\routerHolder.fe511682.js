/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import{U as e,h as a,o as s,d as n,j as t,w as o,T as u,f as l,X as r,m as d,A as i}from"./index.5a1fa56a.js";const f=Object.assign({name:"RouterHolder"},{setup(f){const m=e();return(e,f)=>{const c=a("router-view");return s(),n("div",null,[t(c,null,{default:o((({Component:e})=>[t(u,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(s(),l(r,{include:d(m).keepAliveRouters},[(s(),l(i(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{f as default};
