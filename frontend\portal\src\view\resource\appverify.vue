<script>
export default {
  name: 'Appverify'
}
</script>

<template>
  <div style="width: 100%;height: 100%;background: #FFFFFF">
    <div style="width: 442px;height: 175px;padding-left: 38.5%;padding-top: 21%;text-align: center">
      <div style="font-size: 24px;display: flex;justify-content: center;align-items: center">
        <svg class="icon" style="margin-right: 10px;font-size: 14px;width: 24px;height: 24px;" aria-hidden="true">-->
          <use xlink:href="#icon-shuoming2"/>
        </svg>-->
        该应用需通过安全认证后才可继续访问
      </div>
      <div v-if="smsinfo?.notPhone === false" style="margin-top: 20px;">
        <span style="float: left;margin-left: 34px">验证码已发送至您的账号({{ smsinfo?.userName }})关联的手机，请注意查收</span>
        <base-input
          v-model="auth_code"
          placeholder="请输入短信验证码"
          style="float: left;margin-left: 34px;font-size: 12px;margin-top: 12px;width: 258px;height: 32px"
          class="input-with-select"
        />
        <base-button style="border-radius: 4px;font-size: 12px;float: left;margin-top: 12px;position: relative;margin-left: 10px;width: 92px;height: 32px" :disabled="count_down>0" @click="send_sms">重新发送
          {{ count_down > 0 ? `(${count_down}秒)` : '' }}
        </base-button>
      </div>
      <div v-else style="margin-top: 20px;">
        <span>您的账号({{ smsinfo?.userName }})未关联手机号码，请联系管理员</span>
      </div>
      <base-button
        v-if="smsinfo?.notPhone === false"
        type="primary"
        size="large"
        style="float: left;margin-left: 34px;height: 44px;margin-top: 14px;width: 365px;"
        :disabled="!auth_code"
        @click="verify"
      >确 定
      </base-button>
    </div>
  </div>
</template>
<script setup>
// 使用轻量级 SVG 图标，已在 main.js 中全局加载
import { post_send_sms, post_verify, smsInfo } from '@/api/sms'
import { useRoute } from 'vue-router'
import { ref } from 'vue'
import { Message } from '@/components/base'

const auth_code = ref()
const smsinfo = ref({})
const route = useRoute()
const getSmsInfo = async() => {
  const data = {
    user_id: route.query.user_id,
    idp_id: route.query.idp_id
  }
  const res = await smsInfo(data)
  if (res.status === 200) {
    smsinfo.value = res.data.data
    if (!smsinfo.value?.notPhone) {
      await send_sms()
    }
  }
}

getSmsInfo()

const count_down = ref(60)
let timerId
const startCountDown = () => {
  count_down.value = 60
  timerId = setInterval(() => {
    count_down.value--
    if (count_down.value === 0) {
      stopCountDown()
    }
  }, 1000)
}

const stopCountDown = () => {
  clearInterval(timerId)
}

const send_sms = async() => {
  const query = {
    uniq_key: smsinfo.value.uniqKey,
    idp_id: route.query.idp_id
  }
  const res = await post_send_sms(query)
  if (res.status === 200 && res.data.code !== -1) {
    startCountDown()
  } else {
    Message({
      showClose: true,
      message: res.data.msg,
      type: 'error',
    })
    count_down.value = 0
  }
}

const verify = async() => {
  const query = {
    uniq_key: smsinfo.value.uniqKey,
    auth_code: auth_code.value,
    user_name: smsinfo.value.userName,
    idp_id: route.query.idp_id,
    redirect_uri: 'app_redirect',
    grant_type: 'implicit',
    client_id: 'client_portal'
  }

  const res = await post_verify(query)
  if (res.status === 200 && res.data.code !== -1) {
    location.href = route.query.redirect_url
  } else {
    Message({
      showClose: true,
      message: res.data.msg,
      type: 'error',
    })
  }
}
</script>
<style lang="scss">
.input-with-select {
  .el-input__inner{
    font-size: 12px;
  }
}
</style>
