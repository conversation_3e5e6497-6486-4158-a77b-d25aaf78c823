/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
import{_ as a,h as e,o as s,d as t,f as o,j as r,e as i,g as l}from"./index.df61e453.js";import n from"./header.d7922615.js";import d from"./menu.d9ecb6b4.js";import"./ASD.492c8837.js";const u={class:"layout-page"},c={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},f=a(Object.assign({name:"Client"},{setup:a=>(a,f)=>{const p=e("router-view");return s(),t("div",u,[o("公共顶部菜单-"),r(n),i("div",c,[o("公共侧边栏菜单"),r(d),i("div",m,[o("主流程路由渲染点"),(s(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{f as default};
