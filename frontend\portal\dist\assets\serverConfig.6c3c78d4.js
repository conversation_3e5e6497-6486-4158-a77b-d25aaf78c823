/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
import{_ as e,r,J as s,h as a,o as t,d as l,e as i,j as o,w as n,k as c,Z as u,a0 as d,M as v}from"./index.df61e453.js";const f={class:"server-config"},p={class:"config-form"},g={class:"config-tips"},m={class:"tip-item"},h={class:"tip-item"},_=e(Object.assign({name:"ServerConfig"},{emits:["server-configured"],setup(e,{emit:_}){const b=_,y=r(null),U=r(!1),w=s({serverUrl:""}),k={serverUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的服务器地址（需包含 http:// 或 https://）",trigger:"blur"}]},x=async()=>{if(y.value)try{if(!(await y.value.validate()))return;U.value=!0;if(d(w.serverUrl))try{const e=await fetch(`${w.serverUrl}/auth/idp/list`,{method:"GET",headers:{"Content-Type":"application/json"},timeout:5e3});if(!e.ok&&401!==e.status)throw new Error(`服务器响应错误: ${e.status}`);v.success("服务器连接成功！"),b("server-configured",w.serverUrl)}catch(e){console.warn("服务器连接测试失败，但仍然保存配置:",e),v.warning("服务器地址已保存，但连接测试失败，请检查网络或服务器状态"),b("server-configured",w.serverUrl)}else v.error("服务器地址格式错误")}catch(e){console.error("配置服务器失败:",e),v.error("配置失败，请检查服务器地址格式")}finally{U.value=!1}},C=localStorage.getItem("server_host");return C&&(w.serverUrl=C),(e,r)=>{const s=a("base-input"),d=a("base-form-item"),v=a("base-button"),_=a("base-form"),b=a("base-icon");return t(),l("div",f,[r[5]||(r[5]=i("div",{class:"config-header"},[i("span",{class:"title"},"平台地址")],-1)),i("div",p,[o(_,{ref_key:"serverForm",ref:y,model:w,rules:k,onKeyup:u(x,["enter"])},{default:n((()=>[o(d,{prop:"serverUrl"},{default:n((()=>[o(s,{modelValue:w.serverUrl,"onUpdate:modelValue":r[0]||(r[0]=e=>w.serverUrl=e),size:"large",placeholder:"输入您连接的平台服务器地址","suffix-icon":"link"},null,8,["modelValue"]),r[1]||(r[1]=i("div",{class:"input-tip"},' 请输入平台地址，如：https://*************" ',-1))])),_:1,__:[1]}),o(d,null,{default:n((()=>[o(v,{type:"primary",size:"large",class:"submit-button",loading:U.value,onClick:x},{default:n((()=>r[2]||(r[2]=[c(" 连接服务器 ")]))),_:1,__:[2]},8,["loading"])])),_:1})])),_:1},8,["model"])]),i("div",g,[i("div",m,[o(b,{name:"warning",style:{color:"#f4a261","margin-right":"6px"}}),r[3]||(r[3]=i("span",null,"请确保服务器地址正确且网络连通",-1))]),i("div",h,[o(b,{name:"check",style:{color:"#67c23a","margin-right":"6px"}}),r[4]||(r[4]=i("span",null,"配置成功后将自动跳转到登录页面",-1))])])])}}}),[["__scopeId","data-v-17e3a57d"],["__file","D:/asec-platform/frontend/portal/src/view/login/serverConfig/serverConfig.vue"]]);export{_ as default};
