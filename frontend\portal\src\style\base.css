/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

/* 布局组件 */
.container {
  display: flex;
  min-height: 100vh;
}

.aside {
  width: 220px;
  transition: width 0.3s;
  overflow: hidden;
}

.aside.collapsed {
  width: 54px;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.content {
  flex: 1;
  padding: 20px;
}

/* 栅格系统 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -12px;
  margin-right: -12px;
}

.col {
  padding-left: 12px;
  padding-right: 12px;
  flex: 1;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* 卡片组件 */
.card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.card-body {
  padding: 20px;
}

/* 按钮组件 */
.btn {
  display: inline-block;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.3s;
  user-select: none;
}

.btn:hover {
  opacity: 0.8;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.btn-primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.btn-success {
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}

.btn-warning {
  color: #fff;
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.btn-danger {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.btn-default {
  color: #606266;
  background-color: #fff;
  border-color: #dcdfe6;
}

.btn-small {
  padding: 5px 12px;
  font-size: 12px;
}

.btn-large {
  padding: 12px 20px;
  font-size: 16px;
}

/* 表单组件 */
.form {
  margin: 0;
}

.form-item {
  margin-bottom: 22px;
}

.form-label {
  display: inline-block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.3s;
}

.form-input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-input:disabled {
  background-color: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
}

.form-textarea {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  resize: vertical;
  min-height: 80px;
}

/* 表格组件 */
.table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.table th {
  background-color: #fafafa;
  font-weight: 500;
  color: #909399;
}

.table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 分页组件 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 8px;
}

.pagination-item {
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-item:hover {
  color: #409eff;
  border-color: #409eff;
}

.pagination-item.active {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.pagination-item.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 标签组件 */
.tag {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 4px;
  margin-right: 8px;
}

.tag-primary {
  color: #409eff;
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
}

.tag-success {
  color: #67c23a;
  background-color: #f0f9ff;
  border: 1px solid #c2e7b0;
}

.tag-warning {
  color: #e6a23c;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
}

.tag-danger {
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
}

.tag-info {
  color: #909399;
  background-color: #f4f4f5;
  border: 1px solid #e9e9eb;
}

/* 头像组件 */
.avatar {
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #c0c4cc;
  color: #fff;
  text-align: center;
  line-height: 40px;
  font-size: 14px;
  overflow: hidden;
}

.avatar-small {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

.avatar-large {
  width: 64px;
  height: 64px;
  line-height: 64px;
  font-size: 18px;
}

/* 进度条组件 */
.progress {
  width: 100%;
  height: 6px;
  background-color: #f5f7fa;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #409eff;
  transition: width 0.3s;
}

/* 链接组件 */
.link {
  color: #409eff;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.3s;
}

.link:hover {
  color: #66b1ff;
}

.link-primary {
  color: #409eff;
}

.link-success {
  color: #67c23a;
}

.link-warning {
  color: #e6a23c;
}

.link-danger {
  color: #f56c6c;
}

.link-info {
  color: #909399;
}

/* 分割线组件 */
.divider {
  margin: 24px 0;
  border: none;
  border-top: 1px solid #e8e8e8;
}

.divider-vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  background-color: #e8e8e8;
  vertical-align: middle;
  margin: 0 8px;
}
