/*! 
 Build based on gin-vue-admin 
 Time : 1749623805000 */
import e from"./menuItem.5fee6bc0.js";import t from"./asyncSubmenu.0cd211f5.js";import{c as o,h as n,o as r,f as s,w as u,d as l,F as a,i,g as f,A as c}from"./index.8abc592d.js";const d=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(d){const m=d,h=o((()=>m.routerInfo.children&&m.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return d.routerInfo.hidden?f("",!0):(r(),s(c(h.value),{key:0,"is-collapse":d.isCollapse,theme:d.theme,"router-info":d.routerInfo},{default:u((()=>[d.routerInfo.children&&d.routerInfo.children.length?(r(!0),l(a,{key:0},i(d.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:d.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{d as default};
