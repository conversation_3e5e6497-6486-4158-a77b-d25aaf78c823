/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function a(e){for(var a=1;a<arguments.length;a++){var r=null!=arguments[a]?arguments[a]:{};a%2?t(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function n(t,a,n){return(a=function(t){var a=function(t,a){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,a||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(t)}(t,"string");return"symbol"==e(a)?a:a+""}(a))in t?Object.defineProperty(t,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[a]=n,t}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,a="function"==typeof Symbol?Symbol:{},n=a.iterator||"@@iterator",o=a.toStringTag||"@@toStringTag";function u(a,n,r,o){var u=n&&n.prototype instanceof c?n:c,s=Object.create(u.prototype);return i(s,"_invoke",function(a,n,r){var i,o,u,c=0,s=r||[],h=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,a){return i=t,o=0,u=e,d.n=a,l}};function p(a,n){for(o=a,u=n,t=0;!h&&c&&!r&&t<s.length;t++){var r,i=s[t],p=d.p,v=i[2];a>3?(r=v===n)&&(u=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=p&&((r=a<2&&p<i[1])?(o=0,d.v=n,d.n=i[1]):p<v&&(r=a<3||i[0]>n||n>v)&&(i[4]=a,i[5]=n,d.n=v,o=0))}if(r||a>1)return l;throw h=!0,n}return function(r,s,v){if(c>1)throw TypeError("Generator is already running");for(h&&1===s&&p(s,v),o=s,u=v;(t=o<2?e:u)||!h;){i||(o?o<3?(o>1&&(d.n=-1),p(o,u)):d.n=u:d.v=u);try{if(c=2,i){if(o||(r="next"),t=i[r]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(u=TypeError("The iterator does not provide a '"+r+"' method"),o=1);i=e}else if((t=(h=d.n<0)?u:a.call(n,d))!==l)break}catch(t){i=e,o=1,u=t}finally{c=1}}return{value:t,done:h}}}(a,r,o),!0),s}var l={};function c(){}function s(){}function h(){}t=Object.getPrototypeOf;var d=[][n]?t(t([][n]())):(i(t={},n,(function(){return this})),t),p=h.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,i(e,o,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=h,i(p,"constructor",h),i(h,"constructor",s),s.displayName="GeneratorFunction",i(h,o,"GeneratorFunction"),i(p),i(p,o,"Generator"),i(p,n,(function(){return this})),i(p,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:u,m:v}})()}function i(e,t,a,n){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,a,n){if(t)r?r(e,t,{value:a,enumerable:!n,configurable:!n,writable:!n}):e[t]=a;else{var o=function(t,a){i(e,t,(function(e){return this._invoke(t,a,e)}))};o("next",0),o("throw",1),o("return",2)}},i(e,t,a,n)}function o(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return u(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?u(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,l=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return o=e.done,e},e:function(e){l=!0,i=e},f:function(){try{o||null==a.return||a.return()}finally{if(l)throw i}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=Array(t);a<t;a++)n[a]=e[a];return n}function l(e,t,a,n,r,i,o){try{var u=e[i](o),l=u.value}catch(e){return void a(e)}u.done?t(l):Promise.resolve(l).then(n,r)}function c(e){return function(){var t=this,a=arguments;return new Promise((function(n,r){var i=e.apply(t,a);function o(e){l(i,n,r,o,u,"next",e)}function u(e){l(i,n,r,o,u,"throw",e)}o(void 0)}))}}System.register(["./index-legacy.00b16b45.js"],(function(e,t){"use strict";var n,i,u,l,s,h,d,p,v,f,m,g,y,b,w,x,k,j,O,C,_,S,P,T,z,q,L=document.createElement("style");return L.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+');background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}.error-component{text-align:center;padding:20px;background-color:#fef2f2;border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;margin:10px 0}.error-component:before{content:"\\26a0\\fe0f";display:block;font-size:24px;margin-bottom:8px}.auth-switcher{margin-top:24px}.auth-switcher .auth-switcher-title{text-align:center;color:#929298;font-size:14px;margin-bottom:20px;position:relative}.auth-switcher .auth-switcher-title:before,.auth-switcher .auth-switcher-title:after{content:"";position:absolute;top:50%;width:80px;height:1px;background:#e8e8e8}.auth-switcher .auth-switcher-title:before{left:0}.auth-switcher .auth-switcher-title:after{right:0}.auth-switcher .auth-switcher-container{display:flex;align-items:center;justify-content:center;position:relative}.auth-switcher .auth-switcher-container .auth-nav-btn{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border:1px solid #e8e8e8;border-radius:50%;background:#ffffff;color:#666;cursor:pointer;transition:all .3s ease;z-index:2}.auth-switcher .auth-switcher-container .auth-nav-btn:hover:not(:disabled){border-color:#1890ff;color:#1890ff;box-shadow:0 2px 8px rgba(24,144,255,.2)}.auth-switcher .auth-switcher-container .auth-nav-btn:disabled{opacity:.3;cursor:not-allowed}.auth-switcher .auth-switcher-container .auth-nav-btn.auth-nav-prev{margin-right:8px}.auth-switcher .auth-switcher-container .auth-nav-btn.auth-nav-next{margin-left:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper{flex:1;max-width:320px;overflow:hidden;position:relative}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container{display:flex;transition:transform .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item{flex:0 0 80px;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:12px 8px;cursor:pointer;transition:all .3s ease;border-radius:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover{background:#f8f9fa;transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,.1)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon{width:40px;height:40px;border-radius:50%;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);display:flex;align-items:center;justify-content:center;margin-bottom:8px;color:#fff;font-size:18px;transition:all .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=local]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=qiyewx]{background:linear-gradient(135deg,#07c160 0%,#1aad19 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=feishu]{background:linear-gradient(135deg,#00d4aa 0%,#00b8d4 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=dingtalk]{background:linear-gradient(135deg,#1890ff 0%,#096dd9 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=email]{background:linear-gradient(135deg,#fa8c16 0%,#d46b08 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=sms]{background:linear-gradient(135deg,#52c41a 0%,#389e0d 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=msad]{background:linear-gradient(135deg,#0078d4 0%,#106ebe 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon[data-auth-type=ldap]{background:linear-gradient(135deg,#722ed1 0%,#531dab 100%)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-name{font-size:12px;color:#666;text-align:center;line-height:1.2;max-width:64px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover .auth-method-icon{transform:scale(1.1);box-shadow:0 4px 12px rgba(0,0,0,.2)}\n',document.head.appendChild(L),{setters:[function(e){n=e.x,i=e._,u=e.y,l=e.u,s=e.r,h=e.c,d=e.b,p=e.z,v=e.p,f=e.h,m=e.o,g=e.d,y=e.e,b=e.f,w=e.j,x=e.m,k=e.F,j=e.k,O=e.t,C=e.g,_=e.A,S=e.B,P=e.i,T=e.C,z=e.L,q=e.D}],execute:function(){var L={class:"login-page"},I={class:"content"},A={class:"right-panel"},E={key:0},U={key:0,class:"title"},D={key:1,class:"title"},G={style:{"text-align":"center"}},F={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},N={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},R=["xlink:href"],K={key:2,class:"login_panel_form"},M={key:3,class:"auth-switcher"},H={class:"auth-switcher-container"},J=["disabled"],W={class:"auth-methods-wrapper"},B=["onClick"],V=["data-auth-type"],X={class:"auth-method-name"},Y=["disabled"],$={class:"auth-waiting"},Q={class:"waiting-icon"},Z={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},ee=["xlink:href"],te={class:"waiting-title"},ae={class:"security-tips"},ne=Object.assign({name:"Login"},{setup:function(e){var i=u({loader:function(){return T((function(){return t.import("./localLogin-legacy.d5ce298c.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=u({loader:function(){return T((function(){return t.import("./wechat-legacy.1077c353.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),re=u({loader:function(){return T((function(){return t.import("./feishu-legacy.df7d29fb.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ie=u({loader:function(){return T((function(){return t.import("./dingtalk-legacy.372bc031.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),oe=u({loader:function(){return T((function(){return t.import("./oauth2-legacy.8c49b39c.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=u({loader:function(){return T((function(){return t.import("./sms-legacy.39d22884.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=u({loader:function(){return T((function(){return t.import("./secondaryAuth-legacy.2c6c79f1.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ce=u({loader:function(){return T((function(){return t.import("./serverConfig-legacy.407b7d1a.js")}),void 0,t.meta.url)},loadingComponent:z,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=l(),he=s(0),de=s([]),pe=s("local"),ve=s(""),fe=s(""),me=s(""),ge=s([]),ye=s([]),be=s(!1),we=s(!1),xe=s(),ke=s(""),je=s(!1),Oe=s(""),Ce=s(!1),_e=s(""),Se=s(""),Pe=s(""),Te=s({}),ze=s(0),qe=s(80),Le=s(4),Ie=s(null),Ae=h((function(){var e=be.value?_e.value:fe.value;return de.value.filter((function(t){return t.id!==e}))})),Ee=h((function(){return Math.max(0,Ae.value.length-Le.value)})),Ue=d();h((function(){return ye.value.filter((function(e){return e.id!==fe.value}))}));var De=function(){var e={};if(se.query.type&&(e.type=se.query.type),se.query.wp&&(e.wp=se.query.wp),se.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(se.query.redirect);if(t.includes("?")){var a=t.substring(t.indexOf("?")+1),n=new URLSearchParams(a);n.get("type")&&(e.type=n.get("type")),n.get("wp")&&(e.wp=n.get("wp"))}}catch(r){console.warn("解析redirect参数失败:",r)}return e},Ge=function(){if(q.isClient()){var e=urlHashParams?urlHashParams.get("WebUrl"):"";try{var t=new URL(e);e="".concat(t.protocol,"//").concat(t.host)}catch(a){e="",console.warn("解析 WebUrl 参数失败:",a)}if(e)return!1;if(!localStorage.getItem("server_host"))return!0}return!1},Fe=function(e){logger.log("服务器配置完成:",e),we.value=!1,Ne()},Ne=function(){var e=c(r().m((function e(){var t,a,i,u,l,c,s,h,d,p,v,f,m,g,y,b,w,x,k,j;return r().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,!Ge()){e.n=1;break}return we.value=!0,e.a(2);case 1:return t=De(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=2,n({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 2:if(200===(a=e.v).status){if(de.value=a.data.idpList,(i=se.query.idp_id||Ue.loginType)&&"undefined"!==i){u=!1,l=o(a.data.idpList);try{for(l.s();!(c=l.n()).done;)s=c.value,i===s.id&&(u=!0,fe.value=s.id,pe.value=s.type,ve.value=s.templateType,ge.value=s.attrs,ge.value.name=s.name,ge.value.authType=s.type)}catch(r){l.e(r)}finally{l.f()}u||(me.value=null===(h=de.value[0])||void 0===h?void 0:h.id,fe.value=null===(d=de.value[0])||void 0===d?void 0:d.id,pe.value=null===(p=de.value[0])||void 0===p?void 0:p.type,ve.value=null===(v=de.value[0])||void 0===v?void 0:v.templateType,ge.value=null===(f=de.value[0])||void 0===f?void 0:f.attrs,ge.value.name=de.value[0].name,ge.value.authType=null===(m=de.value[0])||void 0===m?void 0:m.type)}else me.value=null===(g=de.value[0])||void 0===g?void 0:g.id,fe.value=null===(y=de.value[0])||void 0===y?void 0:y.id,pe.value=null===(b=de.value[0])||void 0===b?void 0:b.type,ve.value=null===(w=de.value[0])||void 0===w?void 0:w.templateType,ge.value=null===(x=de.value[0])||void 0===x?void 0:x.attrs,ge.value.name=de.value[0].name,ge.value.authType=null===(k=de.value[0])||void 0===k?void 0:k.type;++he.value}e.n=4;break;case 3:e.p=3,j=e.v,console.error("获取认证列表失败:",j),q.isClient()&&(we.value=!0);case 4:return e.a(2)}}),e,null,[[0,3]])})));return function(){return e.apply(this,arguments)}}();Ne();var Re=h((function(){switch(pe.value){case"local":case"msad":case"ldap":case"web":case"email":return i;case"qiyewx":return ne;case"feishu":return re;case"dingtalk":return ie;case"oauth2":case"cas":return oe;case"sms":return ue;default:return"oauth2"===ve.value?oe:"local"}})),Ke=h((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===Oe.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===Oe.value}]})),Me=function(){be.value=!1,ye.value=[],xe.value="",ke.value="",Oe.value="",Ce.value=!1,_e.value&&(fe.value=_e.value,pe.value=Se.value,ve.value=Pe.value,ge.value=a({},Te.value),_e.value="",Se.value="",Pe.value="",Te.value={}),++he.value,console.log("取消后恢复的状态:",{isSecondary:be.value,auth_id:fe.value,auth_type:pe.value})},He=function(){var e=c(r().m((function e(t){var a,n,i;return r().w((function(e){for(;;)switch(e.n){case 0:a=z.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{n=se.query.redirect_url||"/",t.clientParams&&((i=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&i.set("wp",t.clientParams.wp),n+=(n.includes("?")?"&":"?")+i.toString()),window.location.href=n}finally{null==a||a.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),Je=h((function(){return!["dingtalk","feishu","qiyewx"].includes(pe.value)&&("oauth2"!==ve.value&&"cas"!==pe.value||("cas"===pe.value?1===parseInt(ge.value.casOpenType):"oauth2"===ve.value&&1===parseInt(ge.value.oauth2OpenType)))})),We=function(){ze.value>0&&ze.value--},Be=function(){ze.value<Ee.value&&ze.value++},Ve=function(e){me.value=e.id,ge.value=e.attrs||{},ge.value.name=e.name,ge.value.authType=e.type,be.value&&(ge.value.uniqKey=xe.value,ge.value.notPhone=je.value),fe.value=e.id,pe.value=e.type,ve.value=e.templateType,++he.value};return p(be,function(){var e=c(r().m((function e(t,n){return r().w((function(e){for(;;)switch(e.n){case 0:be.value&&(_e.value=fe.value,Se.value=pe.value,Pe.value=ve.value,Te.value=a({},ge.value),console.log("二次认证数据:",{secondary:ye.value,secondaryLength:ye.value.length}),ye.value.length>0&&Ve(ye.value[0]));case 1:return e.a(2)}}),e)})));return function(t,a){return e.apply(this,arguments)}}()),v("secondary",ye),v("isSecondary",be),v("uniqKey",xe),v("userName",ke),v("notPhone",je),v("last_id",me),v("contactType",Oe),v("hasContactInfo",Ce),function(e,t){var a=f("base-icon");return m(),g("div",L,[y("div",I,[t[3]||(t[3]=y("div",{class:"left-panel"},[b(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),b('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),b(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),y("div",A,[b(" 服务器配置状态 "),we.value?(m(),g("div",E,[w(x(ce),{onServerConfigured:Fe})])):be.value?(m(),g(k,{key:2},[b(" 二次认证等待状态 "),y("div",$,[y("div",Q,[(m(),g("svg",Z,[y("use",{"xlink:href":"#icon-auth-".concat(Se.value||pe.value)},null,8,ee)]))]),y("h4",te,O(Te.value.name||ge.value.name)+" 登录成功",1),t[2]||(t[2]=y("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),y("div",ae,[w(a,{name:"shield",style:{color:"#67c23a"}}),t[1]||(t[1]=y("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(m(),g(k,{key:1},[b(" 正常登录状态 "),y("div",null,["local"===pe.value?(m(),g("span",U,"本地账号登录")):Je.value?(m(),g("span",D,[y("div",G,[y("span",F,[(m(),g("svg",N,[y("use",{"xlink:href":"#icon-auth-"+pe.value},null,8,R)])),j(" "+O(ge.value.name),1)])])])):b("v-if",!0),fe.value?(m(),g("div",K,[b(' <component :is="getLoginType"></component> '),(m(),C(_(Re.value),{auth_id:fe.value,auth_info:ge.value},null,8,["auth_id","auth_info"])),b(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):b("v-if",!0),Ae.value.length>0?(m(),g("div",M,[t[0]||(t[0]=y("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),y("div",H,[ze.value>0?(m(),g("button",{key:0,class:"auth-nav-btn auth-nav-prev",onClick:We,disabled:0===ze.value},[w(a,{name:"chevron-left"})],8,J)):b("v-if",!0),y("div",W,[y("div",{class:"auth-methods-container",ref_key:"authMethodsContainer",ref:Ie,style:S({transform:"translateX(-".concat(ze.value*qe.value,"px)")})},[(m(!0),g(k,null,P(Ae.value,(function(e){return m(),g("div",{key:e.id,class:"auth-method-item",onClick:function(t){return Ve(e)}},[y("div",{class:"auth-method-icon","data-auth-type":e.type},[w(a,{name:(t=e.type,{local:"user",msad:"windows",ldap:"server",email:"mail",qiyewx:"wechat",feishu:"feishu",dingtalk:"dingtalk",oauth2:"shield",cas:"shield",sms:"phone",web:"globe"}[t]||"user")},null,8,["name"])],8,V),y("div",X,O(e.name),1)],8,B);var t})),128))],4)]),ze.value<Ee.value?(m(),g("button",{key:1,class:"auth-nav-btn auth-nav-next",onClick:Be,disabled:ze.value>=Ee.value},[w(a,{name:"chevron-right"})],8,Y)):b("v-if",!0)])])):b("v-if",!0)])],2112))])]),b(" 二次认证弹窗 "),be.value?(m(),C(x(le),{key:0,"auth-info":{uniqKey:xe.value,contactType:Oe.value,hasContactInfo:Ce.value},"auth-id":fe.value,"user-name":ke.value,"last-id":me.value,"auth-methods":Ke.value,onVerificationSuccess:He,onCancel:Me},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):b("v-if",!0)])}}});e("default",i(ne,[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]))}}}))}();
