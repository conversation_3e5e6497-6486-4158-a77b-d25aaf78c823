<template>
  <li 
    :class="[
      'base-sub-menu',
      { 'base-sub-menu--active': isActive },
      { 'base-sub-menu--opened': isOpened },
      { 'base-sub-menu--disabled': disabled }
    ]"
  >
    <div 
      class="base-sub-menu__title"
      @click="handleTitleClick"
    >
      <slot name="title">
        <span>{{ title }}</span>
      </slot>
      <svg 
        class="base-sub-menu__icon"
        :class="{ 'base-sub-menu__icon--opened': isOpened }"
        viewBox="0 0 1024 1024"
        width="1em" 
        height="1em"
      >
        <path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 0 0 302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.8 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 0 0 0-50.4z"/>
      </svg>
    </div>
    
    <transition name="sub-menu-collapse">
      <ul 
        v-show="isOpened"
        class="base-sub-menu__list"
      >
        <slot />
      </ul>
    </transition>
  </li>
</template>

<script>
export default {
  name: 'BaseSubMenu',
  props: {
    index: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    popperClass: {
      type: String,
      default: ''
    },
    showTimeout: {
      type: Number,
      default: 300
    },
    hideTimeout: {
      type: Number,
      default: 300
    }
  },
  inject: {
    menu: {
      default: null
    },
    activeIndex: {
      default: () => ''
    },
    collapse: {
      default: () => false
    },
    uniqueOpened: {
      default: () => false
    }
  },
  data() {
    return {
      isOpened: false,
      timeout: null
    }
  },
  computed: {
    isActive() {
      const activeIndex = this.activeIndex()
      return activeIndex ? activeIndex.startsWith(this.index) : false
    },
    isCollapse() {
      return this.collapse()
    }
  },
  watch: {
    isActive: {
      handler(val) {
        if (val && !this.isOpened) {
          this.open()
        }
      },
      immediate: true
    }
  },
  methods: {
    handleTitleClick() {
      if (this.disabled) return
      
      if (this.isCollapse) {
        // 折叠模式下的处理逻辑
        return
      }
      
      this.toggle()
    },
    open() {
      if (this.disabled) return
      
      if (this.uniqueOpened() && this.menu) {
        // 如果设置了唯一打开，关闭其他子菜单
        this.menu.$children.forEach(child => {
          if (child !== this && child.close) {
            child.close()
          }
        })
      }
      
      this.isOpened = true
      this.$emit('open', this.index)
    },
    close() {
      this.isOpened = false
      this.$emit('close', this.index)
    },
    toggle() {
      if (this.isOpened) {
        this.close()
      } else {
        this.open()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.base-sub-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  
  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    color: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 6px 0;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }
  }
  
  &__icon {
    width: 12px;
    height: 12px;
    fill: currentColor;
    transition: transform 0.3s ease;
    transform: rotate(90deg);
    
    &--opened {
      transform: rotate(180deg);
    }
  }
  
  &__list {
    list-style: none;
    margin: 0;
    padding: 0;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
  }
  
  &--active {
    .base-sub-menu__title {
      background-color: #536ce6;
      color: #ffffff;
      
      &:hover {
        background-color: #536ce6;
      }
    }
  }
  
  &--opened {
    .base-sub-menu__title {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  &--disabled {
    .base-sub-menu__title {
      opacity: 0.5;
      cursor: not-allowed;
      
      &:hover {
        background-color: transparent;
        color: inherit;
      }
    }
  }
}

// 深度选择器，影响子菜单项
:deep(.base-menu-item) {
  padding-left: 40px;
  background-color: transparent;
}

:deep(.base-menu-item:hover) {
  background-color: rgba(255, 255, 255, 0.05);
}

:deep(.base-menu-item--active) {
  background-color: #536ce6;
}

// 折叠动画
.sub-menu-collapse-enter-active,
.sub-menu-collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.sub-menu-collapse-enter-from,
.sub-menu-collapse-leave-to {
  max-height: 0;
  opacity: 0;
}

.sub-menu-collapse-enter-to,
.sub-menu-collapse-leave-from {
  max-height: 300px;
  opacity: 1;
}
</style>
