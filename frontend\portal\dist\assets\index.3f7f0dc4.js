/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
import{_ as e,u as a,a as t,b as o,V as l,r as s,z as n,N as r,R as c,h as u,o as i,d,j as f,w as v,T as m,F as p,i as h,m as g,g as x,f as b,B as k}from"./index.e2f48a61.js";import y from"./index.2ca8c358.js";import"./menuItem.9ce6f016.js";import"./asyncSubmenu.d763f339.js";const T=e(Object.assign({name:"Aside"},{setup(e){const T=a(),j=t(),_=o(),w=l(),B=s({}),F=()=>{switch(_.sideMode){case"#fff":B.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":B.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};F();const M=s("");n((()=>T),(()=>{M.value=T.meta.activeName||T.name}),{deep:!0}),n((()=>_.sideMode),(()=>{F()}));const q=s(!1);(()=>{M.value=T.meta.activeName||T.name;document.body.clientWidth<1e3&&(q.value=!q.value),c.on("collapse",(e=>{q.value=e}))})(),r((()=>{c.off("collapse")}));const D=(e,a,t,o)=>{var l,s;const n={},r={};(null==(l=w.routeMap[e])?void 0:l.parameters)&&(null==(s=w.routeMap[e])||s.parameters.forEach((e=>{"query"===e.type?n[e.key]=e.value:r[e.key]=e.value}))),e!==T.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):j.push({name:e,query:n,params:r}))};return(e,a)=>{const t=u("el-menu"),o=u("el-scrollbar");return i(),d("div",{style:k({background:g(_).sideMode})},[f(o,{style:{height:"calc(100vh - 110px)"}},{default:v((()=>[f(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:v((()=>[f(t,{collapse:q.value,"collapse-transition":!1,"default-active":M.value,"background-color":B.value.background,"active-text-color":B.value.active,class:"el-menu-vertical","unique-opened":"",onSelect:D},{default:v((()=>[(i(!0),d(p,null,h(g(w).asyncRouters[0].children,(e=>(i(),d(p,null,[e.hidden?b("v-if",!0):(i(),x(y,{key:e.name,"is-collapse":q.value,"router-info":e,theme:B.value},null,8,["is-collapse","router-info","theme"]))],64)))),256))])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})],4)}}}),[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/index.vue"]]);export{T as default};
