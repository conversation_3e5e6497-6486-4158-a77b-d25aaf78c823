/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
System.register(["./header-legacy.ebb738c8.js","./menu-legacy.5d3ce157.js","./index-legacy.e6617eb1.js","./ASD-legacy.b6ffb1bc.js"],(function(e,t){"use strict";var a,n,i,u,o,l,c,r,d=document.createElement("style");return d.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% + -0px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(d),{setters:[function(e){a=e.default},function(e){n=e.default},function(e){i=e.h,u=e.o,o=e.d,l=e.j,c=e.e,r=e.f},function(){}],execute:function(){var t={class:"layout-page"},d={class:"layout-wrap"},f={id:"layoutMain",class:"layout-main"};e("default",Object.assign({name:"Client"},{setup:function(e){return function(e,s){var h=i("router-view");return u(),o("div",t,[l(a),c("div",d,[l(n),c("div",f,[(u(),r(h,{key:e.$route.fullPath}))])])])}}}))}}}));
