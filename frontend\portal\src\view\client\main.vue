<template>
  <div class="access-main">
    <ul class="content-wrapper">
      <li class="access-proxy-status">
        <span class="access-proxy-status-text">
          连接状态
        </span>
        <span class="access-proxy-status-span">
          <span class="access-proxy-status-tips">
            点击连接，即可安全便捷地访问应用
          </span>
          <base-button class="access-proxy-status-btn" color="#626aef" type="primary">
            一键连接
          </base-button>
        </span>
      </li>
      <li class="access-common-status">
        <span class="access-common-status-span">
          <span>准入状态（企业网络下使用）：</span>
          <span style="color: red">未入网</span>
          <span>（请重新建立连接）</span>
        </span>
        <span class="access-common-status-detail">
          <span>查看详情</span>
        </span>
      </li>
      <li class="access-app">
        <AppPage class="access-app-page" />
      </li>
    </ul>
  </div>
</template>

<script>
import AppPage from '@/view/app/index.vue'
export default {
  name: 'Access',
  components: {
    AppPage
  }
}
</script>
<style scoped>
.access-main {
  padding: 16px 16px 16px 0px;
  width: 100%;
  height: 100%;
  display: flex;
  border-left: 16px solid #f2f2f6;
  box-sizing: border-box;
  border-image: linear-gradient(to right, #fcfcfc, #fafafa, #ffffff);
  justify-content: center;
  align-items: center;
  background-color: #fcfcf6;

  .content-wrapper {
    margin: 0px;
    width: 100%;
    height: 100%;

    .access-proxy-status {
      height: 102px;
      background: url('@/assets/proxy_background.png') no-repeat center center;
      background-size: cover;
      position: relative;

      .access-proxy-status-text { 
        position: absolute;
        font-size: 16px;
        font-weight: 600;
        height: 22px;
        width: 64px;
        top: 12px;
        left: 16px;
      }

      .access-proxy-status-span {
        position: absolute;
        left: 50%;
        transform: translateX(-260px); 
        width: 300px;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center; 

        .access-proxy-status-btn {
          padding: 0px;
          margin-top: 10px;
          height: 32px;
          width: 152px;
        }
      }
    }

    .access-common-status {
      height: 41px;
      font-size: 13px;
      font-weight: 400;
      display: flex;
      align-items: center; /* 垂直居中 */
      justify-content: space-between; /* 左右贴边布局 */
      margin-top: 16px;
      background-color: #fff;
      box-sizing: border-box;

      .access-common-status-span {
        margin-left: 20px;
      }
      .access-common-status-detail {
        margin-right: 22px;
        color: #536ce6;
      }
    }

    .access-app {
      height: calc(100% - 215px);
      font-size: 13px;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 16px;
      .access-app-page {
        width: 100%;
        overflow: hidden;
      }
    }
  }
}
</style>
