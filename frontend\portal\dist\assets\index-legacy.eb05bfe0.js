/*! 
 Build based on gin-vue-admin 
 Time : 1749628938000 */
System.register(["./menuItem-legacy.13efc3e0.js","./asyncSubmenu-legacy.5418d985.js","./index-legacy.8fdee67f.js"],(function(e,n){"use strict";var t,r,u,o,f,i,l,c,s,a,d,h;return{setters:[function(e){t=e.default},function(e){r=e.default},function(e){u=e.c,o=e.h,f=e.o,i=e.f,l=e.w,c=e.d,s=e.F,a=e.i,d=e.g,h=e.A}],execute:function(){e("default",Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:function(){return null}},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){var n=e,m=u((function(){return n.routerInfo.children&&n.routerInfo.children.filter((function(e){return!e.hidden})).length?r:t}));return function(n,t){var r=o("AsideComponent");return e.routerInfo.hidden?d("",!0):(f(),i(h(m.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:l((function(){return[e.routerInfo.children&&e.routerInfo.children.length?(f(!0),c(s,{key:0},a(e.routerInfo.children,(function(n){return f(),i(r,{key:n.name,"is-collapse":!1,"router-info":n,theme:e.theme},null,8,["router-info","theme"])})),128)):d("",!0)]})),_:1},8,["is-collapse","theme","router-info"]))}}}))}}}));
