/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
System.register(["./index-legacy.4bb28e53.js","./index-legacy.560e870a.js","./menuItem-legacy.8fc53e8d.js","./asyncSubmenu-legacy.8b3d4a7e.js"],(function(e,a){"use strict";var n,t,u,o,r,l,c,i,d,f,s,v,m,p,b,h,g,x,y,k,_,T,j=document.createElement("style");return j.textContent='@charset "UTF-8";:deep(.base-sub-menu__title:hover),:deep(.base-menu-item:hover){background:transparent}:deep(.base-scrollbar) :deep(.base-scrollbar__view){height:100%}.menu-info .menu-contorl{line-height:52px;font-size:20px;display:table-cell;vertical-align:middle}\n',document.head.appendChild(j),{setters:[function(e){n=e._,t=e.u,u=e.a,o=e.b,r=e.U,l=e.r,c=e.z,i=e.K,d=e.P,f=e.h,s=e.o,v=e.d,m=e.j,p=e.w,b=e.T,h=e.F,g=e.i,x=e.m,y=e.g,k=e.f,_=e.B},function(e){T=e.default},function(){},function(){}],execute:function(){var a=Object.assign({name:"Aside"},{setup:function(e){var a=t(),n=u(),j=o(),w=r(),F=l({}),B=function(){switch(j.sideMode){case"#fff":F.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":F.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};B();var M=l("");c((function(){return a}),(function(){M.value=a.meta.activeName||a.name}),{deep:!0}),c((function(){return j.sideMode}),(function(){B()}));var q=l(!1);M.value=a.meta.activeName||a.name,document.body.clientWidth<1e3&&(q.value=!q.value),d.on("collapse",(function(e){q.value=e})),i((function(){d.off("collapse")}));var D=function(e,t,u,o){var r,l,c={},i={};(null===(r=w.routeMap[e])||void 0===r?void 0:r.parameters)&&(null===(l=w.routeMap[e])||void 0===l||l.parameters.forEach((function(e){"query"===e.type?c[e.key]=e.value:i[e.key]=e.value}))),e!==a.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):n.push({name:e,query:c,params:i}))};return function(e,a){var n=f("base-menu"),t=f("base-scrollbar");return s(),v("div",{style:_({background:x(j).sideMode})},[m(t,{height:"calc(100vh - 110px)"},{default:p((function(){return[m(b,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:p((function(){return[m(n,{collapse:q.value,"collapse-transition":!1,"default-active":M.value,"background-color":F.value.background,"active-text-color":F.value.active,mode:"vertical","unique-opened":!0,onSelect:D},{default:p((function(){return[(s(!0),v(h,null,g(x(w).asyncRouters[0].children,(function(e){return s(),v(h,null,[e.hidden?k("v-if",!0):(s(),y(T,{key:e.name,"is-collapse":q.value,"router-info":e,theme:F.value},null,8,["is-collapse","router-info","theme"]))],64)})),256))]})),_:1},8,["collapse","default-active","background-color","active-text-color"])]})),_:1})]})),_:1})],4)}}});e("default",n(a,[["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/index.vue"]]))}}}));
