/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{x as e,r as a,J as s,K as l,N as n,b as t,u as o,D as c,h as r,o as i,d as p,e as u,j as d,w as v,f as m,_ as f,F as b,i as g,g as h,k as y,t as k,I as _,B as w,M as W}from"./index.f6c71253.js";const S={class:"person"},D={class:"header-right"},C={class:"search-controls"},U={class:"category-title"},x=["onClick"],I={key:0,class:"status-badge"},O={class:"app-content"},A={class:"app-icon"},T={class:"tooltip-content"},E={key:0},F={key:1},N={class:"app-details"},P=["title"],V={key:0,class:"app-desc"},$=f(Object.assign({name:"AppPage"},{setup(f){const $=a(""),B=a(null),J=a([]),z=a([]),L=a("1"),M=a(!1),j=a("standard"),q=s([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),R=a(null),X=a(!1),H=(e,a="success",s=3e3)=>{W({message:e,type:a,duration:s})},G=async e=>new Promise(((a,s)=>{let l,n=!1;(async()=>{try{const t=await new Promise(((e,a)=>{if(R.value&&R.value.readyState===WebSocket.OPEN)return void e(R.value);const s=new WebSocket("ws://localhost:50001");X.value=!0,s.onopen=()=>{console.log("WebSocket Connected"),R.value=s,X.value=!1,e(s)},s.onmessage=e=>{const a=e.data;a.startsWith("Ok")||a.startsWith("Failed")&&H(a,"error")},s.onclose=()=>{console.log("WebSocket Disconnected"),R.value=null,X.value=!1},s.onerror=e=>{console.error("WebSocket Error:",e),X.value=!1,a(e)},setTimeout((()=>{X.value&&(X.value=!1,s.close(),a(new Error("连接超时")))}),5e3)})),o={action:3,msg:e};l=setTimeout((()=>{n||(t.close(),s(new Error("启动超时：未收到响应")))}),3e3),t.onmessage=e=>{n=!0,clearTimeout(l);const t=e.data;t.startsWith("Ok")?a():s(new Error(t))},t.send(JSON.stringify(o)),console.log("发送消息:",o)}catch(t){clearTimeout(l),s(t)}})()}));l((()=>{R.value&&(R.value.close(),R.value=null)}));const K=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let s=0;for(let l=0;l<e.length;l++)s+=e.charCodeAt(l);return a[s%a.length]},Q=()=>{M.value=!0},Y=e=>{B.value=parseInt(e),z.value=e?J.value.filter((a=>a.id===parseInt(e))):J.value},Z=()=>{if(!$.value)return void(z.value=J.value);const e=$.value.toLowerCase().trim();z.value=J.value.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))},ee=async()=>{try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(console.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl})))})));console.log("格式化后的数据:",e),J.value=e,z.value=e,e.length>0&&(B.value=e[0].id,L.value=e[0].id.toString())}}catch(a){console.error("API调用出错:",a)}};n((()=>{ee()}));const ae=t(),se=o().query;let le=null;try{if(!c.isClient()){const e=new XMLHttpRequest;e.open("GET",document.location,!1),e.send(null),le=e.getResponseHeader("X-Corp-ID")}}catch(te){console.warn("无法获取 X-Corp-ID header，使用默认值:",te)}const ne={action:0,msg:{token:ae.token.accessToken,refreshToken:ae.token.refreshToken,realm:le||"default"},platform:document.location.hostname};{const e=se.wp||50001,s=a({}),l=a(`ws://127.0.0.1:${e}`),n=navigator.platform;0!==n.indexOf("Mac")&&"MacIntel"!==n||(l.value=`wss://127.0.0.1:${e}`);const t=()=>{s.value=new WebSocket(l.value),s.value.onopen=()=>{console.log("socket连接成功"),o(JSON.stringify(ne))},s.value.onmessage=e=>{console.log(e),c()},s.value.onerror=()=>{console.log("socket连接错误:"+l.value),window.location.href=`asecagent://?web=${JSON.stringify(ne)}`}},o=e=>{console.log(e,"0"),s.value.send(e)},c=()=>{console.log("socket断开链接"),s.value.close()};console.log(`asecagent://?web=${JSON.stringify(ne)}`),t()}return(e,a)=>{const s=r("base-input"),l=r("base-button"),n=r("base-option"),t=r("base-select"),o=r("base-header"),c=r("base-menu-item"),f=r("base-menu"),W=r("base-aside"),B=r("base-avatar"),R=r("base-tooltip"),X=r("base-main"),ae=r("base-container");return i(),p("div",null,[u("div",S,[d(o,{class:"app-header"},{default:v((()=>[a[3]||(a[3]=u("div",{class:"header-left"},[u("h1",{class:"page-title"},"我的应用")],-1)),u("div",D,[u("div",C,[d(s,{class:"search-input",modelValue:$.value,"onUpdate:modelValue":a[0]||(a[0]=e=>$.value=e),placeholder:"搜索应用","prefix-icon":"Search",onInput:Z,clearable:""},null,8,["modelValue"]),d(l,{class:"refresh-btn",icon:"Refresh",size:"small",onClick:ee}),d(t,{class:"view-select",modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),placeholder:"视图",size:"small"},{default:v((()=>[(i(!0),p(b,null,g(q,(e=>(i(),h(n,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])]),m('\r\n        <div class="el-row">\r\n          <span class="el-recent-access">最新访问</span>\r\n          <span class="el-recent-data">\r\n            <span class="el-recent-item">\r\n              最新访问1\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问2\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问3\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <base-icon class="el-recent-clear" name="close" title="清空" />\r\n          </span>\r\n        </div>\r\n        ')])),_:1,__:[3]}),m(" 主体内容区域：使用 el-container 实现左右布局 "),d(ae,null,{default:v((()=>[m(" 左侧分类导航 "),d(W,{width:"96px",class:"category-aside"},{default:v((()=>[d(f,{class:"category-menu",mode:"vertical",onSelect:Y,"default-active":L.value},{default:v((()=>[d(c,{index:"0",onClick:a[2]||(a[2]=e=>Y(null))},{default:v((()=>a[4]||(a[4]=[y(" 全部 ")]))),_:1,__:[4]}),(i(!0),p(b,null,g(J.value,(e=>(i(),h(c,{key:e.id,index:e.id.toString()},{default:v((()=>[y(k(e.name),1)])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),m(" 右侧应用列表 "),d(X,{class:"app-main"},{default:v((()=>[(i(!0),p(b,null,g(z.value,(e=>(i(),p("div",{key:e.id,class:"category-section"},[m(" 分类标题 "),u("h3",U,k(e.name),1),m(" 应用列表 "),u("div",{class:_(["apps-grid",`view-${j.value}`])},[(i(!0),p(b,null,g(e.apps,(e=>(i(),p("div",{key:e.id,class:_(["app-item",{disabled:!e.WebUrl||e.maint}]),onClick:a=>(async e=>{if(e.WebUrl&&!e.maint)if(e.WebUrl.toLowerCase().startsWith("cs:")){const a=e.WebUrl.substring(3);try{H("正在启动爱尔企业浏览器...","info"),await G(a),H("启动成功","success")}catch(te){H("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else window.open(e.WebUrl,"_blank")})(e)},[e.maint?(i(),p("div",I," 维护中 ")):m("v-if",!0),u("div",O,[u("div",A,[d(R,{effect:"light",placement:"bottom"},{content:v((()=>[u("div",T,[e.WebUrl?(i(),p("span",E,k(e.WebUrl),1)):(i(),p("span",F,"暂无访问地址"))])])),default:v((()=>[d(B,{shape:"square",size:"compact"===j.value?40:48,src:e.icon,onError:Q,style:w(!e.icon||M.value?`background-color: ${K(e.app_name)} !important`:"")},{default:v((()=>[y(k(!e.icon||M.value?e.app_name.slice(0,2):""),1)])),_:2},1032,["size","src","style"])])),_:2},1024)]),u("div",N,[u("div",{class:"app-name",title:e.app_name},k(e.app_name),9,P),"standard"===j.value?(i(),p("div",V,k(e.app_desc||"应用程序"),1)):m("v-if",!0)])])],10,x)))),128))],2)])))),128))])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]);export{$ as default};
