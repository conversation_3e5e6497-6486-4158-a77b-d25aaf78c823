const fs = require('fs');
const path = require('path');

// 读取 dist/index.html 文件
const htmlPath = path.join(__dirname, '../dist/index.html');

if (fs.existsSync(htmlPath)) {
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    // 移除 type="module" 和 crossorigin 属性
    htmlContent = htmlContent
        .replace(/type="module"\s+crossorigin/g, '')
        .replace(/type="module"/g, '')
        .replace(/\s+crossorigin/g, '')
        .replace(/crossorigin\s+/g, '');
    
    // 写回文件
    fs.writeFileSync(htmlPath, htmlContent, 'utf8');
    
    console.log('✅ Successfully removed type="module" from index.html');
    console.log('🎉 Build is now compatible with local file access!');
} else {
    console.error('❌ dist/index.html not found');
}
