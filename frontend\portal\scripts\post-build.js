const fs = require('fs');
const path = require('path');

// 读取 dist/index.html 文件
const htmlPath = path.join(__dirname, '../dist/index.html');

if (fs.existsSync(htmlPath)) {
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');

    console.log('原始 HTML 长度:', htmlContent.length);

    // 移除空的 script 标签
    htmlContent = htmlContent.replace(
        /<script\s*><\/script>/g,
        ''
    );

    console.log('处理后 HTML 长度:', htmlContent.length);

    // 写回文件
    fs.writeFileSync(htmlPath, htmlContent, 'utf8');

    console.log('✅ Successfully processed index.html for local file access');
    console.log('🎉 Build is now compatible with local file access using legacy build!');
} else {
    console.error('❌ dist/index.html not found');
}
