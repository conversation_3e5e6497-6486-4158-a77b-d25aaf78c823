/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as k,J as p,r as o,z as g,h as c,o as n,f as s,w as a,d as f,j as d,C as _,g as v,e as h,t as x,F as I,P as B}from"./index.d0594432.js";const C={key:0,class:"gva-subMenu"},T={name:"AsyncSubmenu"},S=Object.assign(T,{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON><PERSON>an},theme:{default:function(){return{}},type:Object}},setup(e){p(r=>({c8e9c8aa:l.value,"6037b64a":u.value}));const t=e,y=o(t.theme.activeBackground),u=o(t.theme.activeText),l=o(t.theme.normalText);return g(()=>t.theme,()=>{y.value=t.theme.activeBackground,u.value=t.theme.activeText,l.value=t.theme.normalText}),(r,j)=>{const m=c("component"),i=c("el-icon"),b=c("el-sub-menu");return n(),s(b,{ref:"subMenu",index:e.routerInfo.name},{title:a(()=>[e.isCollapse?(n(),f(I,{key:1},[e.routerInfo.meta.icon?(n(),s(i,{key:0},{default:a(()=>[d(m,{class:_(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])]),_:1})):v("",!0),h("span",null,x(e.routerInfo.meta.title),1)],64)):(n(),f("div",C,[e.routerInfo.meta.icon?(n(),s(i,{key:0},{default:a(()=>[d(m,{class:_(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])]),_:1})):v("",!0),h("span",null,x(e.routerInfo.meta.title),1)]))]),default:a(()=>[B(r.$slots,"default",{},void 0,!0)]),_:3},8,["index"])}}}),w=k(S,[["__scopeId","data-v-547fcaa6"]]);export{w as default};
