/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
import{_ as a,h as e,o as s,d as t,f as o,j as r,e as i,g as l}from"./index.44d6e232.js";import n from"./header.827eb486.js";import d from"./menu.b746837b.js";import"./lodash.def54e57.js";import"./ASD.492c8837.js";const m={class:"layout-page"},u={class:"layout-wrap"},p={id:"layoutMain",class:"layout-main"},c=a(Object.assign({name:"Client"},{setup:a=>(a,c)=>{const f=e("router-view");return s(),t("div",m,[o("公共顶部菜单-"),r(n),i("div",u,[o("公共侧边栏菜单"),r(d),i("div",p,[o("主流程路由渲染点"),(s(),l(f,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{c as default};
