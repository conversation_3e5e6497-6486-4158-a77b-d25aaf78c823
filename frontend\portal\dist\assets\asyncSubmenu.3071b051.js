/*! 
 Build based on gin-vue-admin 
 Time : 1749623364000 */
import{_ as e,J as t,r as a,z as n,h as o,o as s,f as u,w as l,d as r,j as c,C as i,g as f,e as m,t as d,F as p,P as v}from"./index.49a4551d.js";const h={key:0,class:"gva-subMenu"},I=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:<PERSON>olean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({c8e9c8aa:y.value,"6037b64a":x.value})));const I=e,b=a(I.theme.activeBackground),x=a(I.theme.activeText),y=a(I.theme.normalText);return n((()=>I.theme),(()=>{b.value=I.theme.activeBackground,x.value=I.theme.activeText,y.value=I.theme.normalText})),(t,a)=>{const n=o("component"),I=o("el-icon"),b=o("el-sub-menu");return s(),u(b,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(p,{key:1},[e.routerInfo.meta.icon?(s(),u(I,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)],64)):(s(),r("div",h,[e.routerInfo.meta.icon?(s(),u(I,{key:0},{default:l((()=>[c(n,{class:i(["iconfont",e.routerInfo.meta.icon])},null,8,["class"])])),_:1})):f("",!0),m("span",null,d(e.routerInfo.meta.title),1)]))])),default:l((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-547fcaa6"]]);export{I as default};
