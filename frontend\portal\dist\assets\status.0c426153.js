/*! 
 Build based on gin-vue-admin 
 Time : 1749790504000 */
import{_ as a,u as e,a as s,r as t,b as l,c as n,p as i,o as r,d as o,e as c,f as u,F as d,g as p,L as v}from"./index.f6c71253.js";import h from"./secondaryAuth.33499b05.js";import"./verifyCode.d8469ab3.js";const y=""+new URL("login_building.7b8b4335.png",import.meta.url).href,A={class:"login-page"},g={class:"content"},m={class:"right-panel"},f={key:0,class:"auth-status"},w={class:"status-icon"},U={class:"icon","aria-hidden":"true",style:{height:"48px",width:"48px",color:"#0082ef"}},q={class:"auth-waiting"},b={class:"waiting-icon"},C={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},K=a(Object.assign({name:"Status"},{setup(a){const K=e(),S=s(),x=t({});let{code:I,state:P}=K.query;const{auth_type:B,redirect_url:E,type:Q,wp:N}=K.query,O=window.location.host,D=window.location.protocol,F=l(),T=t(null),J=t(!1),R=t([]),W=t(!1),Y=t(""),Z=t(""),j=t(Array.isArray(P)?P[0]:P),k=t(""),H=t("phone"),G=t(!0),L=n((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===H.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===H.value}])),M=async a=>{var e;T.value=v.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let e=E||"/";if(a.clientParams){const s=new URLSearchParams;s.set("type",a.clientParams.type),a.clientParams.wp&&s.set("wp",a.clientParams.wp),e+=(e.includes("?")?"&":"?")+s.toString()}window.location.href=e}finally{null==(e=T.value)||e.close()}},V=()=>{J.value=!1,W.value=!1;const a=new URLSearchParams;a.set("idp_id",Array.isArray(P)?P[0]:P),E&&a.set("redirect",encodeURIComponent(E)),"client"===Q&&(a.set("type","client"),N&&a.set("wp",N));const e=`/login?${a.toString()}`;S.push(e)};return(async()=>{var a;T.value=v.service({fullscreen:!0,text:"登录中，请稍候..."});try{const a={clientId:"client_portal",grantType:"implicit",redirect_uri:`${D}//${O}/#/status`,idpId:Array.isArray(P)?P[0]:P,authWeb:{authWebCode:Array.isArray(I)?I[0]:I}},e=await F.LoginIn(a,B,j.value);if(-1!==e.code)e.isSecondary&&(W.value=e.isSecondary,R.value=e.secondary,Z.value=e.secondary[0].id,Y.value=e.uniqKey,k.value=e.userName,H.value=e.contactType,G.value=e.hasContactInfo||!1,x.value.uniqKey=e.uniqKey,x.value.name=e.secondary[0].name,x.value.notPhone=e.notPhone,J.value=!0);else{let a=`${D}//${O}/#/login?idp_id=${Array.isArray(P)?P[0]:P}`;E&&(a+=`&redirect=${E}`),"client"===Q&&(a+="&type=client",N&&(a+=`&wp=${N}`)),location.href=a}}catch(e){console.error("登录处理失败:",e);let a=`${D}//${O}/#/login?idp_id=${Array.isArray(P)?P[0]:P}`;"client"===Q&&(a+="&type=client",N&&(a+=`&wp=${N}`)),location.href=a}finally{null==(a=T.value)||a.close()}})(),n((()=>R.value.filter((a=>a.id!==Z.value)))),i("userName",k),i("isSecondary",W),i("contactType",H),i("hasContactInfo",G),i("uniqKey",Y),i("last_id",j),(a,e)=>(r(),o("div",A,[e[8]||(e[8]=c("div",{class:"header"},[c("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAATCAYAAACKsM07AAAAAXNSR0IArs4c6QAAAq5JREFUSEudk11IU2EYx//PcZtmZlREiNgHlNsZqEHmWUYXUhdFIHUh0U3eFNGneGZRXe2uiO2MLKPoqrqRLvqEIvoES3fMGw13Vkr0odCNgmVsuZ3zxFk5t7NNWS8ceJ/n/J//733eD8I8wy2rhxncCcCRRzZDRG3hgHQtnw3l9W/hIrFK/QKgcr5FABjXyqNr4WtK5NLlBYhedQ+Y76UXMfDQjAlozjAj2qsFpPsFAVyy+oTAO1NFjBEt6Kk2Y1EOfQCQnJuDgacRxTOnTSPl7MDZoa4jg0cJEOZM6HJEkU6asUsOXSIgOf8HMHSObxgJbvtk7SInwN0eOs+EMxbxbk3xPDZz1d7+zUWs9wBUPKshxoVw0HN2QYC75Y6Dq9Z8BXhVujhuW1Q1erFubDa3yTdQGp+2l83GuhGrGFakwQUBoty3D6DuHAcWB2B+OQcD00UC7R/2Sy/TBVlbJMrqc4C35zOaN894rQU9TQsAQiqAhv8CAKyT4f4YaIykzsZq5Dz9Zgnptmo2KP8jBGCDUaET3SZgaYYHUVALSHIWYP2JkWK7faKbAFd6AQM9P9loGws2Rq2LEWX1KsBHLPnJaHm08rOvKWbmU6sUZfUAwDetJiwIzRF/w6NcW+aU+5oF0IPsf9SqKdItK+AtwI2ZYvptF0pWDPnrfuUC1HYMLo4bsQmAU+/hr456NUXamgK42gdqiBJD2Sb8QlO27IDvlc05VbqRGPYMjU0oJtbvgrEsq3O21UaC9e+TWyTKoSsAjllFBJwKKx6/6O3vAhtHC7xZXZriOU5mmzNGbJzAmbfBdBP0Gq3sWVj8ses7wCsLATBoyiGUVJLoVQ+C+UaOg/qmKdJqd3tvA5Ngvo3CB9EhEuXQOwD1luoEmM7FE8s77Y7J62BuLdw9WTHwB+of7onYTsUzAAAAAElFTkSuQmCC",alt:"公司logo",class:"logo"}),u(' <h1 class="company-name">安数达</h1>\r\n      <span class="separator"></span> '),c("span",{class:"header-text"},"ASec安全平台")],-1)),c("div",g,[e[7]||(e[7]=c("div",{class:"left-panel"},[u(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),c("img",{src:y,alt:"宣传图",class:"image"}),u(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),c("div",m,[u(" 显示当前认证状态 "),J.value?(r(),o(d,{key:1},[u(" 如果需要二次认证，显示等待提示 "),c("div",q,[c("div",b,[(r(),o("svg",C,e[4]||(e[4]=[c("use",{"xlink:href":"#icon-auth-verify_code"},null,-1)])))]),e[5]||(e[5]=c("h4",{class:"waiting-title"},"需要进行安全验证",-1)),e[6]||(e[6]=c("p",{class:"waiting-message"},"请完成二次身份验证以确保账户安全",-1))])],2112)):(r(),o("div",f,[c("div",w,[(r(),o("svg",U,e[0]||(e[0]=[c("use",{"xlink:href":"#icon-auth-qiyewx"},null,-1)])))]),e[1]||(e[1]=c("h3",{class:"status-title"},"正在登录",-1)),e[2]||(e[2]=c("p",{class:"status-message"},"正在处理信息...",-1)),e[3]||(e[3]=c("div",{class:"loading-dots"},[c("span"),c("span"),c("span")],-1))]))])]),u(" 新的辅助认证组件 "),J.value?(r(),p(h,{key:0,"auth-info":{uniqKey:Y.value,contactType:H.value,hasContactInfo:G.value},"auth-id":Z.value,"user-name":k.value,"last-id":j.value,"auth-methods":L.value,onVerificationSuccess:M,onCancel:V},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):u("v-if",!0)]))}}),[["__file","D:/asec-platform/frontend/portal/src/view/login/wx/status.vue"]]);export{K as default};
