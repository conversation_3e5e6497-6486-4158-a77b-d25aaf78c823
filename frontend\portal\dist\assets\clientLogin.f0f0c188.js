/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
const c={name:"ClientLogin"},a=Object.assign(c,{setup(r){var n=(t=>{console.log("1");var l=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i");console.log(2);var o=window.location.search.substr(1).match(l);return console.log(o),o!=null?decodeURI(o[2]):null})("type");console.log("type"),console.log(n);const e=window.localStorage.getItem("token")||"";return console.log(11),console.log(e),e&&n==="client"&&(window.location.href=`asecagent://?token=${e}`),(t,l)=>null}});export{a as default};
