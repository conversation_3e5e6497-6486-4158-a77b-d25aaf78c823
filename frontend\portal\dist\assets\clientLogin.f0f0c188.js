/*! 
 Build based on gin-vue-admin 
 Time : 1749604390000 */
const o=Object.assign({name:"ClientLog<PERSON>"},{setup(o){var e=(o=>{console.log("1");var e=new RegExp("(^|&)"+o+"=([^&]*)(&|$)","i");console.log(2);var n=window.location.search.substr(1).match(e);return console.log(n),null!=n?decodeURI(n[2]):null})("type");console.log("type"),console.log(e);const n=window.localStorage.getItem("token")||"";return console.log(11),console.log(n),n&&"client"===e&&(window.location.href=`asecagent://?token=${n}`),(o,e)=>null}});export{o as default};
