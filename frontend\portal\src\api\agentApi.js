/*
 * @Description: 和小助手交互的api
 */

import _ from 'lodash'
import ipcApi from '@/utils/ipcPlugin/index'

const agentApi = {
  // 操作的ipcclient客户端
  ipcClient: null,
  // 初始化插件
  init() { 
    if (!this.ipcClient) {
      this.ipcClient = ipcApi.init(app)
    }
  },
  getClientBaseInfo(timeout = false) {
    this.init()
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_GetClientBaseInfo', {}, {
      timeout: {
        time: timeout
      }
    })
  },
  // 小助手重新上报设备信息
  updateClientBaseInfo() {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_UpdateAgentInfo')
  },
  /**
   * 获取网卡列表
   */
  getNetworkList(param) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_GetNetworkList', param)
  },
  /**
   * 切换小助手模式
   */
  switchAgentMode(param) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_SwitchAgentMode', param)
  },
  /**
   * 认证完成将信息发送小助手
   */
  AuthEnd(params) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_AuthEnd', params)
  },
  /**
   * 获取安检策略
   */
  getSecurityCheckPolicy(params) {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_GetSecurityCheckPolicy', params)
  },
  /**
   * 调用小助手通用方法
   */
  callAgentOneFunc(params) {
    const apiParam = {
      WhatToDo: 'CallOneFunc',
      ...params
    }
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_CallOneFunc', apiParam)
  },
  /**
   * 获取安检结果
   */
  getSecurityCheckItem(params) {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_DoSecurityCheckItem', params)
  },
  /**
   * 安检上报
   */
  async securityCheckEnd(params) {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_DoSecurityCheckEnd', params)
  },
  /**
   * 获取需要安装补丁列表
   */
  getNeetInstallPatchList() {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_GetNeetInstallPatchList')
  },
  /**
   * 安检修复
   */
  repairSecCheckItem(params) {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_RepairSecCheckItem', params)
  },
  getAuthBaseInfo() {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_GetAuthBaseInfo')
  },
  /**
   * 802.1x认证
   * @param param
   * @returns {*}
   */
  dot1xAuth(param, timeout = false) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_Dot1xAuth', param, {
      timeout: {
        time: timeout
      }
    })
  },
  setUIContext(param) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_SetUIContext', param)
  },
  getAllUsbKeyCert(param) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_GetAllUsbKeyCert', param)
  },
  /**
   * 客户端调用web,监听
   * @param whatToDo string 信号名
   * @param callback Function 回调函数
   * @returns {*}
   */
  toRequestWeb(whatToDo = null, callback) {
    // if (isQtWebKit()) { // xp webkit 同一事件名(ToRequestWeb)后面回调函数会覆盖前面的，因此存一份回调到window上
    //   if (!window._toRequestWebEvents) {
    //     window._toRequestWebEvents = {}
    //   }
    //   const eventName = _.isNil(whatToDo) ? 'common' : whatToDo
    //   window._toRequestWebEvents[eventName] = callback
    // }
    return this.ipcClient.$ipcOn('UIPlatform_Window', 'ToRequestWeb', (strSerial, strWhatToDo, qMsgRequest) => {
      if (_.isEmpty(qMsgRequest)) {
        qMsgRequest = {}
      } else {
        try {
          // JSON.parse防止解析报错
          qMsgRequest = _.isObject(qMsgRequest) ? qMsgRequest : JSON.parse(qMsgRequest)
        } catch (e) {
          console.error(e)
          qMsgRequest = {}
        }
      }
      callback(strWhatToDo, qMsgRequest)
    })
  },
  /**
   * 唤醒远程协助
   * 0 同意
   * 1 取消
   * @returns {*}
   */
  requestRemoteScreen() {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_RequestRemoteScreen')
  },
  /**
   * 查询远程协助状态
   * 0 已远程
   * 1 未远程
   * @returns {*}
   */
  checkRemoteCtrlState() {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_CheckRemoteCtrlState')
  },
  /**
   * 不能await、then，没有响应
   */
  debugOut(log) {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'DebugOut', { strInfo: log }, {
      isNeedId: false
    })
  },
  fileTools: {
    config: {
      asm_root: '',
      dll: 'MsacFileApi.dll'
    },
    /**
     * 操作本地文件扩展接口
     * @param FileName 要操作的文件名 如x.ini
     * @param Type 要操作的类型如save del read
     * @param Strings 只针对save起作用 要保存的数据
     * @constructor
     */
    async ActionLocalFile(FileName, Type, Strings) {
      Type = Type || 'save'
      if (!FileName) {
        return false
      }
      const asm_root = await this.GetAsmDir()
      switch (Type) {
        case 'save':
          return this.SaveStrToFile(asm_root + FileName, Strings)
        case 'del':
          return this.DeleteOneFile(asm_root + FileName)
        case 'read':
          return this.ReadStrFromFile(asm_root + FileName)
        default:
          console.info('ActionLocalFile Type 错误！')
      }
    },
    /**
     * 获取ASM目录 return C:\Documents and Settings\Administrator\Application Data\Msac\
     */
    async GetAsmDir() {
      if (this.config.asm_root === '') {
        const apiParam = {
          WhereIsModule: this.config.dll,
          WhatFuncToCall: 'GetDir',
          RequestParam: 'ASM'
        }
        const runResult = await agentApi.callAgentOneFunc(apiParam)
        if (!_.isEmpty(runResult)) {
          this.config.asm_root = runResult
        }
      }
      return this.config.asm_root
    },
    /**
     * 读取一个文件
     */
    async ReadStrFromFile(filePath) {
      filePath = filePath.replace(/\s/g, '')
      const apiParam = {
        WhereIsModule: this.config.dll,
        WhatFuncToCall: 'ReadStrFromFile',
        RequestParam: filePath
      }
      const runResult = await agentApi.callAgentOneFunc(apiParam)
      if (!_.isEmpty(runResult)) {
        return runResult
      }
      return ''
    },
    /**
     * 获取目录文件列表
     */
    async GetFileList(dir) {
      const apiParam = {
        WhereIsModule: this.config.dll,
        WhatFuncToCall: 'GetFileList',
        RequestParam: dir
      }
      const runResult = await agentApi.callAgentOneFunc(apiParam)
      if (!_.isEmpty(runResult)) {
        return runResult
      }
      return ''
    },
    /**
     * 删除一个文件
     */
    async DeleteOneFile(file) {
      const apiParam = {
        WhereIsModule: this.config.dll,
        WhatFuncToCall: 'DeleteOneFile',
        RequestParam: file
      }
      const runResult = await agentApi.callAgentOneFunc(apiParam)
      if (!_.isEmpty(runResult)) {
        return runResult
      }
      return ''
    },
    /**
     * 删除指定文件
     */
    async DeleteOneFileNever(filename) {
      const asm_root = await this.GetAsmDir()
      const apiParam = {
        WhereIsModule: this.config.dll,
        WhatFuncToCall: 'DeleteOneFile',
        RequestParam: asm_root + filename
      }
      await agentApi.callAgentOneFunc(apiParam)
    },
    /**
     * 保存一个文件
     */
    async SaveStrToFile(file, str) {
      file = file.replace(/\s/g, '')
      str = file + '|' + str
      const apiParam = {
        WhereIsModule: this.config.dll,
        WhatFuncToCall: 'SaveStrToFile',
        RequestParam: str
      }
      await agentApi.callAgentOneFunc(apiParam)
    }
  },
  // 获取当前语言
  getLocalLangue() {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_IsiGetLangue')
  },
  // 设置本地语言
  setLocalLangue(params) {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_IsiSetLangue', params)
  },
  // 通知小助手注销
  logOut(params) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_AssUserLogout', params)
  },
  // 通过客户端接口使用浏览器打开网页
  windowOpenUrl(Path, ShowType = 0) {
    Path = Path.replace(/\s/g, '')
    if (Path.indexOf('://') === -1) {
      Path = 'http://' + Path
    }
    const params = {
      File: Path,
      Params: '',
      ShowType: ShowType,
      NeedWait: 0
    }

    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_AssShellOpen', params)
  },
  // 极速入网安检结果
  getSecCheckResult() {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_GetPersistantSecCheckResult')
  },
  // 设置内外网
  setNetIsolate(params) {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_NetIsolate', params)
  },
  // 获取内外网配置
  getNetIsolate(params) {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_GetPolicy', params)
  },
  // 设置用户名区域大小
  SetTitleDimension(params) {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'SetTitleDimension', params)
  },
  // ad域防伪造信息
  isDomainNoFake() {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_IsDomainNoFake')
  },
  // 获取第三方客户端的菜单
  getThirdLinkageMenu() {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_GetThirdLinkageMenu')
  },
  operateThirdLinkageMenu(params) {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_OperateThirdLinkageMenu', params)
  },
  // 敲端口-443
  spaToAuth(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_SPAToAuthServer', params)
  },
  // 敲端口-37527
  setLoginRet(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_SetLoginRet', params)
  },
  showWnd(params) {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'ShowWnd', params)
  },
  getGatewayInfos(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_GetGatewayInfos', params)
  },
  // 敲端口切换网络
  spaChangeNet(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_SwitchToInAndEx', params)
  },
  setDevAccessInfo(params) {
    return this.ipcClient.$ipcSend('AssUIPluginAuth', 'WebCall_SetDevAccessInfo', params)
  },
  openRDC(params) {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_OpenRDC', params)
  },
  // 获取已安装补丁
  getInstalledPatchList() {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_GetInstalledPatchList')
  },
  // 获取未安装补丁
  getPatchRepairDetailInfo() {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_GetPatchRepairDetailInfo')
  },
  // 补丁立即修复
  doRepairPatch(params) {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_DoRepairPatch', params)
  },
  // 关闭AssUI,会退出AssUI的进程
  terminateWnd() {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'TerminateWnd')
  },
  normalnizeWnd() {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'NormalnizeWnd')
  },
  maximizeWnd() {
    this.init()
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'MaximizeWnd')
  },
  minimizeWnd() {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'MinimizeWnd')
  },

  // 隐藏AssUI,不会退出AssUI的进程
  hideWend() {
    return this.ipcClient.$ipcSend('UIPlatform_Window', 'HideWnd')
  },

  notifyAssUIExcuteResult(params) {
    return this.ipcClient.$ipcSend('AssUIPluginSecurityCheck', 'WebCall_NotifyAssUIExcuteResult', params)
  },
  // 通过客户端接口使用自定义应用
  windowCustomAppOpenUrl(params) {
    const default_params = {
      ShowType: 0,
      NeedWait: 0,
      ResType: 1
    }
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_AssShellOpen', { ...default_params, ...params })
  },
  // 通知windows客户端当前入网状态
  refreshDeviceStatus(params) {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_RefreshDeviceStatus', params)
  },
  // 内外网切换
  changeNetMode(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_ChangeNetMode', params)
  },
  refreshVPNPolicy() {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_RefreshVPNPolicy', {})
  },
  // 检查安全空间是否安装
  checkSdcInstall(params) {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_CheckSDCClientInstalled')
  },
  // 打开安全空间
  openSdcApp(params) {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_OperateSDCLinkage', params)
  },
  // 读取网盘
  getFreeDriverLetter() {
    return this.ipcClient.$ipcSend('AssUIPluginTools', 'WebCall_GetFreeDriverLetter')
  },
  // 通过客户端接口使用自定义应用
  windowFileServeOpenUrl(params) {
    return this.ipcClient.$ipcSend('AssUIPluginBase', 'WebCall_AssShellOpen', params)
  },
  // 打开electron的调试工具
  openDevTools() {
    window.eleIPC.send('openDevTools')
  },
  // 唤醒electron,并且置于窗口的top
  evokeAndFocus() {
    window.eleIPC.send('evokeAndFocus')
  },
  elelWriteLog(params) {
    if (isElectron()) {
      window.eleIPC.send('log', params)
    }
  },
  // 上报网络数据
  notifyResGroup(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_NotifyResGroup', params)
  },
  // 设置隧道
  setVPNStatus(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_SetVPNStatus', params)
  },
  // 零信任接入设置
  setVPNInterceptMode(params) {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_SetVPNInterceptMode', params)
  },
  // 查询零信任配置
  queryVPNInfo() {
    return this.ipcClient.$ipcSend('AssUIPluginZTPModule', 'WebCall_QueryVPNInfo')
  },
  elelWindowIsVisible() {
    if (isElectron()) {
      return window.eleIPC.invoke('isVisible')
    }
    return ''
  }
}
export default agentApi
