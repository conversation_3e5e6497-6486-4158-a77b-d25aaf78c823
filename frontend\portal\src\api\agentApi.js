/*
 * @Description: 和小助手交互的api
 */

import _ from 'lodash'
import ipcApi from '@/utils/ipcPlugin/index'

const agentApi = {
  // 操作的ipcclient客户端
  ipcClient: null,
  // 初始化状态
  _initPromise: null,

  // 检查是否在 QT 环境中
  isQtEnvironment() {
    return typeof window !== 'undefined' &&
           (window.qt || window.QWebChannel || /QtWebEngine/.test(navigator.userAgent))
  },

  // 初始化插件
  async initIpcClient() {
    if (this._initPromise) {
      return this._initPromise
    }

    this._initPromise = this._doInit()
    return this._initPromise
  },

  async _doInit() {
    if (!this.ipcClient) {
      try {
        if (this.isQtEnvironment()) {
          this.ipcClient = await ipcApi.init()
          console.log('IPC 初始化成功')
        } else {
          console.warn('非 QT 环境，使用模拟 IPC 客户端')
          this.ipcClient = this._createMockIpcClient()
        }
      } catch (error) {
        console.error('IPC 初始化失败:', error)
        this.ipcClient = this._createMockIpcClient()
      }
    }
    return this.ipcClient
  },

  // 创建模拟的 IPC 客户端
  _createMockIpcClient() {
    return {
      $ipcSend: (module, action, params = {}) => {
        console.warn(`模拟 IPC 调用: ${module}.${action}`, params)
        return Promise.reject(new Error(`IPC not available in current environment (${module}.${action})`))
      },
      $ipcOn: (module, event, callback) => {
        console.warn(`模拟 IPC 监听: ${module}.${event}`)
      },
      $ipcOff: (module, event, callback) => {
        console.warn(`模拟 IPC 取消监听: ${module}.${event}`)
      },
      $processId: 0
    }
  },
  async normalnizeWnd() {
    await this.initIpcClient()
    return this.ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Normal'
      }
    })
  },
  async maximizeWnd() {
    await this.initIpcClient()
    return this.ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Max'
      }
    })
  },
  async minimizeWnd() {
    await this.initIpcClient()
    return this.ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Min'
      }
    })
  },

  // 隐藏AssUI,不会退出AssUI的进程
  async hideWend() {
    await this.initIpcClient()
    return this.ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Close'
      }
    })
  },
}
export default agentApi
