/*
 * @Description: 和小助手交互的api
 */

import _ from 'lodash'
import ipcApi from '@/utils/ipcPlugin/index'

const agentApi = {
  // 操作的ipcclient客户端
  _ipcClient: null,
  // 初始化状态
  _initPromise: null,
  // 是否客户端状态
  _isClient: null,
  // 客户端类型
  _ClientType: null,
  // 是否客户端
  isClient() {
    if (this._isClient !== null) {
      return this._isClient
    }
    urlHashParams.forEach((value, key) => {
      logger.log(`Url参数: ${key}: ${value}`);
    });
    let isClient = /QtWebEngine/.test(navigator.userAgent)
    if (!isClient) {
      // 支持url加参数模拟客户端
      if (urlHashParams && urlHashParams.get('AsecClient')) {
        isClient = true
      }
    }
    this._isClient = isClient
    logger.log('是否是客户端:', isClient)
    return this._isClient
  },
  // 获取客户端类型 windows表示windows，mac表示MAC，linux表示Linux，ios表示IOS，android表示Android，harmonyos表示鸿蒙
  getClientType() {
    if (this._ClientType !== null) {
      return this._ClientType
    }
    let clientType = 'web'
    if (this.isClient()) {
      let tmpClientType = urlHashParams ? urlHashParams.get('ClientType') : ''
      if (tmpClientType) {
        clientType = tmpClientType
      }
    }
    logger.log('客户端类型:', clientType)
    this._ClientType = clientType
    return this._ClientType
  },
  // 获取客户端参数
  getClientParams() {
    let params = {t: 1,}
    // 白名单字段
    const whiteList = ['WebUrl', 'ClientType', 'AsecDebug', 'AsecClient']
    if (urlHashParams) {
      whiteList.forEach(key => {
        let value = urlHashParams.get(key)
        if (value) {
          params[key] = value
        }
      })
    }
    return params
  },
  // 检查是否在 QT 环境中
  isQtEnvironment() {
    return typeof window !== 'undefined' &&
           (window.qt || window.QWebChannel || /QtWebEngine/.test(navigator.userAgent))
  },

  // 初始化插件
  async initIpcClient() {
    if (this._initPromise) {
      return this._initPromise
    }

    this._initPromise = this._doInit()
    return this._initPromise
  },

  async _doInit() {
    if (!this._ipcClient) {
      try {
        if (this.isQtEnvironment()) {
          this._ipcClient = await ipcApi.init()
          console.log('IPC 初始化成功')
        } else {
          console.warn('非 QT 环境，使用模拟 IPC 客户端')
          this._ipcClient = this._createMockIpcClient()
        }
      } catch (error) {
        console.error('IPC 初始化失败:', error)
        this._ipcClient = this._createMockIpcClient()
      }
    }
    return this._ipcClient
  },

  // 创建模拟的 IPC 客户端
  _createMockIpcClient() {
    return {
      $ipcSend: (module, action, params = {}) => {
        console.warn(`模拟 IPC 调用: ${module}.${action}`, params)
        return Promise.reject(new Error(`IPC not available in current environment (${module}.${action})`))
      },
      $ipcOn: (module, event, callback) => {
        console.warn(`模拟 IPC 监听: ${module}.${event}`)
      },
      $ipcOff: (module, event, callback) => {
        console.warn(`模拟 IPC 取消监听: ${module}.${event}`)
      },
      $processId: 0
    }
  },
  async normalnizeWnd() {
    await this.initIpcClient()
    return this._ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Normal'
      }
    })
  },
  async maximizeWnd() {
    await this.initIpcClient()
    return this._ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Max'
      }
    })
  },
  async minimizeWnd() {
    await this.initIpcClient()
    return this._ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Min'
      }
    })
  },

  // 隐藏AssUI,不会退出AssUI的进程
  async hideWend() {
    await this.initIpcClient()
    return this._ipcClient.$ipcSend('AsecMainFrame', 'WebCall_MainFrameTitleBar', {
      Action: {
        Type: 'Close'
      }
    })
  },
}
export default agentApi
