/*! 
 Build based on gin-vue-admin 
 Time : 1749722721000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(t,n,a){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a,t}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function l(n,a,r,o){var l=a&&a.prototype instanceof c?a:c,s=Object.create(l.prototype);return i(s,"_invoke",function(n,a,r){var i,o,l,c=0,s=r||[],p=!1,d={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,o=0,l=e,d.n=n,u}};function h(n,a){for(o=n,l=a,t=0;!p&&c&&!r&&t<s.length;t++){var r,i=s[t],h=d.p,v=i[2];n>3?(r=v===a)&&(l=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=h&&((r=n<2&&h<i[1])?(o=0,d.v=a,d.n=i[1]):h<v&&(r=n<3||i[0]>a||a>v)&&(i[4]=n,i[5]=a,d.n=v,o=0))}if(r||n>1)return u;throw p=!0,a}return function(r,s,v){if(c>1)throw TypeError("Generator is already running");for(p&&1===s&&h(s,v),o=s,l=v;(t=o<2?e:l)||!p;){i||(o?o<3?(o>1&&(d.n=-1),h(o,l)):d.n=l:d.v=l);try{if(c=2,i){if(o||(r="next"),t=i[r]){if(!(t=t.call(i,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),o=1);i=e}else if((t=(p=d.n<0)?l:n.call(a,d))!==u)break}catch(t){i=e,o=1,l=t}finally{c=1}}return{value:t,done:p}}}(n,r,o),!0),s}var u={};function c(){}function s(){}function p(){}t=Object.getPrototypeOf;var d=[][a]?t(t([][a]())):(i(t={},a,(function(){return this})),t),h=p.prototype=c.prototype=Object.create(d);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,i(e,o,"GeneratorFunction")),e.prototype=Object.create(h),e}return s.prototype=p,i(h,"constructor",p),i(p,"constructor",s),s.displayName="GeneratorFunction",i(p,o,"GeneratorFunction"),i(h),i(h,o,"Generator"),i(h,a,(function(){return this})),i(h,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:l,m:v}})()}function i(e,t,n,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,a){if(t)r?r(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n;else{var o=function(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},i(e,t,n,a)}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function u(e,t,n,a,r,i,o){try{var l=e[i](o),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(a,r)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function o(e){u(i,a,r,o,l,"next",e)}function l(e){u(i,a,r,o,l,"throw",e)}o(void 0)}))}}System.register(["./index-legacy.21dbeba9.js"],(function(e,t){"use strict";var a,i,l,u,s,p,d,h,v,f,m,g,y,x,b,w,k,j,O,C,_,S,P,T,z,L,q=document.createElement("style");return q.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+');background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:300px;box-sizing:border-box;min-width:380px;max-width:380px;margin-right:310px;margin-top:auto;margin-bottom:auto;padding:40px;background-color:rgba(255,255,255,.9);border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:75%;transform:translate(-50%,-50%)}.title{height:60px;font-size:24px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login_submit_button{width:100%;height:40px;margin-top:20px;font-size:16px;color:#fff;background-color:#2972c8;border:none;border-radius:5px;cursor:pointer}.submit-button:hover,.submit-button:active{background-color:#2972c8}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}.error-component{text-align:center;padding:20px;background-color:#fef2f2;border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;margin:10px 0}.error-component:before{content:"\\26a0\\fe0f";display:block;font-size:24px;margin-bottom:8px}.auth-switcher{margin-top:24px}.auth-switcher .auth-switcher-title{text-align:center;color:#929298;font-size:14px;margin-bottom:20px;position:relative}.auth-switcher .auth-switcher-title:before,.auth-switcher .auth-switcher-title:after{content:"";position:absolute;top:50%;width:80px;height:1px;background:#e8e8e8}.auth-switcher .auth-switcher-title:before{left:0}.auth-switcher .auth-switcher-title:after{right:0}.auth-switcher .auth-switcher-container{display:flex;align-items:center;justify-content:center;position:relative}.auth-switcher .auth-switcher-container .auth-nav-btn{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border:1px solid #e8e8e8;border-radius:50%;background:#ffffff;color:#666;cursor:pointer;transition:all .3s ease;z-index:2}.auth-switcher .auth-switcher-container .auth-nav-btn:hover:not(:disabled){border-color:#1890ff;color:#1890ff;box-shadow:0 2px 8px rgba(24,144,255,.2)}.auth-switcher .auth-switcher-container .auth-nav-btn:disabled{opacity:.3;cursor:not-allowed}.auth-switcher .auth-switcher-container .auth-nav-btn.auth-nav-next{margin-left:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper{flex:1;max-width:320px;overflow:hidden;position:relative}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container{display:flex;transition:transform .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item{flex:0 0 80px;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:12px 8px;cursor:pointer;transition:all .3s ease;border-radius:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover{background:#f8f9fa;transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,.1)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon{width:40px;height:40px;border-radius:50%;background:#f5f5f7;display:flex;align-items:center;justify-content:center;margin-bottom:8px;color:#fff;font-size:18px;transition:all .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-name{font-size:12px;color:#666;text-align:center;line-height:1.2;max-width:64px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover .auth-method-icon{transform:scale(1.1);box-shadow:0 4px 12px rgba(0,0,0,.2)}\n',document.head.appendChild(q),{setters:[function(e){a=e.x,i=e._,l=e.y,u=e.u,s=e.r,p=e.c,d=e.b,h=e.z,v=e.p,f=e.o,m=e.d,g=e.e,y=e.f,x=e.j,b=e.m,w=e.F,k=e.k,j=e.t,O=e.g,C=e.A,_=e.B,S=e.i,P=e.C,T=e.L,z=e.D,L=e.E}],execute:function(){var q={class:"login-page"},I={class:"content"},E={class:"right-panel"},A={key:0},U={key:0,class:"title"},D={key:1,class:"title"},G={style:{"text-align":"center"}},F={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},N={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},R=["xlink:href"],K={key:2,class:"login_panel_form"},M={key:3,class:"auth-switcher"},H={class:"auth-switcher-container"},J=["disabled"],W={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},B={class:"auth-methods-wrapper"},V=["onClick"],X=["data-auth-type"],Y={class:"icon","aria-hidden":"true",style:{height:"18px",width:"18px"}},$=["xlink:href"],Q={class:"auth-method-name"},Z=["disabled"],ee={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px"}},te={class:"auth-waiting"},ne={class:"waiting-icon"},ae={class:"icon","aria-hidden":"true",style:{height:"32px",width:"32px",color:"#f4a261"}},re=["xlink:href"],ie={class:"waiting-title"},oe={class:"security-tips"},le={class:"icon","aria-hidden":"true",style:{height:"16px",width:"16px",color:"#67c23a"}},ue=Object.assign({name:"Login"},{setup:function(e){var i=l({loader:function(){return P((function(){return t.import("./localLogin-legacy.fda7d4d1.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=l({loader:function(){return P((function(){return t.import("./wechat-legacy.47bd54d7.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ce=l({loader:function(){return P((function(){return t.import("./feishu-legacy.3b59ab71.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=l({loader:function(){return P((function(){return t.import("./dingtalk-legacy.507182be.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),pe=l({loader:function(){return P((function(){return t.import("./oauth2-legacy.c6c3fe59.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),de=l({loader:function(){return P((function(){return t.import("./sms-legacy.e5d29405.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">短信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),he=l({loader:function(){return P((function(){return t.import("./secondaryAuth-legacy.53d8844e.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ve=l({loader:function(){return P((function(){return t.import("./serverConfig-legacy.a3c523f4.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),fe=u(),me=s(0),ge=s([]),ye=s("local"),xe=s(""),be=s(""),we=s(""),ke=s([]),je=s([]),Oe=s(!1),Ce=s(!1),_e=s(),Se=s(""),Pe=s(!1),Te=s(""),ze=s(!1),Le=s(""),qe=s(""),Ie=s(""),Ee=s({}),Ae=s(0),Ue=s(80),De=s(3),Ge=s(null),Fe=p((function(){var e=Oe.value?Le.value:be.value;return ge.value.filter((function(t){return t.id!==e}))})),Ne=p((function(){return Math.max(0,Fe.value.length-De.value)})),Re=d();p((function(){return je.value.filter((function(e){return e.id!==be.value}))}));var Ke=function(){var e={};if(fe.query.type&&(e.type=fe.query.type),fe.query.wp&&(e.wp=fe.query.wp),fe.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(fe.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),a=new URLSearchParams(n);a.get("type")&&(e.type=a.get("type")),a.get("wp")&&(e.wp=a.get("wp"))}}catch(r){console.warn("解析redirect参数失败:",r)}return e},Me=function(){if(z.isClient()){var e=urlHashParams?urlHashParams.get("WebUrl"):"";try{var t=new URL(e);e="".concat(t.protocol,"//").concat(t.host)}catch(n){e="",console.warn("解析 WebUrl 参数失败:",n)}if(e)return!1;if(!localStorage.getItem("server_host"))return!0}return!1},He=function(e){logger.log("服务器配置完成:",e),Ce.value=!1,Je()},Je=function(){var e=c(r().m((function e(){var t,n,i,l,u,c,s,p,d,h,v,f,m,g,y,x,b,w,k,j;return r().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,!Me()){e.n=1;break}return Ce.value=!0,e.a(2);case 1:return t=Ke(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),e.n=2,a({url:"/auth/login/v1/user/main_idp/list",method:"get"});case 2:if(200===(n=e.v).status){if(ge.value=n.data.idpList,(i=fe.query.idp_id||Re.loginType)&&"undefined"!==i){l=!1,u=o(n.data.idpList);try{for(u.s();!(c=u.n()).done;)s=c.value,i===s.id&&(l=!0,be.value=s.id,ye.value=s.type,xe.value=s.templateType,ke.value=s.attrs,ke.value.name=s.name,ke.value.authType=s.type)}catch(r){u.e(r)}finally{u.f()}l||(we.value=null===(p=ge.value[0])||void 0===p?void 0:p.id,be.value=null===(d=ge.value[0])||void 0===d?void 0:d.id,ye.value=null===(h=ge.value[0])||void 0===h?void 0:h.type,xe.value=null===(v=ge.value[0])||void 0===v?void 0:v.templateType,ke.value=null===(f=ge.value[0])||void 0===f?void 0:f.attrs,ke.value.name=ge.value[0].name,ke.value.authType=null===(m=ge.value[0])||void 0===m?void 0:m.type)}else we.value=null===(g=ge.value[0])||void 0===g?void 0:g.id,be.value=null===(y=ge.value[0])||void 0===y?void 0:y.id,ye.value=null===(x=ge.value[0])||void 0===x?void 0:x.type,xe.value=null===(b=ge.value[0])||void 0===b?void 0:b.templateType,ke.value=null===(w=ge.value[0])||void 0===w?void 0:w.attrs,ke.value.name=ge.value[0].name,ke.value.authType=null===(k=ge.value[0])||void 0===k?void 0:k.type;++me.value}e.n=4;break;case 3:e.p=3,j=e.v,console.error("获取认证列表失败:",j),z.isClient()&&(Ce.value=!0);case 4:return e.a(2)}}),e,null,[[0,3]])})));return function(){return e.apply(this,arguments)}}();Je();var We=p((function(){switch(ye.value){case"local":case"msad":case"ldap":case"web":case"email":return i;case"qiyewx":return ue;case"feishu":return ce;case"dingtalk":return se;case"oauth2":case"cas":return pe;case"sms":return de;default:return"oauth2"===xe.value?pe:"local"}})),Be=p((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===Te.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===Te.value}]})),Ve=function(){Oe.value=!1,je.value=[],_e.value="",Se.value="",Te.value="",ze.value=!1,Le.value&&(be.value=Le.value,ye.value=qe.value,xe.value=Ie.value,ke.value=n({},Ee.value),Le.value="",qe.value="",Ie.value="",Ee.value={}),++me.value,console.log("取消后恢复的状态:",{isSecondary:Oe.value,auth_id:be.value,auth_type:ye.value})},Xe=function(){var e=c(r().m((function e(t){var n,a,i;return r().w((function(e){for(;;)switch(e.n){case 0:n=T.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{a=fe.query.redirect_url||"/",t.clientParams&&((i=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&i.set("wp",t.clientParams.wp),a+=(a.includes("?")?"&":"?")+i.toString()),window.location.href=a}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ye=p((function(){return!["dingtalk","feishu","qiyewx"].includes(ye.value)&&("oauth2"!==xe.value&&"cas"!==ye.value||("cas"===ye.value?1===parseInt(ke.value.casOpenType):"oauth2"===xe.value&&1===parseInt(ke.value.oauth2OpenType)))})),$e=function(){Ae.value>0&&Ae.value--},Qe=function(){Ae.value<Ne.value&&Ae.value++},Ze=function(e){return L[e]||"user"},et=function(e){we.value=e.id,ke.value=e.attrs||{},ke.value.name=e.name,ke.value.authType=e.type,Oe.value&&(ke.value.uniqKey=_e.value,ke.value.notPhone=Pe.value),be.value=e.id,ye.value=e.type,xe.value=e.templateType,++me.value};return h(Oe,c(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:Oe.value&&(Le.value=be.value,qe.value=ye.value,Ie.value=xe.value,Ee.value=n({},ke.value),console.log("二次认证数据:",{secondary:je.value,secondaryLength:je.value.length}),je.value.length>0&&et(je.value[0]));case 1:return e.a(2)}}),e)})))),v("secondary",je),v("isSecondary",Oe),v("uniqKey",_e),v("userName",Se),v("notPhone",Pe),v("last_id",we),v("contactType",Te),v("hasContactInfo",ze),function(e,t){return f(),m("div",q,[g("div",I,[t[6]||(t[6]=g("div",{class:"left-panel"},[y(' <h2 class="slogan">让办公无界，让数据无忧！</h2> '),y('<img src="@/assets/login_building.png" alt="宣传图" class="image">'),y(' <div class="icons">\r\n          <img src="@/assets/aq.png" alt="图标1">\r\n          <img src="@/assets/sd.png" alt="图标2">\r\n          <img src="@/assets/cj.png" alt="图标3">\r\n        </div> ')],-1)),g("div",E,[y(" 服务器配置状态 "),Ce.value?(f(),m("div",A,[x(b(ve),{onServerConfigured:He})])):Oe.value?(f(),m(w,{key:2},[y(" 二次认证等待状态 "),g("div",te,[g("div",ne,[(f(),m("svg",ae,[g("use",{"xlink:href":"#icon-".concat(Ze(qe.value||ye.value))},null,8,re)]))]),g("h4",ie,j(Ee.value.name||ke.value.name)+" 登录成功",1),t[5]||(t[5]=g("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),g("div",oe,[(f(),m("svg",le,t[3]||(t[3]=[g("use",{"xlink:href":"#icon-shield"},null,-1)]))),t[4]||(t[4]=g("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])],2112)):(f(),m(w,{key:1},[y(" 正常登录状态 "),g("div",null,["local"===ye.value?(f(),m("span",U,"本地账号登录")):Ye.value?(f(),m("span",D,[g("div",G,[g("span",F,[(f(),m("svg",N,[g("use",{"xlink:href":"#icon-auth-"+ye.value},null,8,R)])),k(" "+j(ke.value.name),1)])])])):y("v-if",!0),be.value?(f(),m("div",K,[y(' <component :is="getLoginType"></component> '),(f(),O(C(We.value),{auth_id:be.value,auth_info:ke.value},null,8,["auth_id","auth_info"])),y(' <LocalLogin v-if="auth_type===\'local\'" :auth_id="auth_id"></LocalLogin> ')])):y("v-if",!0),Fe.value.length>0?(f(),m("div",M,[t[2]||(t[2]=g("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),g("div",H,[Ae.value>0?(f(),m("button",{key:0,class:"auth-nav-btn auth-nav-prev",onClick:$e,disabled:0===Ae.value},[(f(),m("svg",W,t[0]||(t[0]=[g("use",{"xlink:href":"#icon-chevron-left"},null,-1)])))],8,J)):y("v-if",!0),g("div",B,[g("div",{class:"auth-methods-container",ref_key:"authMethodsContainer",ref:Ge,style:_({transform:"translateX(-".concat(Ae.value*Ue.value,"px)")})},[(f(!0),m(w,null,S(Fe.value,(function(e){return f(),m("div",{key:e.id,class:"auth-method-item",onClick:function(t){return et(e)}},[g("div",{class:"auth-method-icon","data-auth-type":e.type},[(f(),m("svg",Y,[g("use",{"xlink:href":"#icon-".concat(Ze(e.type))},null,8,$)]))],8,X),g("div",Q,j(e.name),1)],8,V)})),128))],4)]),Ae.value<Ne.value?(f(),m("button",{key:1,class:"auth-nav-btn auth-nav-next",onClick:Qe,disabled:Ae.value>=Ne.value},[(f(),m("svg",ee,t[1]||(t[1]=[g("use",{"xlink:href":"#icon-chevron-right"},null,-1)])))],8,Z)):y("v-if",!0)])])):y("v-if",!0)])],2112))])]),y(" 二次认证弹窗 "),Oe.value?(f(),O(b(he),{key:0,"auth-info":{uniqKey:_e.value,contactType:Te.value,hasContactInfo:ze.value},"auth-id":be.value,"user-name":Se.value,"last-id":we.value,"auth-methods":Be.value,onVerificationSuccess:Xe,onCancel:Ve},null,8,["auth-info","auth-id","user-name","last-id","auth-methods"])):y("v-if",!0)])}}});e("default",i(ue,[["__file","D:/asec-platform/frontend/portal/src/view/login/index.vue"]]))}}}))}();
