/*! 
 Build based on gin-vue-admin 
 Time : 1749730289000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(t,o,i,a){var c=o&&o.prototype instanceof f?o:f,l=Object.create(c.prototype);return n(l,"_invoke",function(t,n,o){var i,a,c,f=0,l=o||[],s=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,a=0,c=e,d.n=n,u}};function p(t,n){for(a=t,c=n,r=0;!s&&f&&!o&&r<l.length;r++){var o,i=l[r],p=d.p,h=i[2];t>3?(o=h===n)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=t<2&&p<i[1])?(a=0,d.v=n,d.n=i[1]):p<h&&(o=t<3||i[0]>n||n>h)&&(i[4]=t,i[5]=n,d.n=h,a=0))}if(o||t>1)return u;throw s=!0,n}return function(o,l,h){if(f>1)throw TypeError("Generator is already running");for(s&&1===l&&p(l,h),a=l,c=h;(r=a<2?e:c)||!s;){i||(a?a<3?(a>1&&(d.n=-1),p(a,c)):d.n=c:d.v=c);try{if(f=2,i){if(a||(o="next"),r=i[o]){if(!(r=r.call(i,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,a<2&&(a=0)}else 1===a&&(r=i.return)&&r.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=e}else if((r=(s=d.n<0)?c:t.call(n,d))!==u)break}catch(r){i=e,a=1,c=r}finally{f=1}}return{value:r,done:s}}}(t,i,a),!0),l}var u={};function f(){}function l(){}function s(){}r=Object.getPrototypeOf;var d=[][i]?r(r([][i]())):(n(r={},i,(function(){return this})),r),p=s.prototype=f.prototype=Object.create(d);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,n(t,a,"GeneratorFunction")),t.prototype=Object.create(p),t}return l.prototype=s,n(p,"constructor",s),n(s,"constructor",l),l.displayName="GeneratorFunction",n(s,a,"GeneratorFunction"),n(p),n(p,a,"Generator"),n(p,i,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:c,m:h}})()}function n(t,e,r,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}n=function(t,e,r,o){if(e)i?i(t,e,{value:r,enumerable:!o,configurable:!o,writable:!o}):t[e]=r;else{var a=function(e,r){n(t,e,(function(t){return this._invoke(e,r,t)}))};a("next",0),a("throw",1),a("return",2)}},n(t,e,r,o)}function e(t,n,e,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?n(u):Promise.resolve(u).then(r,o)}function r(t){return function(){var n=this,r=arguments;return new Promise((function(o,i){var a=t.apply(n,r);function c(t){e(a,o,i,c,u,"next",t)}function u(t){e(a,o,i,c,u,"throw",t)}c(void 0)}))}}System.register(["./index-legacy.960b3170.js"],(function(n,e){"use strict";var o,i,a,c,u,f,l,s,d,p;return{setters:[function(t){o=t._,i=t.a,a=t.r,c=t.b,u=t.z,f=t.o,l=t.d,s=t.e,d=t.k,p=t.Y}],execute:function(){var e={style:{"text-align":"center"}},h={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},y={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},v={name:"Dingtalk",mounted:function(){this.loadThirdPartyScript()},methods:{loadThirdPartyScript:function(){var t=this,n=document.createElement("script");n.src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js",n.onload=function(){t.doSomethingWithThirdPartyLibrary()},document.body.appendChild(n)}}},g=Object.assign(v,{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup:function(n){var o=i(),v=a(0),g=n,m=function(){var n=r(t().m((function n(){var e,r;return t().w((function(t){for(;;)switch(t.n){case 0:return e={type:"dingtalk",data:{idpId:g.auth_id}},t.n=1,p(e);case 1:if(200!==(r=t.v).status){t.n=2;break}return t.a(2,r.data.uniqKey);case 2:return t.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();c();var b=function(){var n=r(t().m((function n(){var e,r,i,a;return t().w((function(t){for(;;)switch(t.n){case 0:return t.n=1,m();case 1:e=g.auth_info.dingtalkAppKey,r=window.location.host,i=window.location.protocol,a="".concat(i,"//").concat(r),setTimeout((function(){window.DTFrameLogin({id:"self_defined_element",width:300,height:300},{redirect_uri:encodeURIComponent(a),client_id:e,scope:"openid",response_type:"code",state:g.auth_id,prompt:"consent"},(function(t){t.redirectUrl;var n=t.authCode,e=t.state;o.push({name:"Status",query:{code:n,state:e,auth_type:"dingtalk"},replace:!0})}),(function(t){t&&console.error("钉钉登录错误:",t)}))}),100);case 2:return t.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return b(),u(g,function(){var n=r(t().m((function n(e,r){return t().w((function(t){for(;;)switch(t.n){case 0:return v.value++,t.n=1,b();case 1:return t.a(2)}}),n)})));return function(t,e){return n.apply(this,arguments)}}()),function(t,n){return f(),l("div",{key:v.value},[s("div",e,[s("span",h,[(f(),l("svg",y,n[0]||(n[0]=[s("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),n[1]||(n[1]=d(" 钉钉认证 "))])]),n[2]||(n[2]=s("div",{id:"self_defined_element",class:"self-defined-classname"},null,-1))])}}});n("default",o(g,[["__file","D:/asec-platform/frontend/portal/src/view/login/dingtalk/dingtalk.vue"]]))}}}))}();
