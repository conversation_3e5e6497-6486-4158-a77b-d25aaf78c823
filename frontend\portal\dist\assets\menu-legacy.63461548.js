/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?t(Object(i),!0).forEach((function(t){r(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):t(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,n||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}System.register(["./index-legacy.00b16b45.js"],(function(e,t){"use strict";var r,i,a,o,u,c,d,l,m,s=document.createElement("style");return s.textContent='@charset "UTF-8";.layout-aside[data-v-aa2fe5d8]{width:56px;height:100%;background:#F5F5F7;overflow:auto;z-index:10}.layout-aside .u-offlineTips[data-v-aa2fe5d8]{width:100%;padding:10px;background:#fceded;display:flex;justify-content:center}.layout-aside .u-offlineTips .off-tip-content[data-v-aa2fe5d8]{display:flex;line-height:20px;font-size:14px;color:#e65353}.layout-aside .u-offlineTips .off-tip-content i[data-v-aa2fe5d8]{padding-right:10px;font-size:14px}.layout-aside .menu-wrapper[data-v-aa2fe5d8]{padding-bottom:60px;padding-top:24px;margin:0}.layout-aside .menu-wrapper .menu-item[data-v-aa2fe5d8]{height:65px;font-size:13px;color:#686e84;font-weight:400;display:flex;flex-direction:column;align-items:center;justify-content:center}.layout-aside .menu-wrapper .menu-item .menu-item-title[data-v-aa2fe5d8]{height:17px;width:24px;font-size:12px;font-family:PingFang SC,PingFang SC-Medium;font-weight:Medium}.layout-aside .menu-wrapper .menu-item .menu-item-icon[data-v-aa2fe5d8]{transform:scaleY(-1);height:18px;width:18px;margin-bottom:6px;fill:currentColor}.layout-aside .menu-wrapper .menu-item[data-v-aa2fe5d8]:hover{background:#EBEBED;color:#536ce6;border-radius:4px;cursor:pointer}.layout-aside .menu-wrapper .menu-item:hover .iconfont[data-v-aa2fe5d8]{color:#536ce6}.layout-aside .menu-wrapper .active-menu-item[data-v-aa2fe5d8]{border-radius:4px;color:#536ce6}.layout-aside .menu-wrapper .active-menu-item[data-v-aa2fe5d8]:hover{border-radius:4px}.layout-aside .version-wrapper[data-v-aa2fe5d8]{position:fixed;bottom:1px;left:1px;width:200px;background:#F5F5F7;font-size:12px;line-height:33px;text-align:center;color:#b3b6c1;z-index:11}\n',document.head.appendChild(s),{setters:[function(e){r=e._,i=e.D,a=e.o,o=e.d,u=e.e,c=e.F,d=e.i,l=e.I,m=e.t}],execute:function(){var t=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],s={name:"ClientMenu",data:function(){return{currentRouteCode:"101"}},computed:{computedMenu:function(){return this.computedMenuFun()}},watch:{$route:{handler:function(e,t){if(logger.log("路由变化",e,t),e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun:function(){var e=[];return t&&t.forEach((function(t){if(t.meta&&t.meta.menu){var n=t.meta.menu,r=n.name,i=n.icon,a=n.uiId,o={name:r,icon:i,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:a};e.push(o)}})),e},changeMenu:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;logger.log("切换菜单:",e,t);var a=i.getClientParams(),o=n(n({},t),a);logger.log("切换菜单携带客户端参数:",o),this.$router.push({path:e,query:o}),this.currentRouteCode=this.cutOut(r)},routerInterceptor:function(e){var t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:function(e){return e&&e.length?e.substr(0,3):e}}},p={class:"layout-aside"},f={class:"menu-wrapper"},h=["onClick"],g={class:"icon menu-item-icon","aria-hidden":"true"},y=["xlink:href"],v={class:"menu-item-title"};e("default",r(s,[["render",function(e,t,n,r,i,s){return a(),o("div",p,[u("ul",f,[(a(!0),o(c,null,d(s.computedMenu,(function(e){return a(),o("li",{key:e.code,class:l(["menu-item",s.cutOut(e.code)===i.currentRouteCode?"active-menu-item":""]),onClick:function(t){return s.changeMenu(e.url,e.params,e.code)}},[(a(),o("svg",g,[u("use",{"xlink:href":"#"+e.icon},null,8,y)])),u("div",v,m(e.name),1)],10,h)})),128))])])}],["__scopeId","data-v-aa2fe5d8"],["__file","D:/asec-platform/frontend/portal/src/view/client/menu.vue"]]))}}}))}();
