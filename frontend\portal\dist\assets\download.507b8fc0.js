/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{g as D,b as C}from"./browser.ff6109de.js";import{_ as I,r as w,h as k,N as O,o as d,d as r,e as a,j as u,w as p,k as f,f as U,g as R,O as E,M as N}from"./index.d0594432.js";const j={class:"client"},q={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}},P={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},V={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px","margin-left":"39%",display:"none"}},T={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},A={class:"icon window-hidden","aria-hidden":"true",style:{"font-size":"43px","margin-left":"39%","margin-top":"60px",display:"none"}},Q={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},W={class:"icon window-show","aria-hidden":"true",style:{"font-size":"43px","margin-top":"60px"}},G={__name:"download",setup(H){const L=w(""),M=w(""),y=w(!1),h=w(!1),_=w(!1),b=w(!1),m=w({windows:0,darwin:0}),F=s=>s===100?"\u5B8C\u6210":`${s}%`,S=(s,e)=>new Promise((o,i)=>{const n=new XMLHttpRequest;n.open("GET",s,!0),n.responseType="blob",n.onprogress=l=>{if(l.lengthComputable){const t=l.loaded/l.total*100;m.value[e]=Math.round(t)}},n.onload=()=>{n.status===200?o(n.response):i(new Error("\u4E0B\u8F7D\u5931\u8D25"))},n.onerror=()=>{i(new Error("\u7F51\u7EDC\u9519\u8BEF"))},n.send()}),B=(s,e)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(s,e);else{const o=document.createElement("a"),i=document.querySelector("body");o.href=window.URL.createObjectURL(s),o.download=e,o.style.display="none",i.appendChild(o),o.click(),i.removeChild(o),window.URL.revokeObjectURL(o.href)}z()},z=()=>{y.value=!1,h.value=!1,_.value=!1,b.value=!1,Object.keys(m.value).forEach(s=>{m.value[s]=0})},c=w(!1),x=async s=>{if((s==="android"||s==="ios")&&c.value)return;c.value=!0;const e={windows:y,darwin:h,ios:_,android:b}[s];e.value=!0;try{const o=await D({platform:s});if(o.data.code===0)if(s==="ios"){const i=await C.toDataURL(o.data.data.download_url),n=document.getElementById("ioscanvas");if(M.value=i,n){const l=n.getContext("2d"),t=new Image;t.onload=()=>{n.width=t.width,n.height=t.height,l.drawImage(t,0,0)},t.src=i}}else if(s==="android"){const i=window.location.port,n=new URL(o.data.data.download_url);let l;i?n.toString().includes("asec-deploy")?l=o.data.data.download_url:(n.port=i,l=n.toString()):(n.port="",l=n.toString());const t=await C.toDataURL(l),g=document.getElementById("canvas");if(L.value=t,g){const $=g.getContext("2d"),v=new Image;v.onload=()=>{g.width=v.width,g.height=v.height,$.drawImage(v,0,0)},v.src=t}}else{const i=window.location.port,n=new URL(o.data.data.download_url);let l,t;i?(n.toString().includes("asec-deploy")?l=o.data.data.download_url:(n.port=i,l=n.toString()),t=o.data.data.latest_filename.replace(/@(\d+)/,`@${i}`)):(n.port="",l=n.toString(),t=o.data.data.latest_filename);const g=await S(l,s);B(g,t)}else throw new Error(o.data.msg)}catch(o){N({type:"error",message:o.message||"\u4E0B\u8F7D\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458"})}finally{e.value=!1}};return(s,e)=>{const o=k("el-link"),i=k("el-progress"),n=k("base-main"),l=O("loading");return d(),r("div",null,[a("div",j,[u(n,null,{default:p(()=>[a("div",q,[a("div",{style:{float:"left","margin-right":"5%",width:"209px",height:"209px",background:"#F1F8FF"},onClick:e[0]||(e[0]=t=>x("windows"))},[(d(),r("svg",P,e[6]||(e[6]=[a("use",{"xlink:href":"#icon-windows"},null,-1)]))),(d(),r("svg",V,e[7]||(e[7]=[a("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[10]||(e[10]=a("br",null,null,-1)),u(o,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p(()=>e[8]||(e[8]=[f("Windows\u5BA2\u6237\u7AEF")])),_:1,__:[8]}),u(o,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:p(()=>e[9]||(e[9]=[f("\u70B9\u51FB\u4E0B\u8F7DWindows\u5BA2\u6237\u7AEF")])),_:1,__:[9]}),y.value?(d(),U(i,{key:0,percentage:m.value.windows,format:F,style:{"margin-top":"10px"}},null,8,["percentage"])):R("",!0)]),a("div",{style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onClick:e[1]||(e[1]=t=>x("darwin"))},[(d(),r("svg",T,e[11]||(e[11]=[a("use",{"xlink:href":"#icon-mac"},null,-1)]))),(d(),r("svg",A,e[12]||(e[12]=[a("use",{"xlink:href":"#icon-xiazai"},null,-1)]))),e[15]||(e[15]=a("br",null,null,-1)),u(o,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p(()=>e[13]||(e[13]=[f("Mac\u5BA2\u6237\u7AEF")])),_:1,__:[13]}),u(o,{class:"window-hidden",underline:!1,style:{"margin-top":"30px",display:"none"}},{default:p(()=>e[14]||(e[14]=[f("\u70B9\u51FB\u4E0B\u8F7DMac\u5BA2\u6237\u7AEF")])),_:1,__:[14]}),h.value?(d(),U(i,{key:0,percentage:m.value.darwin,format:F,style:{"margin-top":"10px"}},null,8,["percentage"])):R("",!0)]),E((d(),r("div",{"element-loading-text":"\u4E0B\u8F7D\u7801\u751F\u6210\u4E2D...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF","margin-right":"5%"},onMousemove:e[2]||(e[2]=t=>x("ios")),onMouseleave:e[3]||(e[3]=t=>c.value=!1)},[(d(),r("svg",Q,e[16]||(e[16]=[a("use",{"xlink:href":"#icon-ios"},null,-1)]))),e[18]||(e[18]=a("br",null,null,-1)),u(o,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p(()=>e[17]||(e[17]=[f("iOS\u5BA2\u6237\u7AEF")])),_:1,__:[17]}),e[19]||(e[19]=a("div",{id:"ios",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[a("canvas",{id:"ioscanvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[l,_.value]]),E((d(),r("div",{"element-loading-text":"\u4E0B\u8F7D\u7801\u751F\u6210\u4E2D...",style:{float:"left",width:"209px",height:"209px",background:"#F1F8FF"},onMousemove:e[4]||(e[4]=t=>x("android")),onMouseleave:e[5]||(e[5]=t=>c.value=!1)},[(d(),r("svg",W,e[20]||(e[20]=[a("use",{"xlink:href":"#icon-android"},null,-1)]))),e[22]||(e[22]=a("br",null,null,-1)),u(o,{class:"window-show",underline:!1,style:{"margin-top":"42px"}},{default:p(()=>e[21]||(e[21]=[f("Android\u5BA2\u6237\u7AEF")])),_:1,__:[21]}),e[23]||(e[23]=a("div",{id:"android",class:"window-hidden",style:{width:"100%",height:"100%",display:"none"}},[a("canvas",{id:"canvas",style:{top:"-16px",position:"relative",width:"100%"}})],-1))],32)),[[l,b.value]])])]),_:1})])])}}},K=I(G,[["__scopeId","data-v-2db15365"]]);export{K as default};
