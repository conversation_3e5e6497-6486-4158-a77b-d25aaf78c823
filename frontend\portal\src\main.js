import { createApp } from 'vue'
import './style/element_visiable.scss'
import './style/base.css'
import './style/menu.css'
// 引入基础组件
import BaseComponents from '@/components/base'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import { store } from '@/pinia'
import App from './App.vue'
// 轻量级 SVG 图标替代 iconfont
import loadSvgIcons from '@/assets/icons'

/**
 * @description 导入加载进度条，防止首屏加载时间过长，用户等待
 *
 * */
import Nprogress from 'nprogress'
import 'nprogress/nprogress.css'
import { useAppStore } from '@/pinia/modules/app'
logger.log(navigator.userAgent)
logger.log(document.location.href)
Nprogress.configure({ showSpinner: false, ease: 'ease', speed: 500 })
Nprogress.start()

/**
 * 无需在这块结束，会在路由中间件中结束此块内容
 * */

// 检测浏览器是否为IE
const isIE = /msie|trident/i.test(navigator.userAgent)
// 如果是IE浏览器，则提示不支持
if (isIE) {
  const unsupportedMessage = `
    对不起，您正在使用的浏览器版本过低。
    本网站不支持IE浏览器，请使用现代浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。
  `
  alert(unsupportedMessage)
}

const app = createApp(App)
app.config.productionTip = false

const host = document.location.protocol + '//' + document.location.host

// 获取 corpID，在本地文件环境下使用默认值
let corpID = null
try {
  // 检查是否为本地文件协议
  if (document.location.protocol !== 'file:') {
    const req = new XMLHttpRequest()
    req.open('GET', document.location, false)
    req.send(null)
    corpID = req.getResponseHeader('X-Corp-ID')
  }
} catch (error) {
  console.warn('无法获取 X-Corp-ID header，使用默认值:', error)
}
// 安全获取环境变量，兼容本地文件环境
const getEnvVar = (key, defaultValue = '') => {
  try {
    // 检查是否在本地文件环境中
    if (typeof window !== 'undefined' && window.location && window.location.protocol === 'file:') {
      return defaultValue
    }
    // 使用 eval 来避免静态分析错误
    const importMeta = eval('import.meta')
    return (importMeta && importMeta.env) ? importMeta.env[key] || defaultValue : defaultValue
  } catch (e) {
    return defaultValue
  }
}

// console.log(corpID)
let url = ''
const VITE_BASE_PATH = getEnvVar('VITE_BASE_PATH')
const VITE_SERVER_PORT = getEnvVar('VITE_SERVER_PORT')
const VITE_BASE_API = getEnvVar('VITE_BASE_API', '/auth')

// console.log(`host:${host}`)
// console.log(`VITE_BASE_PATH:${VITE_BASE_PATH}`)
if (VITE_BASE_PATH) {
  url = VITE_BASE_PATH + ':' + VITE_SERVER_PORT + VITE_BASE_API
} else {
  url = host + VITE_BASE_API
}
logger.log(`url:${url}`)
// 加载轻量级 SVG 图标
loadSvgIcons()

app
  .use(run)
  .use(store)
  .use(auth)
  .use(router)
  .use(BaseComponents)
  .mount('#app')

// 判断是否是客户端访问
const appStore = useAppStore()
appStore.setIsClient()
logger.log('是否是客户端:', appStore.isClient, "客户端类型:", appStore.clientType)

export default app
