import { createApp } from 'vue'
import './style/element_visiable.scss'
import './style/base.css'
import './style/menu.css'
// 引入基础组件
import BaseComponents from '@/components/base'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import { store } from '@/pinia'
import App from './App.vue'
import ipcApi from '@/utils/ipcPlugin/index'
// 轻量级 SVG 图标替代 iconfont
import loadSvgIcons from '@/assets/icons'

/**
 * @description 导入加载进度条，防止首屏加载时间过长，用户等待
 *
 * */
import Nprogress from 'nprogress'
import 'nprogress/nprogress.css'
import { useAppStore } from '@/pinia/modules/app'
logger.log(navigator.userAgent)
logger.log(document.location.href)
Nprogress.configure({ showSpinner: false, ease: 'ease', speed: 500 })
Nprogress.start()

/**
 * 无需在这块结束，会在路由中间件中结束此块内容
 * */

// 检测浏览器是否为IE
const isIE = /msie|trident/i.test(navigator.userAgent)
// 如果是IE浏览器，则提示不支持
if (isIE) {
  const unsupportedMessage = `
    对不起，您正在使用的浏览器版本过低。
    本网站不支持IE浏览器，请使用现代浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。
  `
  alert(unsupportedMessage)
}

const app = createApp(App)
app.config.productionTip = false

// 加载轻量级 SVG 图标
loadSvgIcons()

app
  .use(run)
  .use(store)
  .use(auth)
  .use(router)
  .use(BaseComponents)
  .mount('#app')

// 判断是否是客户端访问
const appStore = useAppStore()
appStore.setIsClient()
logger.log('是否是客户端:', appStore.isClient, "客户端类型:", appStore.clientType)

// 初始化客户端通信
if (appStore.isClient) {
  ipcApi.init(app)
}

export default app
