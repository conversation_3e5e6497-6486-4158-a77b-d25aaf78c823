/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import{_ as g,G as b,E as F,r as w,o as d,d as h}from"./index.d0594432.js";var v={exports:{}};/*!
* screenfull
* v5.2.0 - 2021-11-03
* (c) <PERSON>dre Sorhus; MIT License
*/(function(o){(function(){var t=typeof window<"u"&&typeof window.document<"u"?window.document:{},i=o.exports,l=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],n=0,s=r.length,c={};n<s;n++)if(e=r[n],e&&e[1]in t){for(n=0;n<e.length;n++)c[r[0][n]]=e[n];return c}return!1}(),f={change:l.fullscreenchange,error:l.fullscreenerror},a={request:function(e,r){return new Promise(function(n,s){var c=function(){this.off("change",c),n()}.bind(this);this.on("change",c),e=e||t.documentElement;var m=e[l.requestFullscreen](r);m instanceof Promise&&m.then(c).catch(s)}.bind(this))},exit:function(){return new Promise(function(e,r){if(!this.isFullscreen){e();return}var n=function(){this.off("change",n),e()}.bind(this);this.on("change",n);var s=t[l.exitFullscreen]();s instanceof Promise&&s.then(n).catch(r)}.bind(this))},toggle:function(e,r){return this.isFullscreen?this.exit():this.request(e,r)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){var n=f[e];n&&t.addEventListener(n,r,!1)},off:function(e,r){var n=f[e];n&&t.removeEventListener(n,r,!1)},raw:l};if(!l){i?o.exports={isEnabled:!1}:window.screenfull={isEnabled:!1};return}Object.defineProperties(a,{isFullscreen:{get:function(){return Boolean(t[l.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[l.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[l.fullscreenEnabled])}}}),i?o.exports=a:window.screenfull=a})()})(v);const u=v.exports;const _={key:0,class:"gvaIcon gvaIcon-fullscreen-expand"},E={key:1,class:"gvaIcon gvaIcon-fullscreen-shrink"},p={name:"Screenfull"},k=Object.assign(p,{props:{width:{type:Number,default:22},height:{type:Number,default:22},fill:{type:String,default:"#48576a"}},setup(o){b(()=>{u.isEnabled&&u.on("change",l)}),F(()=>{u.off("change")});const t=()=>{u.isEnabled&&u.toggle()},i=w(!0),l=()=>{i.value=!u.isFullscreen};return(f,a)=>(d(),h("div",{onClick:t},[i.value?(d(),h("div",_)):(d(),h("div",E))]))}}),S=g(k,[["__scopeId","data-v-944c2140"]]);export{S as default};
