/*! 
 Build based on gin-vue-admin 
 Time : 1749642209000 */
System.register(["./index-legacy.e795fa57.js","./verifyCode-legacy.2c6cc29a.js"],(function(e,t){"use strict";var a,n,r,i,o,u,c,l,s,d,h,f,p,v,m,y,x=document.createElement("style");return x.textContent='@charset "UTF-8";.secondary-auth-overlay[data-v-24e922d3]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.5);z-index:1000;display:flex;justify-content:center;align-items:center}.secondary-auth-container[data-v-24e922d3]{background:#fff;padding:40px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,.1);min-width:340px;max-width:90%}.auth-selector .title[data-v-24e922d3]{height:60px;font-size:24px;text-align:center;margin-bottom:20px}.auth-selector .auth-methods[data-v-24e922d3]{display:flex;justify-content:center;flex-wrap:wrap;gap:20px;margin-bottom:20px}.auth-selector .auth-method-card[data-v-24e922d3]{width:120px;height:120px;cursor:pointer;transition:all .3s}.auth-selector .auth-method-card[data-v-24e922d3]:hover{transform:translateY(-5px);box-shadow:0 5px 15px rgba(0,0,0,.1)}.auth-selector .auth-method-content[data-v-24e922d3]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.auth-selector .auth-method-name[data-v-24e922d3]{margin-top:10px;font-size:14px}.auth-selector .selector-footer[data-v-24e922d3]{text-align:center;margin-top:20px}\n',document.head.appendChild(x),{setters:[function(e){a=e.r,n=e.c,r=e.h,i=e.o,o=e.d,u=e.e,c=e.F,l=e.i,s=e.j,d=e.w,h=e.f,f=e.g,p=e._,v=e.t,m=e.k},function(e){y=e.default}],execute:function(){var t={class:"secondary-auth-overlay"},x={class:"secondary-auth-container"},g={key:0,class:"auth-selector"},b={class:"auth-methods"},_={class:"auth-method-content"},k={class:"icon","aria-hidden":"true"},w=["xlink:href"],j={class:"auth-method-name"},C={class:"selector-footer"},I=Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]}},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup:function(e,p){var I=p.emit,S=e,q=a(!0),A=a(null),z=n((function(){return S.authMethods.filter((function(e){return e.available}))})),F=function(e){A.value=e,q.value=!1};1===z.value.length&&F(z.value[0]);var M=I,N=function(){M("cancel")},O=function(e){"client"===route.query.type&&(e.clientParams={type:"client",wp:route.query.wp||"50001"}),M("verification-success",e)};return function(a,n){var p=r("base-avatar"),I=r("base-card"),S=r("base-button");return i(),o("div",t,[u("div",x,[q.value?(i(),o("div",g,[n[3]||(n[3]=u("h2",{class:"title"},"请选择二次认证方式",-1)),u("div",b,[(i(!0),o(c,null,l(z.value,(function(e){return i(),f(I,{key:e.type,class:"auth-method-card",onClick:function(t){return F(e)}},{default:d((function(){return[u("div",_,[s(p,null,{default:d((function(){return[(i(),o("svg",k,[u("use",{"xlink:href":"#icon-auth-"+e.icon},null,8,w)]))]})),_:2},1024),u("div",j,v(e.name),1)])]})),_:2},1032,["onClick"])})),128))]),u("div",C,[s(S,{type:"info",onClick:n[0]||(n[0]=function(){return N()})},{default:d((function(){return n[2]||(n[2]=[m("取消")])})),_:1,__:[2]})])])):h("v-if",!0),h(" 加载统一的验证码组件，传入不同的模板类型 "),!q.value&&A.value?(i(),f(y,{key:1,auth_info:e.authInfo,auth_id:e.authId,"user-name":e.userName,last_id:e.lastId,"secondary-type":A.value.type,onVerificationSuccess:O,onBack:n[1]||(n[1]=function(e){return q.value=!0}),onCancel:N},null,8,["auth_info","auth_id","user-name","last_id","secondary-type"])):h("v-if",!0)])])}}});e("default",p(I,[["__scopeId","data-v-24e922d3"],["__file","D:/asec-platform/frontend/portal/src/view/login/secondaryAuth/secondaryAuth.vue"]]))}}}));
