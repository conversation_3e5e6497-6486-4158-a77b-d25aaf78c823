import axios from 'axios' // 引入axios
import { Message, MessageBox } from '@/components/base'
import { useUserStore } from '@/pinia/modules/user'
import { emitter } from '@/utils/bus.js'
import router from '@/router/index'
import agentApi from '@/api/agentApi'

// 获取服务器地址的函数
const getServerHost = () => {
  if (agentApi.isClient()) {
    // 从 URL 参数中获取 WebUrl
    const webUrl = urlHashParams ? urlHashParams.get('WebUrl') : ''

    if (webUrl) {
      try {
        const url = new URL(webUrl)
        return `${url.protocol}//${url.host}`
      } catch (error) {
        console.warn('解析 WebUrl 参数失败:', error)
      }
    }

    // 默认服务器地址（可以设为空，让用户输入）
    return ''
  }

  // 浏览器环境，使用当前域名
  return document.location.protocol + '//' + document.location.host
}

let host = getServerHost()

// 更新服务器地址的函数
export const updateServerHost = (newHost) => {
  if (!newHost) return false

  try {
    // 验证 URL 格式
    const url = new URL(newHost)
    const validHost = `${url.protocol}//${url.host}`

    // 更新当前 host
    host = validHost

    // 更新 axios 实例的 baseURL
    if (import.meta.env.VITE_BASE_PATH) {
      service.defaults.baseURL = import.meta.env.VITE_BASE_PATH + ':' + import.meta.env.VITE_SERVER_PORT
    } else {
      service.defaults.baseURL = validHost
    }

    return true
  } catch (error) {
    console.error('无效的服务器地址:', error)
    return false
  }
}

// 获取当前服务器地址
export const getCurrentHost = () => host

let baseURL = ''
if (import.meta.env.VITE_BASE_PATH) {
  baseURL = import.meta.env.VITE_BASE_PATH + ':' + import.meta.env.VITE_SERVER_PORT
} else {
  baseURL = host
}
// console.log(baseURL)
const service = axios.create({
  baseURL: baseURL,
  timeout: 99999,
})

let acitveAxios = 0
let timer
const showLoading = () => {
  acitveAxios++
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    if (acitveAxios > 0) {
      emitter.emit('showLoading')
    }
  }, 400)
}

const closeLoading = () => {
  acitveAxios--
  if (acitveAxios <= 0) {
    clearTimeout(timer)
    emitter.emit('closeLoading')
  }
}

// http request 拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    if (!config.donNotShowLoading) {
      showLoading()
    }
    if (config.url.match(/(\w+\/){0}\w+/)[0] === 'console' && import.meta.env.VITE_BASE_CONSOLE_PATH) {
      config.baseURL = import.meta.env.VITE_BASE_CONSOLE_PATH
    }
    config.headers = {
      'Content-Type': 'application/json',
      // 'x-token': userStore.token,
      ...config.headers,
    }

    if (userStore.token.accessToken) {
      if (config.url.includes('refresh_token')) {
        config.headers.Authorization = `${userStore.token.tokenType} ${userStore.token.refreshToken}`
      } else {
        config.headers.Authorization = `${userStore.token.tokenType} ${userStore.token.accessToken}`
      }
    }

    return config
  },
  error => {
    closeLoading()
    Message({
      showClose: true,
      message: error,
      type: 'error',
    })
    return error
  },
)

// http response 拦截器
service.interceptors.response.use(
  response => {
    // console.log('service.interceptors.response')
    // console.log('response')
    // console.log(document.location.href)
    const userStore = useUserStore()
    closeLoading()
    if (response.headers['new-token']) {
      userStore.setToken(response.headers['new-token'])
    }
    logger.log("请求：", { request_url: response.config.url, response: response })
    if (response.status === 200 || response.status === 204 || response.status === 201 || response.headers.success === 'true') {
      // if (response.statusText) {
      //   response.data.msg = decodeURI(response.headers.msg)
      // }
      // console.log('response end')
      // console.log(document.location.href)
      return response
    } else {
      Message({
        showClose: true,
        message: response.data.msg || decodeURI(response.headers.msg),
        type: 'error',
      })
      if (response.data.data && response.data.data.reload) {
        userStore.token = ''
        localStorage.clear()
        router.push({ name: 'Login', replace: true })
      }
      return response.data.msg ? response.data : response
    }
  },
  error => {
    const userStore = useUserStore()
    closeLoading()
    if (!error.response) {
      MessageBox.confirm(`
        <p>检测到请求错误</p>
        <p>${error}</p>
        `, '请求报错', {
        dangerouslyUseHTMLString: true,
        distinguishCancelAndClose: true,
        confirmButtonText: '稍后重试',
        cancelButtonText: '取消',
      })
      return
    }

    switch (error.response.status) {
      case 500:
        MessageBox.confirm(`
        <p>检测到接口错误${error}</p>
        <p>错误码<span style="color:red"> 500 </span>：此类错误内容常见于后台panic，请先查看后台日志，如果影响您正常使用可强制登出清理缓存</p>
        `, '接口报错', {
          dangerouslyUseHTMLString: true,
          distinguishCancelAndClose: true,
          confirmButtonText: '清理缓存',
          cancelButtonText: '取消',
        })
          .then(() => {
            const userStore = useUserStore()
            userStore.token = ''
            localStorage.clear()
            router.push({ name: 'Login', replace: true })
          })
        break
      case 404 || 400:
        Message({
          showClose: true,
          message: error.response.data.error,
          type: 'error',
        })
        // MessageBox.confirm(`
        //   <p>检测到接口错误${error}</p>
        //   <p>错误码<span style="color:red"> 404 </span>：此类错误多为接口未注册（或未重启）或者请求路径（方法）与api路径（方法）不符--如果为自动化代码请检查是否存在空格</p>
        //   `, '接口报错', {
        //   dangerouslyUseHTMLString: true,
        //   distinguishCancelAndClose: true,
        //   confirmButtonText: '我知道了',
        //   cancelButtonText: '取消',
        // })
        break
      case 401:
        userStore.authFailureLoginOut()
        const refresh_times = window.localStorage.getItem('refresh_times') || 0
        window.localStorage.setItem('refresh_times', Number(refresh_times) + 1)
        break
      default:
        console.log(error.response)
        Message({
          showClose: true,
          message: error.response.data.errorMessage || error.response.data.error,
          type: 'error',
        })
        break
    }

    return error
  },
)

export default service
