/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
import a from"./header.ec60720d.js";import s from"./menu.87e53e1b.js";import{h as e,o as t,d as o,j as r,e as i,f as l}from"./index.5a1fa56a.js";import"./ASD.492c8837.js";const u={class:"layout-page"},m={class:"layout-wrap"},n={id:"layoutMain",class:"layout-main"},d=Object.assign({name:"Client"},{setup:d=>(d,c)=>{const p=e("router-view");return t(),o("div",u,[r(a),i("div",m,[r(s),i("div",n,[(t(),l(p,{key:d.$route.fullPath}))])])])}});export{d as default};
