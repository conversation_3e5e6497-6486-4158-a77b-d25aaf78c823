/*! 
 Build based on gin-vue-admin 
 Time : 1749726600000 */
import{_ as e,N as n,K as l,r,o as t,d as s}from"./index.342352cf.js";var c,u,i,o,a,f,d={exports:{}};
/*!
* screenfull
* v5.2.0 - 2021-11-03
* (c) <PERSON>dre Sorhus; MIT License
*/c=d,u="undefined"!=typeof window&&void 0!==window.document?window.document:{},i=c.exports,o=function(){for(var e,n=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],l=0,r=n.length,t={};l<r;l++)if((e=n[l])&&e[1]in u){for(l=0;l<e.length;l++)t[n[0][l]]=e[l];return t}return!1}(),a={change:o.fullscreenchange,error:o.fullscreenerror},f={request:function(e,n){return new Promise(function(l,r){var t=function(){this.off("change",t),l()}.bind(this);this.on("change",t);var s=(e=e||u.documentElement)[o.requestFullscreen](n);s instanceof Promise&&s.then(t).catch(r)}.bind(this))},exit:function(){return new Promise(function(e,n){if(this.isFullscreen){var l=function(){this.off("change",l),e()}.bind(this);this.on("change",l);var r=u[o.exitFullscreen]();r instanceof Promise&&r.then(l).catch(n)}else e()}.bind(this))},toggle:function(e,n){return this.isFullscreen?this.exit():this.request(e,n)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,n){var l=a[e];l&&u.addEventListener(l,n,!1)},off:function(e,n){var l=a[e];l&&u.removeEventListener(l,n,!1)},raw:o},o?(Object.defineProperties(f,{isFullscreen:{get:function(){return Boolean(u[o.fullscreenElement])}},element:{enumerable:!0,get:function(){return u[o.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(u[o.fullscreenEnabled])}}}),i?c.exports=f:window.screenfull=f):i?c.exports={isEnabled:!1}:window.screenfull={isEnabled:!1};const h=d.exports,m={key:0,class:"gvaIcon gvaIcon-fullscreen-expand"},b={key:1,class:"gvaIcon gvaIcon-fullscreen-shrink"},g=e(Object.assign({name:"Screenfull"},{props:{width:{type:Number,default:22},height:{type:Number,default:22},fill:{type:String,default:"#48576a"}},setup(e){n((()=>{h.isEnabled&&h.on("change",i)})),l((()=>{h.off("change")}));const c=()=>{h.isEnabled&&h.toggle()},u=r(!0),i=()=>{u.value=!h.isFullscreen};return(e,n)=>(t(),s("div",{onClick:c},[u.value?(t(),s("div",m)):(t(),s("div",b))]))}}),[["__scopeId","data-v-d082a95e"],["__file","D:/asec-platform/frontend/portal/src/view/layout/screenfull/index.vue"]]);export{g as default};
