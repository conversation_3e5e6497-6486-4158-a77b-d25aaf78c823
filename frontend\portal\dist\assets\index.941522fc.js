/*! 
 Build based on gin-vue-admin 
 Time : 1749628938000 */
import{_ as e,r as s,b as a,h as t,o as l,d as c,j as i,w as d,e as n,m as o,f as r,g as u}from"./index.c733b50d.js";const m={class:"setting_body"},v={class:"setting_card"},g={class:"setting_content"},p={class:"theme-box"},k={class:"item-top"},b={class:"item-top"},h=e(Object.assign({name:"Setting"},{setup(e){const h=s(!1),y=s("rtl"),f=a(),_=()=>{h.value=!1},j=()=>{h.value=!0},w=e=>{null!==e?f.changeSideMode(e):f.changeSideMode("dark")};return(e,s)=>{const a=t("base-button"),C=t("check"),S=t("el-icon"),M=t("el-drawer");return l(),c("div",null,[i(a,{type:"primary",class:"drawer-container",icon:"setting",onClick:j}),i(M,{modelValue:h.value,"onUpdate:modelValue":s[2]||(s[2]=e=>h.value=e),title:"系统配置",direction:y.value,"before-close":_},{default:d((()=>[n("div",m,[n("div",v,[n("div",g,[n("div",p,[n("div",{class:"item",onClick:s[0]||(s[0]=e=>w("light"))},[n("div",k,["light"===o(f).mode?(l(),r(S,{key:0,class:"check"},{default:d((()=>[i(C)])),_:1})):u("",!0),s[3]||(s[3]=n("img",{src:"https://gw.alipayobjects.com/zos/antfincdn/NQ%24zoisaD2/jpRkZQMyYRryryPNtyIC.svg"},null,-1))]),s[4]||(s[4]=n("p",null," 简约白 ",-1))]),n("div",{class:"item",onClick:s[1]||(s[1]=e=>w("dark"))},[n("div",b,["dark"===o(f).mode?(l(),r(S,{key:0,class:"check"},{default:d((()=>[i(C)])),_:1})):u("",!0),s[5]||(s[5]=n("img",{src:"https://gw.alipayobjects.com/zos/antfincdn/XwFOFbLkSM/LCkqqYNmvBEbokSDscrm.svg"},null,-1))]),s[6]||(s[6]=n("p",null," 商务黑 ",-1))])])])])])])),_:1},8,["modelValue","direction"])])}}}),[["__scopeId","data-v-f5d2d3d7"]]);export{h as default};
