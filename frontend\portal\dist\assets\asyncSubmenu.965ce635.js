/*! 
 Build based on gin-vue-admin 
 Time : 1749729201000 */
import{_ as e,O as t,r as a,z as n,h as o,o as s,g as u,w as l,d as r,I as i,f as c,e as f,t as m,F as d,Q as v}from"./index.5ebe5a72.js";const p={key:0,class:"gva-subMenu"},x=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"1f4df5c8-normalText":b.value,"1f4df5c8-activeText":I.value})));const x=e,y=a(x.theme.activeBackground),I=a(x.theme.activeText),b=a(x.theme.normalText);return n((()=>x.theme),(()=>{y.value=x.theme.activeBackground,I.value=x.theme.activeText,b.value=x.theme.normalText})),(t,a)=>{const n=o("el-sub-menu");return s(),u(n,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(d,{key:1},[e.routerInfo.meta.icon?(s(),r("i",{key:0,class:i(["iconfont",e.routerInfo.meta.icon])},null,2)):c("v-if",!0),f("span",null,m(e.routerInfo.meta.title),1)],64)):(s(),r("div",p,[e.routerInfo.meta.icon?(s(),r("i",{key:0,class:i(["iconfont",e.routerInfo.meta.icon])},null,2)):c("v-if",!0),f("span",null,m(e.routerInfo.meta.title),1)]))])),default:l((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-1f4df5c8"],["__file","D:/asec-platform/frontend/portal/src/view/layout/aside/asideComponent/asyncSubmenu.vue"]]);export{x as default};
