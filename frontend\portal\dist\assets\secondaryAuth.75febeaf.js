/*! 
 Build based on gin-vue-admin 
 Time : 1749612918000 */
import I from"./verifyCode.5ac023d8.js";import{r as y,c as V,h as r,o as s,d as l,e as a,F as N,i as w,j as f,w as d,g as m,f as p,_ as A,t as B,k as M}from"./index.d0594432.js";const q={class:"secondary-auth-overlay"},j={class:"secondary-auth-container"},F={key:0,class:"auth-selector"},O={class:"auth-methods"},$={class:"auth-method-content"},D={class:"icon","aria-hidden":"true"},E=["xlink:href"],L={class:"auth-method-name"},P={class:"selector-footer"},T={name:"SecondaryAuth"},z=Object.assign(T,{props:{authMethods:{type:Array,default:()=>[{type:"sms",name:"\u77ED\u4FE1\u9A8C\u8BC1",icon:"duanxin",available:!0},{type:"email",name:"\u90AE\u7BB1\u9A8C\u8BC1",icon:"email",available:!0}]},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup(n,{emit:b}){const k=n,c=y(!0),i=y(null),u=V(()=>k.authMethods.filter(t=>t.available)),_=t=>{i.value=t,c.value=!1};u.value.length===1&&_(u.value[0]);const h=b,v=()=>{h("cancel")},g=t=>{route.query.type==="client"&&(t.clientParams={type:"client",wp:route.query.wp||"50001"}),h("verification-success",t)};return(t,e)=>{const x=r("base-avatar"),C=r("base-card"),S=r("base-button");return s(),l("div",q,[a("div",j,[c.value?(s(),l("div",F,[e[3]||(e[3]=a("h2",{class:"title"},"\u8BF7\u9009\u62E9\u4E8C\u6B21\u8BA4\u8BC1\u65B9\u5F0F",-1)),a("div",O,[(s(!0),l(N,null,w(u.value,o=>(s(),p(C,{key:o.type,class:"auth-method-card",onClick:G=>_(o)},{default:d(()=>[a("div",$,[f(x,null,{default:d(()=>[(s(),l("svg",D,[a("use",{"xlink:href":"#icon-auth-"+o.icon},null,8,E)]))]),_:2},1024),a("div",L,B(o.name),1)])]),_:2},1032,["onClick"]))),128))]),a("div",P,[f(S,{type:"info",onClick:e[0]||(e[0]=()=>v())},{default:d(()=>e[2]||(e[2]=[M("\u53D6\u6D88")])),_:1,__:[2]})])])):m("",!0),!c.value&&i.value?(s(),p(I,{key:1,auth_info:n.authInfo,auth_id:n.authId,"user-name":n.userName,last_id:n.lastId,"secondary-type":i.value.type,onVerificationSuccess:g,onBack:e[1]||(e[1]=o=>c.value=!0),onCancel:v},null,8,["auth_info","auth_id","user-name","last_id","secondary-type"])):m("",!0)])])}}}),K=A(z,[["__scopeId","data-v-3e719deb"]]);export{K as default};
