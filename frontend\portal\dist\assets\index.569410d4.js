/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
import{u as e,a,b as t,S as o,r as l,z as s,E as n,K as r,h as u,o as c,d as i,j as d,w as f,T as v,F as m,i as p,m as h,f as b,g,I as k}from"./index.29054947.js";import x from"./index.cd92238f.js";import"./menuItem.8dfcf1bd.js";import"./asyncSubmenu.346b337b.js";const y=Object.assign({name:"Aside"},{setup(y){const T=e(),j=a(),F=t(),M=o(),w=l({}),B=()=>{switch(F.sideMode){case"#fff":w.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":w.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};B();const q=l("");s((()=>T),(()=>{q.value=T.meta.activeName||T.name}),{deep:!0}),s((()=>F.sideMode),(()=>{B()}));const O=l(!1);(()=>{q.value=T.meta.activeName||T.name;document.body.clientWidth<1e3&&(O.value=!O.value),r.on("collapse",(e=>{O.value=e}))})(),n((()=>{r.off("collapse")}));const S=(e,a,t,o)=>{var l,s;const n={},r={};(null==(l=M.routeMap[e])?void 0:l.parameters)&&(null==(s=M.routeMap[e])||s.parameters.forEach((e=>{"query"===e.type?n[e.key]=e.value:r[e.key]=e.value}))),e!==T.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):j.push({name:e,query:n,params:r}))};return(e,a)=>{const t=u("el-menu"),o=u("el-scrollbar");return c(),i("div",{style:k({background:h(F).sideMode})},[d(o,{style:{height:"calc(100vh - 110px)"}},{default:f((()=>[d(v,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[d(t,{collapse:O.value,"collapse-transition":!1,"default-active":q.value,"background-color":w.value.background,"active-text-color":w.value.active,class:"el-menu-vertical","unique-opened":"",onSelect:S},{default:f((()=>[(c(!0),i(m,null,p(h(M).asyncRouters[0].children,(e=>(c(),i(m,null,[e.hidden?g("",!0):(c(),b(x,{key:e.name,"is-collapse":O.value,"router-info":e,theme:w.value},null,8,["is-collapse","router-info","theme"]))],64)))),256))])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})],4)}}});export{y as default};
