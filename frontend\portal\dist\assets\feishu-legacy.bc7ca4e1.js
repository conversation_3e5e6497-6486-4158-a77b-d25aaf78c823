/*! 
 Build based on gin-vue-admin 
 Time : 1749631156000 */
!function(){function t(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,a,u=[],c=!0,f=!1;try{if(i=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;c=!1}else for(;!(c=(r=i.call(e)).done)&&(u.push(r.value),u.length!==n);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(f)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function u(e,o,i,a){var u=o&&o.prototype instanceof f?o:f,l=Object.create(u.prototype);return r(l,"_invoke",function(e,r,o){var i,a,u,f=0,l=o||[],s=!1,d={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(n,e){return i=n,a=0,u=t,d.n=e,c}};function p(e,r){for(a=e,u=r,n=0;!s&&f&&!o&&n<l.length;n++){var o,i=l[n],p=d.p,h=i[2];e>3?(o=h===r)&&(u=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=p&&((o=e<2&&p<i[1])?(a=0,d.v=r,d.n=i[1]):p<h&&(o=e<3||i[0]>r||r>h)&&(i[4]=e,i[5]=r,d.n=h,a=0))}if(o||e>1)return c;throw s=!0,r}return function(o,l,h){if(f>1)throw TypeError("Generator is already running");for(s&&1===l&&p(l,h),a=l,u=h;(n=a<2?t:u)||!s;){i||(a?a<3?(a>1&&(d.n=-1),p(a,u)):d.n=u:d.v=u);try{if(f=2,i){if(a||(o="next"),n=i[o]){if(!(n=n.call(i,u)))throw TypeError("iterator result is not an object");if(!n.done)return n;u=n.value,a<2&&(a=0)}else 1===a&&(n=i.return)&&n.call(i),a<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((n=(s=d.n<0)?u:e.call(r,d))!==c)break}catch(n){i=t,a=1,u=n}finally{f=1}}return{value:n,done:s}}}(e,i,a),!0),l}var c={};function f(){}function l(){}function s(){}n=Object.getPrototypeOf;var d=[][i]?n(n([][i]())):(r(n={},i,(function(){return this})),n),p=s.prototype=f.prototype=Object.create(d);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,s):(t.__proto__=s,r(t,a,"GeneratorFunction")),t.prototype=Object.create(p),t}return l.prototype=s,r(p,"constructor",s),r(s,"constructor",l),l.displayName="GeneratorFunction",r(s,a,"GeneratorFunction"),r(p),r(p,a,"Generator"),r(p,i,(function(){return this})),r(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:u,m:h}})()}function r(t,n,e,o){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}r=function(t,n,e,o){if(n)i?i(t,n,{value:e,enumerable:!o,configurable:!o,writable:!o}):t[n]=e;else{var a=function(n,e){r(t,n,(function(t){return this._invoke(n,e,t)}))};a("next",0),a("throw",1),a("return",2)}},r(t,n,e,o)}function o(t,n,e,r,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?n(c):Promise.resolve(c).then(r,o)}function i(t){return function(){var n=this,e=arguments;return new Promise((function(r,i){var a=t.apply(n,e);function u(t){o(a,r,i,u,c,"next",t)}function c(t){o(a,r,i,u,c,"throw",t)}u(void 0)}))}}System.register(["./index-legacy.e6617eb1.js"],(function(n,r){"use strict";var o,a,u,c,f,l,s,d;return{setters:[function(t){o=t.r,a=t.u,u=t.z,c=t.o,f=t.d,l=t.e,s=t.k,d=t.X}],execute:function(){var r={style:{"text-align":"center"}},p={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},h={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}};n("default",Object.assign({name:"Feishu"},{props:{auth_info:{type:Array,default:function(){return[]}},auth_id:{type:String,default:function(){return""}}},setup:function(n){var v=o(0),y=n,g=function(){var t=i(e().m((function t(){var n,r;return e().w((function(t){for(;;)switch(t.n){case 0:return n={type:"feishu",data:{idpId:y.auth_id}},t.n=1,d(n);case 1:if(200!==(r=t.v).status){t.n=2;break}return t.a(2,r.data.uniqKey);case 2:return t.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),w=a(),b=function(){var n=i(e().m((function n(){var r,o,i,a,u,c,f,l,s,d,p,h,v;return e().w((function(n){for(;;)switch(n.n){case 0:return n.n=1,g();case 1:if(o=y.auth_info.fsAppId,i="".concat(window.location.origin,"/#/status?stat=0"),null!==(r=w.query)&&void 0!==r&&r.redirect)f=(null===(a=w.query)||void 0===a?void 0:a.redirect.indexOf("?"))>-1?null===(u=w.query)||void 0===u?void 0:u.redirect.substring((null===(c=w.query)||void 0===c?void 0:c.redirect.indexOf("?"))+1):"",i=i+"&"+f;else if(w.query){for(l=new URLSearchParams,s=0,d=Object.entries(w.query);s<d.length;s++)p=t(d[s],2),h=p[0],v=p[1],l.append(h,v);i=i+"&"+l.toString()}setTimeout((function(){var t="https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=".concat(o,"&redirect_uri=").concat(encodeURIComponent(i+"&auth_type=feishu"),"&response_type=code&state=").concat(y.auth_id),n=window.QRLogin({id:"login_container",goto:"".concat(t),style:"width:300px;height:300px; border: 0; background-size: cover"}),e=function(e){var r=e.origin;if(n.matchOrigin(r)&&window.location.href.indexOf("/login")>-1){var o=e.data;window.location.href="".concat(t,"&tmp_code=").concat(o)}};void 0!==window.addEventListener?window.addEventListener("message",e,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",e)}),100);case 2:return n.a(2)}}),n)})));return function(){return n.apply(this,arguments)}}();return b(),u(y,function(){var t=i(e().m((function t(n,r){return e().w((function(t){for(;;)switch(t.n){case 0:return v.value++,t.n=1,b();case 1:return t.a(2)}}),t)})));return function(n,e){return t.apply(this,arguments)}}()),function(t,n){return c(),f("div",{key:v.value},[l("div",r,[l("span",p,[(c(),f("svg",h,n[0]||(n[0]=[l("use",{"xlink:href":"#icon-auth-feishu"},null,-1)]))),n[1]||(n[1]=s(" 飞书认证 "))])]),n[2]||(n[2]=l("div",{id:"login_container",slot:"content",class:"wechat-class"},null,-1))])}}}))}}}))}();
