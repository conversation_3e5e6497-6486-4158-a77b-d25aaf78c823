/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
System.register(["./index-legacy.dacd3ee5.js","./index-legacy.692cb95e.js"],(function(e,t){"use strict";var a,s,c,n,o,i,p,r,l,d=document.createElement("style");return d.textContent=".access-main{&[data-v-199e7ebc]{padding:16px 16px 16px 0;width:100%;height:100%;display:flex;border-left:16px solid #f2f2f6;box-sizing:border-box;border-image:linear-gradient(to right,#fcfcfc,#fafafa,#ffffff);justify-content:center;align-items:center;background-color:#fcfcf6}.content-wrapper {&[data-v-199e7ebc] {margin: 0px; width: 100%; height: 100%;} .access-proxy-status {&[data-v-199e7ebc] {height: 102px; background: url("+new URL("proxy_background.51cd78fd.png",t.meta.url).href+") no-repeat center center; background-size: cover; position: relative;} .access-proxy-status-text[data-v-199e7ebc] {position: absolute; font-size: 16px; font-weight: 600; height: 22px; width: 64px; top: 12px; left: 16px;} .access-proxy-status-span {&[data-v-199e7ebc] {position: absolute; left: 50%; transform: translateX(-260px); width: 300px; height: 100px; display: flex; flex-direction: column; align-items: center; justify-content: center;} .access-proxy-status-btn[data-v-199e7ebc] {margin-top: 10px; height: 32px; width: 152px;}}} .access-common-status {&[data-v-199e7ebc] {height: 41px; font-size: 13px; font-weight: 400; display: flex; align-items: center; justify-content: space-between; margin-top: 16px; background-color: #fff; box-sizing: border-box;} .access-common-status-span[data-v-199e7ebc] {margin-left: 16px;} .access-common-status-detail[data-v-199e7ebc] {margin-right: 16px; color: #536ce6;}} .access-app {&[data-v-199e7ebc] {height: calc(100% - 215px); font-size: 13px; font-weight: 400; display: flex; flex-direction: column; align-items: center; justify-content: center; margin-top: 16px;} .access-app-page[data-v-199e7ebc] {width: 100%; overflow: auto;}}}}\n",document.head.appendChild(d),{setters:[function(e){a=e.default},function(e){s=e._,c=e.h,n=e.o,o=e.d,i=e.e,p=e.j,r=e.w,l=e.k}],execute:function(){var t={class:"access-main"},d={class:"content-wrapper"},f={class:"access-proxy-status"},u={class:"access-proxy-status-span"},x={class:"access-app"};e("default",s({name:"Access",components:{AppPage:a}},[["render",function(e,a,s,g,m,b){var h=c("base-button"),y=c("AppPage");return n(),o("div",t,[i("ul",d,[i("li",f,[a[2]||(a[2]=i("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),i("span",u,[a[1]||(a[1]=i("span",{class:"access-proxy-status-tips"}," 点击连接，即可安全便捷地访问应用 ",-1)),p(h,{class:"access-proxy-status-btn",color:"#626aef",type:"primary"},{default:r((function(){return a[0]||(a[0]=[l(" 一键连接 ")])})),_:1,__:[0]})])]),a[3]||(a[3]=i("li",{class:"access-common-status"},[i("span",{class:"access-common-status-span"},[i("span",null,"准入状态（企业网络下使用）："),i("span",{style:{color:"red"}},"未入网"),i("span",null,"（请重新建立连接）")]),i("span",{class:"access-common-status-detail"},[i("span",null,"查看详情")])],-1)),i("li",x,[p(y,{class:"access-app-page"})])])])}],["__scopeId","data-v-199e7ebc"],["__file","D:/asec-platform/frontend/portal/src/view/client/main.vue"]]))}}}));
