/*! 
 Build based on gin-vue-admin 
 Time : 1749712617000 */
System.register(["./index-legacy.60f18f5a.js"],(function(e,n){"use strict";var t,u,r,i,o,a,f,l,c,d,s;return{setters:[function(e){t=e.U,u=e.h,r=e.o,i=e.d,o=e.j,a=e.w,f=e.T,l=e.f,c=e.X,d=e.m,s=e.A}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[o(v,null,{default:a((function(e){var t=e.Component;return[o(f,{mode:"out-in",name:"el-fade-in-linear"},{default:a((function(){return[(r(),l(c,{include:d(n).keepAliveRouters},[(r(),l(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
