/*! 
 Build based on gin-vue-admin 
 Time : 1749716481000 */
System.register(["./index-legacy.00b16b45.js"],(function(e,n){"use strict";var o;return{setters:[function(e){o=e._}],execute:function(){var n=Object.assign({name:"ClientLogin"},{setup:function(e){var n=function(e){console.log("1");var n=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i");console.log(2);var o=window.location.search.substr(1).match(n);return console.log(o),null!=o?decodeURI(o[2]):null}("type");console.log("type"),console.log(n);var o=window.localStorage.getItem("token")||"";return console.log(11),console.log(o),o&&"client"===n&&(window.location.href="asecagent://?token=".concat(o)),function(e,n){return null}}});e("default",o(n,[["__file","D:/asec-platform/frontend/portal/src/view/login/clientLogin.vue"]]))}}}));
