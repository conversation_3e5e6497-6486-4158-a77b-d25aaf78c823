/*! 
 Build based on gin-vue-admin 
 Time : 1749732095000 */
import{_ as a,h as s,o as e,d as t,f as o,j as r,e as i,g as l}from"./index.8e61b3f9.js";import n from"./header.814d2220.js";import d from"./menu.d0370a20.js";import"./ASD.492c8837.js";const u={class:"layout-page"},m={class:"layout-wrap"},c={id:"layoutMain",class:"layout-main"},f=a(Object.assign({name:"Client"},{setup:a=>(a,f)=>{const p=s("router-view");return e(),t("div",u,[o("公共顶部菜单-"),r(n),i("div",m,[o("公共侧边栏菜单"),r(d),i("div",c,[o("主流程路由渲染点"),(e(),l(p,{key:a.$route.fullPath}))])])])}}),[["__file","D:/asec-platform/frontend/portal/src/view/client/index.vue"]]);export{f as default};
