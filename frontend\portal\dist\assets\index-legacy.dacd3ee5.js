/*! 
 Build based on gin-vue-admin 
 Time : 1749731440000 */
!function(){function e(n){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(n)}function n(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);n&&(a=a.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,a)}return t}function t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(n){a(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))}))}return e}function a(n,t,a){return(t=function(n){var t=function(n,t){if("object"!=e(n)||!n)return n;var a=n[Symbol.toPrimitive];if(void 0!==a){var r=a.call(n,t||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(n)}(n,"string");return"symbol"==e(t)?t:t+""}(t))in n?Object.defineProperty(n,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[t]=a,n}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,t="function"==typeof Symbol?Symbol:{},a=t.iterator||"@@iterator",i=t.toStringTag||"@@toStringTag";function c(t,a,r,i){var c=a&&a.prototype instanceof l?a:l,s=Object.create(c.prototype);return o(s,"_invoke",function(t,a,r){var o,i,c,l=0,s=r||[],d=!1,u={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(n,t){return o=n,i=0,c=e,u.n=t,p}};function f(t,a){for(i=t,c=a,n=0;!d&&l&&!r&&n<s.length;n++){var r,o=s[n],f=u.p,g=o[2];t>3?(r=g===a)&&(c=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=f&&((r=t<2&&f<o[1])?(i=0,u.v=a,u.n=o[1]):f<g&&(r=t<3||o[0]>a||a>g)&&(o[4]=t,o[5]=a,u.n=g,i=0))}if(r||t>1)return p;throw d=!0,a}return function(r,s,g){if(l>1)throw TypeError("Generator is already running");for(d&&1===s&&f(s,g),i=s,c=g;(n=i<2?e:c)||!d;){o||(i?i<3?(i>1&&(u.n=-1),f(i,c)):u.n=c:u.v=c);try{if(l=2,o){if(i||(r="next"),n=o[r]){if(!(n=n.call(o,c)))throw TypeError("iterator result is not an object");if(!n.done)return n;c=n.value,i<2&&(i=0)}else 1===i&&(n=o.return)&&n.call(o),i<2&&(c=TypeError("The iterator does not provide a '"+r+"' method"),i=1);o=e}else if((n=(d=u.n<0)?c:t.call(a,u))!==p)break}catch(n){o=e,i=1,c=n}finally{l=1}}return{value:n,done:d}}}(t,r,i),!0),s}var p={};function l(){}function s(){}function d(){}n=Object.getPrototypeOf;var u=[][a]?n(n([][a]())):(o(n={},a,(function(){return this})),n),f=d.prototype=l.prototype=Object.create(u);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,o(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=d,o(f,"constructor",d),o(d,"constructor",s),s.displayName="GeneratorFunction",o(d,i,"GeneratorFunction"),o(f),o(f,i,"Generator"),o(f,a,(function(){return this})),o(f,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:c,m:g}})()}function o(e,n,t,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}o=function(e,n,t,a){if(n)r?r(e,n,{value:t,enumerable:!a,configurable:!a,writable:!a}):e[n]=t;else{var i=function(n,t){o(e,n,(function(e){return this._invoke(n,t,e)}))};i("next",0),i("throw",1),i("return",2)}},o(e,n,t,a)}function i(e,n,t,a,r,o,i){try{var c=e[o](i),p=c.value}catch(e){return void t(e)}c.done?n(p):Promise.resolve(p).then(a,r)}function c(e){return function(){var n=this,t=arguments;return new Promise((function(a,r){var o=e.apply(n,t);function c(e){i(o,a,r,c,p,"next",e)}function p(e){i(o,a,r,c,p,"throw",e)}c(void 0)}))}}System.register(["./index-legacy.692cb95e.js"],(function(e,n){"use strict";var a,o,i,p,l,s,d,u,f,g,m,h,v,x,b,y,w,k,_,S,O,P,j,F,C=document.createElement("style");return C.textContent='@charset "UTF-8";.person[data-v-f0c977cd]{background:#FFFFFF;border-radius:4px;height:100vh;max-height:calc(100vh - 68px)}.person .base-header[data-v-f0c977cd]{justify-content:space-between;font-weight:Medium;color:#282a33;font-size:16px;line-height:22px;padding:0 15px;display:flex;align-items:center}.person .base-header .el-title[data-v-f0c977cd]{padding-top:19px}.person .base-header .el-search[data-v-f0c977cd]{padding-top:16px;display:flex;justify-content:space-between}.person .base-header .el-search-input[data-v-f0c977cd]{width:200px;height:28px;border-radius:4px;font-size:14px}.person .base-header .el-search-input>.el-input__wrapper[data-v-f0c977cd]{background:#f5f5f7}.person .base-header .el-search-btn[data-v-f0c977cd]{font-size:14px!important;margin-left:10px;min-height:28px;padding:0;width:28px;height:28px;background:#f5f5f7;border-radius:4px}.person .base-header .el-search-select[data-v-f0c977cd]{margin-left:10px;padding:3px 0 0;width:80px;height:20px!important}.person .base-header .el-search-select[data-v-f0c977cd] .el-input__wrapper{background-color:transparent!important;box-shadow:none!important;border:none!important;padding:0!important;line-height:1!important;height:20px!important}.person .base-header .el-search-select[data-v-f0c977cd] .el-input__suffix{padding:0!important;height:20px!important}.person .base-header .el-search-select[data-v-f0c977cd] .el-input__suffix-inner{margin-left:-28px}.person .base-header .el-search-select[data-v-f0c977cd] .el-input__inner{height:20px!important;line-height:20px!important}.person .base-header .search-input[data-v-f0c977cd]{width:300px}.person .base-header .search-input[data-v-f0c977cd] .el-input__wrapper{border-radius:4px;background-color:#f5f7fa}.person .base-header .search-input[data-v-f0c977cd] .el-input__wrapper.is-focus{background-color:#fff}.person .base-header .search-input[data-v-f0c977cd] .el-input__inner{height:32px;line-height:32px;font-size:14px}.person .base-header .search-input[data-v-f0c977cd] .el-input__inner::placeholder{color:#909399}.person .category-aside .category-menu[data-v-f0c977cd]{border-right:none}.person .category-aside .category-menu .el-menu-item[data-v-f0c977cd]{height:28px;line-height:28px;font-size:14px;color:#606266;position:relative;transition:all .3s;cursor:pointer;border-radius:4px;margin:4px 0 4px 20px}.person .category-aside .category-menu .el-menu-item[data-v-f0c977cd]:not(.is-active){background-color:transparent;color:#606266}.person .category-aside .category-menu .el-menu-item[data-v-f0c977cd]:hover:not(.is-active){background-color:#f0f7ff;color:#409eff}.person .category-aside .category-menu .el-menu-item[data-v-f0c977cd]:focus{outline:none;background-color:#f0f7ff}.person .category-aside .category-menu .el-menu-item[data-v-f0c977cd]:focus:not(.is-active){color:#409eff}.person .category-aside .category-menu .el-menu-item.is-active[data-v-f0c977cd]{background-color:#536ce6;color:#fff;font-weight:500}.person .category-aside .category-menu .el-menu-item[data-v-f0c977cd]:active{background-color:#dcedff}.person .app-main[data-v-f0c977cd]{padding:12px 20px;overflow-y:auto}.person .app-main .category-section[data-v-f0c977cd]{margin-bottom:24px}.person .app-main .category-section .category-title[data-v-f0c977cd]{font-size:16px;font-weight:700;margin-bottom:12px;margin-top:0;padding-bottom:6px;width:fit-content;min-width:100px;max-width:200px;line-height:1.2}.person .app-main .category-section .apps-container[data-v-f0c977cd]{display:grid;grid-template-columns:repeat(auto-fill,265px);gap:16px;padding:16px 0}.person .app-main .category-section .apps-container .app-card[data-v-f0c977cd]{width:257px;height:64px;margin-right:8px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px;position:relative;display:flex;justify-content:center}.person .app-main .category-section .apps-container .app-card[data-v-f0c977cd] .el-link{display:flex;flex-direction:column;align-items:center;justify-content:space-between;width:100%;height:100%;padding:0}.person .app-main .category-section .apps-container .app-card[data-v-f0c977cd] .el-link__inner{width:100%!important;height:100%!important}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-f0c977cd]{width:48px;height:48px;margin-left:13px;display:flex;align-items:center}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-f0c977cd] .el-avatar{width:48px;height:48px;color:#fff;font-size:16px;display:flex;align-items:center;justify-content:center;text-align:center}.person .app-main .category-section .apps-container .app-card .icon-wrapper[data-v-f0c977cd] .el-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.person .app-main .category-section .apps-container .app-card .app-info[data-v-f0c977cd]{width:100%;display:flex;flex-direction:column;justify-content:center}.person .app-main .category-section .apps-container .app-card .app-info .app-name[data-v-f0c977cd]{height:20px;width:56px;font-size:14px;font-family:PingFang SC,PingFang SC-Medium;font-weight:700;text-align:left;color:#282a33;line-height:20px;margin-left:12px;margin-top:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.person .app-main .category-section .apps-container .app-card .app-info .app-remark[data-v-f0c977cd]{margin-left:12px;width:168px;height:17px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#686e84;line-height:17px}.person .app-main .category-section .apps-container .app-card .status-badge[data-v-f0c977cd]{position:absolute;top:-6px;right:-6px;padding:1px 6px;border-radius:8px;font-size:11px;background-color:#d3c5a5;color:#000;font-weight:500;z-index:2}.person .app-main .category-section .apps-container .app-card[data-v-f0c977cd] .el-link:hover .icon-wrapper{transform:scale(1.05)}.person .app-main .category-section .apps-container .app-card.disabled[data-v-f0c977cd] .el-link{cursor:not-allowed}.person .app-main .category-section .apps-container .app-card.disabled[data-v-f0c977cd] .el-link:hover .icon-wrapper{transform:none}.person .app-main .category-section .apps-container .app-card.disabled .icon-wrapper[data-v-f0c977cd]{opacity:.6}.person .app-main .category-section .apps-container .app-card.disabled[data-v-f0c977cd] .el-avatar{filter:grayscale(100%)}.person .app-main .category-section[data-v-f0c977cd]:first-child{margin-top:0}.person .app-main .el-recent-access[data-v-f0c977cd]{margin-left:4px;margin-top:3px;width:56px;height:20px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;text-align:left;color:#282a33;line-height:20px}.person .app-main .el-recent-data[data-v-f0c977cd]{margin-left:15px;height:20px}.person .app-main .el-recent-item[data-v-f0c977cd]{margin-top:2px;margin-right:6px;height:25px;padding:4px 0 4px 6px;background:#f5f6fe;border-radius:4px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular;font-weight:Regular;color:#536ce6}.person .app-main .el-recent-icon[data-v-f0c977cd]{width:8px;height:8px;margin:8px 6px 8px 5px}.person .app-main .el-recent-clear[data-v-f0c977cd]{opacity:.6;margin-top:6px;position:absolute;width:14px;height:14px;right:6px}@media screen and (max-width: 1439px){.apps-container[data-v-f0c977cd]{grid-template-columns:repeat(auto-fill,180px)}.apps-container .app-card[data-v-f0c977cd]{width:180px;height:200px}}@media screen and (max-width: 1023px){.apps-container[data-v-f0c977cd]{grid-template-columns:repeat(auto-fill,200px)}.apps-container .app-card[data-v-f0c977cd]{width:200px;height:220px}}@media screen and (max-width: 767px){.apps-container[data-v-f0c977cd]{grid-template-columns:repeat(2,1fr)}.apps-container .app-card[data-v-f0c977cd]{width:100%;height:auto;aspect-ratio:1/1.125}}.tooltip-content[data-v-f0c977cd]{width:200px;text-align:center}.web-link[data-v-f0c977cd]{color:#409eff;text-decoration:none;word-break:break-all}.el-select__popper[data-v-f0c977cd]{background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1);margin-left:-10px!important;right:15px;top:110px;max-width:88px}.el-select__popper .el-select-dropdown__item[data-v-f0c977cd]{width:72px;height:28px;border-radius:4px;margin-left:7px;margin-bottom:4px;padding:0 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular;line-height:20px;display:flex;align-items:center;background:#f5f5f7!important}.el-select__popper .el-select-dropdown__item.selected[data-v-f0c977cd]{color:#fff;background:#536ce6!important}.text-center[data-v-f0c977cd]{text-align:center}.web-link[data-v-f0c977cd]{color:#409eff;text-decoration:none}.web-link[data-v-f0c977cd]:hover{text-decoration:underline}.el-message{white-space:pre-line!important;line-height:1.5!important;padding:12px 20px!important}\n',document.head.appendChild(C),{setters:[function(e){a=e.x,o=e.r,i=e.J,p=e.K,l=e.N,s=e.b,d=e.u,u=e.D,f=e.h,g=e.o,m=e.d,h=e.e,v=e.j,x=e.w,b=e.f,y=e._,w=e.F,k=e.i,_=e.g,S=e.k,O=e.t,P=e.I,j=e.B,F=e.M}],execute:function(){var n={class:"person"},C={class:"el-search"},W={class:"category-title"},D={class:"apps-container"},z={key:0,class:"status-badge"},T={class:"icon-wrapper"},E={class:"tooltip-content text-center"},U={key:0},I={key:1},R={class:"app-info"},A={class:"app-name"},N=Object.assign({name:"AppPage"},{setup:function(e){var y=o(""),N=o(null),G=o([]),M=o([]),V=o("1"),B=o(!1),J=o("standard"),L=i([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),q=o(null),X=o(!1),H=function(e){F({message:e,type:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",duration:arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3})},K=function(){var e=c(r().m((function e(n){return r().w((function(e){for(;;)if(0===e.n)return e.a(2,new Promise((function(e,t){var a,o=!1,i=function(){var i=c(r().m((function i(){var c,p,l;return r().w((function(r){for(;;)switch(r.n){case 0:return r.p=0,r.n=1,new Promise((function(e,n){if(q.value&&q.value.readyState===WebSocket.OPEN)e(q.value);else{var t=new WebSocket("ws://localhost:50001");X.value=!0,t.onopen=function(){console.log("WebSocket Connected"),q.value=t,X.value=!1,e(t)},t.onmessage=function(e){var n=e.data;n.startsWith("Ok")||n.startsWith("Failed")&&H(n,"error")},t.onclose=function(){console.log("WebSocket Disconnected"),q.value=null,X.value=!1},t.onerror=function(e){console.error("WebSocket Error:",e),X.value=!1,n(e)},setTimeout((function(){X.value&&(X.value=!1,t.close(),n(new Error("连接超时")))}),5e3)}}));case 1:c=r.v,p={action:3,msg:n},a=setTimeout((function(){o||(c.close(),t(new Error("启动超时：未收到响应")))}),3e3),c.onmessage=function(n){o=!0,clearTimeout(a);var r=n.data;r.startsWith("Ok")?e():t(new Error(r))},c.send(JSON.stringify(p)),console.log("发送消息:",p),r.n=3;break;case 2:r.p=2,l=r.v,clearTimeout(a),t(l);case 3:return r.a(2)}}),i,null,[[0,2]])})));return function(){return i.apply(this,arguments)}}();i()})))}),e)})));return function(n){return e.apply(this,arguments)}}(),Q=function(){var e=c(r().m((function e(n){var t;return r().w((function(e){for(;;)switch(e.n){case 0:if(n.WebUrl&&!n.maint){e.n=1;break}return e.a(2);case 1:if(!n.WebUrl.toLowerCase().startsWith("cs:")){e.n=6;break}return t=n.WebUrl.substring(3),e.p=2,H("正在启动爱尔企业浏览器...","info"),e.n=3,K(t);case 3:H("启动成功","success"),e.n=5;break;case 4:e.p=4,e.v,H("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3);case 5:e.n=7;break;case 6:window.open(n.WebUrl,"_blank");case 7:return e.a(2)}}),e,null,[[2,4]])})));return function(n){return e.apply(this,arguments)}}();p((function(){q.value&&(q.value.close(),q.value=null)}));var Y=function(e){for(var n=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,a=0;a<e.length;a++)t+=e.charCodeAt(a);return n[t%n.length]},Z=function(){B.value=!0},$=function(e){N.value=parseInt(e),M.value=e?G.value.filter((function(n){return n.id===parseInt(e)})):G.value},ee=function(){if(y.value){var e=y.value.toLowerCase().trim();M.value=G.value.map((function(n){return t(t({},n),{},{apps:n.apps.filter((function(n){return n.app_name.toLowerCase().includes(e)}))})})).filter((function(e){return e.apps.length>0}))}else M.value=G.value},ne=function(){var e=c(r().m((function e(){var n,t,o,i;return r().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,a({url:"/console/v1/application/getuserapp",method:"get"});case 1:n=e.v,t=n.data,console.log("API返回数据:",t),0===t.code&&t.data&&(o=t.data.map((function(e,n){return{id:n+1,name:e.category,apps:e.apps.map((function(e){return{id:e.id,app_name:e.app_name,app_desc:e.app_type,icon:e.icon,maint:2===e.maintenance,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl}}))}})),console.log("格式化后的数据:",o),G.value=o,M.value=o,o.length>0&&(N.value=o[0].id,V.value=o[0].id.toString())),e.n=3;break;case 2:e.p=2,i=e.v,console.error("API调用出错:",i);case 3:return e.a(2)}}),e,null,[[0,2]])})));return function(){return e.apply(this,arguments)}}();l((function(){ne()}));var te=s(),ae=d().query,re=null;try{if(!u.isClient()){var oe=new XMLHttpRequest;oe.open("GET",document.location,!1),oe.send(null),re=oe.getResponseHeader("X-Corp-ID")}}catch(fe){console.warn("无法获取 X-Corp-ID header，使用默认值:",fe)}var ie={action:0,msg:{token:te.token.accessToken,refreshToken:te.token.refreshToken,realm:re||"default"},platform:document.location.hostname},ce=ae.wp||50001,pe=o({}),le=o("ws://127.0.0.1:".concat(ce)),se=navigator.platform;0!==se.indexOf("Mac")&&"MacIntel"!==se||(le.value="wss://127.0.0.1:".concat(ce));var de=function(e){console.log(e,"0"),pe.value.send(e)},ue=function(){console.log("socket断开链接"),pe.value.close()};return console.log("asecagent://?web=".concat(JSON.stringify(ie))),pe.value=new WebSocket(le.value),pe.value.onopen=function(){console.log("socket连接成功"),de(JSON.stringify(ie))},pe.value.onmessage=function(e){console.log(e),ue()},pe.value.onerror=function(){console.log("socket连接错误:"+le.value),window.location.href="asecagent://?web=".concat(JSON.stringify(ie))},function(e,t){var a=f("base-input"),r=f("base-button"),o=f("base-option"),i=f("base-select"),c=f("base-header"),p=f("base-menu-item"),l=f("base-menu"),s=f("base-aside"),d=f("base-avatar"),u=f("base-tooltip"),F=f("base-link"),N=f("base-main"),q=f("base-container");return g(),m("div",null,[h("div",n,[v(c,null,{default:x((function(){return[t[3]||(t[3]=h("span",{class:"el-title"},"我的应用",-1)),h("span",C,[v(a,{class:"el-search-input",modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return y.value=e}),placeholder:"搜索应用","prefix-icon":"Search",onInput:ee,clearable:"",style:{width:"200px"}},null,8,["modelValue"]),v(r,{class:"el-search-btn",icon:"Refresh",size:"small"}),v(i,{class:"el-search-select","suffix-icon":"CaretTop",modelValue:J.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return J.value=e}),placeholder:"Select",size:"small"},{default:x((function(){return[(g(!0),m(w,null,k(L,(function(e){return g(),_(o,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]),b('\r\n        <div class="el-row">\r\n          <span class="el-recent-access">最新访问</span>\r\n          <span class="el-recent-data">\r\n            <span class="el-recent-item">\r\n              最新访问1\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问2\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <span class="el-recent-item">\r\n              最新访问3\r\n              <base-icon class="el-recent-icon" name="close" />\r\n            </span>\r\n            <base-icon class="el-recent-clear" name="close" title="清空" />\r\n          </span>\r\n        </div>\r\n        ')]})),_:1,__:[3]}),b(" 主体内容区域：使用 el-container 实现左右布局 "),v(q,null,{default:x((function(){return[b(" 左侧分类导航 "),v(s,{width:"96px",class:"category-aside"},{default:x((function(){return[v(l,{class:"category-menu",mode:"vertical",onSelect:$,"default-active":V.value},{default:x((function(){return[v(p,{index:"0",onClick:t[2]||(t[2]=function(e){return $(null)})},{default:x((function(){return t[4]||(t[4]=[S(" 全部 ")])})),_:1,__:[4]}),(g(!0),m(w,null,k(G.value,(function(e){return g(),_(p,{key:e.id,index:e.id.toString()},{default:x((function(){return[S(O(e.name),1)]})),_:2},1032,["index"])})),128))]})),_:1},8,["default-active"])]})),_:1}),b(" 右侧应用列表 "),v(N,{class:"app-main"},{default:x((function(){return[(g(!0),m(w,null,k(M.value,(function(e){return g(),m("div",{key:e.id,class:"category-section"},[b(" 分类标题 "),h("h3",W,O(e.name),1),b(" 应用列表 "),h("div",D,[(g(!0),m(w,null,k(e.apps,(function(e){return g(),m("div",{key:e.id,class:P(["app-card",{disabled:!e.WebUrl||e.maint}])},[e.maint?(g(),m("div",z," 维护中 ")):b("v-if",!0),v(F,{class:"app_list",underline:!1,disabled:!e.WebUrl||e.maint,onClick:function(n){return Q(e)}},{default:x((function(){return[h("div",T,[v(u,{effect:"light",placement:"bottom"},{content:x((function(){return[h("div",E,[e.WebUrl?(g(),m("span",U,O(e.WebUrl),1)):(g(),m("span",I,"暂无访问地址"))])]})),default:x((function(){return[v(d,{shape:"square",size:48,src:e.icon,onError:Z,style:j(!e.icon||B.value?"background-color: ".concat(Y(e.app_name)," !important"):"")},{default:x((function(){return[S(O(!e.icon||B.value?e.app_name.slice(0,2):""),1)]})),_:2},1032,["src","style"])]})),_:2},1024)]),h("div",R,[h("div",A,O(e.app_name),1),t[5]||(t[5]=h("div",{class:"app-remark"}," 这是一段应用程序的描述信息。 ",-1))])]})),_:2},1032,["disabled","onClick"])],2)})),128))])])})),128))]})),_:1})]})),_:1})])])}}});e("default",y(N,[["__scopeId","data-v-f0c977cd"],["__file","D:/asec-platform/frontend/portal/src/view/app/index.vue"]]))}}}))}();
