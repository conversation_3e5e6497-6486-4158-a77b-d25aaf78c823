<template>
  <label class="base-checkbox" :class="{ 'is-disabled': disabled, 'is-checked': isChecked }">
    <span class="base-checkbox__input">
      <span class="base-checkbox__inner"></span>
      <input
        type="checkbox"
        class="base-checkbox__original"
        :disabled="disabled"
        :value="label"
        v-model="model"
        @change="handleChange"
      >
    </span>
    <span v-if="$slots.default || label" class="base-checkbox__label">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script>
export default {
  name: 'BaseCheckbox',
  props: {
    modelValue: {
      type: [<PERSON>olean, String, Number, Array],
      default: false
    },
    label: {
      type: [String, Number, Boolean],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['large', 'default', 'small'].includes(value)
    }
  },
  emits: ['update:modelValue', 'change'],
  computed: {
    model: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    isChecked() {
      if (Array.isArray(this.modelValue)) {
        return this.modelValue.includes(this.label)
      }
      return this.modelValue === true
    }
  },
  methods: {
    handleChange(e) {
      this.$emit('change', e.target.checked)
    }
  }
}
</script>

<style scoped>
.base-checkbox {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  user-select: none;
  margin-right: 30px;
}

.base-checkbox.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.base-checkbox__input {
  white-space: nowrap;
  cursor: pointer;
  outline: none;
  display: inline-flex;
  position: relative;
}

.base-checkbox__inner {
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  z-index: 1;
  transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
    background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}

.base-checkbox__inner::after {
  box-sizing: content-box;
  content: "";
  border: 1px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(0);
  width: 3px;
  transition: transform 0.15s ease-in 0.05s;
  transform-origin: center;
}

.base-checkbox.is-checked .base-checkbox__inner {
  background-color: #536ce6;
  border-color: #536ce6;
}

.base-checkbox.is-checked .base-checkbox__inner::after {
  transform: rotate(45deg) scaleY(1);
}

.base-checkbox.is-disabled .base-checkbox__inner {
  background-color: #edf2fc;
  border-color: #dcdfe6;
}

.base-checkbox__original {
  opacity: 0;
  outline: none;
  position: absolute;
  margin: 0;
  width: 0;
  height: 0;
  z-index: -1;
}

.base-checkbox__label {
  display: inline-block;
  padding-left: 8px;
  line-height: 19px;
  font-size: 14px;
}
</style>
